<?php
/**
* <PERSON><PERSON><PERSON> GClient - uses SOAP
*
* <AUTHOR> | zvarik.cz
* @version 2019-05-17
*/
class Geis
{
	private $inputEncoding = 'UTF-8'; // default input encoding
	private $outputEncoding = 'UTF-8'; // default output encoding
	
	private $clientNumber;
	private $clientPassword;
	public $client;
	private $language;
	
	private $defaultDistributionChannel;
	const PARCEL = 1;
	const CARGO = 2;
	
	private $serviceType;
	
	
	/**
	* __construct
	*/
	function __construct($clientNumber, $clientPassword, $serviceType = 'CZ', $testService = false)
	{
		$this->defaultDistributionChannel = Geis::PARCEL; // default
		
		$this->clientNumber = $clientNumber;
		$this->clientPassword = $clientPassword;
		
		if ($serviceType == 'PL')
		{
			// PL (ET Logistik)
			$this->language = 'PL';
			if ($testService) {
				$url = 'https://gclient.geis.pl/GServiceTest/GService.svc';
			} else {
				$url = 'https://gclient.geis.pl/GService/GService.svc';
			}
		}
		elseif ($serviceType == 'PL2')
		{
			// PL (K-EX Sp. Z.o.o.)
			$this->language = 'PL';
			if ($testService) {
				$url = 'https://gclient.etlogistik.com/GServiceTest/GService.svc';
			} else {
				$url = 'https://gclient.etlogistik.com/GService/GService.svc';
			}
		}
		elseif ($serviceType == 'SK')
		{
			$this->language = 'SK';
			if ($testService) {
				$url = 'https://gclient.geis.cz/GServiceTest/GService.svc';
			} else {
				$url = 'https://gclient.geis.cz/GService/GService.svc';
			}
		}
		elseif ($serviceType == 'CZ')
		{
			$this->language = 'CZ';
			if ($testService) {
				$url = 'https://gclient.geis.cz/GServiceTest/GService.svc';
			} else {
				$url = 'https://gclient.geis.cz/GService/GService.svc';
			}
		}
		else {
			throw new Exception('Uknown Service Type');
		}
		
		$this->serviceType = $serviceType;
		
		
		$url.= '?wsdl';
		
		$client = new SoapClient(
			$url,
			array(
				'location' => $url,
				'trace' => 1,
				'cache_wsdl' => WSDL_CACHE_NONE,
				'soap_version' => SOAP_1_1 ,
				'exceptions' => 0, // Soap vyhodí vlastní chybu s popisem
				'encoding' => 'UTF-8',
				'stream_context'=> stream_context_create(array(
					'ssl'=> array(
						'verify_peer'=>false,
						'verify_peer_name'=>false, 
						'allow_self_signed' => true
						)
					)
				)
			)
		);
		
		$this->client = $client;
		
		//var_dump($client->__getFunctions());
		//var_dump($client->__getTypes());
	}
	
	
	/**
	* Call any Soap function
	*/
	function __call($name, $params)
	{
		$args = array(
			'Request' => array(
				'Header' => array(
					'CustomerCode' => $this->clientNumber,
					// CZ, SK, EN, DE, PL
					'Language' => $this->language,
					'Password' => $this->clientPassword
				),
				'RequestObject' => isset($params) ? self::myEncode($params, $this->inputEncoding, 'UTF-8') : null
			)
		);
		
		$ret = $this->client->$name( $args );
		
		if (!$ret) {
			throw new Exception('Server error (not available, timeout...) ' . $php_errormsg, -1);
		}
		
		if (isset($ret->{$name.'Result'})) {
			$ret = $ret->{$name.'Result'};
		}
		
		if ($ret && isset($ret->ErrorCode) && $ret->ErrorCode && preg_match('~[^0\s]~', $ret->ErrorCode))
		{
			throw new Exception($ret->ErrorMessage, (string) $ret->ErrorCode); // error
		}
		
		if (isset($ret->ResponseObject)) {
			$ret = $ret->ResponseObject;
		}
		
		return self::myEncode($ret, $this->outputEncoding, 'UTF-8');
	}
	
	
	/**
	* Převede kodování
	*/
	static function myEncode($mixed, $from, $to)
	{
		if ($from == $to) {
			return $mixed; // no change
		}
		
		if (is_object($mixed)) {
			foreach ($mixed as $k => $v) {
				$mixed->$k = self::myEncode($v, $from, $to);
			}
		}
		elseif (is_array($mixed)) {
			foreach ($mixed as $k => $v) {
				$mixed[$k] = self::myEncode($v, $from, $to);
			}
		}
		elseif (is_string($mixed)) {
			$mixed = iconv($from, $to.'//TRANSLIT//IGNORE', $mixed);
		}
		
		return $mixed;
	}
	
	
	/**
	* setEncoding - Changes default encoding
	*/
	function setEncoding($enc)
	{
		$this->inputEncoding = $enc;
		$this->outputEncoding = $enc;
	}
	
	
	/**
	* Nastaví výchozí typ služby / Sets type of service
	* @param $distributionChannel - Geis::PARCEL, Geis::CARGO
	*/
	function setDefaultDistribution($distributionChannel)
	{
		$this->defaultDistributionChannel = (int) $distributionChannel;
	}

	
	/**
	* Returns the first possible pickup date, which is work day /or same day if before 14 a.m.
	*
	* @param (int) Ym = 1400 - if before certain time (14 a.m.), the same day will be returned (if workday)
	* @return (string) YYYY-MM-DD
	*/
	function getNextPossiblePickup($Hm = 1400)
	{
		$ts = time();
		$day = date("N");
		$Hi = (int) date("Hi");
		
		if ($day <= 5 && $Hi < $Hm); // it is work day and before 14 a.m.
		elseif ($day == 5) $ts = strtotime("+3 day"); // friday
		elseif ($day == 6) $ts = strtotime("+2 day"); // saturday
		else $ts = strtotime("+1 day");
		
		return date("Y-m-d", $ts);
	}
	

	/**
	* Returns address
	*
	* @used-by InsertExport function
	*
	* @param (string) $name
	* @param (string) $street
	* @param (string) $city
	* @param (string) $zipcode
	* @param (string) $countryCode - 2 char ISO
	* @param (string) $email
	* @param (string) $phone
	*/
	static function getFullAddress($name, $street, $city, $zipcode, $countryCode, $email, $phone)
	{
		$ret = (object) array();
		$ret->address = self::getAddress($name, $street, $city, $zipcode, $countryCode);
		$ret->contact = self::getContact($email, $phone, $countryCode, $name);
		$ret->email = $ret->contact['Email'];
		$ret->phone = $ret->contact['Phone'];
		return $ret;
	}
	
	/**
	* Returns address
	*
	* @param (string) $name
	* @param (string) $street
	* @param (string) $city
	* @param (string) $zipcode
	* @param (string) $countryIso - 2 char ISO
	*/
	static function getAddress($name, $street, $city, $zipcode, $countryIso)
	{
		return array(
			'Name' => trim($name),
			'Street' => trim($street),
			'City' => trim($city),
			'Country' => trim($countryIso),
			'ZipCode' => trim($zipcode),
		);
	}
	
	/**
	* Returns contact
	*
	* @param (string) $email
	* @param (string) $phone
	* @param (string) $countryIso = null (only for formatting phone prefix) - 2 char ISO
	*/
	static function getContact($email, $phone, $countryIso = null, $fullname = '')
	{
		return array(
			'Email' => strtolower(trim($email)),
			'FullName' => $fullname, // kontaktní údaj - je zapotřebí u výdejního místa
			'Phone' => self::standardizePhoneNumber($phone, $countryIso)
		);
	}
	
	
	/**
	* Clean phone number, returns like +420732123456
	*
	* @param string Phone Number
	* @param string Add Code Prefix by country ISO code (will be added only if needed)
	*/
	static function standardizePhoneNumber($s, $phonePrefix = null)
	{
		$s = preg_replace("~(^00|(?<!^)\+|[^0-9\+])~", "", $s);
		
		if (strpos($s, '+') === FALSE)
		{
			$prefixesByIso = array(
				'CZ' => '+420',
				'SK' => '+421',
				'PL' => '+48',
				'DE' => '+49',
				'AT' => '+43',
				'CH' => '+41'
			);
			
			if (isset($phonePrefix) && isset($prefixesByIso[strtoupper($phonePrefix)])) {
				$phonePrefix = $prefixesByIso[strtoupper($phonePrefix)];
			}
			
			$noSignsPrefix = preg_replace("~[^0-9]~", "", $phonePrefix);
			if ($noSignsPrefix == substr($s, 0, strlen($noSignsPrefix))) {
				$s = '+' . $s; // only plus was missing
			}
			else {
				$s = $phonePrefix . $s;
			}
		}
		return $s;
	}
	
	
	/**
	* Format number
	* @return (string)
	*/
	static function formatNum($str)
	{
		if (is_string($str)) {
			if (strpos($str, ".") === false) {
				$str = str_replace(",", ".", $str);
			}
			$str = trim($str);
		}
		return number_format($str, 2, ".", "");
	}
	

	#######################################################################################################
	#
	# FUNCTIONS LISTED IN G-SERVICE DOCUMENTATION
	#

	/**
	* List of extra services
	*
	* Vrací seznam doplňkových služeb, které má zákazník nastaven v primárním systému.
	* Mezi tyto služby může patřit například COD - Dobírka, POJ – pojištění atd.
	*/
	function AddServiceList($service_id, $country = 'CZ')
	{
		$res = $this->__call('AddServiceList', array(
			'DeliveryCountry' => $country,
			'Service' => $service_id
		));
		return $res;
	}
	
	
	/**
	* Create Pickup
	*
	* @param (string) $dateFrom YYYY-MM-DD HH:II
	* @param (string) $phone
	* @param (string) $email
	* @param (int) $countItems = 1
	* @param (string) $note
	* @param (float) $totalWeight - Weight in Kg
	* @param (int) $temperatureMode - 1/2
	* @param (int) $distributionChannel = default -- Geis::PARCEL, Geis::CARGO
	*
	* @return (void) Either books the collection or throws an exception
	*/
	function CreatePickup($dateFrom, $phone, $email, $countItems = 1, $note = '', $totalWeight = null, $temperatureMode = null, $distributionChannel = null)
	{
		$dateFrom = (new DateTime($dateFrom))->format("c");
		
		$args = array(
			'Contact' => self::getContact($email, $phone, $countryIso = substr($this->serviceType, 0, 2)),
			'CountItems' => $countItems,
			'DateFrom' => $dateFrom,
			'DistributionChannel' => isset($distributionChannel) ? $distributionChannel : $this->defaultDistributionChannel,
			'Note' => $note
		);
		if (isset($totalWeight)) {
			$args['TotalWeight'] = self::formatNum($totalWeight);
		}
		if (isset($temperatureMode)) {
			$args['TemperatureMode'] = $temperatureMode;
		}
		
		$res = $this->__call('CreatePickUp', $args);

		return $res;
	}
	
	
	/**
	* StatusList
	* @param $distributionChannel - Geis::PARCEL, Geis::CARGO
	* @return (object)
	*/
	function StatusList($distributionChannel = null)
	{
		$res = $this->__call('StatusList', array(
			'DistributionChannel' => isset($distributionChannel) ? $distributionChannel : $this->defaultDistributionChannel
		));
		return $res;
	}
	
	
	/**
	* New shipment
	*/
	private function _Insert(
		$deliveryFrom,
		$pickupDate,
		$deliveryTo,
		$packages,
		$noteDriver = null,
		$COD = null,
		$mailConfirmation = true,
		$reference = '',
		$B2C = false,
		$pickupPointId = 0,
		$distributionChannel = null
		)
	{
		$params	= array();
		$fn = 'InsertExport';
		
		if ($deliveryFrom != null) {
			// InsertOrder function
			$fn = 'InsertOrder';
			$params['SenderAddress'] = $deliveryFrom->address;
			$params['SenderContact'] = $deliveryFrom->contact;
		}
		
		
		$params['DeliveryAddress'] = $deliveryTo->address;
		
		$params['DistributionChannel'] = isset($distributionChannel) ? $distributionChannel : $this->defaultDistributionChannel;
		
		$countItems = count($packages);
		$firstPackage = array_shift($packages); // first package is skipped
		
		// this is array of all shipments for this recipient (I THINK) - just call InsertExport() multiple times
		// @note: MergedItems is used to include more packages in one shipment
		$params['ExportItems']['ExportItem'][0] = array(
			'CountItems' => $countItems,
			'Description' => '',
			'Type' => $firstPackage[4],
			'Weight' => self::formatNum($firstPackage[0]),
			'Width' => self::formatNum($firstPackage[1]),
			'Height' => self::formatNum($firstPackage[2]),
			'Length' => self::formatNum($firstPackage[3])
		);
		
		$params['DeliveryContact'] = $deliveryTo->contact;
			
		/* BurstId */ // Packages ID, you can use in method ListOfShipments
		/* CoverAddress */ // Address Krycí adresa
		
		$pickupDate = (new DateTime($pickupDate))->format("Y-m-d");
		$params['PickUpDate'] = $pickupDate;
		
		
		// this will combine packages as one shipment
		if ($countItems <= 1) {
			$params['MergedShipment'] = 0;
		}
		else {
			$params['MergedPackages'] = array();
			
			foreach ($packages as $package) {
				$params['MergedPackages']['MergedItem'][] = array(
					// PackNumber
					// PartnerNumber
					'Weight' => self::formatNum($package[0])
				);
			}
			$params['MergedShipment'] = 1;
		}
		
		// $params['Note'] // Note to receiver
		
		$params['NoteDriver'] = $noteDriver; // Note to driver
		
		/* PartnerNumber */
		
		$params['Reference'] = $reference;
		
		//$xml.= '<ShipmentNumber></ShipmentNumber>';
		
		$params['Weight'] = $firstPackage[0]; // weight (kg decimal) - not combined weight, the first package weight
		
		
		//--------------------- EXTRA SERVICES - call AddServiceList()
		$params['ExportServices'] = array();
		
		if (!empty($COD) && $COD[0] > 0) {
			// Cash On Delivery (dobírka)
			
			// CODE = 2 - Might be different, call AddServiceList()
			$params['ExportServices']['ExportService'][] = array(
				'Code' => 2,
				'Parameter_1' => number_format($COD[0], 2, '.', ''),
				'Parameter_2' => $COD[1], // currency
				'Parameter_3' => $COD[2] // variable symbol
			);
		}
		
		if ($mailConfirmation) {
			// CODE = 34 - Might be different, call AddServiceList()
			$params['ExportServices']['ExportService'][] = array(
				'Code' => 34,
				'Parameter_1' => $deliveryTo->email,
			);
		}
		
        if (strlen($pickupPointId) > 4) {
			// VYDEJNI MISTO - GEIS POINT PICKUP
			$params['ExportServices']['ExportService'][] = array(
				'Code' => 111,
				'Parameter_1' => $pickupPointId,
			);
		}
		
		if ($B2C) {
			// CODE = 31 - Might be different, call AddServiceList(10)
			$params['ExportServices']['ExportService'][] = array(
				'Code' => 31,
				'Parameter_1' => $deliveryTo->phone,
			);
		}
		
		
		$res = $this->__call($fn, $params);
		return $res;
	}
	
	/*
	* New package to be sent from your pickup place
	* Nová expedice
	*
	* @param (string) $pickupDate YYYY-MM-DD
	* @param (self::getFullAddress) $deliveryTo - Shipment Receiver / Příjemce zásilky
	* @param (array) $packages (weight_kg | width | height | length)
	* @param (string) $noteDriver = null - Note to driver
	* @param (array) $COD = null (amount | currency | variable symbol)
	* @param (bool) $mailConfirmation = true - sends an email confirmation to receiver
	* @param (string) $reference = null - Package Reference string
	* @param (bool) $B2C - true if private address / zda je to soukromá adresa
	* @param (string) $pickupPointId = null - Geis Pickup Point destination ID
	* @param (int) $distributionChannel = default - choose Geis::CARGO or Geis::PARCEL
	*/
	function InsertExport(
		$pickupDate,
		$deliveryTo,
		$packages,
		$noteDriver = null,
		$COD = null,
		$mailConfirmation = true,
		$reference = '',
		$B2C = false,
		$pickupPointId = 0,
		$distributionChannel = null
		)
	{
		$args = func_get_args();
		array_unshift($args, null);
		return call_user_func_array(array(__CLASS__, '_Insert'), $args);
	}
	
	/**
	* New package to be sent from place A to B
	* Nová objednávka z místa A do B
	*
	* @param (self::getFullAddress) $deliveryFrom - Shipment Sender / Odesílatel zásilky
	* @param (string) $pickupDate YYYY-MM-DD
	* @param (self::getFullAddress) $deliveryTo - Shipment Receiver / Příjemce zásilky
	* @param (array) $packages (weight_kg | width | height | length)
	* @param (string) $noteDriver = null - Note to driver
	* @param (array) $COD = null (amount | currency | variable symbol)
	* @param (bool) $mailConfirmation = true - sends an email confirmation to receiver
	* @param (string) $reference = null - Package Reference string
	* @param (bool) $B2C - true if private address / zda je to soukromá adresa
	* @param (string) $pickupPointId = null - Geis Pickup Point destination ID
	* @param (int) $distributionChannel = default - choose Geis::CARGO or Geis::PARCEL
	*/
	function InsertOrder(
		$deliveryFrom,
		$pickupDate,
		$deliveryTo,
		$packages,
		$noteDriver = null,
		$COD = null,
		$mailConfirmation = true,
		$reference = '',
		$B2C = false,
		$pickupPointId = 0,
		$distributionChannel = null
		)
	{
		return call_user_func_array(array(__CLASS__, '_Insert'), func_get_args());
	}
	
	
	/**
	* GetLabel / Štítek (PDF)
	*
	* @param (string/array) $ShipmentNumbers - Čísla zásilek
	* @param (int) $Position 1-4
	* @param (int) $Format - PDF, EPL, ZPL, BMP, PDF_TERMO
	* @param (int) $distributionChannel = default -- Geis::PARCEL, Geis::CARGO
	* @param (bool) $printOutput = false
	*/
	function GetLabel($ShipmentNumbers, $Position = 1, $Format = 'PDF', $distributionChannel = null, $printOutput = false)
	{
		$formats = array(
			'PDF' => 1, 'EPL' => 2, 'ZPL' => 3, 'BMP' => 4, 'PDF_TERMO' => 5
		);
		if (!isset($formats[$Format])) {
			throw new Exception('getLabels - Invalid Format');
		}
		$Format = $formats[$Format];
		 
		$params = array();
		$params['DistributionChannel'] = isset($distributionChannel) ? $distributionChannel : $this->defaultDistributionChannel;
		$params['Format'] = $Format;
		// Resolution
		$params['Position'] = $Position;
		$params['ShipmentNumbers'] = array();
		if (!is_array($ShipmentNumbers)) $ShipmentNumbers = array($ShipmentNumbers);
		foreach ($ShipmentNumbers as $num) {
			$params['ShipmentNumbers']['LabelItem'][] = array(
				'ShipmentNumber' => $num
			);
		}
		
		$res = $this->__call('GetLabel', $params);
		
		$content = $res->LabelData->LabelItemData->Data;
		
		if (!$content) return false; // nothing to print
		
		if ($printOutput) {
			ob_end_clean();
			header("Content-Type: application/pdf");
			echo $content;
		}
		else {
			return $content;
		}
	}
	
	/**
	* printLabels - Outputs labels (PDF)
	*
	* @param (string/array) $ShipmentNumbers - Čísla zásilek
	* @param (int) $Position 1-4
	* @param (int) $Format - PDF, EPL, ZPL, BMP, PDF_TERMO
	* @param (bool) $printOutput = false
	*/
	function PrintLabel($ShipmentNumbers, $Position = 1, $Format = 'PDF')
	{
		return $this->GetLabel($ShipmentNumbers, $Position, $Format, $printOutput = true);
	}
	
	
	
	/**
	* List Of Shipments
	*
	* @param (string) $DateExpOrOrder	YYYY-MM-DD, Date of expedition or order
	* @param (string) $BurstId			Group ID of the packages (not required)
	* @param (string) $Created			YYYY-MM-DD, Date of creation
	*/
	function ListOfShipments($DateExpOrOrder, $BurstId = null, $Created = null)
	{
		$params = array();
		$params['DateExpOrOrder'] = (new DateTime($DateExpOrOrder))->format("Y-m-d");
		if (isset($Created)) $params['Created'] = (new DateTime($Created))->format("Y-m-d");
		if (isset($BurstId)) $params['BurstId'] = $BurstId;
		
		$res = $this->__call('ListOfShipments', $params);
		return $res;
	}
	
	
	/**
	* ShipmentDetail / Detail zásilky
	*
	* @param (string) $ShipmentNumber Length 11/13 characters
	* @param (int) $distributionChannel = default -- Geis::PARCEL, Geis::CARGO
	*/
	function ShipmentDetail($ShipmentNumber, $distributionChannel = null)
	{
		$params = array();
		$params['DistributionChannel'] = isset($distributionChannel) ? $distributionChannel : $this->defaultDistributionChannel;
		$params['ShipmentNumber'] = $ShipmentNumber;
		$res = $this->__call('ShipmentDetail', $params);
		return $res;
	}
	
	
	/**
	* Smaže zásilku
	* @param (string/array) $ShipmentNumbers - Čísla zásilek
	* @param (int) $distributionChannel = default -- Geis::PARCEL, Geis::CARGO
	*/
	function DeleteShipment($ShipmentNumbers, $distributionChannel = null)
	{
		if (!is_array($ShipmentNumbers)) {
			$ShipmentNumbers = array($ShipmentNumbers);
		}
		
		$params = array();
		$params['ShipmentsNumbers'] = array();
		foreach ($ShipmentNumbers as $num) {
			$params['ShipmentsNumbers']['DeleteShipmentItem'][] = array(
				'DistributionChannel' => isset($distributionChannel) ? $distributionChannel : $this->defaultDistributionChannel,
				'ShipmentNumber' => $num
			);
		}
		$res = $this->__call('DeleteShipment', $params);
		return $res;
	}
	
	
	/**
	* GetPickupList / Předávací protokol
	*
	* @param (string) $ExpeditionCreateDate YYYY-MM-DD
	* @param (int) $distributionChannel = default -- Geis::PARCEL, Geis::CARGO
	* @param (bool) $printOutput = false
	*
	* @return (string) PDF
	*/
	function GetPickupList($ExpeditionCreateDate = null, $distributionChannel = null, $printOutput = false)
	{
		$params = array(
			'DistributionChannel' => isset($distributionChannel) ? $distributionChannel : $this->defaultDistributionChannel,
			'ExpeditionCreateDate' => (new DateTime($ExpeditionCreateDate))->format("c")
		);
		$res = $this->__call('GetPickupList', $params);
		
		$content = $res->PickupListData;
		if (!$content) return false; // nothing to print
		if ($printOutput) {
			header("Content-Type: application/pdf");
			echo $content;
			return;
		}
		else {
			return $content;
		}
	}
	
	/**
	* PrintPickupList / Předávací protokol - Vytiskne PDF včetně hlavičky
	*
	* @param (string) $ExpeditionCreateDate YYYY-MM-DD
	* @param (int) $distributionChannel = default -- Geis::PARCEL, Geis::CARGO
	*
	* @return (void) - Outputs the PDF directly
	*/
	function PrintPickupList($ExpeditionCreateDate = null, $distributionChannel = null)
	{
		return $this->GetPickupList($ExpeditionCreateDate, $distributionChannel, true);
	}
}

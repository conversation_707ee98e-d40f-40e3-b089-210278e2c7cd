FROM php:7.4-apache

#RUN version=$(php -r "echo PHP_MAJOR_VERSION.PHP_MINOR_VERSION;") \
#    && architecture=$(case $(uname -m) in i386 | i686 | x86) echo "i386" ;; x86_64 | amd64) echo "amd64" ;; aarch64 | arm64 | armv8) echo "arm64" ;; *) echo "amd64" ;; esac) \
#    && curl -A "Docker" -o /tmp/blackfire-probe.tar.gz -D - -L -s https://blackfire.io/api/v1/releases/probe/php/linux/$architecture/$version \
#    && mkdir -p /tmp/blackfire \
#    && tar zxpf /tmp/blackfire-probe.tar.gz -C /tmp/blackfire \
#    && mv /tmp/blackfire/blackfire-*.so $(php -r "echo ini_get ('extension_dir');")/blackfire.so \
#    && printf "extension=blackfire.so\nblackfire.agent_socket=tcp://blackfire:8707\n" > $PHP_INI_DIR/conf.d/blackfire.ini \
#    && rm -rf /tmp/blackfire /tmp/blackfire-probe.tar.gz


RUN apt-get update --fix-missing
RUN apt-get install -y curl libzip-dev libwebp-dev libicu-dev
RUN apt-get install -y build-essential libssl-dev zlib1g-dev libpng-dev libjpeg-dev libfreetype6-dev

RUN apt-get update && \
    apt-get install -y libxml2-dev
RUN docker-php-ext-install soap && docker-php-ext-enable soap

RUN a2enmod rewrite

RUN apt-get update \
 && DEBIAN_FRONTEND=noninteractive apt-get install -y ssl-cert \
 && rm -r /var/lib/apt/lists/*
RUN a2enmod ssl && a2ensite default-ssl

RUN docker-php-ext-install mysqli pdo   pdo_mysql opcache zip && docker-php-ext-enable pdo_mysql opcache
RUN docker-php-ext-install intl
RUN docker-php-ext-install bcmath
RUN docker-php-ext-configure gd --enable-gd --with-freetype --with-jpeg --with-webp
RUN docker-php-ext-install gd
RUN echo "session.save_path=\"/tmp\"" >> /usr/local/etc/php/php.ini
RUN echo "session.cookie_secure=\"0\"" >> /usr/local/etc/php/php.ini
RUN echo "session.cookie_lifetime=\"1209600\"" >> /usr/local/etc/php/php.ini
RUN echo "upload_max_filesize = 100M " >> /usr/local/etc/php/php.ini
RUN echo "post_max_size = 100M " >> /usr/local/etc/php/php.ini
RUN echo "memory_limit = 2048M " >> /usr/local/etc/php/php.ini
RUN echo "max_execution_time = 240 " >> /usr/local/etc/php/php.ini

#RUN pecl install xdebug
#COPY php/php.ini /etc/php/7.4/apache2/php.ini
#COPY php/php.ini /usr/local/etc/php/php.ini

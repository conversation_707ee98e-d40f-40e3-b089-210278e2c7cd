<?php

class TemplateFilters {
    /** @var string */
    private $wwwDir;

    /** @var int */
    private $formatPriceDecimals=0;

    /** @var string */
    private $formatPriceCurrency='Kč';

    /** @var array */
    private $neonParameters = array();

    public function __construct($wwwDir, array $neonParameters) {
      $this->wwwDir = $wwwDir;
      if (!empty($neonParameters) !== NULL) {
        $this->formatPriceCurrency = (string)$neonParameters['currency'][1]["code"];
        $this->formatPriceDecimals = (int)$neonParameters['currency'][1]["decimals"];
        $this->neonParameters = $neonParameters;
      }
    }

  /**
   * Method we will register as callback
   * in method $template->addFilter().
   * @param $helper
   * @return mixed
   */
    public function loader($helper) {
      if (method_exists($this, $helper)) {
        //return [$this, $helper];
      }
      return call_user_func_array([$this, $helper], array_slice(func_get_args(), 1));
    }

    /*
  ===============  PRÁCE S OBRÁZKY  ======================
  */

  /**
   * vygeneruje obrázek
   *
   * @param $fileName string
   * @param $type
   * @param string $size
   * @return boolean
   * @throws \Nette\Utils\UnknownImageFileException
   */
  private function genPic($fileName, $type, $size, $fromEmpty=TRUE) {
    //podle $path a $size zjistím velikost požaovaného obrázku
    $path = $this->wwwDir . '/pic/' . $type . '/';


    //zjistím jestli existuje adresář s velikostí
    if (!is_dir($path . '/' . $size)) {
      if (!mkdir($path . '/' . $size) && !is_dir($path . '/' . $size)) {
        return FALSE;
      }
    }

    //zjistim jestli existuje obrázek
    if (!file_exists($path . '/' . $size . '/' . $fileName)) {

      $pathSrc = $path . 'src/';
      $srcFile = "";
      if (file_exists($pathSrc . '/' . $fileName)) {
        $srcFile = $pathSrc . '/' . $fileName;
      } else if ($fromEmpty) {
        $srcFile = $this->wwwDir . '/pic/no.jpg';
      }

      if (!empty($srcFile)) {

        $w = '';
        $h = '';

        //zjistím rozměry
        if (strpos($size, 'x') !== FALSE) {
          list($w, $h) = explode('x', $size);
        }

        if (empty($w) || empty($h)) {
          return false;
        }

        //obrazek vygeneruju
        try {
          $pic = Nette\Utils\Image::fromFile($srcFile);
        } catch (Exception $e) {
          return FALSE;
        }

        $pic->resize($w, $h, \Nette\Utils\Image::EXACT); // resize, co přesahuje oříznu

        //->crop('50%', '50%', $w, $h)); // ořezání po stranách
        //$blank = Nette\Utils\Image::fromBlank($w, $h, Nette\Utils\Image::rgb(255, 255, 255));
        //$blank->place($pic, '50%', '50%');

        return $pic->save($path . '/' . $size . '/' . $fileName, 100, Nette\Utils\Image::JPEG);
      }
      return FALSE;
    }
    return TRUE;
  }

  /**
   * @param $fileName
   * @param $type
   * @param string $size
   * @param bool $fromEmptyPic
   * @return string
   * @throws \Nette\Utils\UnknownImageFileException
   */
  private function getPicName($fileName, $type, $size, $fromEmptyPic=TRUE) {
    $path = "pic/".$type."/".$size."/";

    if (!$this->genPic($fileName, $type, $size, $fromEmptyPic)) {
      return "";
    }
    $fileName = rawurlencode($fileName);
    return($path.$fileName);
  }


  /**
   * odstraní mezery, závorky z telefonního čísla pokud tam jsou
   *
   * @param $number
   * @return mixed
   */
    public function formatPhoneNumber($number) {
      $search = array(" ", "(", ")");
      $replace = array("", "", "");

      return trim(str_replace($search, $replace, $number));
    }

  /**
   * vraci nazev obrazku katalogu
   *
   * @param dibirow $row
   * @param string $size
   * @param bool $returnEmptyPic
   * @return string cesta k obrazku
   * @throws \Nette\Utils\UnknownImageFileException
   */
    public function getCatalogPicName($row, $size, $returnEmptyPic=TRUE) {
      return $this->getPicName($row->catid.'.jpg', 'catalog', $size, $returnEmptyPic);
    }

  /**
   * vraci nazev obrazku článku
   *
   * @param dibirow $row
   * @param string $size
   * @param bool $returnEmptyPic
   * @return string cesta k obrazku
   * @throws \Nette\Utils\UnknownImageFileException
   */
    public function getArtPicName($row, $size, $returnEmptyPic=TRUE) {
      return $this->getPicName($row->artid.'.jpg', 'article', $size, $returnEmptyPic);
    }

   /**
   * vraci nazev 2. obrázku článku
   *
   * @param dibirow $row
   * @param string $size
   * @param bool $returnEmptyPic
   * @return string cesta k obrazku
   * @throws \Nette\Utils\UnknownImageFileException
   */
    public function getArtPicNameBlock($row, $size, $returnEmptyPic=TRUE) {
      return $this->getPicName("block_" . $row->artid.'.jpg', 'article', $size, $returnEmptyPic);
    }

    /**
   * vraci nazev pozadí hlavičky článku
   *
   * @param dibirow $row
   * @param string $size
   * @param bool $returnEmptyPic
   * @return string cesta k obrazku
   * @throws \Nette\Utils\UnknownImageFileException
   */
    public function getArtPicNameBg($row, $size, $returnEmptyPic=TRUE) {
      return $this->getPicName("bg_" . $row->artid.'.jpg', 'article', $size, $returnEmptyPic);
    }

  /**
   * vraci nazev obrazku novinky
   *
   * @param dibirow $row
   *
   * @param string $size [list,detail,big]
   * @param bool $returnEmptyPic
   * @return string cesta k obrazku
   * @throws \Nette\Utils\UnknownImageFileException
   */
    public function getNewPicName($row, $size, $returnEmptyPic=TRUE) {
      return $this->getPicName($row->newid.'.jpg', 'new', $size, $returnEmptyPic);
    }

  /**
   * vraci nazev obrazku výrobce
   *
   * @param $row
   * @param string $size
   * @param bool $returnEmptyPic
   * @return string
   * @throws \Nette\Utils\UnknownImageFileException
   */
    public function getManufacturerPicName($row, $size, $returnEmptyPic=TRUE) {
      return $this->getPicName($row->manid.'.jpg', 'manufacturer', $size, $returnEmptyPic);
    }

  /**
   * vraci nazev obrazku zbozi
   *
   * @param $row
   * @param string $size
   * @param bool $returnEmptyPic
   * @param null $fileName
   * @return string
   * @throws \Nette\Utils\UnknownImageFileException
   */
    public function getProductPicName($row, $size, $returnEmptyPic=TRUE, $fileName=NULL) {
      if ($fileName === NULL) {
        $fileName = (!empty($row->propicname) ? $row->propicname : $row->procode) . '.jpg';
      }
      return $this->getPicName($fileName, 'product', $size, $returnEmptyPic);
    }

  /**
   * vraci nazev obrazku zbozi - pokud se jedna o variantu vrati nazev obrazku master polozky
   *
   * @param dibirow $row
   *
   * @param string $size [list,detail,big]
   * @param bool $empty
   * @return string cesta k obrazku
   * @throws \Dibi\Exception
   * @throws \Nette\Utils\UnknownImageFileException
   */
    public function getProductPicNameMaster($row, $size, $empty=FALSE) {
      if ((int)$row->promasid > 0) {
        $row = dibi::fetch("SELECT procode, propicname FROM products WHERE proid=%i", $row->promasid);
      }
      return $this->getProductPicName($row, $size, $empty);
    }

    /**
    * vraci nazev obrazku dopravy
    *
    * @param dibirow $row doprava
    * @param boolean $empty pokud obrázek neexistuje vrátí prázdný string
    *
    * @return string cesta k obrazku
    */
    public function getDelModePicName($row, $empty=FALSE) {
      $path = "img/delivery/";
      $picPath = $this->wwwDir."/".$path;
      $fileName = \Nette\Utils\Strings::lower($row->delcode).'.png';

      if (!file_exists($picPath.$fileName)) {
        if ($empty) {
          return "";
        } else {
          $fileName = "no.png";
        }
      }

      $fileName = rawurlencode($fileName);
      return($path.$fileName);
    }

    /**
    * vraci nazev obrazku platby
    *
    * @param dibirow $row doprava
    * @param boolean $empty pokud obrázek neexistuje vrátí prázdný string
    *
    * @return string cesta k obrazku
    */
    public function getPayModePicName($row, $empty=FALSE) {
      $path = "img/payment/";
      $picPath = $this->wwwDir."/".$path;
      $fileName = \Nette\Utils\Strings::lower($row->delcode).'.png';

      if (!file_exists($picPath.$fileName)) {
        if ($empty) {
          return "";
        } else {
          $fileName = "no.png";
        }
      }

      $fileName = rawurlencode($fileName);
      return($path.$fileName);
    }

    /**
    * vraci URL klic zbozi
    *
    * @param dibirow $row
    * @return string
    */
    public function getProKey($row) {
      return (!empty($row->prokey) ? $row->prokey : Nette\Utils\Strings::webalize($row->proname));
    }

    /**
    * vraci URL klic zbozi
    *
    * @param dibirow $row
    * @return string
    */
    public function getArtKey($row) {
      return (!empty($row->artkey) ? $row->artkey : Nette\Utils\Strings::webalize($row->arttitle));
    }

    /**
    * vraci URL klic katalogu
    *
    * @param dibirow $row
    * @return string
    */
    public function getCatKey($row) {
      return (!empty($row->catkey) ? $row->catkey : Nette\Utils\Strings::webalize($row->catname));
    }

    public function getDiscountInPer($pricecom, $price) {
      if ((int)$pricecom > $price) {
        return (round(($pricecom - $price) / $pricecom * 100)."%");
      } else {
        return "";
      }
    }

    public function getUrlKey($key, $name) {
      return((!empty($key) ? $key : Nette\Utils\Strings::webalize($name)));
    }

    public function getQty($qty) {
      return $this->getUnits($qty, "kus");
    }

    /**
   * vraci jednotky podle poctu kusu
   *
   * @param integer $qty pocet
   * @param string $unitName nazev jednotky
   * @return URL klic
   */
    public function getUnits($qty, $unitName) {
      $qtyText = $qty;
      if ($unitName == 'kus') {
        $text = "kusů";
        switch ((int)$qty) {
          case 1:
            $text = "kus";
            break;
          case 2:
          case 3:
          case 4:
            $text = "kusy";
            break;
        }
        if ($qty > 5) $qtyText = ">5";
      }
      if ($unitName == 'hodinou') {
        $text = "hodinami";
        switch ((int)$qty) {
          case 1:
            $text = "hodinou";
            break;
        }
      }
      if ($unitName == 'zákazník') {
        $text = "zákazníků";
        switch ((int)$qty) {
          case 1:
            $text = "zákazník";
            break;
          case 2:
          case 3:
          case 4:
            $text = "zákazníci";
            break;
        }

      }
      return ($qtyText.' '.$text);
    }


  /**
   * vrací naformátová štítky článku
   *
   * @param $tags
   * @return mixed
   */
    public function formatTags($tags) {
      return str_replace('|', ', ', trim($tags, '|'));
    }

  /**
   * vraci naformatovanou hmotnost
   *
   * @param mixed $weight
   * @param mixed $decimals
   * @return string
   */
    public function formatWeight($weight, $decimals=3) {
      //if ((double)$price == 0) return "na dotaz";
      $weight = (double)$weight;
      $formated = str_replace(" ", "\xc2\xa0", number_format($weight, $decimals, ",", " "));
      if ($decimals === 1) {
        $formated .= '0';
      }
      return $formated." Kg";
    }

    /**
   * vraci naformatovanou price
   *
   * @param mixed $price
   * @param mixed $decimals
   * @return string
   */
    public function formatPrice($price, $decimals=Null) {
      //if ((double)$price == 0) return "na dotaz";
      $price = (double)$price;
      if ($decimals == Null) $decimals = $this->formatPriceDecimals;
      $formated = str_replace(" ", "\xc2\xa0", number_format($price, $decimals, ",", " "));
      if ($decimals == 1) $formated .= '0';
      return $formated." ".$this->formatPriceCurrency;
    }

    public function formatNumber($number, $decimals) {
      //if ((double)$price == 0) return "na dotaz";
      $number = (double)$number;
      $formated = str_replace(" ", "\xc2\xa0", number_format($number, $decimals, ",", " "));
      return $formated;
    }

    /**
    * vraci naformatovanou cenu
    *
    * @param mixed $price
    * @param mixed $decimals
    */
    public function formatPriceByCurId($price, $curId) {
      //if ((double)$price == 0) return "na dotaz";
      $price = (double)$price;
      $priceCurrency = $this->formatPriceCurrency;
      $priceDecimals = $this->formatPriceDecimals;
      if (isset($this->presenter->neonParameters["currency"][$curId])) {
        $priceCurrency = $this->presenter->neonParameters["currency"][$curId]["code"];
        $priceDecimals = $this->presenter->neonParameters["currency"][$curId]["decimals"];
      }
      $formated = str_replace(" ", "\xc2\xa0", number_format($price, $priceDecimals, ",", " "));
      if ($priceDecimals == 1) $formated .= '0';
      return $formated." ".$priceCurrency;
    }

    public function commentStripTags($text, $isAdmin=FALSE) {
      $text = nl2br($text);
      if (!$isAdmin) {
        $text = strip_tags($text, '<br>');
      }
      return $text;
    }

    public function toUtf8 ($cp1250String) {
      return iconv('windows-1250','utf-8',$cp1250String);
    }

  /**
   * vrací hodnoty z textové proměnné |hodnota|hodnota|hodnota|
   *
   * @param $s
   * @return array|null
   */
    public function getTagValues($s) {
      $s = trim($s, '|');
      if (empty($s)) {
        return array();
      }
      return explode('|', $s);
    }

  /***
   * vrací klíč na odkaz na status objednávky
   *
   * @param $order
   * @return string
   */
    public function getOrderStatusKey($order) {
      return $order->ordid.substr(md5($order->ordid . $order->orddatec->getTimestamp()), 0, 8);
    }

    public Function isEgg($product) {
      $eggSwitch = (int)dibi::fetchSingle("SELECT cfgvalue FROM config WHERE cfgcode='EGG_SWITCH'");
      if ($eggSwitch === 1) {
        return (strpos($product->procode, 'VEJCE') === 0);
      }
      return FALSE;
    }

    public function getMonthName($date) {
      if (!$date) {
        return NULL;
      }
      $month = (int)$date->format("m");
      $year = $date->format("Y");

      $monthNames = [
        1 => "leden",
        2 => "únor",
        3 => "březen",
        4 => "duben",
        5 => "květen",
        6 => "červen",
        7 => "červenec",
        8 => "srpen",
        9 => "září",
        10 => "říjen",
        11 => "listopad",
        12 => "prosinec",
      ];

      if (isset($monthNames[$month])) {
        return $monthNames[$month] . " " . $year;
      }

      return $date->format("d.m.Y");
    }

    public function nl2br($string): string {
      return nl2br($string);
    }

    public function glyph($name, $title="") {
      $glyphName = "";
      switch ($name) {
        case 'add':
          if (empty($title)) $title = "přidat položku";
          $glyphName = "plus";
          break;
        case 'edit':
          if (empty($title)) $title = "upravit položku";
          $glyphName = "pencil";
          break;
        case 'delete':
          if (empty($title)) $title = "vymazat položku";
          $glyphName = "remove";
          break;
        case 'active':
          if (empty($title)) $title = "aktivní";
          $glyphName = "ok";
          break;
        case 'blocked':
          if (empty($title)) $title = "blokovaná";
          $glyphName = "off";
          break;
        case 'front':
          if (empty($title)) $title = "Otevřít ve veřejné části";
          $glyphName = "globe";
          break;
        case 'info':
          if (empty($title)) $title = "Informace";
          $glyphName = "info-sign";
          break;
        case 'user':
          if (empty($title)) $title = "Uživatel";
          $glyphName = "user";
          break;
        case 'export':
          if (empty($title)) $title = "Export";
          $glyphName = "export";
          break;
        case 'stats':
          if (empty($title)) $title = "Statistika";
          $glyphName = "stats";
          break;
        case 'send':
          if (empty($title)) $title = "Přepravce";
          $glyphName = "plane";
          break;
        case 'ok':
          if (empty($title)) $title = "OK";
          $glyphName = "ok";
          break;
      }

      return '
      <span class="glyphicon glyphicon-'.$glyphName.'" aria-hidden="true" title="'.$title.'"></span>';
    }
}

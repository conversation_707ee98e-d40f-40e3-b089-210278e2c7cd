php: # PHP configuration
  date.timezone: Europe/Prague
  zlib.output_compression: yes
nette:
  application:
    catchExceptions: TRUE
    errorPresenter: Front:Error
  session:
    autoStart: true
  security:
    debugger: true
    roles:
      guest:
      editor:
      expedice:
      admin:
    resources:
      Admin:Admin:
      Admin:User:
      Admin:Catalog:
      Admin:Product:
      Admin:ProductCS:
      Admin:ProparamDef:
      Admin:Export:
      Admin:Import:
      Admin:Order:
      Admin:OrderStore:
      Admin:Coupon:
      Admin:DeliveryMode:
      Admin:DeliveryModeCS:
      Admin:Manufacturer:
      Admin:Page:
      Admin:Article:
      Admin:Menu:
      Admin:New:
      Admin:MenuIndex:
      Admin:Config:
      Admin:Dictionary:
      Admin:Discount:
      Admin:Comment:
      Admin:Mailing:
      Admin:WidexImport:
      Admin:Accounting:
      Admin:Delivery:
      Admin:StoItem:
      Admin:Holiday:
  debugger:
      email: <EMAIL>
      strictMode: TRUE
parameters:
  app:
    domain: pstrosivejcecz
    basketOrderOnOnePage: FALSE
  adminModule:
    menuBackgroundColor: "#009cdd"
  labels:
    #nazvy cenovych hladin
    com: Cena běžná
    a: Cena A
    b: Cena B
    c: Cena C
    d: Cena D
    #nazvy priznaku u zbozi (akce, novinka, atd.)
    protypid: Akce
    protypid2: Novinka
    protypid3: Tip
    protypid4: Nejprodávánější
    protypid5: Připravujeme
  currency:
    1:
      id: 1
      key: CZK
      code: Kč
      decimals: 0
    #2:
    #  id: 2
    #  code: €
    #  key: EUR
    #  decimals: 1

  hosts:
    localhost:
      curId: 1
    127.0.0.1:
      curId: 1
    shop.widex.cz:
      curId: 1

  heureka:
    IdOverenoZakazniky:
    IdOverenoZakaznikyCertifikat:
    KeyMereniKonverzi:

  google:
    ua:  #GoogleAnalytics UA
    conversionId: #Google Retargeting
    conversionLabel:

  zbozicz:
    seznamRetargetingId:
    IdMereniKonverzi:
    ChSumMereniKonverzi: ''
    secretKey: '' #pokrocile mereni konverzi
    ShopId:

  sklik:
    IdMereniKonverzi:

  facebook:
    pixelCodeId:

  zasilkovna:
    apiPassword:
    apiKey:
    eshoName:

  ulozenka:
    shopId:
    apiKey: ''

  instagram:
    userId: **********
    accessToken: '**********.MTY1NGQwYw==.MTY4NjE0YzA4MWJl.NDM3MDg0M2JlMDFiZjIyOGNjZWI='

  geis:
    customerCode: ********
    password: 'FNZpHfgQE8DPrUT'
    phone: '*********'
    email: <EMAIL>
    pickupDeadLine: '1000' # do kolika hodin (HHMM) objednat svoz, aby přijel ještě týž den

  toplist:
    id:

  #https://smsmanager.cz/api
  sms:
    apiKey: '99423e2df20506fe3a12f7d933413f7dc65db4be'

  fio:
    account: **********/2010
    token: 'l6bO44nN2yx7zvmD6FWuRDe9rU90PS1HQ3RdJcwhFAy96eusDiv1bw7oZiSn0k4T'

  fioECom:
    Id: IO160714
    CertPass: 8hWfUYST
    TransType: sms

  goPay:
    goId: '**********'
    clientId: '**********'
    clientSecret: 'b5Zy4Ld9'
    mode: 'PROD' # PROD|TEST
    returnUrl: 'https://www.pstrosivejce.cz/go-pay/return'
    notifyUrl: 'https://www.pstrosivejce.cz/go-pay/notify'


  idoklad:
    id: '69db9016-95a2-436a-aaca-d35ce4ab1d23'
    secret: 'f4d6780d-6cc4-4dcd-8676-223b74249c12'

  coolBalik:
    apiKey: # pstrosivejce

  BoxxiApi:
    apiKey: ********************************

extensions:
  dibi: Dibi\Bridges\Nette\DibiExtension22
  recaptcha: Contributte\ReCaptcha\DI\ReCaptchaExtension
  visualPaginator: IPub\VisualPaginator\DI\VisualPaginatorExtension
  comgate: LZaplata\Comgate\DI\Extension
  calendarPicker: DotBlue\Nette\Forms\CalendarPickerExtension
  datePicker: RadekDostal\NetteComponents\DateTimePicker\DatePicker\DI\DatePickerExtension

datePicker:
  format: d.m.Y

comgate:
  merchant:
  secret: ''
  sandbox: true
  preauth: false

recaptcha:
  secretKey: ''
  siteKey: ''

dibi:
  driver: mysqli
  host: 127.0.0.1
  database: pstrosivejcecz
  username: root
  password: root

services:
  - Classes\NeonParametersRepository(@container::getParameters())

  myTemplateFilters:
    factory: \TemplateFilters(%wwwDir%, @container::getParameters())

  connection.panel:
    class: Dibi\Bridges\Tracy\Panel

  authenticator:
    class: MyAuthenticator

  # SMTP Mailer configuration
  nette.mailer:
    class: Nette\Mail\SmtpMailer
    arguments:
      - host: 'smtp.pstrosivejce.cz'
        port: 587
        username: '<EMAIL>'
        password: '*q7RdthWjFWW!YsC'
        secure: 'tls'

  nette.authorizator:
    setup:
     #guest
      - @self::allow( guest, 'Admin:Admin', ['login', 'logout', 'default'])
      - @self::allow( guest, 'Admin:WidexImport', ['import'])
     #admin - moduly
      - @self::allow( admin, 'Admin:Admin', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:User', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Catalog', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Product', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:ProparamDef', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Import', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Delivery', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Order', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:OrderStore', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:DeliveryMode', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Manufacturer', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Page', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Article', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Menu', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:New', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:MenuIndex', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Config', Nette\Security\Permission::ALL)
     #- @self::allow( admin, 'Admin:Discount', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Coupon', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Mailing', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:WidexImport', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Holiday', Nette\Security\Permission::ALL)
     #admin - akce
      - @self::allow( admin, 'Admin:Admin', ['changeForeign', 'orderSetAuthor', 'multiaccounts'])
     #CustomAction práva Název modulu+CS(Custom Service)
     #- @self::allow( admin, 'Admin:ProductCS', ['gifts'])
     #- @self::allow( admin, 'Admin:ProductCS', ['ActionPrices'])
     #CustomService práva Název modulu+CS(Custom Service)
     #- @self::allow( admin, 'Admin:DeliveryModeCS', ['zasilkovna'])
     #- @self::allow( admin, 'Admin:DeliveryModeCS', ['ulozenka'])
      - @self::allow( admin, 'Admin:DeliveryModeCS', ['geis','coolBalik'])
     #editor - moduly
      - @self::allow( editor, 'Admin:Admin', ['login', 'logout', 'default', 'edit'])
      - @self::allow( editor, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( editor, 'Admin:Article', Nette\Security\Permission::ALL)
     #expedice - moduly
      - @self::allow( expedice, 'Admin:Admin', ['login', 'logout', 'default', 'edit'])
      - @self::allow( expedice, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:Article', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:New', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:Order', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:User', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:DeliveryMode', Nette\Security\Permission::ALL)

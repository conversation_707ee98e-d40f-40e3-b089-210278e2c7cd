<?php
namespace AdminModule;

use Model\CatalogsModel;
use <PERSON><PERSON>,
  dibi,
  <PERSON>;

final class WidexImportPresenter extends BasePresenter
{

  /** @var \dibi\Connection */
  private $msCn = null;

  protected function startup()
  {
    parent::startup();
    ini_set('mssql.charset', 'WINDOWS-1250');
    $options = [
      'driver' => 'mssql',
      'host' => '***********:1433',
      'username' => 'ew',
      'password' => 'ew',
      'database' => 'Widex2006',
      'charset' => 'WINDOWS-1250',
    ];
    $this->msCn = new \Dibi\Connection($options);
  }

  public function renderDefault()
  {
    $this->template->rows = $this->getRows();
  }

  private function getRows()
  {

    $sql = 'SELECT 
    CAST(IdProduktu AS TEXT) AS IdProduktu,
    CAST(Cena AS decimal(12,2)) AS Cena,
    CAST(CenaB AS decimal(12,2)) AS CenaB,
    CAST(DPH AS decimal(4,2)) AS DPH,
    CAST(NazevProduktu AS TEXT) AS NazevProduktu,
    CAST(PopisProduktu AS TEXT) AS PopisProduktu,
    CAST(PopisProduktu2 AS TEXT) AS PopisProduktu2,
    CAST(Mnozstvi AS decimal) AS Mnozstvi,
    CAST(IdSkupiny AS decimal) AS IdSkupiny,
    CAST(NazevSkupiny AS TEXT) AS NazevSkupiny,
    CAST(IdPodskupiny AS decimal) AS IdPodskupiny,
    CAST(NazevPodskupiny AS TEXT) AS NazevPodskupiny,
    CAST(IDPodskupiny1 AS decimal) AS IdPodskupiny1,
    CAST(NazevPodskupiny1 AS TEXT) AS NazevPodskupiny1,
    CAST(UmoznitVsem AS decimal) AS UmoznitVsem
    FROM HPCeShopData';

    return $this->msCn->fetchAll($sql);
  }

  public function actionImport() {
    $k = (string)$this->getParameter('k');
    if ($k != '942a13a8cb5840') {
      $this->terminate();
    }

    //nactu si datum a cas začátku aktualizace
    $start = dibi::fetchSingle("SELECT NOW()");

    $cats = new CatalogsModel();

    $cntNew = 0;
    $cntUpd = 0;

    //offset pro ID podskupiny
    $subcatoff = 10000;
    //offset pro ID 3. podskupiny
    $subcat1off = 20000;

    //naliju hlavni uroven katalogu
    $sql = 'SELECT 
    CAST(IdSkupiny AS decimal) AS IdSkupiny,
    CAST(NazevSkupiny AS TEXT) AS NazevSkupiny
    FROM HPCeShopData
    GROUP BY IdSkupiny, NazevSkupiny';
    $rows = $this->msCn->fetchAll($sql);
    foreach ($rows as $row) {
      $cat = $cats->load($row->IdSkupiny);
      $vals = array(
        'catmasid' => 0,
        'catname' => $this->toUtf8($row->NazevSkupiny),
        'catstatus' => 0,
      );
      if ($cat) {
        $cats->update($cat->catid, $vals, true, false);
        //echo $cat->catid . "-u1<br>";
        $cntUpd++;
      } else {
        $vals["catid"] = $row->IdSkupiny;
        $catid = $cats->insert($vals, false);
        $cntNew++;
        //echo $catid . "-i1<br>";
      }
    }

    //naliju druhou úroveň katalogu
    $sql = 'SELECT 
CAST(IdPodskupiny AS decimal ) AS IdPodskupiny,
CAST(NazevPodskupiny AS TEXT) AS NazevPodskupiny,
CAST(IdSkupiny AS decimal) AS IdSkupiny
FROM HPCeShopData
GROUP BY IdPodskupiny, NazevPodskupiny, IdSkupiny';

    $rows = $this->msCn->fetchAll($sql);
    foreach ($rows as $row) {
      $catid = $row->IdPodskupiny + $subcatoff;
      $cat = $cats->load($catid);
      $vals = array(
        'catmasid' => $row->IdSkupiny,
        'catname' => $this->toUtf8($row->NazevPodskupiny),
        'catstatus' => 0,
      );
      if ($cat) {
        $cats->update($cat->catid, $vals, true, false);
        //echo $cat->catid . "-u2<br>";
        $cntUpd++;
      } else {
        $vals["catid"] = $catid;
        $catid = $cats->insert($vals, false);
        $cntNew++;
        //echo $catid . "-i2<br>";
      }
    }

    //naliju třetí úroveň katalogu
    $sql = 'SELECT 
CAST(IdPodskupiny1 AS decimal ) AS IdPodskupiny1,
CAST(NazevPodskupiny1 AS TEXT) AS NazevPodskupiny1,
CAST(IdPodskupiny AS decimal) AS IdPodskupiny
FROM HPCeShopData
WHERE IdPodskupiny1 > 0
GROUP BY IdPodskupiny1, NazevPodskupiny1, IdPodskupiny';

    $rows = $this->msCn->fetchAll($sql);
    foreach ($rows as $row) {
      $catid = $row->IdPodskupiny1 + $subcat1off;
      $cat = $cats->load($catid);
      $vals = array(
        'catmasid' => $row->IdPodskupiny + $subcatoff,
        'catname' => $this->toUtf8($row->NazevPodskupiny1),
        'catstatus' => 0,
      );
      if ($cat) {
        $cats->update($cat->catid, $vals, true, false);
        //echo $cat->catid . "-u3<br>";
        $cntUpd++;
      } else {
        $vals["catid"] = $catid;
        $catid = $cats->insert($vals, false);
        $cntNew++;
        //echo $catid . "-i3<br>";
      }
    }
    $cats->rebuildPaths();

    echo "Nových kategorií: $cntNew<br>";
    echo "Upravených kategorií: $cntUpd<br>";

    //vymazu tabulku se zarazenim do katalogu
    dibi::query("TRUNCATE catplaces");

    $cntNew = 0;
    $cntUpd = 0;
    $cnt = 0;

    $sql = 'SELECT 
CAST(IdProduktu AS TEXT) AS IdProduktu,
CAST(Cena AS decimal(12,2)) AS Cena,
CAST(CenaB AS decimal(12,2)) AS CenaB,
CAST(DPH AS decimal(4,2)) AS DPH,
CAST(NazevProduktu AS TEXT) AS NazevProduktu,
CAST(PopisProduktu AS TEXT) AS PopisProduktu,
CAST(PopisProduktu2 AS TEXT) AS PopisProduktu2,
CAST(Mnozstvi AS decimal) AS Mnozstvi,
CAST(IdPodskupiny AS decimal) AS IdPodskupiny,
CAST(IdPodskupiny1 AS decimal) AS IdPodskupiny1,
CAST(UmoznitVsem AS decimal) AS UmoznitVsem
FROM HPCeShopData';

    $pros = new \Model\ProductsModel();
    $caps = new \Model\CatPlacesModel();
    $rows = $this->msCn->fetchAll($sql);

    foreach ($rows as $row) {
      $cnt++;
      $pro = $pros->load($row->IdProduktu, 'code');

      $vatId = 0;
      if ($row->DPH == 0.15) {
        $vatId = 1;
      }
      if ($row->DPH == 0.10) {
        $vatId = 2;
      }

      $vals = [
        'procode' => $row->IdProduktu,
        'proname' => $this->toUtf8($row->NazevProduktu),
        'proprice1a' => round($row->Cena * (1 + $row->DPH)),
        'proprice1b' => round($row->CenaB * (1 + $row->DPH)),
        'provatid' => $vatId,
        'prostatus' => 0,
        'proaccess' => (int)$row->Mnozstvi > 0 ? 0 : 100, //skladem
        'proqty' => (int)$row->Mnozstvi //skladem počet kusů
      ];
      $updateCatRootId = FALSE;
      if ($pro) {
        $proid = $pro->proid;
        $pros->update($proid, $vals, true);
        //echo $proid . "-up[".$row->IdProduktu."]<br>";
        $cntUpd++;
      } else {
        $vals["protypid2"] = 1; //novinka
        $vals["promanid"] = 1; //výrobce

        $proid = $pros->insert($vals);
        $cntNew++;
        //echo $proid . "-ip[".$row->IdProduktu."]<br>";
      }
      $idskup = 0;
      if ($row->IdPodskupiny1 > 0) {
        $idskup = $row->IdPodskupiny1 + $subcat1off;
      } else {
        $idskup = $row->IdPodskupiny + $subcatoff;
      }
      if ($idskup > 0) {
        $cvals = array(
          'capproid' => $proid,
          'capcatid' => $idskup,
        );
        $caps->insert($cvals);
        $updateCatRootId = TRUE;
      }
      //aktualizuji catrootid
      if ($updateCatRootId) {
        $pro = $pros->load($proid);
        $pros->setProCatRootId($pro);
      }
    }
    //skryji polozky, ktere nejsou aktualizovany behem importu ale jiz existuji v databazi
    $sql = "UPDATE products SET prostatus=1 WHERE coalesce(prodateu, prodatec) < '$start'";
    dibi::query($sql);
    $sql = "UPDATE catalogs SET catstatus=1 WHERE coalesce(catdateu, catdatec) < '$start'";
    dibi::query($sql);
    echo "Nových produktů: $cntNew<br>";
    echo "Upravených produktů: $cntUpd<br>";

    $this->terminate();
  }

  public function toUtf8($cp1250String) {
    return iconv('windows-1250', 'utf-8', $cp1250String);
  }
}

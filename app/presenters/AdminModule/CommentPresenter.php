<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  <PERSON>;

final class CommentPresenter extends BasePresenter {
  /** @persistent */
  public $sText = '';

  /** @persistent */
  public $sProCode = '';

  /** @persistent */
  public $sUsrMail = '';

  public function renderDefault() {
    //seznam aktualnich upozorneni
    $coms = $this->model->getCommentsModel();
    $where = "";

    if (!empty($this->sText)) $where .= " (cmtsubj like '%$this->sText%' OR cmttext like '%$this->sText%') AND ";
    if (!empty($this->sUsrMail)) $where .= " cmtmail LIKE '%$this->sUsrMail%' AND ";
    if (!empty($this->sProCode)) $where .= " procode LIKE '%$this->sProCode%' AND ";
    if (!empty($where)) {
      $where = substr($where, 0, -5);
      $where = " WHERE $where";
    }

    $dataSource = $coms->getDataSource("
      SELECT * FROM comments
      LEFT JOIN products ON (cmtproid=proid)
      $where 
      ORDER BY cmtdatec DESC
     ");
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->comments = $dataRows;
    $this->template->enum_cmtcatid = $coms->getEnumCmtCatId();
  }

  public function renderEdit($id, $reid=0) {
    $form = $this['editForm'];
    $imageRows = array();
    if (!$form->isSubmitted()) {
      $id = $this->getParameter('id');
      $this->template->id = $id;
      $coms = $this->model->getCommentsModel();
      if ($id > 0) {
        $row = $coms->load($id);
        $this->template->commnent = $row;
        if ($row->cmtproid > 0) {
          $this->template->product = dibi::fetch("SELECT proid, proname, prokey FROM products WHERE proid=%i", $row->cmtproid);
        }
        if (!$row) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        if ($row->cmtreid > 0) {
          $this->template->masterComment = $coms->load($row->cmtreid);
        }

        $form->setDefaults($row);
      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $coms = $this->model->getCommentsModel();
      if ($coms->delete($id)) {
        $this->flashMessage('Záznam byl vymazán');
      } else {
        $this->flashMessageErr('Záznam nebyl vymazán');
      }
    }
    $this->redirect('default');
  }

  protected function createComponentEditForm() {
    $form = $this->createAppForm();

    $id = (int)$this->getParameter("id");
    $showActiveButton = FALSE;
    if ($id > 0) {
      $coms = $this->model->getCommentsModel();
      $com = $coms->load($id);
      $showActiveButton = $com->cmtstatus !== 0;
    }
    /*
    $arr1 = array(
      '0' => 'Nezařazeno',
    );

    $coms = $this->model->getCommentsModel();
    $arr = $coms->getEnumCmtCatId();

    $form->addSelect("cmtcatid", "Téma 1", $arr)
      ->setPrompt('');

    $form->addSelect("cmtcatid2", "Téma 2", $arr)
      ->setPrompt('');
    */

    $form->addText('cmtnick', 'Jméno:', 100)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('cmtmail', 'Email:', 100);

    $form->addSelect('cmtrate', "Hodnocení", $coms->getEnumCmtRate());

    //$form->addText('cmtsubj', 'Předmět:', 100);

    $form->addTextArea('cmttext', 'Text', 100, 4)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addSubmit('save', 'Uložit');

    if ($showActiveButton) {
      $form->addSubmit('activate', 'Zveřejnit recenzi');
    }
    $form->onSuccess[] = [$this, 'editFormSubmitted'];

    return $form;
  }

  public function editFormSubmitted(Nette\Application\UI\Form $form) {
    $id = $this->getParameter('id');
    $coms = $this->model->getCommentsModel();

    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();

      $cmtReId = (int)$this->getParameter('reid');

      if($cmtReId > 0) {
        $formVals->cmtreid = $cmtReId;
      }

      if ($id > 0) {
        $coms->update($id, $formVals);
        $this->flashMessage('Změny uloženy v pořádku');
      } else {
        $id = $coms->insert($formVals);
        $this->flashMessage('Nový záznam uložen v pořádku');
      }

      $this->redirect('edit', $id);
    } else if ($form['activate']->isSubmittedBy()) {
      $coms->update($id, array("cmtstatus"=>0));
      $this->flashMessage('Recenze zveřejněna');
      $this->redirect('edit', $id);
    }
  }

  protected function createComponentSearchForm() {
    $catalogs = $this->model->getCatalogsModel();

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");
    $form->addText("text", "Text", 30)
      ->setDefaultValue($this->sText);
    $form->addText("usrmail", "Email", 20)
      ->setDefaultValue($this->sUsrMail);
    $form->addText("procode", "Kód zboží", 10)
      ->setDefaultValue($this->sProCode);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sText= Null;
        $this->sProCode = Null;
        $this->sUsrMail = Null;
      } else {
        $vals = $form->getValues();
        $this->sText= $vals["text"];
        $this->sProCode = $vals["procode"];
        $this->sUsrMail = $vals["usrmail"];
      }
    }
    $this->redirect("default");
  }
}

<?php
namespace AdminModule;

use <PERSON><PERSON>,
    dibi,
    Model;


final class OrderStorePresenter extends BasePresenter {

  /** @persistent */
  public $ids = '';

  public function renderDispatch($ids) {
    $arr = explode("|", trim($ids, "|"));

    $ords = $this->model->getOrdersModel();

    $this->template->orders = array();
    $items = array();
    $eans = array();
    foreach ($arr as $ordId) {
      $this->template->orders[$ordId] = $ords->load($ordId);
      $items[$ordId] = $ords->getProducts($ordId);

      foreach ($items[$ordId] as $row) {
        $eans[$row->oriid] = dibi::fetchAll("SELECT * FROM stoitems WHERE stioriid=%i", $row->oriid);
      }
    }

    $this->template->items = $items;
    $this->template->eans = $eans;
  }

  protected function  createComponentEanForm() {
    $form = $this->createAppForm();

    $form->addText("ean", "EAN:")
      ->setHtmlId("ean");

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'eanFormSubmitted');

    return $form;
  }

  public function actionEanForm() {
    $stis = $this->model->getStoItems();
    $ords = $this->model->getOrdersModel();

    $vals["ean"] = (string)$this->getParameter("ean");
    $vals["oriid"] = (int)$this->getParameter("oriid");
    $vals["ordids"] = (string)$this->getParameter("ids");

    $arr = explode("|", trim($vals["ordids"], "|"));

    foreach($arr as $i => $id) {
      $arr[$i] = (int)$id;
    }

    foreach ($arr as $id => $ordId) {
      $orifill = (int)dibi::fetchSingle("SELECT SUM(orifill) FROM orditems WHERE oritypid=0 AND oriordid=%i", $ordId);
      if ($orifill === 0) {
        unset($arr[$id]);
        /*
        $ords->update($ordId, array(
          "ordstatus"=>8,
          "ordfilled"=>1
        ));
        $ords->logStatus($ordId, 8, $this->adminData->admid, "Vyskladněno");
        */
      }
    }

    $ean = $vals["ean"];

    $sti = dibi::fetch("
      SELECT * 
      FROM stoitems
      WHERE stiqtyfree>0 AND stiean=%s", $ean
    );

    if (count($arr) === 0) {
      $this->flashMessageOk("Vybrané objednávky byly kompletně vyskladněny.");
      $this->redirect("Order:default");
    } else if ($sti === FALSE) {
      $this->flashMessageErr("EAN $ean se nepodařilo nalézt nebo je už vyskladněný.");
    } else if ($sti->stiqtyfree === 0) {
      $this->flashMessageErr("EAN $ean je už vyskladněný.");
    } else {
      dibi::begin();
      try {
        if ($vals["oriid"] > 0) {
          $rows = dibi::fetchAll("SELECT * FROM orditems WHERE oriid=%i", $vals["oriid"]);
        } else {
          $rows = dibi::fetchAll("SELECT * FROM orditems WHERE oriordid IN (%i)", $arr, " AND orifill>0 AND oriproid=%i", $sti->stiproid);
        }

        foreach ($rows as $key => $ori) {
          $prccat = 'a';
          //vykryji
          if ($stis->dispatch($sti, $ori, $prccat)) {
            break;
          }
        }
        dibi::commit();
      } catch (\Exception $e) {
        $this->flashMessageErr("EAN $ean se nepodařilo vyskladnit." . $e->getMessage());
        dibi::rollback();
      }
    }


    foreach ($arr as $id => $ordId) {
      $orifill = (int)dibi::fetchSingle("SELECT SUM(orifill) FROM orditems WHERE oritypid=0 AND oriordid=%i", $ordId);
      if ($orifill === 0) {
        $ords->update($ordId, array(
          "ordstatus"=>8,
          "ordfilled"=>1
        ));
        $ords->logStatus($ordId, 8, $this->adminData->admid, "Vyskladněno");
      }
    }
    $this->redirect("dispatch", $this->ids);
  }
}

<?php
namespace AdminModule;

use dibi;
use Nette;

final class CatalogPresenter extends BasePresenter {

  public function catalogEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $catalogs = $this->model->getCatalogsModel();
      $id = (int)$this->getParameter('id');
      $formVals = $form->getValues();

      //kontrola zda nedal stejnou nadrizenou uroven jako je id
      if ($id > 0 && $id == $formVals["catmasid"]) {
        $form->addError("Nelze zadat do nadřízené kategorie editovanou kategorii.");
      } else {
        //vezmu si obrazek pokud byl odeslan
        $image = null;
        if ($formVals["picture"]->isOk()) {
          $image = $formVals["picture"];
        }
        unset($formVals["picture"]);
        if ($id > 0) {
          $catalogs->update($id, $formVals);
          $this->flashMessage('Aktualizováno v pořádku');
        } else {
          $id = $catalogs->insert($formVals);
          $this->flashMessage('Nový záznam uložen v pořádku');
        }
        //ulozim obrazek
        if ($image !== NULL) {
          $image->move(WWW_DIR."/pic/catalog/src/".$id.".jpg");
          $this->deletePic(WWW_DIR."/pic/catalog/", $id.".jpg");
        }
      }
      if (!$form->hasErrors()) $this->redirect('default');
    }
  }

  /********************* view default *********************/

  public function renderDefault() {
    $catalogs = $this->model->getCatalogsModel();
    //$dataRows = $catalogs->fetchAll("SELECT * FROM catalogs WHERE ORDER BY catorder");
    $this->template->items = $catalogs->getEnumCatalogTree();

    //ciselnik statusu
    $this->template->enum_catstatus = $catalogs->getEnumCatStatus();
  }

  public function renderEdit($id) {
    $form = $this->createAppForm();
    $form = $this['catalogEditForm'];

    if (!$form->isSubmitted()) {
      $catalogs = $this->model->getCatalogsModel();
      if ($id > 0) {
        $dataRow = $catalogs->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
        $this->template->dataRow = $dataRow;

      } else {
        $defVals = array();
        $catmasid = $this->getParameter("catmasid");
        if ($catmasid > 0) $defVals["catmasid"] = $catmasid;

        $form->setDefaults($defVals);
      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      //zjistim jestli nema podrizene vetve
      $cnt = dibi::fetchSingle("SELECT count(*) FROM catalogs WHERE catmasid=%i", $id);
      if ($cnt == 0) {
        $catalogs = $this->model->getCatalogsModel();
        $catalogs->delete($id);
        $this->flashMessage('Záznam byl vymazán.');
        $catalogs->cacheClean();
      } else {
        $this->flashMessageErr('Záznam nebyl vymazán. Existují jemu podřízené větve katalogu.');
      }
    }
    $this->redirect('default');
  }


  /********************* facilities *********************/

  protected function createComponentCatalogEditForm() {
    $catalog = $this->model->getCatalogsModel();
    $form = $this->createAppForm();
    $id = (int)$this->getParameter('id');

    $form->addSelect('catmasid', 'Nadřízená úroveň:', $catalog->getEnumCatalogCombo())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název.');

    $form->addText('catname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název.');

    $form->addText('catkey', 'URL klíč:', 30)
      ->setOption('description', 'Pokud ponecháte prázdné, generuje se z názvu kategorie');

    $form->addText('catkeywords', 'Klíčové slova:', 30)
      ->setOption('description', 'slova oddělené čárkou');

    $form->addText('catsalestat', 'Nejprodávanější zboží:', 75)
      ->setOption('description', 'kódy zboží oddělené čárkou');

    //$form->addText('catparams', 'Heuréka katalogová cesta:', 75);

    $form->addText('catpathheureka', 'Katalogová cesta dle taxonomie heureka.cz:', 100)
      ->setOption('description', Nette\Utils\Html::el('')->setHtml('<br>[ <a href="http://www.heureka.cz/direct/xml-export/shops/heureka-sekce.xml" target="_blank">zde kompletní taxonomie<a/> ] Vyplňte buď CATEGORY_ID nebo CATEGORY_FULLNAME'));
    $form->addText('catpathgoogle', 'Katalogová cesta dle taxonomie Google nákupy:', 100)
      ->setOption('description', Nette\Utils\Html::el('')->setHtml('<br>[ <a href="https://support.google.com/merchants/answer/1705911" target="_blank">zde kompletní taxonomie<a/> ] Vyplňte buď ID nebo kompletní cestu katalogovou cestu. Např. pro MP3 přehrávače vyplňte Elektronika > Audio > Audiopřehrávače a rekordéry > Přehrávače MP3'));
    $form->addText('catpathzbozi', 'Katalogová cesta dle taxonomie zbozi.cz:', 100)
      ->setOption('description', Nette\Utils\Html::el('')->setHtml('<br>[ <a href="http://www.zbozi.cz/static/categories.csv" target="_blank">zde kompletní taxonomie<a/> ] Vyplňte hodnotu ze sloupce: Celá cesta'));

    $form->addText('catclass', 'Název stylu:', 75);

    $form->addTextArea('catdescription', 'Krátký popis:', 75, 2)
      ->setAttribute('maxlength', '255');

    $form->addTextArea('catdesc', 'Popis:', 75, 5);
      $form['catdesc']->getControlPrototype()->class('mceEditor');

    //obrazek
    $form->addUpload('picture', 'Obrázek:')
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');

    $form->addText('catorder', 'Pořadí:', 15)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte pořadí.');

    $form->addSelect('catstatus', 'Status:', $catalog->getEnumCatStatus())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte status.');

    $form->addSubmit('save', 'Uložit');

    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'catalogEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }
}

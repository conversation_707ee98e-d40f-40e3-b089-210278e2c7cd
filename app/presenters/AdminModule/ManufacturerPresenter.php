<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class ManufacturerPresenter extends BasePresenter {

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $manufacturer = $this->model->getManufacturersModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();

      //projdu formularova pole a ktere nejsou treba odstranim
      foreach ($vals as $key => $value) {
      }

      try {
        if ($manufacturer->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('Manufacturer:default');
        }

      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }

  /********************* view default *********************/

  public function renderDefault() {
    $manufacturer = $this->model->getManufacturersModel();
    $dataRows = dibi::query("SELECT * FROM manufacturers ORDER BY manname")
      ->fetchAssoc('manid');

    $this->template->dataRows = $dataRows;
    //ciselnik statusu
    $this->template->enum_manstatus = $manufacturer->getEnumManStatus();
  }

  public function renderEdit($id) {
    $form = $this['editForm'];

    if (!$form->isSubmitted()) {
      $manufacturer = $this->model->getManufacturersModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $manufacturer->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow;
      $this->template->id = $id;
    }
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');

    $manufacturer = $this->model->getManufacturersModel();

    $form = $this->createAppForm();

    $form->addText('manname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');

    $form->addText('manurl', 'URL:', 30);

    $form->addTextArea('mandesc', 'Popis:', 60, 10);

    $form->addSelect('manstatus', 'Status:', $manufacturer->getEnumManStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }
}

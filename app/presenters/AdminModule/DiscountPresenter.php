<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class DiscountPresenter extends BasePresenter {

  /********************* view default *********************/

  public function renderDefault() {
    $discount = $this->model->getDiscountsModel();
    $this->template->dataRows = dibi::fetchAll("SELECT * FROM discounts WHERE distypid='volume' ORDER BY disfrom");
    //ciselnik statusu
    $this->template->enum_disstatus = $discount->getEnumDisStatus();
    $this->template->enum_discurid = $this->getEnumCurr();
    $this->template->enum_prccat = $this->getEnumPrcCat();
  }

  public function renderEdit($id) {
    $form = $this['editForm'];

    if (!$form->isSubmitted()) {
      $discount = $this->model->getDiscountsModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $discount->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow;
      $this->template->id = $id;
    }
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');

    $discount = $this->model->getDiscountsModel();

    $form = $this->createAppForm();

    if ($this->secondCurrency) {
      $form->addSelect("discurid", "Měna:", $this->getEnumCurr())
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněná")
        ->setPrompt('Vyberte ...');
    } else {
      $form->addHidden("discurid", 1);
    }
    $form->addSelect('disprccat', 'Cenová hladina', $this->getEnumPrcCat());

    $form->addText('disfrom', 'Cena od:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Cena od: je nutné vyplnit')
      ->addRule(Nette\Forms\Form::INTEGER, "Cena od musí být celé číslo");

    $form->addText('disto', 'Cena do:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Cena do: je nutné vyplnit')
      ->addRule(Nette\Forms\Form::INTEGER, "Cena do musí být celé číslo");

    $form->addText('dispercent', 'Sleva v %:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Sleva v procentech: je nutné vyplnit')
      ->addRule(Nette\Forms\Form::NUMERIC, "Sleva v procentech musí být číslo");

    $form->addSelect('disstatus', 'Status:', $discount->getEnumDisStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $discount = $this->model->getDiscountsModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();
      $vals["distypid"] = "volume";
      try {
        if ($discount->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('default');
        }

      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }
}

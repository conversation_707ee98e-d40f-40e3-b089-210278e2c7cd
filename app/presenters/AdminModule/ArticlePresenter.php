<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class ArticlePresenter extends BasePresenter {

  public function renderDefault() {
    $articles = $this->model->getArticlesModel();
    $dataRows = dibi::query("SELECT * FROM articles ORDER BY artdate DESC")
      ->fetchAssoc('artid');

    $this->template->dataRows = $dataRows;
    $this->template->enum_arttypid = $articles->getEnumArtTypId();
    $this->template->enum_arttop = $articles->getEnumArtTop();
  }

  public function renderEdit($id) {
    $articles = $this->model->getArticlesModel();
    $dataRow = array();
    if ($id > 0) {
      $dataRow = $articles->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
    }
    $form = $this['editForm'];
    if (!empty($dataRow->artdate)) {
      $dataRow->artdate = $this->formatDate($dataRow->artdate);
    }

    if (!empty($dataRow->arttags)) {
      $arr = explode('|', trim($dataRow->arttags, '|'));
      $dataRow->arttags = '';
      foreach ($arr as $tag) {
        $dataRow->arttags .= trim($tag) . ",";
      }
      $dataRow->arttags = trim($dataRow->arttags, ',');
    }

    $form->setDefaults($dataRow);
    $this->template->dataRow = $dataRow;
    $this->template->id = $id;
    $this->template->enum_arttypid = $articles->getEnumArtTypId();
    $this->template->enum_arttop = $articles->getEnumArtTop();

    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $id);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype IN ('jpg', 'png', 'gif')", $id);
    if (count($this->template->images) > 0) $this->template->imagesListId = $id;

    //vlozim hlavni obrazek
    $fileName = 'art_'.$id.'.jpg';
    $this->template->mainImageName = "";
    if (file_exists(WWW_DIR."/pic/art/list/$fileName")) $this->template->mainImageName = $fileName;
  }

  public function renderJsImagesList($id) {
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype IN ('jpg', 'png', 'gif')", $id);
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');

    $articles = $this->model->getArticlesModel();

    $form = $this->createAppForm();
    $form->addSelect('arttypid', 'Typ článku:', $articles->getEnumArtTypId());

    /*
    $form->addText('arttags', 'Štítky:', 30)
      ->setOption('description', "Vyplňte názvy štítků oddělené čárkou");
    */

    $form->addText('artname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');

    //$form->addText('artdate', 'Datum článku:', 30);

    $form->addText('arturlkey', 'URL:', 30);

    $form->addText('arttitle', 'Title:', 100);

    $form->addTextArea('artdescription', 'Anotace:', 75, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', Nette\Utils\Html::el('p')->class('charsRemaining'));
    $form->addText('artkeywords', 'Keywords:', 100);

    if ($id === 13) {
      $form->addTextArea('artbody3', 'Aktuální akce:', 60, 20);
      $form['artbody3']->getControlPrototype()->class('mceEditor');
    }

    $form->addTextArea('artbody', 'Popis:', 60, 20);
    $form['artbody']->getControlPrototype()->class('mceEditor');

    $caption = "Blok 2:";
    if ($id === 13) {
      $caption = "Objednávejte online:";

      $form->addTextArea('artbody4', $caption, 60, 5);
      $form['artbody4']->getControlPrototype()->class('mceEditor');
    }

    $form->addTextArea('artbody2', $caption, 60, 5);
    $form['artbody2']->getControlPrototype()->class('mceEditor');

    $form->addUpload("imageBg", "Pozadí hlavičky:")
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete');

    $caption = "Hlavní obrázek:";
    if ($id === 13) {
      $caption = "Obrázek aktuální akce:";
    }
    $form->addUpload("imageMain", $caption)
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete');

    $form->addUpload("imageBlock", "Obrázek popis:")
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete');

    $form->addSelect('artstatus', 'Status:', $articles->getEnumArtStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    return $form;
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $article = $this->model->getArticlesModel();
      $id = (int)$this->getParameter('id');
      $formVals = $form->getValues();

      $image = null;
      if ($formVals["imageMain"]->isOk()) {
        $image = $formVals["imageMain"];
      }

      if ($formVals["imageBg"]->isOk()) {
        $imageBg = $formVals["imageBg"];
      }

      if ($formVals["imageBlock"]->isOk()) {
        $imageBlock = $formVals["imageBlock"];
      }

      unset($formVals["imageMain"]);
      unset($formVals["imageBg"]);
      unset($formVals["imageBlock"]);

      if (empty($formVals["arturlkey"])) $formVals["arturlkey"] = Nette\Utils\Strings::webalize($formVals["artname"]);
      if (empty($formVals["artdate"])) {
        $formVals["artdate"] = new \DateTime();
      } else {
        $formVals["artdate"] = $this->formatDateMySQL($formVals["artdate"]);
      }

      if (!empty($formVals["arttags"])) {
        $arr = explode(',', trim($formVals["arttags"], ','));
        $formVals["arttags"] = '|';
        foreach ($arr as $tag) {
          $formVals["arttags"] .= trim($tag) . "|";
        }
      }

      try {
        if ($article->save($id, $formVals)) {
          $this->flashMessage('Uloženo v pořádku');

          if (isset($image)) {
            //ulozim obrazek
            $image->move(WWW_DIR."/pic/article/src/" . $id.".jpg");
            $this->deletePic(WWW_DIR."/pic/article/", $id.".jpg");
          }

          if (isset($imageBg)) {
            //ulozim obrazek
            $imageBg->move(WWW_DIR."/pic/article/src/bg_" . $id.".jpg");
            $this->deletePic(WWW_DIR."/pic/article/", "bg_" .$id.".jpg");
          }

          if (isset($imageBlock)) {
            //ulozim obrazek
            $imageBlock->move(WWW_DIR."/pic/article/src/block_" . $id.".jpg");
            $this->deletePic(WWW_DIR."/pic/article/", "desc_" .$id.".jpg");
          }

          $this->redirect('default');
        }
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }


  protected function createComponentUploadForm() {
    $form = $this->createAppForm();
    $form->addText("ataname", 'Popis obrázku:')
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addText("ataurl", 'Odkaz:');
    $form->addUpload('file', 'Obrázek:')
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addSubmit('save', 'Připojit')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'UploadFormSubmitted');
    return $form;
  }

  public function UploadFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $artid = $this->getParameter('id');
      if ($vals["file"]->isOk()) {
        //ulozim do db
        $atts = $this->model->getAttachmentsModel();
        $ataVals = array(
          'ataartid'=>$artid,
          'ataname'=>$vals["ataname"],
          'ataurl'=>$vals["ataurl"],
          'atafilename'=>Nette\Utils\Strings::webalize($vals["ataname"]).'_'.$artid.'.'.substr($vals["file"]->getName(), -3),
          'atasize'=>(string)$vals["file"]->getSize(),
          'atatype'=>substr($vals["file"]->getName(), -3),
        );
        $ataid = $atts->insert($ataVals);
        $ataVals["atafilename"] = Nette\Utils\Strings::webalize($ataVals["ataname"]).'_'.$ataid.'.'.$ataVals["atatype"];
        $atts->update($ataid, $ataVals);
        $vals["file"]->move(WWW_DIR.'/files/'.$ataVals["atafilename"]);
      }
      $this->redirect('edit', $artid);
    }
  }

  public function actionDeleteAttachment($ataid, $artid) {
    $file = dibi::fetch("SELECT * FROM attachments WHERE ataid=%i", $ataid);
    if ($file) {
      @unlink(WWW_DIR.'/files/'.$file->atafilename);
      dibi::query("DELETE FROM attachments WHERE ataid=%i", $ataid);
    }
    $this->redirect('edit', $artid);
  }

  public function actionDelete($artid) {
    $arts = $this->model->getArticlesModel();
    $arts->delete($artid);
    $rows = dibi::fetch("SELECT * FROM attachments WHERE ataartid=%i", $artid);
    if ($rows !== FALSE) {
      foreach ($rows as $key => $row) {
        @unlink(WWW_DIR.'/files/'.$row->atafilename);
      }
    }
    dibi::query("DELETE FROM attachments WHERE ataartid=%i", $artid);
    $this->redirect('default');
  }

}

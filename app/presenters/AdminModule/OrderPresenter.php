<?php
namespace AdminModule;

use dibi;
use Model\OrdersModel;
use Nette;

final class OrderPresenter extends BasePresenter {

  /** @persistent */
  public $sCode = '';

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sAdmin = Null;

  /** @persistent */
  public $sStatus = '';

  /** @persistent */
  public $sDelId;

  /** @persistent */
  public $sPayCode;

  /** @persistent */
  public $sCoupon = '';

  /** @persistent */
  public $sDelDateFrom;

  /** @persistent */
  public $sDelDateTo;

  /** @persistent */
  public $sDelMonth;

  /** @persistent */
  public $sOnlyEgg;

  /** @persistent */
  public $sPhone;

  /** @persistent */
  public $sIdoklad;

  /** @persistent */
  public $sNotClosed =  true;

  /** @persistent */
  public $sIsPayed =  FALSE;

  /** @persistent */
  public $sOrderBy = 'orddatec';

  /** @persistent */
  public $sOrderByType = 'ASC';

  public function orderEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $orders = $this->model->getOrdersModel();
      $id = $this->getParameter('id');
      $values = $form->getValues();
      //nactu si objednavku pokud existuje

      if (!empty($values->orddeldatedel)) {
        $values->orddeldatedel = $this->formatDateMySQL($values->orddeldatedel);
      }

      if (!empty($values->orddeldatestart)) {
        $values->orddeldatestart = $this->formatDateMySQL($values->orddeldatestart);
      }

      $order = false;
      if ($id > 0) {
        $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
        $orders->setCurrency($this->currencies, $order->ordcurid);
      }
      try {
        $orders->save($id, $values);
        $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
        $orders->recalcOrder($id);
      } catch (\Exception $e) {
        $this->flashMessage('Nastala chyba: ' . $e->getMessage(), "err");
      }


      $this->flashMessage('Uloženo v pořádku');
    }
    $this->redirect('edit', $id);
  }

  public function orderChangeStateFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVals = $form->getValues();
      $formVals["ordid"] = $this->getParameter('id');
      $this->changeOrderStatus($formVals, $this->adminData->admid);
    }
    $this->redirect('default');
  }

  /********************* view default *********************/

  public function renderDefault() {
    $orders = $this->model->getOrdersModel();
    //$where = "orddatec + INTERVAL 3 DAY >= Now() AND ";
    $where = "";
    if (!empty($this->sCode)) $where .= " ordcode LIKE '%$this->sCode%' AND ";
    if (!empty($this->sAdmin)) $where .= " ordadmid = ".$this->sAdmin." AND ";
    if (!empty($this->sName)) $where .= " (ordilname LIKE '%$this->sName%' OR ordstlname LIKE '%$this->sName%' OR ordstfirname LIKE '%$this->sName%' OR ordifirname LIKE '%$this->sName%') AND ";
    if ($this->sNotClosed) $where .= " ordstatus NOT IN (4,5,7) AND ";
    if ($this->sIsPayed) $where .= " (ordpaystatus=1 or d.delcode='cash') AND ";
    if ((string)$this->sStatus!='') $where .= " ordstatus=$this->sStatus AND ";
    if (!empty($this->sCoupon)) $where .= " ordcoucode = '$this->sCoupon' AND ";

    if (!empty($this->sDelDateFrom)) {
      $where .= "orddeldatedel>='" . $this->formatDateMySQL($this->sDelDateFrom) . "' AND ";
    }
    if (!empty($this->sDelDateTo)) {
      $where .= "orddeldatedel<='" . $this->formatDateMySQL($this->sDelDateTo) . "' AND ";
    }

    //měsíc doručení
    if (!empty($this->sDelMonth)) {
      $where .= "orddelmonth=" . $this->sDelMonth . " AND ";
    }

    //jen objednávky s vejci
    if ($this->sOnlyEgg) {
      $where .= "EXISTS (SELECT oriid FROM orditems WHERE ordid=oriordid AND oriprocode IN ('VEJCE')) AND ";
    }

    //telefon
    if (!empty($this->sPhone)) {
      $where .= "ordtel LIKE '%" . $this->sPhone . "%' AND ";
    }

    //idoklad
    if ($this->sIdoklad === "yes") {
      $where .= "ordinvcode IS NOT NULL AND ordinvcode!='' AND ";
    } else if ($this->sIdoklad === "no") {
      $where .= "coalesce(ordinvcode,'')='' AND ";
    }

    if (!empty($this->sDelId)) {
      if (is_numeric($this->sDelId)) {
        $where .= " dm.delid=".$this->sDelId." AND ";
      } else {
        $where .= " dm.delcode='".$this->sDelId."' AND ";
      }
    }

    if (!empty($this->sPayCode)) {
      $where .= " d.delcode='".$this->sPayCode."' AND ";
    }

    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }

    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY orddatec ASC";
    }
    $dataSource = dibi::dataSource($orders->getList($where, $orderBy));

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();

    //dočtu zda objednávka má více aktivních obj podle telefonu
    foreach ($dataRows as $key => $row) {
      $dataRows[$key]->otherOrdersCnt = dibi::fetchSingle("SELECT count(ordid) FROM orders WHERE ordtel=%s", $row->ordtel, " AND ordid!=%i", $row->ordid, " AND ordstatus NOT IN (4,5,7)");
    }

    //doplním položky objednávky
    foreach ($dataRows as $key => $row) {
      $dataRows[$key]["items"] = dibi::fetchAll("SELECT * FROM orditems WHERE oriordid=%i", $row->ordid, " AND oritypid=0");
    }
    $this->template->page = $paginator->page;
    $this->template->dataRows = $dataRows;

    /*
    foreach ($dataRows as $row) {
      $row->bl = $orders->blAnalyse($row);
    }
    */

    //ciselnik statusu
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
  }

  public function renderProducts() {
    $sql = array();
    $where = array();
    if ($this->sDelDateFrom) {
      $where[] = " AND orddeldatedel>=%s";
      $where[] = $this->formatDateMySQL($this->sDelDateFrom);
    }
    if ($this->sDelDateTo) {
      $where[] = " AND orddeldatedel<=%s";
      $where[] = $this->formatDateMySQL($this->sDelDateTo);
    }
    if ($this->sDelId) {
      $where[] = " AND EXISTS (SELECT 1 FROM deliverymodes WHERE delid=orddelid AND delmasid=%i)";
      $where[] = $this->sDelId;
    }

    if ($this->sPayCode) {
      $where[] = " AND EXISTS (SELECT 1 FROM deliverymodes WHERE delid=orddelid AND delcode=%s)";
      $where[] = $this->sPayCode;
    }

    if ($this->sNotClosed) {
      $where[] = " AND ordstatus NOT IN (4,5,7) ";
    }

    if ((string)$this->sStatus !== "") {
      $where[] = " AND ordstatus=%i";
      $where[] = $this->sStatus;
    }

    //jen objednávky s vejci
    if ($this->sOnlyEgg) {
      $where[] = " AND EXISTS (SELECT oriid FROM orditems WHERE ordid=oriordid AND oriprocode IN ('VEJCE')) ";
    }

    $sql[] = "
      SELECT oriproid, oriname, oriprocode, sum(oriqty) AS oriqty, coalesce(d.delnames, d.delname) AS delname, coalesce(dm.delnames, dm.delname) AS delnamemas FROM orditems
        INNER JOIN orders ON (oriordid=ordid)
        LEFT JOIN deliverymodes AS d ON (orddelid=d.delid)
        LEFT JOIN deliverymodes AS dm ON (d.delmasid=dm.delid)
        WHERE oritypid=0 ";

    $sql = array_merge($sql, $where);

    $sql[] = " GROUP BY oriproid,oriname";
    $sql[] = " ORDER BY oriname";

    $rows = dibi::fetchAll($sql);
    $this->template->rows = $rows;

    $sql = array();

    $sql[] = "
      SELECT oriordid, oriproid, ordcode FROM orditems
        INNER JOIN orders ON (oriordid=ordid)
        WHERE oritypid=0";

    $sql = array_merge($sql, $where);

    $this->template->orders = dibi::query($sql)->fetchAssoc("oriproid,oriordid");

  }

  public function renderAutocompleteProducts() {
    $term = $this->getParameter('term');
    if (!empty($term)) {
      $this->template->rows = dibi::fetchAll("
      SELECT proid, procode, proname, proprice" . $this->curId . "a AS propricea
      FROM products
      WHERE proismaster=0 AND
      (proname LIKE '%$term%' OR  procode LIKE '$term%')");
    }
  }

  public function actionReverseFioPayment($id) {
    $ord = dibi::fetch("SELECT ordid, ordpaysesid, ordpricevat FROM orders WHERE ordid=%i", $id);
    if (!empty($ord->ordpaysesid)) {
      $fioEComConfig = $this->neonParameters["fioECom"];
      $fioECom = new \fioECom($fioEComConfig["Id"], $fioEComConfig["CertPass"]);
      $ret = $fioECom->reverse($ord->ordpaysesid, $ord->ordpricevat);
      if (substr($ret,8,2)==="OK") {
        $this->flashMessage("Vrácení platby proběhlo úspěšně.");
        $ords = $this->model->getOrdersModel();
        $ords->cancelPayment($ord->ordid, $this->adminData->admid);
      } else {
        $this->flashMessage("Vrácení platby se nezdařilo. ".$ret, "err");
      }
    }
    $this->redirect("edit", $id);
  }

  public function renderEdit($id) {
    $form = $this['orderEditForm'];
    $formState = $this['orderChangeStateForm'];

    if (!$form->isSubmitted() && !$formState->isSubmitted()) {
      $orders = $this->model->getOrdersModel();
      $dataRow = $orders->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      //naformatuju datum
      if (!empty($dataRow->orddeldatedel)) {
        $dataRow->orddeldatedel = $this->formatDate($dataRow->orddeldatedel);
      }
      if (!empty($dataRow->orddeldatestart)) {
        $dataRow->orddeldatestart = $this->formatDate($dataRow->orddeldatestart);
      }

      $form->setDefaults($dataRow);
      $formState->setDefaults($dataRow);

      $this->template->dataRow = $dataRow;

      //doplnim polozky objednavky
      $this->template->ordItems = dibi::query("SELECT * FROM orditems LEFT JOIN products ON (proid=oriproid) WHERE oriordid=%i", $dataRow->ordid)->fetchAssoc('oriid');

      //doplnim polozku se slevou
      $this->template->ordItemDisc = dibi::fetch("SELECT * from orditems where oriordid=%i AND oritypid=3", $dataRow->ordid);
      //načtu zpusb dopravy
      $this->template->ordPayType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $dataRow->orddelid);
      $ordDelType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $this->template->ordPayType->delmasid);

      //pokud geis a balík je odeslaný doplním info z api
      if ($ordDelType->delcode === 'GEIS' && !empty($dataRow->ordparcode)) {
        try {
          $geisApi = new \GeisApi($this->neonParameters["geis"]);
          $parcel = $geisApi->ParcelDetail($dataRow->ordparcode);
          $parcelHistory = array();
          foreach ($parcel->History as $history) {
            $parcelHistory[] = array(
              'date' => $history->StatusDate,
              'description' => $history->StatusName,

            );
          }
          $this->template->parcelHistory = $parcelHistory;
        } catch (\Exception $e) {

        }

      } else if ($ordDelType->delcode === 'COOL_BALIK' && !empty($dataRow->ordparcode)) {
        $coolBalikApi = new \BoxxiApi($this->neonParameters["BoxxiApi"], $this->model);
        $status = $coolBalikApi->getParcelStatus($dataRow);
        $this->template->apiStatus = $status;
      }

       //doplnim log zmen
      $this->template->statusLog = dibi::fetchAll("
      SELECT orldatec, orlstatus, admname AS orladmname, orlnote
      FROM orders_log
      LEFT JOIN admins ON (admid=orladmid)
      WHERE
      orlordid=%i", $dataRow->ordid, "ORDER BY orlid DESC");

      $this->template->enum_ordstatus = $orders->getEnumOrdStatus();

      //kontrola cerne listiny
      //$this->template->bl = $orders->blAnalyse($dataRow);

      //eet data
      $this->template->eetRows = dibi::fetchAll('SELECT * FROM eet_log WHERE eet_log.logordid=%i', $dataRow->ordid);

    }
  }

  public function actionDelete($id) {
    if ($id > 0) {
      $orders = $this->model->getOrdersModel();
      $orders->delete($id);
      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }

  public function actionCsobReverse($ordid) {
    $ords = $this->model->getOrdersModel();
    $ord = $ords->load($ordid);
    if (!empty($ord->ordpaymentid)) {
      $config = $this->neonParameters["csob"];
      $csob = new \CsobPayment($config);
      if ($csob->paymentReverse($ord->ordpaymentid)){
        $this->flashMessage("Platbu se podařilo zrušit");
      } else {
        $this->flashMessageErr("Platbu se NEpodařilo zrušit");
      }
    } else {
      $this->flashMessageErr("ID platby se nepodařilo zjistit");
    }
    $this->redirect("edit", $ordid);
  }

  public function actionMakeInvoice($id) {
    if ($id > 0) {
      $orders = $this->model->getOrdersModel();
      try {
        $orders->makeInvoice($id);
        $this->flashMessage('Faktura byla vystavena');
      } catch (ModelException $e) {
        $this->flashMessageErr($e->getMessage());
      }
    }
    $this->redirect('edit', $id);
  }

  public function actionPostIdokladInvoice($id) {
    $idokladApi = new \iDokladApi($this->neonParameters["idoklad"], $this->model);
    $ret = $idokladApi->postOrder($id, $this->curKey, 'https://www.'.$this->config["SERVER_NAMESHORT"].'/idoklad/redirect');
    if ($ret === FALSE) {
      $this->flashMessageErr($idokladApi->lastErrorMsg);
    } else {
      if (is_numeric($ret)) {
        $this->redirectUrl("https://app.idoklad.cz/IssuedInvoice/Detail/" . $ret);
        $this->terminate();
      }
    }
    $this->redirect("edit", $id);
  }

  public function actionPrint($id) {
    if ($id > 0) {
      $this->printOrder($id, 'D');
    }
    //$this->redirect('default');
  }

  public function actionExportPohodaXph($id) {
    if ($id > 0) {
      $ordCode = dibi::fetchSingle("SELECT ordcode FROM orders WHERE ordid=%i", $id);
      $xml=$this->exportPohodaXph($id);
      header('Content-Description: File Transfer');
      header('Cache-Control: public, must-revalidate, max-age=0');
      header('Pragma: public');
      header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
      header('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT');
      header('Content-Type: application/force-download');
      header('Content-Type: application/octet-stream', false);
      header('Content-Type: application/download', false);
      header('Content-Type: application/xml', false);
      if (!isset($_SERVER['HTTP_ACCEPT_ENCODING']) OR empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
        // don't use length if server using compression
        header('Content-Length: '.strlen($xml));
      }
      header('Content-disposition: attachment; filename="'.$ordCode.'.xph"');
    }
    echo $xml;
    $this->terminate();
  }

  public function actionPrintInvoice($id, $target='I') {
    if ($id > 0) {
      $this->printOrder($id, $target, 'Invoice.latte');
    }
    //$this->redirect('default');
  }

  public function actionBatchAction() {
    $vyskladnit = $this->getParameter('vyskladnit');
    if (isset($vyskladnit)) {
      $ids = $this->getParameter('ordid');
      $ids = implode("|", $ids);
      $this->redirect('OrderStore:dispatch', $ids);
    }

    $orders = $this->getParameter('export_orders');
    $ids = $this->getParameter('ordid');
    if ($orders !== NULL) {
      if (count($ids) === 0) {
        $this->flashMessage("Nevybrali jste žádný záznam", "err");
        $this->redirect('Order:default');
      }
      $this->printOrder($ids, "D", 'Order.latte');
      $this->terminate();
    }

    $ulozenka = $this->getParameter('export_ulozenka');
    if (isset($ulozenka)) {
      $ids = $this->getParameter('ordid');
      $orders = $this->model->getOrdersModel();
      $parcels = array();
      $parCodesOrdIds = array();
      if (count($ids) === 0) {
        $this->flashMessage("Nevybrali jste žádný záznam", "err");
        $this->redirect('Order:default');
      }
      $ulozenkaApi = new \UlozenkaApi($this->neonParameters["ulozenka"]);
      foreach ($ids as $id) {
        $order = $orders->load($id);
        if ($order) {
          //nastaveno odeslano pokud se jedna o ulozenku
          $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
          $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

          if (($delMode->delcode === 'ULOZENKA') && $ulozenkaApi->isApiOn()) {
            $ret = $ulozenkaApi->postParcelAdd($order);
            if ($ret === FALSE) {
              $this->flashMessage("Balík pro objednávku $order->ordcode se nepodařilo předat do Uloženky. ".$ulozenkaApi->errMsg, "err");
            } else if ($ret > 0) {
              $orders->update($order->ordid, array('ordparcode'=>$ret));
              $parcels[] = $ret;
            }
          }
        }
      }
      /*
      $format = $this->getParameter('f');
      $firstPosition = $this->getParameter('fp');
      if (count($parcels) > 0) {
        $file = $ulozenkaApi->postLabels($parcels, $format, $firstPosition);
        if ($file) {
          header('Content-Disposition: attachment;filename="ulozenka_labels.'.$format.'"');
          header('Content-Type: application/force-download');
          if ($format == 'pdf') {
            echo base64_decode($file);
          } else {
            echo $file;
          }
          $this->terminate();
        } else {
          $this->flashMessage("Štítek objednávky č. $order->ordcode se nepodařilo vytisknout. ".$ulozenkaApi->errMsg, "err");
        }
      }
      */
      $this->redirect('Order:default');
    }

    $geis = $this->getParameter('export_geis');
    if (isset($geis)) {
      $ids = $this->getParameter('ordid');

      $orders = $this->model->getOrdersModel();
      $parcels = array();
      if (count($ids) === 0) {
        $this->flashMessage("Nevybrali jste žádný záznam", "err");
        $this->redirect('Order:default');
      }

      $action = $this->getParameter('geis_action');
      $print_position = (int)$this->getParameter('print_position');

      $makePickup = (boolean)$this->getParameter('makePickup') && $action === 'export';

      try {
        $geisApi = new \GeisApi($this->neonParameters["geis"]);

        //zjistím pickupdate
        $pickupDate = (string)$this->getParameter('pickupDate');
        if (!empty($pickupDate)) {
          $pickupDate = $this->formatDateMySQL($pickupDate);
        } else {
          $pickupDate = $geisApi->getNextPossiblePickup();
        }

        //založím svoz
        if ($makePickup) {
          $geisApi->createPickup($pickupDate, count($ids));
        }

        foreach ($ids as $id) {
          $order = $orders->load($id);
          if ($order) {
            //nastaveno odeslano pokud se jedna o ulozenku
            $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
            $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

            if ($delMode->delcode === 'GEIS') {
              if ($action === 'export') {
                $ret = $geisApi->postParcel($order, $pickupDate);
                if ($ret === FALSE) {
                  $this->flashMessage("Balík pro objednávku $order->ordcode se nepodařilo předat do Uloženky. " . $geisApi->errMsg, "err");
                } else if ($ret > 0) {
                  $orders->update($order->ordid, array('ordparcode' => $ret));
                  $parcels[] = $ret;
                }
              } else if ($action === 'print' || $action === 'delete') {
                $parcels[] = $order["ordparcode"];
                $parCodesOrdIds[$order["ordparcode"]] = $order["ordid"];
              }
            }
          }
        }
        if (count($parcels)) {
          if ($action === 'print') {
            $geisApi->PrintLabel($parcels, $print_position);
          } else if ($action === 'delete') {
            $ret = $geisApi->deleteShipment($parcels);
            if (!empty($ret->ShipmentsNumbers)) {
              if (!empty($ret->ShipmentsNumbers->DeleteShipmentItemResponse->ErrorMessage)) {
                $this->flashMessageErr($ret->ShipmentsNumbers->DeleteShipmentItemResponse->ErrorMessage);
                return false;
              }
              foreach ($ret->ShipmentsNumbers as $parcel) {
                if ($parcel->ErrorMessage === NULL && !empty($parcel->ShipmentNumber)) {
                  $parCode = (string)$parcel->DeleteShipmentItemResponse->ShipmentNumber;
                  if (isset($parCodesOrdIds[$parCode])) {
                    $ordId = $parCodesOrdIds[$parCode];
                    $orders->update($ordId, array("ordparcode"=>NULL));
                  }
                }
              }
            }
            //smažu kódy balíku u zásilek
          }
        } else if ($action === 'print_list') {
          $geisApi->PrintPickupList($pickupDate);
        }

      } catch (\Exception $e) {
        $this->flashMessageErr((isset($order->ordcode) ? $order->ordcode . ":" : "") . $e->getMessage());
      }

      $this->redirect('Order:default');
    }

    $coolBalik = $this->getParameter('export_coolbalik');
    if (isset($coolBalik)) {
      $ids = $this->getParameter('ordid');

      $orders = $this->model->getOrdersModel();
      if (empty($ids)) {
        $this->flashMessage("Nevybrali jste žádný záznam", "err");
        $this->redirect('Order:default');
      }

      $action = $this->getParameter('coolbalik_action');

      try {
        $boxxiApi = new \BoxxiApi($this->neonParameters["BoxxiApi"], $this->model);

        foreach ($ids as $id) {
          $order = $orders->load($id);
          if ($order) {
            //nastaveno odeslano pokud se jedna o ulozenku
            $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
            $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

            if ($delMode->delcode == 'COOL_BALIK') {
              if ($action === 'export') {
                $ret = $boxxiApi->postParcelAdd($order);
                if ($ret === FALSE) {
                  $this->flashMessage("Balík pro objednávku $order->ordcode se nepodařilo předat do Boxxi. " . $boxxiApi->errMsg, "err");
                } else if ($ret > 0) {
                  $orders->update($order->ordid, array(
                    'ordparcode' => $ret["PackageNumbers"][0],
                    'ordparid' => $ret["ShipmentNumber"]
                  ));
                }
              }
            }
          }
        }
      } catch (\Exception $e) {
        $this->flashMessageErr((isset($order->ordcode) ? $order->ordcode . ":" : "") . $e->getMessage());
      }

      $this->redirect('Order:default');
    }

    $changeStatus = $this->getParameter('change_status');
    if (isset($changeStatus)) {
      $status = $this->getParameter('new_status');
      $ids = $this->getParameter('ordid');

      foreach ($ids as $value) {
        $this->changeOrderStatus(array('ordid'=>$value, 'ordstatus'=>$status), $this->adminData->admid);
      }
      $this->redirect('default');
    }

    $smsSend = $this->getParameter('sms_send');
    if (isset($smsSend)) {
      $ids = $this->getParameter('ordid');

      foreach ($ids as $value) {
        $ords = $this->model->getOrdersModel();
        $delModes = $this->model->getDeliveryModesModel();
        $ord = $ords->load($value);
        if (!empty($ord->ordtel)) {
          $smsTemplate = $this->createTemplate();
          $smsTemplate->payMode = $delModes->load((int)$ord->orddelid);
          $smsTemplate->delMode = $delModes->load($smsTemplate->payMode->delmasid);
          $smsTemplate->order = $ord;
          $smsTemplate->setFile(WWW_DIR.'/../templates/Mails/smsOrderSend.latte');
          $this->smsSend($ord->ordtel, $smsTemplate);
        }
      }
      $this->redirect('default');
    }
  }


  public function actionDeleteItem($id, $ordid) {
    if ($id > 0) {
      $orders = $this->model->getOrdersModel();
      $ordItems = $this->model->getOrdItemsModel();
      $ordItems->delete($id);
      $orders->recalcOrder($ordid);
      $this->flashMessage('Položka byla vymazána');
    }
    $this->redirect('edit', $ordid);
  }

  /********************* facilities *********************/
  protected function createComponentOrderChangeStateForm() {
    $order = $this->model->getOrdersModel();
    $form = $this->createAppForm();

    $enum_ordstatus = $order->getEnumOrdStatus();
    /*
    //načtu zpusb dopravy
    $ordPayType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $ord->orddelid);
    $ordDelType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $ordPayType->delmasid);


    //podle typu dopravy vyhodím některé statusy
    if ($ordDelType->delcode === 'OSOBNE') {
      unset($enum_ordstatus[3]);
    }

    $id = (int)$this->getParameter('id');
    $ord = $order->load($id);
    */

    $form->addSelect("ordstatus", "Nový stav objednávky", $enum_ordstatus);
    $form->addSubmit('newstate', 'Zmenit stav');
    $form->onSuccess[] = array($this, 'orderChangeStateFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }


  protected function createComponentOrderEditForm() {
    $order = $this->model->getOrdersModel();
    $dels = $this->model->getDeliveryModesModel();
    $enums = $this->model->getEnumcatsModel();
    $admins = $this->model->getAdminsModel();
    $enumCountries = $enums->getEnumCountries(false);
    $id = $this->getParameter('id');
    $ord = $order->load($id);
    $payMode = $dels->load($ord->orddelid);
    $delMode = $dels->load($payMode->delmasid);

    $form = $this->createAppForm();

    $form->addGroup('');

    $form->addText("ordinvcode", "Číslo faktury:", 15);

    $form->addSelect("ordmode", "Typ objednávky:", $order->getEnumOrdMode())
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněný")
      ->setPrompt('Vyberte ...');

    $form->addCheckbox("ordpaystatus", "Zaplaceno");

    $form->addText("ordpricevat", "Celková cena:", 15)
      ->setDisabled(True);

    if ((int)$this->config["DISCOUNT_DISSOLVE"] === 1) {
      $form->addText("orddisc", "Celková sleva:", 15)
        ->setDisabled(True);

      $form->addText("orddiscpercent", "Sleva v %:", 15)
        ->setRequired(FALSE)
        ->addRule(Nette\Forms\Form::NUMERIC, "Sleva v procentech musí být číslo;");
    }

    $form->addText("ordcoucode", "Slevový kupón:", 15);


    $form->addText("ordweight", "Celková hmotnost:", 15)
      ->setDisabled(True);

    $form->addSelect("ordadmid", "Obchodník:", $admins->getEnumAdmins())
      ->setPrompt('');

    $form->addText("ordparcode", "Číslo balíku:", 15);

    $form->addSelect("orddelid", "Doprava / platba:", $order->getEnumOrdDelId(TRUE))
      ->addRule(Nette\Forms\Form::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte ...');

    if (!empty($delMode->delcode === 'ZASILKOVNA')) {
      $form->addSelect("orddelspec", "Odběrné místo:", $dels->getEnumZasilkovnaPlaces())
        ->setPrompt('Vyberte ...');
    }

    if (!empty($delMode->delcode === 'ULOZENKA')) {
      $form->addSelect("orddelspec", "Odběrné místo:", $dels->getEnumUlozenkaPlaces())
        ->setPrompt('Vyberte ...');
    }

    $form->addText("orddeldatestart", "Datum odeslání:", 10)
      ->setOption('description', 'Zadejte ve fromátu dd.mm.rrr');

    $form->addText("orddeldatedel", "Datum doručení:", 10)
      ->setOption('description', 'Zadejte ve fromátu dd.mm.rrr');

    $form->addText("ordmail", "Email:", 20);
    $form->addText("ordtel", "Telefon:", 10);

    $form->addSelect("ordregid", "Oblast doručení:", $order->getEnumCoolBalikRegId());

    $form->addGroup('Fakturační adresa');

    $form->addText("ordiname", "Jméno:", 60);
    $form->addText("ordilname", "Přijmení:", 60);
    $form->addText("ordifirname", "Název firmy:", 60);

    $form->addText("ordistreet", "Ulice:", 60);
    $form->addText("ordistreetno", "Číslo popisné:", 60);
    $form->addText("ordicity", "Město, obec:", 60);
    $form->addText("ordipostcode", "PSČ:", 6);
    $form->addSelect("ordicouid", "Země:", $enumCountries);

    $form->addText("ordic", "IČ:", 10);
    $form->addText("orddic", "DIČ:", 10);
    $form->addCheckbox("ordusrvat", "Plátce DPH");

    $form->addGroup('Dodací adresa');

    $form->addText("ordstname", "Jméno:", 60);
    $form->addText("ordstlname", "Přijmení:", 60);
    $form->addText("ordstfirname", "Název firmy:", 60);

    $form->addText("ordststreet", "Ulice:", 60);
    $form->addText("ordststreetno", "Číslo popisné:", 60);
    $form->addText("ordstcity", "Město, obec:", 60);
    $form->addText("ordstpostcode", "PSČ:", 6);
    $form->addSelect("ordstcouid", "Země:", $enumCountries);

    $form->addGroup('Poznámka');

    $form->addTextArea("ordnote", "", 100, 3);
    /*
    $form->addGroup('Fakturační údaje');
    $form->addText("ordinvcode", "Číslo faktury:", 10);
    //$form->addText("ordinvdate", "Datum vystavení:", 10);
    */

    $form->addSubmit('makeorder', 'Uložit');
    $form->onSuccess[] = array($this, 'orderEditFormSubmitted');
    return $form;
  }

  protected function createComponentOrdItemsEditForm() {
    $form = $this->createAppForm();
    $ordid = (int)$this->getParameter("id");
    //nactu polozky objedmavky
    if ($ordid > 0) {
      $form->addContainer('items');
      $rows = dibi::fetchAll("SELECT * FROM orditems WHERE oritypid=0 AND oriordid=%i", $ordid, " ORDER BY oriname");
      foreach ($rows as $row) {
        $key = 'item_'.$row->oriid;
        $form["items"]->addContainer($key);
        $form["items"][$key]->addHidden("oriid", $row->oriid);
        $form["items"][$key]->addText("oriproid", "", 5)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ID zboží.')
          ->setDefaultValue($row->oriproid);
        $form["items"][$key]->addText("oriname", "", 45)
          ->setAttribute('class', 'autocomplete')
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název položky.')
          ->setDefaultValue($row->oriname);
        $form["items"][$key]->addText("oriprice", "", 5)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte cenu položky.')
          ->setDefaultValue($row->oriprice);
        $form["items"][$key]->addText("sn", "", 20);
        $form["items"][$key]->addText("oriqty", "", 2)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte počet položek.')
          ->setDefaultValue($row->oriqty);
      }
      //postovne
      $deliv = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $ordid, " ORDER BY oriname");
      if ($deliv!==false) {
        $form['items']->addContainer('delivery');
        $form['items']['delivery']->addHidden("oriid", $deliv->oriid);
        $form['items']['delivery']->addHidden("oritypid", 1);
        $form['items']['delivery']->addHidden("oriqty", 1);
        $form['items']['delivery']->addText("oriname", "", 50)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte popis poštovného.')
          ->setDefaultValue($deliv->oriname);
        $form['items']['delivery']->addText("oripricemaster", "", 5)
          ->setDefaultValue($deliv->oripricemaster);
      }
      //nova polozka
      $form->addContainer('newitem');
      $form['newitem']->addHidden("oriordid", $ordid);
      $form['newitem']->addText("oriproid", "", 5);
      $form['newitem']->addText("oriname", "", 50)
        ->setAttribute('class', 'autocomplete');
      $form['newitem']->addText("oriprice", "", 5);
      $form['newitem']->addText("oriqty", "", 3)
        ->setDefaultValue(1);
    }
    $form->addSubmit('saveitems', 'Uložit');
    $form->onSuccess[] = array($this, 'ordItemsEditFormSubmitted');

    return $form;
  }

   public function ordItemsEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $ordid = (int)$this->getParameter("id");
      $formVals = $form->getValues(TRUE);

      $orders = $this->model->getOrdersModel();
      $products = $this->model->getProductsModel();
      $ordItems = $this->model->getOrdItemsModel();
      //nejdrive zjistim jestli nepipnul S/N
      $isSn = false;
      foreach ($formVals['items'] as $item) {
        if (!empty($item["sn"])) {
          $isSn = true;
          dibi::query("UPDATE orditems SET orisn=CONCAT(COALESCE(orisn, ''), IF(orisn IS NOT NULL, '|', ''), '".$item["sn"]."') WHERE oriid=%i", $item["oriid"]);
        }
      }
      if ($isSn) $this->redirect('edit#edititems', $ordid);

      foreach ($formVals['items'] as $item) {
        $id = $item["oriid"];
        unset($item["oriid"]);
        unset($item["sn"]);
        if (isset($item["oripricemaster"])) {
          if (trim($item["oripricemaster"]) == "") $item["oripricemaster"] = NULL;
        }
        if (!empty($item["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $item["oriproid"]);
          $item["oriprocode"] = $product->procode;
          $item["oriprocode2"] = $product->procode2;
          $item["orivatid"] = $product->provatid;
        }
        $ordItems->update($id, $item);

      }
      if (!empty($formVals['newitem']['oriname'])) {
        if (!empty($formVals['newitem']["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $formVals['newitem']["oriproid"]);
          $formVals['newitem']["oriprocode"] = $product->procode;
          $formVals['newitem']["oriprocode2"] = $product->procode2;
          $formVals['newitem']["orivatid"] = $product->provatid;
          $oriid = $ordItems->insert($formVals['newitem']);

          //pokud má dárek vložím dárek
          $gifts = $products->getGifts($product);
          if (!empty($gifts)) {
            foreach ($gifts as $grow) {
              if ($grow) {
                $gifori = array();
                $gifori['orioriid'] = $oriid;
                $gifori['oriordid'] = $ordid;
                $gifori['oriproid'] = $grow->proid;
                $gifori['oriprocode'] = $grow->procode;
                $gifori['oriprocode2'] = $grow->procode2;
                $gifori['oritypid'] = 0;
                $gifori['oriname'] = $grow->proname;
                $gifori['oriprice'] = 0;
                $gifori['oripriceoriginal'] = 0;
                $gifori['orivatid'] = $grow->provatid;
                $gifori['oricredit'] = $grow->procredit;
                $gifori['oriqty'] = $formVals['newitem']["oriqty"];
                $gifori['oriprobigsize'] = $grow->probigsize;
                $gifori['oriprooffer'] = $grow->prooffer;
                $ordItems->insert($gifori);
              }
            }
          }

        } else {
          $this->flashMessageErr("Nelze vložit položku, která není v databázi. Id není vyplněno.");
          $this->redirect('edit', $ordid);
        }
      }
      $orders->recalcOrder($ordid);

      $this->flashMessage("Položky objednávky byly aktualizovány, celková cena objednávky byla prepočítána.");
    }
    $this->redirect('edit#edititems', $ordid);
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sCode = Null;
        $this->sName = Null;
        $this->sAdmin = Null;
        $this->sStatus = Null;
        $this->sDelDateFrom = Null;
        $this->sDelDateTo = Null;
        $this->sPhone = Null;
        $this->sDelMonth = Null;
        $this->sOnlyEgg = Null;
        $this->sIdoklad = Null;
        $this->sDelId = Null;
        $this->sPayCode = Null;
        $this->sNotClosed = Null;
        $this->sIsPayed = Null;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
      } else {
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sName = $vals["name"];
        $this->sAdmin = $vals["admin"];
        $this->sStatus = $vals["status"];
        $this->sDelId = $vals["delid"];
        $this->sPayCode = $vals["paycode"];
        if (!empty($vals["deldatefrom"])) {
          $this->sDelDateFrom = $vals["deldatefrom"]->format("d.m.Y");
        } else {
          $this->sDelDateFrom = "";
        }
        if (!empty($vals["deldateto"])) {
          $this->sDelDateTo = $vals["deldateto"]->format("d.m.Y");
        } else {
          $this->sDelDateTo = "";
        }
        $this->sPhone = $vals["phone"];
        $this->sDelMonth = $vals["delmonth"];
        $this->sOnlyEgg = $vals["onlyegg"];
        $this->sIdoklad = $vals["idoklad"];
        $this->sNotClosed = $vals["notclosed"];
        $this->sIsPayed = $vals["ispayed"];
        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
      }
    }
    $this->redirect("Order:default");
  }

  protected function createComponentSearchForm() {
    $orders = $this->model->getOrdersModel();
    $catalogs = $this->model->getCatalogsModel();
    $admins = $this->model->getAdminsModel();
    $pros = $this->model->getProductsModel();
    $dels = $this->model->getDeliveryModesModel();

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");
    $form->addText("code", "Kód objednávky", 10)
      ->setDefaultValue($this->sCode);

    $form->addText("name", "Příjmení nebo název firmy", 10)
      ->setDefaultValue($this->sName);

    $form->addText("phone", "Telefon", 10)
      ->setDefaultValue($this->sPhone);

    $form->addSelect("admin", "Obchodník", $admins->getEnumAdmins())
      ->setPrompt('')
      ->setDefaultValue($this->sAdmin);

    $form->addSelect("delmonth", "Měsíc", $pros->getEnumEggMonths())
      ->setPrompt('')
      ->setDefaultValue($this->sDelMonth);

    $form->addSelect("status", "Stav", $orders->getEnumOrdStatus())
      ->setPrompt('');
    if ((string)$this->sStatus !== "") {
      $form["status"]->setDefaultValue($this->sStatus);
    }

    $rows = dibi::query("SELECT delid, delname FROM deliverymodes WHERE delstatus=0 AND delmasid=0 ORDER BY delorder")->fetchPairs('delid', 'delname');
    $form->addSelect("delid", "Doprava", $rows)
      ->setPrompt("");
    if (!empty($this->sDelId)) {
      $form["delid"]->setDefaultValue($this->sDelId);
    }

    $form->addSelect("paycode", "Platba", $dels->getEnumPayTypes())
      ->setPrompt("");
    if (!empty($this->sPayCode)) {
      $form["paycode"]->setDefaultValue($this->sPayCode);
    }

    $form->addCheckbox("onlyegg", "Jen vejce")
      ->setDefaultValue($this->sOnlyEgg);

    $form->addCheckbox("notclosed", "Neuzavřené")
      ->setDefaultValue($this->sNotClosed);

    $arr = array(
      'all' => "Vše",
      'yes' => "Ano",
      'no' => "Ne",
    );
    $form->addSelect("idoklad", "iDoklad", $arr)
      ->setDefaultValue($this->sIdoklad);

    $form->addCheckbox("ispayed", "Zaplacené")
      ->setDefaultValue($this->sIsPayed);

    $form->addDatePicker("deldatefrom", "Datum doručení od", 8)
      ->setFormat('d.m.Y')
      ->setReadOnly(FALSE)
      ->setHtmlAttribute('size', 8)
      ->setDefaultValue($this->sDelDateFrom);

    $form->addDatePicker("deldateto", "Datum doručení do", 8)
      ->setFormat('d.m.Y')
      ->setReadOnly(FALSE)
      ->setHtmlAttribute('size', 8)
      ->setDefaultValue($this->sDelDateTo);

    $arr = array(
      'orddatec'=>'Data vytvoření',
    );
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);

    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    );
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  protected function createComponentSearchProductsForm() {
    $orders = $this->model->getOrdersModel();

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");

    $form->addSelect("status", "Stav", $orders->getEnumOrdStatus())
      ->setPrompt('');

    if ((string)$this->sStatus !== "") {
      $form["status"]->setDefaultValue($this->sStatus);
    }

    $rows = dibi::query("SELECT delid, delname FROM deliverymodes WHERE delstatus=0 AND delmasid=0 ORDER BY delorder")->fetchPairs('delid', 'delname');
    $form->addSelect("delid", "Doprava", $rows)
      ->setPrompt("");
    if (!empty($this->sDelId)) {
      $form["delid"]->setDefaultValue($this->sDelId);
    }

    $form->addText("deldatefrom", "Datum doručení od", 10)
      ->setDefaultValue($this->sDelDateFrom);

    $form->addText("deldateto", "Datum doručení do", 10)
      ->setDefaultValue($this->sDelDateTo);

    $form->addCheckbox("notclosed", "Neuzavřené")
      ->setDefaultValue($this->sNotClosed);

    $form->addCheckbox("onlyegg", "Jen vejce")
      ->setDefaultValue($this->sOnlyEgg);

    $form->addCheckbox("ispayed", "Zaplacené")
      ->setDefaultValue($this->sIsPayed);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchProductsFormSubmitted');
    return $form;
  }

  public function searchProductsFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sDelDateFrom = Null;
        $this->sDelDateTo = Null;
        $this->sDelId = Null;
        $this->sNotClosed = Null;
        $this->sOnlyEgg = Null;
        $this->sIsPayed = Null;
        $this->sStatus = Null;
      } else {
        $vals = $form->getValues();
        $this->sDelId = $vals["delid"];
        $this->sDelDateFrom = $vals["deldatefrom"];
        $this->sDelDateTo = $vals["deldateto"];
        $this->sNotClosed = $vals["notclosed"];
        $this->sOnlyEgg = $vals["onlyegg"];
        $this->sIsPayed = $vals["ispayed"];
        $this->sStatus = $vals["status"];
      }
    }
    $this->redirect("products");
  }

  /**
   * vrací data pro našeptávač PSČ
   *
   * @param $query
   * @throws \Dibi\Exception
   */
  public function renderFirNameAc($query) {
    $this->template->setFile(APP_DIR . "/../templates/json/firNameAc.latte");
    $this->template->rows = dibi::fetchAll("SELECT * FROM users WHERE usrifirname LIKE %~like~", $query, "OR usric LIKE %~like~", $query);
  }

  protected function createComponentOrderQuickForm() {
    $order = $this->model->getOrdersModel();
    $enums = $this->model->getEnumcatsModel();
    $admins = $this->model->getAdminsModel();
    $form = $this->createAppForm();

    $form->addHidden("ordadmid", $this->adminData->admid);

    $form->addSelect("ordmode", "Typ objednávky:", $order->getEnumOrdMode())
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněný")
      ->setPrompt('Vyberte ...');

    $form->addText("ordifirname", "Název firmy:", 60)
      ->setHtmlId("ordifirname_ac");

    $form->addHidden("ordusrid")
      ->setHtmlId("ordusrid_ac");

    $form->addText("ordiname", "Jméno:", 60)
      ->setHtmlId("ordiname_ac")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");

    $form->addText("ordilname", "Přijmení:", 60)
      ->setHtmlId("ordilname_ac")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");

    $form->addSelect("orddelid", "Doprava / platba:", $order->getEnumOrdDelId())
      ->addRule(Nette\Forms\Form::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte ...');

    if ($this->secondCurrency) {
      $form->addSelect("ordcurid", "Měna:", $this->getEnumCurr())
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněná")
        ->setPrompt('Vyberte ...');
    } else {
      $form->addHidden("ordcurid", 1);
    }

    $form->addText("ordtel", "Telefon:", 30)
      ->setHtmlId("ordtel_ac");

    $form->addText("ordmail", "Email:", 30)
      ->setHtmlId("ordmail_ac")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addSubmit('makeorder', 'Vytvořit objednávku a upravit položky');
    $form->onSuccess[] = array($this, 'orderQuickFormSubmitted');
    return $form;
  }

  public function orderQuickFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $values = $form->getValues();

      //pokud je usrid předplním co je prázdné
      if (!empty($values->ordusrid)) {
        $usrs = $this->model->getUsersModel();
        $usr =$usrs->load($values->ordusrid);
        if ($usr) {
          if (empty($values->ordistreet) && !empty($usr->usristreet)) $values->ordistreet = $usr->usristreet;
          if (empty($values->ordistreetno) && !empty($usr->usristreetno)) $values->ordistreetno = $usr->usristreetno;
          if (empty($values->ordicity) && !empty($usr->usricity)) $values->ordicity = $usr->usricity;
          if (empty($values->ordipostcode) && !empty($usr->usripostcode)) $values->ordipostcode = $usr->usripostcode;
        }
      }


      $orders = $this->model->getOrdersModel();
      $dels = $this->model->getDeliveryModesModel();
      $dels->setCurrency($this->currencies, $values["ordcurid"]);
      $orders->setCurrency($this->currencies, $values["ordcurid"]);
      $id = $orders->insert($values);

      //vlozim dopravu
      //nactu si zpusob dopravy

      $paymode = $dels->load($values["orddelid"]);
      $delmode = $dels->load($paymode->delmasid);
      $delValues['oriordid'] = $id;
      $delValues['oritypid'] = 1;
      $delValues['oriproid'] = 0;
      $delValues['oriname'] = "Doprava: ".$delmode->delname." - ".$paymode->delname;
      $delValues['oriprice'] = $paymode->delprice;
      $delValues['orivatid'] = Null;
      $delValues['oricredit'] = 0;
      $delValues['oriqty'] = 1;
      $delValues['oriprobigsize'] = 0;
      $delValues['oriprooffer'] = 0;
      $orditems = $this->model->getOrdItemsModel();
      $orditems->insert($delValues);
      $orders->recalcOrder($id);
      $this->flashMessage('Uloženo v pořádku');
      $this->redirect('edit#edititems', $id);
    }
  }
}

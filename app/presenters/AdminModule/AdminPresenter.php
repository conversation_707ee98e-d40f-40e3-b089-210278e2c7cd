<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class AdminPresenter extends BasePresenter {

  public function renderLogin($backlink) {

  }

  public function actionLogout() {
    $this->user->logout();
    $this->flashMessage("Odhlášení proběhlo <PERSON>ě.");
    $this->redirect('Admin:login');
  }

  public function loginFormSubmitted($form) {
    try {
      $this->user->login($form['admmail']->getValue(), $form['admpassw']->getValue(), self::LOGIN_NAMESPACE);
      $this->restoreRequest($this->backlink);
      $this->redirect('Admin:default');

    } catch (Nette\Security\AuthenticationException $e) {
      $form->addError($e->getMessage());
    }
  }



  /********************* view default *********************/

  public function renderDefault() {
    //seznam aktualnich objednavek
    $orders = $this->model->getOrdersModel();
    $dataRows = dibi::fetchAll($orders->getList("WHERE ordstatus NOT IN (4,5,7)", "ORDER BY orddatec ASC") . " LIMIT 20");
    $this->template->dataRows = $dataRows;

    //ciselnik statusu
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
  }

  /********************* view add, edit *********************/

  public function renderEditSelf() {
    $this->renderEdit($this->user->id);
  }

  public function renderEdit($id) {
    if ($this->adminData->admrole!='admin' && $this->adminData->admid != $id) {
      $this->flashMessageErr("Nemáte oprávnění editovat cizí účty a vytvářet nové.");
      $this->redirect('list');
    }

    $form = $this['adminForm'];

    if (!$form->isSubmitted() && $id > 0) {
      $admin = $this->model->getAdminsModel();
      $row = $admin->load($id);
      if (!$row) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      $form->setDefaults($row);
    }
  }

  public function renderList() {
    $admin = $this->model->getAdminsModel();
    $this->template->dataRows = dibi::fetchAll("SELECT * FROM admins ORDER BY admname");
    $this->template->enum_admstatus = $admin->getEnumAdmStatus();
  }

  public function adminFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVars = $form->getValues();

      $id = (int) $this->getParameter('id');
      $admin = $this->model->getAdminsModel();

      if ($id > 0) {
        $admRow = $admin->load($id);
        //kontrola pokud chce zmenit heslo
        $passw_changed = false;
        if ($formVars["admpassw_old"] != "") {
          if (!$this->passwordVerify($formVars["admpassw_old"], $admRow["admpassw"])) {
            $this->flashMessageErr("Původní heslo jste nevyplnil/a správně. Heslo nebylo změněno");
            unset($formVars["admpassw"]);
          } else {
            $formVars["admpassw"] = $this->passwordHash($formVars["admpassw"]);
            $passw_changed = true;
          }
        } else {
          unset($formVars["admpassw"]);
        }
      } else {
        $formVars["admpassw"] = $this->passwordHash($formVars["admpassw"]);
      }

      unset($formVars["admpassw_old"]);
      unset($formVars["admpassw2"]);

      try {
        $admin->checkDuplicityEMail($id, $formVars['admmail']);
        if ($id > 0) {
          $admin->update($id, $formVars);
          $this->flashMessage('Údaje byly aktualizovány.');
        } else {
          $id = $admin->insert($formVars);
          $this->flashMessage('Účet byl vytvořen.');
        }
      } catch (Exception $e) {
        $form->addError($e->getMessage());
        return false;
      }
    }
    $this->redirect('edit', $id);
  }

  /********************* facilities *********************/

  /**
   * Component factory.
   * @param  string  component name
   * @return void
   */
  protected function createComponentAdminLoginForm() {
    $form = $this->createAppForm();
    $form->addText('admmail', 'Přihlašovací jméno:')
      ->setAttribute('autofocus')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).');

    $form->addPassword('admpassw', 'Heslo:')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.');

    $form->addSubmit('login', 'Přihlásit se');
    $form->onSuccess[] = array($this, 'loginFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  protected function createComponentAdminForm() {
    $id = $this->getParameter('id');

    $admin = $this->model->getAdminsModel();
    $form = $this->createAppForm();
    $form->addText('admname', 'Jméno:', 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Jméno.');

    $form->addText('admmail', 'Login:', 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Login.');
    if (empty($id)) {
      $form->addPassword('admpassw', 'Heslo:')
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.');
      $form->addPassword('admpassw2', 'Heslo podruhé:')
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo podruhé.')
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["admpassw"]);
    } else if ($id == $this->adminData->admid) {
      $form->addPassword('admpassw_old', 'Původní heslo:');
      $form->addPassword('admpassw', 'Nové heslo:')
        ->addConditionOn($form["admpassw_old"], Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte nové heslo.');
      $form->addPassword('admpassw2', 'Heslo podruhé:')
        ->addConditionOn($form["admpassw_old"], Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo podruhé.')
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["admpassw"]);
    }

    if ($this->user->isAllowed('Admin:Admin', 'changeForeign')) {
      $roles = $this->user->getAuthorizator()->getRoles();
      $selArr = array();
      foreach ($roles as $role) {
        $selArr[$role] = $role;
      }
      $form->addSelect('admrole', 'Role', $selArr);
    }

    if ($this->user->isAllowed('Admin:Admin', 'changeForeign')) {
      $form->addSelect('admstatus', 'Status', $admin->getEnumAdmStatus());
    }


    $form->addSubmit('save', 'Uložit');
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = [$this, 'adminFormSubmitted'];

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
}

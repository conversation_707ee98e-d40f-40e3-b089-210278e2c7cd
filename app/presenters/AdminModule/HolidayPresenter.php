<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class HolidayPresenter extends BasePresenter {

  public $backlink = '';

  public function formHolidaysSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $holidays = $this->model->getHolidaysModel();
      $id = $this->getParam('id');
      if ($id > 0) {
        $holidays->update($id, $form->getValues());
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $holidays->insert($form->getValues());
        $this->flashMessage('Nový svátek uložen v pořádku');
      }
    }
    $this->redirect('default');
  }

  /********************* view default *********************/

  public function renderDefault() {
    $dataRows = dibi::fetchAll("SELECT * FROM holidays ORDER BY holmonth, holday");
    $this->template->dataRows = $dataRows;
  }

  public function renderEdit($id) {

  }

  public function renderDelete($id) {
    if ($id > 0) {
      $holidays = $this->model->getHolidaysModel();
      $holidays->delete($id);
      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }


  /********************* facilities *********************/

  protected function createComponentHolidaysForm() {
    $holidays = $this->model->getHolidaysModel();
    $form = $this->createAppForm();

    $form->addText('holname', 'Jméno svátku', 25)
      ->addRule(Nette\Application\UI\Form::FILLED, 'Vyplňte prosím název svátku');
    $form->addText('holday', 'Den v měsíci', 3)
      ->addRule(Nette\Application\UI\Form::FILLED, 'Vyplňte prosím den svátku')
      ->addRule(Nette\Application\UI\Form::RANGE, 'Hodnota musí být v rozmezí od %d do %d', array(1, 31));

    $form->addText('holmonth', 'Měsíc', 3)
      ->addRule(Nette\Application\UI\Form::FILLED, 'Vyplňte prosím měsíc svátku')
      ->addRule(Nette\Application\UI\Form::RANGE, 'Hodnota musí být v rozmezí od %d do %d', array(1, 12));
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'formHolidaysSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    $id = $this->getParameter('id');
    if ($id > 0) {
      $dataRow = $holidays->load($id);
      $form->setDefaults($dataRow);
    }
    return $form;
  }
}

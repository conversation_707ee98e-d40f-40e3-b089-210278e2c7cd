<?php
namespace AdminModule;

use dibi;
use Nette;

final class DeliveryModePresenter extends BasePresenter {

  /** @persistent */
  public $sDelId;

  /** @persistent */
  public $delId;

  /** @persistent */
  public $sName;

  /** @persistent */
  public $sPostCode;

  /** @persistent */
  public $sStatus;

  /** @persistent */
  public $sOrderBy = 'dpiorder';

  /** @persistent */
  public $sOrderByType = 'ASC';

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $deliveryMode = $this->model->getDeliveryModesModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();
      if (empty($vals["delprice1a"])) $vals["delprice1a"] = 0;
      if (empty($vals["delprice1b"])) $vals["delprice1b"] = 0;
      if (empty($vals["delprice1c"])) $vals["delprice1c"] = 0;
      if (empty($vals["delprice1d"])) $vals["delprice1d"] = 0;
      if (empty($vals["delprice2a"])) $vals["delprice2a"] = 0;
      if (empty($vals["delprice2b"])) $vals["delprice2b"] = 0;
      if (empty($vals["delprice2c"])) $vals["delprice2c"] = 0;
      if (empty($vals["delprice2d"])) $vals["delprice2d"] = 0;

      //naformatuji datumy
      if (!empty($vals["deldates"])) {
        $arr = explode(",", trim($vals["deldates"], ","));
        $datesArr = array();
        foreach ($arr as $date) {
          $datesArr[] = $this->formatDateMySQL($date);
        }
        if (count($datesArr) > 0) {
          $vals["deldates"] = implode(",", $datesArr);
        }
      }

      try {
        if ($deliveryMode->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('DeliveryMode:default');
        }

      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }

  /********************* view default *********************/

  public function renderDefault($status=0) {
    $deliveryMode = $this->model->getDeliveryModesModel();
    $statusSql = ($status == 1 ? ''  : ' AND delstatus=0');
    $dataRows = dibi::query($deliveryMode->getSql()." WHERE delmasid=0 $statusSql ORDER BY delorder")
      ->fetchAssoc('delid');

    foreach ($dataRows as $key => $value) {
      $dataRows[$key]["subItems"] = dibi::fetchAll($deliveryMode->getSql()." WHERE delmasid=$key $statusSql ORDER BY delorder");
    }
    $this->template->dataRows = $dataRows;
    //ciselnik statusu
    $this->template->enum_delstatus = $deliveryMode->getEnumDelStatus();
  }

  public function renderDelPath()  {
    $dels = $this->model->getDeliveryModesModel();
    $dpis = $this->model->getDelPathItemsModel();

    $this->template->id = $this->sDelId;

    $this->template->rows = array();

    $sql[] = "SELECT * FROM delpathitems";

    $where = $this->getPathWhere();

    if (count($where) > 0) {
      $sql[] = " WHERE ";
    }

    $orderBy[] = " ORDER BY " . $this->sOrderBy . " " . $this->sOrderByType;

    $sql = array_merge($sql, $where, $orderBy);

    $this->template->rows = dibi::fetchAll($sql);

    $this->template->enum_delid = $dels->getEnumPathDelId();
    $this->template->enum_dpistatus = $dpis->getEnumDpiStatus();

    $this->template->delPaths = dibi::fetchAll("SELECT * FROM deliverymodes WHERE delcode='VLASTNI_PREPRAVA' ORDER BY deldayid ASC");
  }

  public function renderPathBatchUpdate() {

  }

  protected function createComponentExportPathForm() {
    $dels = $this->model->getDeliveryModesModel();

    $form = $this->createAppForm();

    $form->addSelect("delid", "Rozvozová trasa", $dels->getEnumPathDelId())
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
      ->setPrompt("Vyberte ...");

    $form->addSubmit('submit', 'Exportovat');
    $form->onSuccess[] = array($this, 'exportPathFormSubmitted');
    return $form;

  }

  public function exportPathFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $rows = dibi::fetchAll("SELECT * FROM delpathitems WHERE dpidelid=%i", $vals->delid);

      $file = TEMP_DIR . "/export_trasy.csv";
      @unlink($file);

      $data = array();
      $data[] = array("Obec", $this->utf8ToWin1250("Pošta"), $this->utf8ToWin1250("PSČ"), "Okres", "Cena");
      foreach ($rows as $key => $row) {
        $data[] = array($this->utf8ToWin1250($row->dpiplacename), $this->utf8ToWin1250($row->dpipostname), $row->dpipostcode, $this->utf8ToWin1250($row->dpidistrictname), $row->dpiprice);
      }

      //pošlu na výstup
      header('Content-Disposition: attachment; filename="export_trasy.csv";');
      header('Content-Type: application/csv; charset=UTF-8');

      $fp = fopen("php://output", 'wb');
      foreach ($data as $row) {
        fputcsv($fp, $row, ";");
      }

      $this->terminate();
    }
  }

  public function renderGeisStatus() {
    $geisApi = New \GeisApi($this->neonParameters["geis"]);

    $this->template->status = $geisApi->getServiceStatus();
  }

  public function renderEdit($id, $masid=0) {
    $form = $this['editForm'];

    if (!$form->isSubmitted()) {
      $deliveryMode = $this->model->getDeliveryModesModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $deliveryMode->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }

        //naformatuji datumy
      if (!empty($dataRow->deldates)) {
        $arr = explode(",", trim($dataRow->deldates, ","));
        $datesArr = array();
        foreach ($arr as $date) {
          $datesArr[] = $this->formatDate($date);
        }
        if (count($datesArr) > 0) {
          $dataRow->deldates = implode(",", $datesArr);
        }
      }

        $form->setDefaults($dataRow);
      }
      if ($masid > 0) $this->template->delMas = $deliveryMode->load($masid);
      $this->template->dataRow = $dataRow;
      $this->template->id = $id;

      //nactu stavajici slevy
      $this->template->discounts = dibi::fetchAll("SELECT * FROM discounts WHERE disdelid=%i", $id);
    }
  }

  public function actionDeleteDelFree($disid, $delid) {
    $diss = $this->model->getDiscountsModel();
    $diss->delete($disid);
    $this->redirect('edit#freedelivery', $delid);
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');
    $masid = (int)$this->getParameter('masid');

    $deliveryMode = $this->model->getDeliveryModesModel();
    $pros = $this->model->getProductsModel();

    if ($id > 0) {
      $dataRow = $deliveryMode->load($id);
      $masid = $dataRow->delmasid;
    }

    $form = $this->createAppForm();

    $form->addText('delname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');

    $form->addText('delnames', 'Krátký název:', 30)
      ->setOption("description", 'Zobrazuje se v administraci');

    if ($masid > 0) {
      //jedna se o platbu
      $form->addSelect('delmasid', 'Způsob dopravy:', $deliveryMode->getEnumDelModes())
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna");
      $form["delmasid"]->setDefaultValue($masid);

      $form->addSelect('delcode', 'Typ platby:', $deliveryMode->getEnumPayTypes())
        ->setPrompt("");

      $form->addCheckbox("delnodelfree", "Neplatí platba zdarma");
    } else {
      //jedna se odopravu
      $delTypes = $deliveryMode->getEnumDelTypes();
      if (!$this->getUser()->isAllowed('Admin:DeliveryModeCS', 'zasilkovna')) {
        unset($delTypes["ZASILKOVNA"]);
      }
      if (!$this->getUser()->isAllowed('Admin:DeliveryModeCS', 'ulozenka')) {
        unset($delTypes["ULOZENKA"]);
      }
      $form->addSelect('delcode', 'Typ dopravy:', $delTypes)
        ->setPrompt("");

      $form->addCheckbox("delnodelfree", "Neplatí doprava zdarma");
    }

    $form->addSelect('deldurid', 'Omezení dle typu zboží:', $pros->getEnumProDurId());

    $labelA = 'Cena A';
    if (isset($this->neonParameters["labels"]["a"])) $labelA = $this->neonParameters["labels"]["a"];
    $labelB = 'Cena B';
    if (isset($this->neonParameters["labels"]["b"])) $labelB = $this->neonParameters["labels"]["b"];
    $labelC = 'Cena C';
    if (isset($this->neonParameters["labels"]["c"])) $labelC = $this->neonParameters["labels"]["c"];
    $labelD = 'Cena D';
    if (isset($this->neonParameters["labels"]["d"])) $labelD = $this->neonParameters["labels"]["d"];


    $text = ($masid > 0 ? 'platbu' : 'dopravu') ;

    $form->addText('delprice1a', $labelA.' za '.$text.' v '.$this->curCodes[1].':', 30, 5)
      ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('delprice1b', $labelB.' za '.$text.' v '.$this->curCodes[1].':', 30, 5)
      ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('delprice1c', $labelC.' za '.$text.' v '.$this->curCodes[1].':', 30, 5)
      ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('delprice1d', $labelD.' za '.$text.' v '.$this->curCodes[1].':', 30, 5)
      ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    if ($this->secondCurrency) {
      $form->addText('delprice2a', $labelA.' za '.$text.'  v '.$this->curCodes[2].':', 30, 5)
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

      $form->addText('delprice2b', $labelB.' za '.$text.'  v '.$this->curCodes[2].':', 30, 5)
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

      $form->addText('delprice2c', $labelC.' za '.$text.'  v '.$this->curCodes[2].':', 30, 5)
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

      $form->addText('delprice2d', $labelD.' za '.$text.'  v '.$this->curCodes[2].':', 30, 5)
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");
    }

    if ($masid > 0) {
      //$form->addTextArea('deldesc', 'Popis:', 100, 5);
    } else {
      $form->addText('delweightlimitfrom', 'Hmotnost od:', 30, 10)
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "Hmotnost od: hodnota musí být číslo");
      $form->addText('delweightlimitto', 'Hmotnost do:', 30, 10)
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "Hmotnost do: hodnota musí být číslo");

      //$form->addCheckbox('delspecdel', 'individuální doprava');

      //$form->addCheckbox('delpricelimit', 'drobná doprava do 500Kč');

      $form->addText('delurlparcel', 'URL sledování zásilky:', 130);

      $form->addText('deldates', 'Termíny doručení:', 90)
       ->setOption("description", 'Zadejte datumy ve tvaru "dd.mm.rrrr" oddělené čárkou, nebo čísla dnů oddělené znakem # (např. Po, St, Pa takto: #1#3#5),nebo frázi "pracovnidny"');
    }

    $form->addTextArea('deltext1', "Kontaktní informace", 100, 7);
    $form->addTextArea('deltext2', "Informace při odeslání", 100, 7);

    $form->addText('delorder', 'Pořadí:', 30, 10)
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::INTEGER, "Pořadí: hodnota musí být celé číslo");

    $form->addSelect('delstatus', 'Status:', $deliveryMode->getEnumDelStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }


  protected function createComponentEditDelFreeForm() {
    $delid = (int)$this->getParameter('id');
    $masid = (int)$this->getParameter('masid');

    $discount = $this->model->getDiscountsModel();

    //nactu stavajici slevy
    $rows = dibi::fetchAll("SELECT * FROM discounts WHERE distypid='delfree' AND disdelid=%i", $delid);

    $form = $this->createAppForm();
    $conItems = $form->addContainer('items');
    foreach ($rows as $key => $row) {
      $con = $conItems->addContainer($row->disid);
      $con->addHidden("disdelid", $delid);
      $con->addHidden("distypid", 'delfree');

      if ($this->secondCurrency) {
        $con->addSelect('discurid', 'Měna:', $this->getEnumCurr())
          ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
          ->setDefaultValue($row->discurid);
      } else {
        $con->addHidden("discurid", 1);
      }

      $con->addSelect('disprccat', 'Cenová hladina:', $this->getEnumPrcCat())
        ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
        ->setDefaultValue($row->disprccat);

      $con->addText('disfrom', 'Cena od:', 30)
        ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
        ->addRule(Nette\Forms\Form::INTEGER, "%label musí být celé číslo")
        ->setDefaultValue($row->disfrom);

      $con->addSelect('disstatus', 'Status:', $discount->getEnumDisStatus())
        ->setDefaultValue($row->disstatus);
    }

    $con = $conItems->addContainer(0);
    $con->addHidden("disdelid", $delid);
    $con->addHidden("distypid", 'delfree');

    if ($this->secondCurrency) {
        $con->addSelect('discurid', 'Měna:', $this->getEnumCurr())
          ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
          ->setDefaultValue($row->discurid);
      } else {
        $con->addHidden("discurid", 1);
      }

    $con->addSelect('disprccat', 'Cenová hladina:', $this->getEnumPrcCat())
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit');

    $con->addText('disfrom', 'Cena od:', 30)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::INTEGER, "%label musí být celé číslo");

    $con->addSelect('disstatus', 'Status:', $discount->getEnumDisStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editDelFreeFormSubmitted');

    return $form;
  }

  public function editDelFreeFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $diss = $this->model->getDiscountsModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();
      foreach ($vals["items"] as $key => $row) {
        try {
          if ($key == 0 && (double)$row->disfrom > 0) {
            $diss->insert($row);
          } else if ($key > 0) {
            $diss->update($key, $row);
          }
        } catch (ModelException $e) {
          $form->addError($e->getMessage());
          return false;
        }
      }
      $this->flashMessage('Uloženo v pořádku');
    }
    $this->redirect('this#freedelivery');
  }

  protected function createComponentSearchForm() {
    $dels = $this->model->getDeliveryModesModel();
    $dpis = $this->model->getDelPathItemsModel();

    $form = $this->createAppForm();

    $form->addText("name", "Jméno", 10)
      ->setDefaultValue($this->sName);

    $form->addText("postcode", "PSČ", 10)
      ->setDefaultValue($this->sPostCode);

    $form->addSelect("delid", "Rozvozová trasa", $dels->getEnumPathDelId())
      ->setPrompt("");

    if ($this->sDelId !== NULL) {
      $form["delid"]->setDefaultValue($this->sDelId);
    }

    $form->addSelect("status", "Status", $dpis->getEnumDpiStatus())
      ->setPrompt("");

    if ($this->sStatus !== NULL) {
      $form["status"]->setDefaultValue($this->sStatus);
    }

    $arr = array(
      'dpiorder'=>'Pořadí',
      'dpipostcode'=>'PSČ',
      'dpiplacename'=>'Město/obec',
      'dpipostname'=>'Pošta',
    );

    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);

    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    );

    //pole pro hromadnou aktualizaci
    $form->addSelect("ustatus", "Nastavit status:", $dpis->getEnumDpiStatus())
      ->setPrompt("");

    $form->addText("uprice", "Nová cena", 10);

    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->addSubmit('update', 'Aktualizovat')->setValidationScope([]);
    $form->addSubmit('delete', 'Vymazat')->setValidationScope([]);
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

    public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sName= Null;
        $this->sPostCode = Null;
        $this->sDelId = Null;
        $this->sStatus = NULL;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
      } else {
        $vals = $form->getValues();
        $this->sName= $vals["name"];
        $this->sPostCode = $vals["postcode"];
        $this->sDelId= $vals["delid"];
        $this->sStatus = $vals["status"];
        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
        if ($form["update"]->isSubmittedBy() || $form["delete"]->isSubmittedBy()) {
          $data = array();
          if ($vals["ustatus"] !== "") {
            $data["dpistatus"] = $vals["ustatus"];
          }
          if ($vals["uprice"] !== "") {
            $data["dpiprice"] = (double)$vals["uprice"];
          }
          $ids = $_POST["dpiid"];
          if (count($data) > 0 && is_array($ids) && count($ids) > 0) {
            $dpis = $this->model->getDelPathItemsModel();
            $cnt = 0;
            foreach ($ids as $id) {
              $typText = "aktualizováno";
              if ($form["update"]->isSubmittedBy()) {
                if ($dpis->update($id, $data)) {
                  $cnt ++;
                }
              } else if ($form["delete"]->isSubmittedBy()) {
                $typText = "vymazáno";
                if ($dpis->delete($id)) {
                  $cnt ++;
                }
              }
            }
            $this->flashMessage("Bylu $typText $cnt záznamů.");
          }
        }
      }
    }
    $this->redirect("delPath");
  }

  protected function createComponentImportPathForm() {
    $dels = $this->model->getDeliveryModesModel();

    $form = $this->createAppForm();

    $form->addSelect("delid", "Rozvozová trasa", $dels->getEnumPathDelId())
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
      ->setPrompt("Vyberte ...");

    $form->addTextArea("items", "Data:", 60, 5)
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit');

    $form->addSubmit('submit', 'Uložit');
    $form->onSuccess[] = array($this, 'importPathFormSubmitted');
    return $form;

  }

  public function importPathFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $rows = explode("\n", $vals["items"]);

      $dpis = $this->model->getDelPathItemsModel();

      $cnt = 0;
      foreach ($rows as $row) {
        $item = str_getcsv($row, "\t");
        if (empty($item[0])) {
          break;
        }
        $data = array(
         'dpidelid' => $vals["delid"],
         'dpiplacename' => $this->win1250ToUtf8($item[0]),
         'dpipostname' => $this->win1250ToUtf8($item[1]),
         'dpipostcode' => $item[2],
         'dpidistrictname' => $this->win1250ToUtf8($item[3]),
         'dpiprice' => $item[4],
         'dpistatus' => 0,
         'dpidatec' => new \DateTime(),
       );
       //zjistím jestli záznam existuje
       $dpiId = (int)dibi::fetchSingle("SELECT dpiid FROM delpathitems WHERE dpiplacename=%s", $item[0], " AND dpipostname=%s", $item[1], " AND dpipostcode=%s", $item[2]);
       if ($dpiId > 0) {
         $dpis->update($dpiId, $data);
       } else {
         $dpis->insert($data);
       }
       $cnt++;
      }
      $this->flashMessage("Naimportováno $cnt záznamů");
      $this->redirect("PathBatchUpdate");
    }
  }

  private function getPathWhere() {
    $where = array();

    if (!empty($this->sDelId)) {
      $where[] = " dpidelid=%i";
      $where[] = $this->sDelId;
    }

    if (!empty($this->sName)) {
      if (count($where) > 0) {
        $where[] = " AND ";
      }
      $where[] = " (dpiplacename LIKE %~like~";
      $where[] = $this->sName;
      $where[] = " OR dpipostname LIKE %~like~";
      $where[] = $this->sName;
      $where[] = " OR dpidistrictname LIKE %~like~";
      $where[] = $this->sName;
      $where[] = ")";
    }

    if (!empty($this->sPostCode)) {
      if (count($where) > 0) {
        $where[] = " AND ";
      }
      $where[] = " dpipostcode LIKE %~like~";
      $where[] = $this->sPostCode;
    }

    if ($this->sStatus !== NULL) {
      if (count($where) > 0) {
        $where[] = " AND ";
      }
      $where[] = " dpistatus=%i";
      $where[] = $this->sStatus;
    }

    return $where;
  }

}

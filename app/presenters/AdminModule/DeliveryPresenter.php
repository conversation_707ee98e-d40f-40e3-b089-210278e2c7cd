<?php
namespace AdminModule;

use DateTime;
use http\Exception\RuntimeException;
use Nette,
  dibi,
  Model;

final class DeliveryPresenter extends BasePresenter {
  /** @persistent */
  public $page = 1;

  /** @persistent */
  public $sCode = '';

  /** @persistent */
  public $sVenId = '';

  /** @persistent */
  public $sAdmin = Null;

  /** @persistent */
  public $sStatus = '';

  /** @persistent */
  public $sProId = '';

  /** @persistent */
  public $sSpecMode;

  /** @persistent */
  public $sByEans;

  /** @persistent */
  public $sOrderBy = 'deldatec';

  /** @persistent */
  public $sOrderByType = 'DESC';

  private function  changeStatus($id, $status) {
    $dels = $this->model->getDeliveriesModel();
    $stis = $this->model->getStoItems();
    $del = $dels->load($id);

    if ($del) {
      //ujistim jestli se meni status
      if ($del->delstatus === $status) {
        return TRUE;
      }
    } else {
      throw new Nette\Application\BadRequestException("Dodávka ID:$id nenalezena", '404');
    }
    $vals = array('delstatus'=>$status);
    if ($status === 1 && empty($del->deldate)) {
      $vals["deldate"] = new DateTime();
      $del->deldate = new DateTime();
    }

    $ret = true;
    dibi::begin();
    try {
      if ($status === 1) {
        //naskladnim
        //kontrola zda nejsou zmršené produkty (stiproid=0)
        $proId0Cnt = (int)dibi::fetchSingle("SELECT count(stiid) FROM stoitems WHERE stidelid=%i", $id, " AND stiproid=0");
        if ($proId0Cnt > 0) {
          $this->flashMessage('Změna stavu neprovedena, dávka obsahuje nulové ID produktu [ID:'.$id.']', 'err');
          return false;
        }

        $ret = $stis->putOnStore($id, $del->deldate);

      }
      if ($ret) {
        $dels->update($id, $vals);
        $dels->logStatus($id, $status, $this->adminData->admid);
        $this->flashMessage('Změna stavu provedena v pořádku [ID:'.$id.']');
      } else {
        $this->flashMessage('Změna stavu neprovedena [ID:'.$id.']', 'err');
      }
    } catch (Exception $e) {
      dibi::rollback();
    }
    dibi::commit();
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $dels = $this->model->getDeliveriesModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();
      if (!empty($vals->deldate)) {
        $vals->deldate = $this->formatDateMySQL($vals->deldate);
      } else {
        $vals->deldate = NULL;
      }
      if (isset($vals["delstatus"])) {
       $status = $vals["delstatus"];
       unset($vals["delstatus"]);
      }

      try {
        if ($id > 0) {
          $dels->update($id, $vals);
        } else {
          $id = $dels->insert($vals);
          if (isset($status)) {
            $dels->logStatus($id, $status, $this->adminData->admid);
          }
        }
        if (isset($status)) {
          $this->changeStatus($id, $status);
        }
        $this->flashMessage('Uloženo v pořádku');
        $this->redirect('edit', $id);
      } catch (Model\ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }

  public function renderStore() {
    $where = "";
    if ((string)$this->sCode!=='') {
      $where .= " AND procode LIKE '%$this->sCode%'";
    }

    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY deldate DESC";
    }

    if ($this->sByEans) {
      $this->template->rows = dibi::fetchAll("
        SELECT procode, procode2, proprice1a, proweight, proname, stiean, stiprice, stiweight AS stiweight, stiqty
        FROM stoitems
        INNER JOIN products ON proid=stiproid
        INNER JOIN deliveries ON delid=stidelid
        WHERE proismaster=0 AND stiqtyfree>0 $where
        $orderBy
      ");
    } else {
      $this->template->rows = dibi::fetchAll("
        SELECT procode, procode2, proprice1a, proweight, proname, SUM(stiprice) AS stiprice, SUM(stiweight) AS stiweight, SUM(stiqty) AS stiqty
        FROM stoitems
        INNER JOIN products ON proid=stiproid
        INNER JOIN deliveries ON delid=stidelid
        WHERE proismaster=0 AND stiqtyfree>0 $where
        GROUP BY proid
        $orderBy
      ");
    }

    $this->template->byEans = $this->sByEans;
  }

  public function renderDefault() {
    $dels = $this->model->getDeliveriesModel();

    $where = "";
    if ((string)$this->sStatus!=='') {
      $where .= " AND delstatus=$this->sStatus";
    }
    if ((string)$this->sProId!=='') {
      $where .= " AND EXISTS (SELECT * FROM delitems WHERE deidelid=delid AND deiproid=" . $this->sProId . ")";
    }

    if (!empty($where)) {
      $where = " WHERE " . $where;
    }


    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY deldatec DESC";
    }

    $dataSource = $dels->getDataSource("
    SELECT * FROM deliveries
     $where ORDER BY delid DESC");

    $paginator = $this['paginator']->paginator;
    $paginator->page = $this->page;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->dataRows = $dataRows;

    //ciselnik statusu
    $this->template->enum_delstatus = $dels->getEnumDelStatus();
  }

  public function actionDelete($delid) {
    $dels = $this->model->getDeliveriesModel();
    if ($dels->delete($delid)) {
      $this->flashMessage("Dodávka byla smazána.", 'err');
      $this->redirect('default');
    } else {
      $this->flashMessage("Smazání dodávky se nezdařilo.", 'err');
      $this->redirect('edit', $delid);
    }
  }

  public function actionDeleteItem($id, $delid) {
    if ($id > 0) {
      $stis = $this->model->getStoItems();
      $stis->delete($id);
      $this->flashMessage('Položka byla vymazána');
    }
    $this->redirect('edit', $delid);
  }

  public function renderAddQuick() {


  }

  public function renderEdit($id, $m='') {
    $form = $this['editForm'];

    if (!$form->isSubmitted()) {
      $dels = $this->model->getDeliveriesModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $dels->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        if (!empty($dataRow->deldate)) {
          $dataRow->deldate = $this->formatDate($dataRow->deldate);
        }

        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow;
      $this->template->id = $id;

      //položky
      if ($id > 0) {
        $this->template->stoItems = dibi::query("
          SELECT stiid, stiproid, stiean, stiprice, procode, procode2, proname, proprice1a, stiweight, stiqty
          FROM stoitems
          INNER JOIN products ON proid=stiproid
          WHERE stidelid=%i", $id
        )->fetchAssoc("stiid");
      }

      $this->template->enum_delstatus = $dels->getEnumDelStatus();

    }
  }

  public function renderAddItems($id) {
    $dels = $this->model->getDeliveriesModel();
    $this->template->delivery = $dels->load($id);
  }

  protected function  createComponentEditForm() {
    $dels = $this->model->getDeliveriesModel();
    $form = $this->createAppForm();

    $id = (int)$this->getParameter("id");

    $form->addText('delname', 'Popis naskladnění', 10);
    $form->addText('deldate', 'Datum naskladnění', 10);

    if ($id > 0)  {
      $del = $dels->load($id);

      $form->addSelect('delstatus', 'Stav', $dels->getEnumDelStatus())
        ->setPrompt("Vyberte ...")
        ->addRule(Nette\Application\UI\Form::FILLED, 'Prosím vyplňte %label.');
      if ($del->delstatus === 1) {
        $form["deldate"]->setDisabled(True);
        $form["delstatus"]->setDisabled(True);
      }
    } else {
      $form->addHidden('delstatus', 0);
    }

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    return $form;
  }

  protected function createComponentAddItemsForm() {
    $form = $this->createAppForm();

    $form->addTextArea("scannerItems", "Načtené EANy:", 50, 25);
    $form->addSubmit("submit");
    $form->onSuccess[] = array($this, 'addItemsFormSubmitted');
    return $form;

  }

  protected function createComponentAddItemQuickForm() {
    $form = $this->createAppForm();

    $form->addText("stiproid", "", 2);
    $form->addText("stiname", "", 40)
      ->setAttribute('class', 'autocomplete');
    $form->addText("stiqty", "", 2)
      ->setDefaultValue(1)
      ->addConditionOn($form["stiproid"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte počet.');

    $form->onSuccess[] = array($this, 'addItemQuickFormSubmitted');
    return $form;
  }

  public function addItemQuickFormSubmitted (Nette\Application\UI\Form $form) {
     if ($form->isSubmitted()) {
      $formVals = $form->getValues();

      $dels = $this->model->getDeliveriesModel();
      $stis = $this->model->getStoItems();
      if (!empty($formVals['stiproid'])) {
        unset($formVals["stiname"]);
        $item = $formVals;
        $pro = dibi::fetch("SELECT procode, products.procode2, proname, proweight FROM products WHERE proid=%i", (int)$formVals['stiproid']);
        $item["stiean"] = $pro->procode2;
        $item["stiweight"] = $pro->proweight;
        $item["stidelid"] = 0;

        if (empty($item->stiproid)) {
          $this->flashMessage("ProId je 0 pro procode:" . $pro->procode);
        } else {
          $id = $stis->insert($item);

        }
      }
      $this->flashMessage("Položky dodávky byly aktualizovány.");
    }
    $this->redirect('addQuick');
  }


  public function addItemsFormSubmitted (Nette\Application\UI\Form $form) {
      /* testovací EAN kódy
8594190180204
8594190180304
8594190180014
8594190180014
8594190180021
8594190180021
2964810002441
2964810002611
2964810005011
2964810005041
2988960003011
2988960003051
2988960003031
     */

    if ($form->isSubmitted()) {
      $pros = $this->model->getProductsModel();
      $stis = $this->model->getStoItems();
      $formVals = $form->getValues();
      $delId = (int)$this->getParameter("id");
      if (!empty($formVals["scannerItems"])) {
        $arr = explode("\n", $formVals["scannerItems"]);
        foreach ($arr as $ean) {
          $ean = trim($ean);
          if (empty($ean)) {
            continue;
          }
          //analyzuji EAN
          //zjistím kód podle kterého budu hledat EANech
          $proWeight = 0;
          if (strpos($ean, '29') === 0) {
            //hmotnostní ean
            $proCode2 = substr($ean, 0, 6);
            $proWeight = (int)substr($ean, 6, 6) / 1000;
            $pro = $pros->load($proCode2, 'code2');
            if ($pro === FALSE) {
              $this->flashMessageErr("Nenalezena položka pro váhový EAN $ean");
              continue;
            }
            if ($pro->proismaster === 1) {
              //načtu variantu podle váhy
              $pro = dibi::fetch($pros->getSqlList("promasid=" . $pro->proid . " AND " . (double)$proWeight . " BETWEEN prolimitfrom AND prolimitto"));
              if ($pro === FALSE) {
                $this->flashMessageErr("Nenalezena varianta pro váhový EAN $ean a hmotnost $proWeight kg");
                continue;
              }
            }
          } else {
            $proCode2 = $ean;
            $pro = $pros->load($proCode2, 'code2');
            if ($pro === FALSE) {
              $this->flashMessageErr("Nenalezena položka pro EAN $ean");
              continue;
            }
          }

          //podivam se jestli tam není v dodávce už stejný ean
          $sti = dibi::fetch("SELECT stiid, stiqty FROM stoitems WHERE stidelid=%i", $delId, " AND stiean=%s", $ean);
          if ($sti) {
            $vals = array(
              "stiqty" => $sti->stiqty + 1,
            );
            $stis->update($sti->stiid, $vals);
          }else {
            //vložím položku do dodávky
            $vals = array(
              'stidelid' => $delId,
              'stiproid' => $pro->proid,
              'stiean' => $ean,
              'stiweight' => $proWeight,
              'stiqty' => 1,
            );
            $stis->insert($vals);
          }
        }
      }

      $this->redirect("edit", $delId);
    }
  }

  protected function createComponentStoItemsEditForm() {
    $form = $this->createAppForm();
    $id = (int)$this->getParameter("id");

    //nactu polozky dodávky
    if ($id > 0) {
      $dels = $this->model->getDeliveriesModel();
      $del = $dels->load($id);
      $form->addContainer('items');
      $rows = dibi::fetchAll("SELECT * FROM stoitems WHERE stidelid=%i", $id, " ORDER BY stiid");
      foreach ($rows as $row) {
        $key = 'item_'.$row->stiid;
        $form["items"]->addContainer($key);
        $form["items"][$key]->addHidden("stiid", $row->stiid);
        $form["items"][$key]->addHidden("stiproid", $row->stiproid);
        $form["items"][$key]->addText("stiqty", "", 3)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte počet položek.')
          ->setDefaultValue($row->stiqty);

        if ($del->delstatus === 1) {
          $form["items"][$key]["stiproid"]->setDisabled(True);
          $form["items"][$key]["stiqty"]->setDisabled(True);
        }
      }

      if ($del->delstatus !== 1) {
        //nova polozka
        $con = $form->addContainer('newitem');
        $con->addText("stiproid", "", 2);
        $con->addText("stiname", "", 40)
          ->setAttribute('class', 'autocomplete');

        //$form['newitem']->addText("stiproean", "", 10);
        $form['newitem']->addText("stiqty", "", 2)
          ->setDefaultValue(1)
          ->addConditionOn($form['newitem']["stiproid"], Nette\Forms\Form::FILLED)
            ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte počet.');
      }
    }
    $form->addSubmit('saveitems', 'Uložit');
    $form->onSuccess[] = array($this, 'stoItemsEditFormSubmitted');

    return $form;
  }

   public function stoItemsEditFormSubmitted (Nette\Application\UI\Form $form) {
     $id = (int)$this->getParameter("id");
     if ($form->isSubmitted()) {
      $formVals = $form->getValues();

      $dels = $this->model->getDeliveriesModel();
      $stis = $this->model->getStoItems();
      $del = $dels->load($id);
      if ($del->delstatus === 1) {
        $this->flashMessage("Tuto dodávku nemůžete editovat. Je již naskladněna.");
        $this->redirect('edit', $id);
      }

      foreach ($formVals['items'] as $item) {
        $stiid = $item["stiid"];
        unset($item["stiid"]);
        unset($item["stiname"]);
        unset($item["stiproid"]);

        $stis->update($stiid, $item);
      }

      if (!empty($formVals['newitem']['stiproid'])) {
        unset($formVals['newitem']["stiname"]);
        $item = $formVals['newitem'];
        $pro = dibi::fetch("SELECT procode, products.procode2, proname, proweight FROM products WHERE proid=%i", (int)$formVals['newitem']['stiproid']);
        $item["stiean"] = $pro->procode2;
        $item["stiweight"] = $pro->proweight;
        $item["stidelid"] = $id;

        if (empty($item->stiproid)) {
          $this->flashMessage("ProId je 0 pro procode:" . $pro->procode);
        } else {
          $stis->insert($item);
        }
      }
      $this->flashMessage("Položky dodávky byly aktualizovány.");
    }
    $this->redirect('edit', $id);
  }


  protected function createComponentSearchForm() {
    $dels = $this->model->getDeliveriesModel();

    $form = $this->createAppForm();

    $form->addGroup("Vyhledávání");
    $form->addText("code", "Kód ", 10)
      ->setDefaultValue($this->sCode);

    $form->addSelect("status", "Stav", $dels->getEnumDelStatus())
      ->setPrompt('');

    if ($this->sStatus !== '') {
      $form["status"]->setDefaultValue($this->sStatus);
    }


    $form->addText("proid", "Id zboží ", 10)
      ->setHtmlId("proid")
      ->setDefaultValue($this->sProId);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;

  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sCode = Null;
        $this->sVenId = Null;
        $this->sStatus = Null;
        $this->sProId = Null;
        $this->sSpecMode = Null;
        //$this->sOrderBy = Null;
        //$this->sOrderByType = Null;
      } else {
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sVenId = $vals["venid"];
        $this->sStatus = $vals["status"];
        $this->sProId = $vals["proid"];
        $this->sSpecMode = $vals["specmode"];
        //$this->sOrderBy = $vals["orderby"];
        //$this->sOrderByType = $vals["orderbytype"];
      }
    }
    $this->redirect("default");
  }

  protected function createComponentSearchStoreForm() {
    $form = $this->createAppForm();

    $form->addGroup("Vyhledávání");
    $form->addText("procode", "Kód ", 10)
      ->setDefaultValue($this->sCode);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('search_ean', 'Vyhledat po EANech');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchStoreFormSubmitted');
    return $form;

  }

  public function searchStoreFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sByEans = FALSE;
        $this->sCode = Null;
        //$this->sOrderBy = Null;
        //$this->sOrderByType = Null;
      } else {
        if ($form["search"]->isSubmittedBy()) {
          $this->sByEans = FALSE;
        } else if ($form["search_ean"]->isSubmittedBy()) {
          $this->sByEans = TRUE;
        }
        $vals = $form->getValues();
        $this->sCode = $vals["procode"];
        //$this->sOrderBy = $vals["orderby"];
        //$this->sOrderByType = $vals["orderbytype"];
      }
    }
    $this->redirect("store");
  }
}

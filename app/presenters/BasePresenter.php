<?php

abstract class BasePresenter extends Nette\Application\UI\Presenter {
  /**
   * @inject
   * @var Nette\Caching\IStorage
   */
  public $storage;

  /**
    * @var \TemplateFilters @inject
    */
  public $myTemplateFilters;

  /** nastaveni serveru */
  public $config = array();

  /** nastaveni z neonu */
  public $neonParameters = array();

  /** aktualni mena */
  public $currency = array();

  /** vsechny aktivni meny */
  public $currencies = array();

  /** id aktualni meny */
  public $curId = 0;

  /** id aktualni meny */
  public $curKey = '';

  /** kody men */
  public $curCodes = array();

  /* @var ModelFactory */
  protected $model;

  /** zda eshop vyuziva dve meny */
  public $secondCurrency = FALSE;

  /** prekladac */
  protected $translator;

  public $lang = 'cs';

  /** @var Nette\Mail\IMailer @inject */
  public $mailer;

  public function injectNeonParametersRepository(Classes\NeonParametersRepository $paramRepository) {
    $this->neonParameters = $paramRepository->getParameters();
  }

  protected function startup() {
    parent::startup();
    $curId = 1;

    if (isset($this->neonParameters["hosts"][$_SERVER["SERVER_NAME"]]["curId"])) {
      $curId =$this->neonParameters["hosts"][$_SERVER["SERVER_NAME"]]["curId"];
    }

    $this->currency = $this->neonParameters["currency"][$curId];
    $this->currencies = $this->neonParameters["currency"];
    $this->curId = $this->currency["id"];
    $this->curKey = $this->currency["key"];

    $this->secondCurrency = isset($this->neonParameters["currency"][2]);
    $this->curCodes[1] = $this->neonParameters["currency"][1]["code"];
    $this->curCodes[2] = "";
    if (isset($this->neonParameters["currency"][2]["code"])) {
      $this->curCodes[2] = $this->neonParameters["currency"][2]["code"];
    }

    //nastavím továrnu na model
    $this->model = new ModelFactory($this->storage);
    $this->model->setCurrency($this->currencies, $this->curId);

    //nactu nastaveni z datatabaze
    //nactu uzivatelske nastaveni do cache
    $config = $this->model->getConfigModel();
    $this->config = $config->getConfig();

    //nastaveni prekladani
    $this->lang = $this->config["SERVER_LANGUAGE"];
    $dic = array();
    If ($this->lang !== 'cs') {
      //nactu slovnik
      //pokud neni cache vytvorim - mela by byt vzdy vytvorena
      $dic = DictionariesModel::getDictionary($this->lang);
    }
    $this->translator = new MyTranslator($this->lang, $dic);
  }

  protected function beforeRender() {
    $this->addEnum('protag1');
    $this->addEnum('protag2');
    $this->addEnum('protag3');


    $this->template->secondCurrency = $this->secondCurrency;
    $this->template->curKey = $this->curKey;

    if (isset($this->neonParameters["labels"])) $this->template->neonLabels = $this->neonParameters["labels"];
    $this->template->curId = $this->curId;
    $this->template->config = $this->config;
  }

  public function printOrder($id, $dest="I", $templateName='Order.latte') {
    $orders = $this->model->getOrdersModel();

    if (is_numeric($id)) {
      $ids = array($id);
    } else {
      $ids = $id;
    }

    $order = array();

    // mPDF
    $mpdf = new Mpdf\Mpdf();
    $mpdf->SetDisplayMode('real');

    foreach ($ids as $ordId) {

      $order = $orders->load($ordId);

      $document = $this->fillTemplatePrintOrder($order->ordid, $templateName);
      $mpdf->AddPage('P');
      $mpdf->WriteHTML($document, 2);

      //poznačím vytištěno
      //$orders->update($ordId, array("ordprinted" => 1));
    }

    $fname = "export_dokladu";

    if ($dest == 'P') {
      $mpdf->SetJS('window.onload = function() { window.print(); }');
      $dest="I";
    }
    if ($dest=="I" || $dest=="F") {
      $name = TEMP_DIR."/".$fname.".pdf";
    } else {
      $name = $fname.".pdf";
    }
    $mpdf->Output($name, $dest);
  }

  private function fillTemplatePrintOrder($ordId, $templateName) {
    $enums = $this->model->getEnumcatsModel();
    $delModes = $this->model->getDeliveryModesModel();
    $orders = $this->model->getOrdersModel();

    $template = $this->getTemplate();

    $template->headers = (object) NULL;
    $template->order = dibi::fetch("SELECT *, ordinvdate + INTERVAL 14 DAY AS datepay FROM orders WHERE ordid=%i", $ordId);
    //nastavim menu objednavky
    $orders->setCurrency($this->currencies, $template->order->ordcurid);
    $delModes->setCurrency($this->currencies, $template->order->ordcurid);

    $ordItems = dibi::fetchAll("SELECT * FROM orditems LEFT JOIN products ON (oriproid=proid) WHERE oriordid=%i", $ordId, "  ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END, orivatid");

    $delRow = Null;
    $vatSum = 0;
    $sum = 0;

    $template->ordItems = $ordItems;
    $template->ordItemDelivery = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $ordId);
    $template->payMode = $delModes->load($template->order->orddelid);
    $template->delMode = $delModes->load($template->payMode->delmasid);
    $template->setFile(WWW_DIR.'/../templates/pdf/'.$templateName);

    $template->headers = (object) NULL;

    return (string)$template;
  }

  protected function getVerifyCode($length = 6) {
    $base = "abcdefghjkmnpqrstwxyz123456789";
    $max = strlen($base)-1;
    $string = "";
    mt_srand((double)microtime()*1000000);
    while (strlen($string) < $length) {
      $string .= $base[mt_rand(0, $max)];
    }
    return $string;
  }

  protected function mailSend($mailTo, $subject, $bodyTemplate, $mailFrom="", $attachments=array()) {
    if (empty($mailTo)) {
      return TRUE;
    }
    $mail = new Nette\Mail\Message();
    if ($mailFrom === "") {
      $mailFrom = $this->config["SERVER_MAIL"];
    }
    $mail->setFrom($mailFrom, $this->config["SERVER_NAMESHORT"]);
    $mail->addTo($mailTo);
    $mail->setSubject($subject." - ".$this->config["SERVER_NAMESHORT"]);
    $mail->setHtmlBody($bodyTemplate);

    //doplnim prilohy pokud jsou
    foreach ($attachments as $fileName) {
      $mail->addAttachment($fileName);
    }

    try {
      // Použití SMTP maileru pro všechna prostředí
      $mailer = $this->mailer;
      $mailer->send($mail);
    } catch (Exception $e) {
      return false;
    }
    return true;
  }

  protected function mailMail($mail) {
    // Použití SMTP maileru pro všechna prostředí
    $mailer = $this->mailer;
    $mailer->send($mail);
  }

  public function injectMailer(Nette\Mail\IMailer $mailer) {
    $this->mailer = $mailer;
  }

    protected function smsSend($gsm, $bodyTemplate) {
    if (empty($gsm)) {
      return TRUE;
    }

    $bodyTemplate->setTranslator($this->translator);
    $bodyTemplate->lang = $this->lang;
    $text = trim((string)$bodyTemplate);
    $smsConfig = $this->neonParameters['sms'];
    if (!empty($smsConfig["apiKey"])) {

      try {
        $sms = new SmsManagerApi($smsConfig["apiKey"]);

        $ret = $sms->sendSms($gsm, $text);
        if($ret === FALSE) {
          $this->flashMessage("Na číslo $gsm se nepodařilo SMS odeslat. Chyba: " . $sms->lastErrorDesc,"err");
        } else {
          $this->flashMessage("SMS na číslo $gsm byla odeslána");
        }
      } catch (Exception $e) {
        \Tracy\Debugger::log($e->getMessage());
        return false;
      }
    } else {
      return TRUE;
    }
  }

  /**
   * Formats view template file names.
   * @return array
   */
  public function formatTemplateFiles() {
    $root = WWW_DIR . '/../templates';
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';

    return array(
      "$root/$module/$presenter.$this->view.latte",
    );
  }

  public function getPriceVat($price, $vatid) {
    $vatLevel = (int)$this->config["VATTYPE_".$vatid];
    return($price * (1+($vatLevel / 100)));
  }

  /**
   * Formats layout template file names.
   * @return array
   */
  public function formatLayoutTemplateFiles() {

    $root = WWW_DIR . '/../templates';
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';
    $layout = $this->layout ? $this->layout : 'layout';
    return array(
      "$root/$module/@$layout.latte",
      "$root/@$layout.latte",
    );
  }

  public function formatNumberMySQL($value) {
    $value = str_replace(',', '.', $value);
    $value = str_replace(' ', '', $value);
    return $value;
  }

  /**
   * formátuje datum do SQL formátu
   *
   * @param strund $date datum ve formátu dd.mm.rrr
   * @return string|strund
   */
  public function formatDateMySQL($date) {
    $d = "";
    $m = "";
    $y = "";

    if (!empty($date) && strpos($date, '.') > 0) {
      list($d, $m, $y) = explode('.', $date);
    } else {
      return($date);
    }
    return(trim($y).'-'.trim($m).'-'.trim($d));
  }

  /**
   * formátuje datum do dd.mm.rrr
   *
   * @param string $date datum SQL formátu
   * @return string
   */
  public function formatDate($date) {
    $d = "";
    $m = "";
    $y = "";
    $arr = explode(' ', $date);
    if (isset($arr[0])) {
      $date = $arr[0];
    }
    if (!empty($date) && strpos($date, '-') > 0) {
      list($y, $m, $d) = explode('-', $date);
    } else {
      return($date);
    }
    return($d.'.'.$m.'.'.$y);
  }

  public function createTemplate($class = NULL) {
    $template = parent::createTemplate($class);

    $template->addFilter(NULL, [
      $this->myTemplateFilters,
      'loader'
    ]);

    $template->config = $this->config;
    $template->setTranslator($this->translator);
    $template->lang = $this->lang;
    return $template;
  }

  /**
  * číselník na meny
  *
  */
  public function getEnumCurr() {
    $items = array();
    if ($this->secondCurrency) {
      $items = array(
        1 => $this->curCodes[1],
        2 => $this->curCodes[2]
      );
    }
    return $items;
  }

  /**
   * nastaví enum do šablony pokud není
   *
   * @param $name
   * @param bool $onlyActive
   */
  public function addEnum($name, $onlyActive = TRUE) {
    $enumName = 'enum_'.$name;
    $this->template->$enumName = $this->model->getEnum($name, $onlyActive);
  }


  /**
  * sazby DPH
  *
  */
  public function getVatLevels() {
    $items = array();
    for ($i = 0; $i <= 3; $i++) {
      if (isset($this->config["VATTYPE_$i"])) {
        $items[$i] = (int)$this->config["VATTYPE_$i"];
      }
    }
    return $items;
  }

  /**
  * číselník cenove kategorie
  *
  */
  public function getEnumPrcCat() {
    $usrs = $this->model->getUsersModel();
    $labels = array();
    if (isset($this->neonParameters["labels"])) $labels = $this->neonParameters["labels"];
    return $usrs->getEnumUsrPrcCat($labels);
  }

  function curl_get_contents($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
  }

  protected function setBootstrapForm($form) {
    $renderer = $form->getRenderer();
    $renderer->wrappers['controls']['container'] = NULL;
    $renderer->wrappers['pair']['container'] = 'div class=form-group';
    $renderer->wrappers['pair']['.error'] = 'has-error';
    $renderer->wrappers['control']['container'] = 'div class=col-sm-10';
    $renderer->wrappers['label']['container'] = 'div class="col-sm-2 control-label"';
    $renderer->wrappers['control']['description'] = 'span class=help-block';
    $renderer->wrappers['control']['errorcontainer'] = 'span class=help-block';
    $renderer->wrappers['error']['container'] = '';
    $renderer->wrappers['error']['item'] = 'div class="alert alert-danger" role=alert';

    $form->getElementPrototype()->class('form-horizontal');
    $form->onRender[] = function ($form) {
      foreach ($form->getControls() as $control) {
        $type = $control->getOption('type');
        if ($type === 'button') {
          $control->getControlPrototype()->addClass(empty($usedPrimary) ? 'btn btn-primary' : 'btn btn-default');
          $usedPrimary = TRUE;
        } elseif (in_array($type, ['text', 'textarea', 'select'], TRUE)) {
          $control->getControlPrototype()->addClass('form-control');
        } elseif (in_array($type, ['checkbox', 'radio'], TRUE)) {
          $control->getSeparatorPrototype()->setName('div')->addClass($type);
        }
      }
    };
    return $form;
  }

  /**
   * projde všechny adr. v $path (kromě "src") a vymaže $fileName
   *
   * @param $path
   * @param $fileName
   * @param bool $fullDelete pokud TRUE vymaže i src
   */
  public function deletePic($path, $fileName, $fullDelete=FALSE) {
    $dir = new DirectoryIterator($path);
    foreach ($dir as $fileinfo) {
      if ($fileinfo->isDir() && !$fileinfo->isDot()) {
        $dir = $fileinfo->getFilename();

        if ($dir === 'src' && $fullDelete===FALSE) {
          continue;
        }

        if ($dir === 'big' && $fullDelete===FALSE) {
          continue;
        }

        @unlink($path.$dir.'/'.$fileName);
      }
    }
  }

  public function getTagValues($s, $separator='|') {
    if (empty($s)) {
      return array();
    }
    $arr = explode($separator, trim($s, $separator));
    return array_flip($arr);
  }

  public function checkEan13($code) {
    $len = strlen($code);
    if ($len != 13) return "";
    if (!is_numeric($code)) return "Není číslo";
    $weightflag = true;
    $sum = 0;
    $code12 = substr($code, 0, 12);
    $chSum =  substr($code, -1);
    for ($i = strlen($code12) - 1; $i >= 0; $i--) {
      $sum += (int)$code12[$i] * ($weightflag?3:1);
      $weightflag = !$weightflag;
    }
    $chSumMy = (10 - ($sum % 10)) % 10;
    if ($chSumMy != $chSum) return "Špatná komtrolní(poslední) číslice. Je $chSum, má být $chSumMy .";
    return "";
  }

  protected function passwordHash($password) {
    $hash = Nette\Security\Passwords::hash($password); // Zahashuje heslo
    return $hash;
  }

  protected function passwordVerify($password, $hash) {
    return Nette\Security\Passwords::verify($password, $hash);
  }

  public function formatNumber($value) {
    $value = str_replace('.', ',', $value);
    return $value;
  }

  /**
   * nastaví nový stav objednávky
   *
   * @param $formVals
   * @param string $logNote
   * @return bool
   * @throws \Dibi\Exception
   */
  protected function  changeOrderStatus($formVals, $admid, $logNote="", $flashOn=TRUE) {
    $id = $formVals["ordid"];
    unset($formVals["ordid"]);
    $orders = $this->model->getOrdersModel();
    $order = $orders->load($id);
    $orders->setCurrency($this->currencies, $order->ordcurid);
    $formOrdStatus = (int)$formVals["ordstatus"];
    //ujistim jestli se meni status
    if ($order && $order->ordstatus === $formOrdStatus) {
      return TRUE;
    }
    //pokud storno vymazu cislo fa a datum vystaveni
    if ($formOrdStatus === 5) {
      $formVals["ordinvcode"] = Null;
      $formVals["ordinvdate"] = Null;
      if ($flashOn) $this->flashMessage('Byla vystornována fa, pokud existovala.');
    } else if ($formOrdStatus === 6) {
      //nastavim zaplaceno
      $formVals["ordpaystatus"] = 1;
    } else if ($formOrdStatus === 3) {
      //nastaveno odeslano
      $formVals["ordadmid"] = $this->adminData->admid;
    }

    $orders->update($id, $formVals);
    $orders->logStatus($id, $formOrdStatus, $admid, $logNote);
    if ($flashOn) $this->flashMessage('Změna stavu provedena v pořádku [ID:'.$id.']');
    if ($formOrdStatus === 3 || $formOrdStatus === 4 || $formOrdStatus === 5) {
      //prepocitam statistiku prodejnosti
      $products = $this->model->getProductsModel();
      $products->recalcSaleStat() ;
      if ($flashOn) $this->flashMessage('Byla přepočtena statistika prodejnosti [ID:'.$id.']');
    }

    //mailuju zakaznikovi zmenu stavu jen některé stavy
    if ($formOrdStatus === 1 || $formOrdStatus === 3 || $formOrdStatus === 5 || $formOrdStatus === 2 || $formOrdStatus === 6 || $formOrdStatus === 8 || $formOrdStatus === 9) {
      $mailTemplate = $this->createTemplate();
      $orderRow = $orders->load($id);

      //zjistím jestli v objednávce jsou jen vejce
      $mailTemplate->onlyEgg = $this->isOnlyEggOrder($id);

      $mailTemplate->orderRow = $orderRow;
      $delModes = $this->model->getDeliveryModesModel();
      $delModes->setCurrency($this->currencies, $orderRow->ordcurid);
      $mailTemplate->payMode = $delModes->load($orderRow->orddelid);
      $mailTemplate->delMode = $delModes->load($mailTemplate->payMode->delmasid);
      if (!empty($orderRow->ordparcode)) {
        //zjistim URL
        $url = $mailTemplate->delMode->delurlparcel;
        $url = str_replace('#CODE#', $orderRow->ordparcode, $url);
        $mailTemplate->parcelURL = $url;
      }
      $mailTemplate->enum_ordStatus = $orders->getEnumOrdStatus();

      //nastavím šablonu
      //pokud je pro status speciální šablona
      $templateName = WWW_DIR.'/../templates/Mails/mailOrderChanged.latte';
      if (file_exists(WWW_DIR.'/../templates/Mails/mailOrderChanged_' . $formOrdStatus . '.latte')) {
        $templateName = WWW_DIR.'/../templates/Mails/mailOrderChanged_' . $formOrdStatus . '.latte';
      }
      $mailTemplate->setFile($templateName);

      if ($formOrdStatus === 3) {
        //nastaveno odeslano - poslu SMS
        //$smsTemplate = $this->createTemplate();
        //$smsTemplate->delMode = $mailTemplate->delMode;
        //$smsTemplate->orderRow = $orderRow;
        //$smsTemplate->setFile(WWW_DIR.'/../templates/Mails/smsOrderSend.latte');
        //try {
          //$this->smsSend($orderRow->ordtel, $smsTemplate);
        //} catch (InvalidStateException $e) {
        //  $this->flashMessageErr("Nepodařilo se odeslat informační SMS o odeslání objednávky (".$e->getMessage().")".' [ID:'.$id.']');
        //}
      }

      if ($formOrdStatus === 1 || $formOrdStatus === 3 || $formOrdStatus === 5 || $formOrdStatus === 2 || $formOrdStatus === 6 || $formOrdStatus === 8) {
        //mailuju zakaznikovi
        try {
          $this->mailSend($orderRow->ordmail, $this->translator->translate("Změna stavu objednávky č.")." ".$orderRow->ordcode, $mailTemplate);
        } catch (Nette\InvalidStateException $e) {
          if ($flashOn) $this->flashMessageErr("Nepodařilo se odeslat informační email o změně stavu objednávky (".$e->getMessage().")".' [ID:'.$id.']');
        }
      }

    }
    return TRUE;
  }


  protected function flashMessageErr($message) {
    $this->flashMessage($message, 'danger');
  }

  protected function flashMessageInfo($message) {
    $this->flashMessage($message, 'info');
  }

  protected function flashMessageOk($message) {
    $this->flashMessage($message, 'success');
  }

  public function actionStartFioPayment($id, $key) {
    $ords = $this->model->getOrdersModel();
    $order = $ords->load($id);
    $key2 = substr(md5($order->ordid . $order->ordcode), 0, 6);
    if ($key !== $key2) {
      echo "Špatné volání stránky";
      $this->terminate();
    }
    $amount = (double)$order->ordpricevat;
    $account = (double)$order->ordcode;
    $ip = $_SERVER['REMOTE_ADDR'];
    //$ip = '***********';
    $description = 'č. obj.:'.$order->ordcode;

    $fioEComConfig = $this->neonParameters["fioECom"];

    $fioECom = new fioECom($fioEComConfig["Id"], $fioEComConfig["CertPass"]);
    $ret = $fioECom->startTrans($fioEComConfig["TransType"], $amount, $ip, $description, $account);
    if ($fioECom->isOk() && !empty($ret["url"])) {
      $ords->update($id, array("ordpaysesid"=>$ret["transId"]));
      $this->redirectUrl($ret["url"]);
    } else {
      $this->flashMessage(implode(" | ", $fioECom->errors), 'danger');
      $key = $order->ordid.substr(md5($order->ordid . $order->orddatec->getTimestamp()), 0, 8);
      $this->redirect("Order:status", $key);
    }
    $this->terminate();
  }

  /**
   * převod UTF-8 na WINDOWS-1250
   * @param $string
   * @return false|string
   */
  protected function utf8ToWin1250($string) {
    return iconv("utf-8", "WINDOWS-1250", $string);
  }

  /**
   * převod WINDOWS-1250 na UTF-8
   * @param $string
   * @return false|string
   */
  protected function win1250ToUtf8($string) {
    return iconv("WINDOWS-1250", "utf-8", $string);
  }

  /***
 * vrací klíč na odkaz na status objednávky
 *
 * @param $order
 * @return string
 */
  public function getOrderStatusKey($order) {
    return $order->ordid.substr(md5($order->ordid . $order->orddatec->getTimestamp()), 0, 8);
  }

  /**
   * aktualizuje platební status obj del gopayresponse
   *
   * @param array $goPayResponse
   * @throws \Dibi\Exception
   */

  protected function updateOrderGoPayStatus($goPayResponse, $redirect=FALSE) {
    $isOk = FALSE;
    if ($goPayResponse !== FALSE && !empty($goPayResponse["order_number"])) {
      //načtu příslušnou objednávku
      $ords = $this->model->getOrdersModel();
      $ord = $ords->load($goPayResponse["order_number"], "code");
      if ($ord) {
        //podle aktuálního stavu objednávky nastavím stav úhrady
        if ($goPayResponse["state"] === "PAID" && (int)$ord->ordpaystatus === 0) {
          $vals = array(
            'ordstatus' => 6,
            'ordpaystatus' => 1,
            'orddatepayed' => new \DateTime()
          );
          $ret = $ords->update($ord->ordid, $vals);
          $ords->logStatus($ord->ordid, $vals["ordstatus"], 1, "GOPAY platba");
          $this->flashMessageOk("Platba byla přijata a objednávka označena jako uhrazena.");
        } else if ($goPayResponse["state"] === GoPayApi::GOPAY_STATUS_REFUNDED && (int)$ord->ordpaystatus === 1) {
          $ords->cancelPayment($ord->ordid, 1);
        } else if ($goPayResponse["state"] === GoPayApi::GOPAY_STATUS_PARTIALLY_REFUNDED && (int)$ord->ordpaystatus === 1) {

        } else {
          if ($ord->ordstatus === 0) {
            //pokud je obj. ve stavu 0 a platba zatím neproběhla nastavím status "Čekáme na platbu"
            $vals = array(
              'ordid' => $ord->ordid,
              'ordstatus'=> 2,
            );
            $this->changeOrderStatus($vals, 1, "GOPAY platba - zatím neproběhla", FALSE);

          }
          $this->flashMessageOk("Vaše platba není zatím uhrazena a je ve stavu: " . GoPayApi::getPayStatusDesc($goPayResponse["state"]) . ".");
        }
        if ($redirect) {
          $this->redirect("Order:status", $this->getOrderStatusKey($ord));
        }
      }
    }
    if ($isOk === FALSE) {
      $this->flashMessage("Platbu se nepodařilo identifikovat v našem systému. To by se němělo stávat. Prosím kontraktujte nás.", "danger");
    }
    if ($redirect) {
      $this->redirect("GoPay:error");
    }
  }

  public function isEgg($product) {
    if ((int)$this->config["EGG_SWITCH"] === 1) {
      return (strpos($product->procode, 'VEJCE') === 0);
    }
    return FALSE;
  }

  public function isOnlyEggOrder($ordId) {

    $rows = dibi::fetchAll("SELECT oriprocode AS procode FROM orditems WHERE oritypid=0 AND oriordid=%i", $ordId);
    $eggCnt = 0;
    $cnt = (count($rows));
    foreach ($rows as $row) {
      if ($this->isEgg($row)) {
        $eggCnt++;
      }
    }

    return $cnt === $eggCnt;
  }

}

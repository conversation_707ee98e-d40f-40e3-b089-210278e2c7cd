<?php
namespace FrontModule;
use dibi;
use Heureka\ShopCertification\Exception;
use Nette;
use <PERSON>\Debugger;

final class OrderPresenter extends BasePresenter {

  public function actionPrint($id, $key) {
    if ($id > 0) {
      $ords = $this->model->getOrdersModel();
      $order = $ords->load($id);
      $key2 = substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8);
      if ($key !== $key2) {
        $this->flashMessage("Neplatné volání tisku dokladu.", 'critical');
        $this->redirect("Basket:default");
      }

      $this->printOrder($id);
    }
  }




  public function renderStatus($id) {
    $ords = $this->model->getOrdersModel();

    $ordId = substr($id, 0,-8);
    $key = substr($id, -8);

    $order = FALSE;

    if (!empty($ordId) && strlen($key) === 8) {
      $order = $ords->load($ordId);
      if ($order) {
        //kontrola klíče
        $key2 = substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8);
        if ($key !== $key2) {
          $this->flashMessage("Neplatné volání stránky.", 'critical');
          $this->redirect("Basket:default");
        }
      }
    }

    if ($order === FALSE) {
      throw new Nette\Application\BadRequestException('Záznam nenalezen');
    }

    $order->items = dibi::fetchAll("SELECT orditems.*, catpath, catpathids, procode, proname, proaccess, manname FROM orditems
INNER JOIN products ON (oriproid=proid)
LEFT JOIN catplaces ON (oriproid=capproid)
LEFT JOIN catalogs ON (catid=capcatid)
LEFT JOIN manufacturers ON (manid=promanid)
WHERE oriordid=%i GROUP BY oriid", $order->ordid);

    $priceSum = 0;
    $priceSumVat = 0;
    $maxProAccess = NULL;

    foreach ($order->items as $key=>$row) {
      $vatType = (string)$this->config["PRICEVAT"];
      if ($vatType == 'inclvat') {
        $priceSumVat += $row->oriprice * $row->oriqty;
        $vatLevel = (int)$this->config["VATTYPE_".$row->orivatid];
        $price = round($row->oriprice / (1+($vatLevel / 100)) * $row->oriqty, 2);
        $order->items[$key]->oripricenovat = $price;
        $priceSum += $price;
      } else {
        $price = $row->oriprice * $row->oriqty;
        $priceSum += $price;
        $order->items[$key]->oripricenovat = $price;
        $priceSumVat += round($this->getPriceVat($row->oriprice, $row->orivatid) * $row->oriqty, 2);
      }

      if ($maxProAccess === NULL) {
        $maxProAccess = $row->proaccess;
      } else {
        $maxProAccess = $row->proaccess > $maxProAccess ? $row->proaccess : $maxProAccess;
      }
    }

    $order->ordpricenovat = $priceSum;
    $order->ordpriceinclvat = $priceSumVat;
    $order->orddelprice = dibi::fetchSingle("SELECT oriprice FROM orditems WHERE oritypid=1 AND oriordid=%i", $order->ordid);

    $this->template->payment = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    $this->template->delivery = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $this->template->payment->delmasid);
    if ($this->template->payment->delcode == 'cetelem') {
      $cetelem  = new Cetelem($this->config["CETELEM"]["kodProdejce"]);
      $cetelem->cenaZbozi = $order->ordpricevat;
      $this->template->cetelem = $cetelem;
    }
    $this->template->order = $order;
    $this->template->blockOrderAccepted = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='objednavka_odeslana' AND pagstatus=0");

    //pokud je platba prevodem, nactu QR kod a info k platbe
    if ($this->template->payment->delcode == 'paybefore') {
      $accNo = $this->config["SERVER_ACCNO"];
      $qrPlatba = new \QRPlatba($accNo, $order->ordpricevat);
      $qrPlatba->setVariableSym($order->ordcode);
      $qrPlatba->setMessage($order->ordiname." ".$order->ordilname.", ".$order->ordicity);
      if ($this->curId == 2) $qrPlatba->setCurrency("EUR");
      $this->template->qrPlatba = urlencode((string)$qrPlatba);
    }

    if ($order->ordpublished != 1) {

      //zařazení do maillistu
      $ecomail = new \EcomailApi();
      $ecomail->logNewOrder(2, $order);

      if (IS_PRODUCTION && (int)$order->ordheurekagdpr === 1 && $this->currency["key"] === 'CZK') {
        //zavolam heureku, overeno zakazniky
        $heurekaConfig = $this->neonParameters["heureka"];
        if (count($order->items) > 0 && !empty($heurekaConfig["IdOverenoZakazniky"])) {
          try {
            $overeno = new \Heureka\ShopCertification($heurekaConfig["IdOverenoZakazniky"]);
            $overeno->setOrderId($order->ordid);
            $overeno->setEmail($order->ordmail);
            foreach ($order->items as $row) {
              $overeno->addProductItemId($row->oriproid);
            }
            $overeno->logOrder();
            $this->template->heurekaConfig = $heurekaConfig;
          } catch (Exception $e) {
            // handle errors
            $mailText = "ORDID: $order->ordid, msg:" . $e->getMessage();
            $mail = new Nette\Mail\Message();
            $mail->setFrom($this->config["SERVER_MAIL"]);
            $mail->addTo('<EMAIL>');
            $mail->setSubject('nová objednávka - heureka ERR');
            $mail->setHtmlBody($mailText);
            $mail->send();
          }
        }
      }

      //zbozi pokrocile konverze
      $zboziConfig = $this->neonParameters["zbozicz"];
      if (count($order->items) > 0 && !empty($zboziConfig["IdMereniKonverzi"]) && !empty($zboziConfig["secretKey"]) && !empty($order->ordmail)) {
        include LIBS_DIR . "/zbozi/ZboziKonverze.php";
        $pay = $this->template->payMode;
        $del = $this->template->delMode;

        //tedmín dodání
        if ($maxProAccess === NULL) {
          $maxProAccess = 100;
        }
        $date = dibi::fetchSingle("SELECT Now() + INTERVAL $maxProAccess DAY");
        $dateFormated = $date->format('Y-m-d');

        try {
          // inicializace
          $zbozi = new \ZboziKonverze($zboziConfig["IdMereniKonverzi"], $zboziConfig["secretKey"]);

          // nastavení informací o objednávce
          $zbozi->setOrder(array(
            "orderId" => $order->ordcode,
            "email" => $order->ordmail,
            "totalPrice" => $order->ordpricevat,
            "paymentType" => $pay->delname,
            "deliveryType" => $del->delname,
            "deliveryDate" => $dateFormated,
            "deliveryPrice" => $order->orddelprice,
            "otherCosts" => 0,
          ));

          foreach ($items as $key => $row) {
            // přidání zakoupené položky
            $zbozi->addCartItem(array(
              "itemId" => $row->oriproid,
              "productName" => $row->oriname,
              "quantity" => $row->oriqty,
              "unitPrice" => $row->oriprice
            ));
          }

          // odeslání
          $zbozi->send();
          Debugger::log("zbozi:konverze:send:" . $order->ordcode);
        } catch (\ZboziKonverzeException $e) {
          Debugger::log("Error: " . $e->getMessage());
        }
      }
    }

    //jestli nebyla ještě objednávka publikována do statistik nastavím
    if ($order->ordpublished != 1) {
      $ords->update($order->ordid, array('ordpublished' => 1,));
    }

    $this->addEnum('ordstatus');
  }

  protected function createComponentFeedbackForm() {
    $form = $this->createAppForm();

    $form->addTextArea('feedback', "Text zprávy", 40, 4)
      ->setRequired("Prosím vyplňte %label");

    $form->addSubmit('submit', 'Odeslat zprávu');
    $form->onSuccess[] = array($this, 'feedbackFormSubmitted');

    return $form;
  }

  public function feedbackFormSubmitted(Nette\Application\UI\Form $form) {
    $ords = $this->model->getOrdersModel();
    $formVals = $form->getValues();
    $k = $this->getParameter('k');
    $ordId = substr($k, 0,-8);
    $key = substr($k, -8);

    if (!empty($ordId) && strlen($key) === 8) {
      $ord = $ords->load($ordId);
      if ($ord) {
        //kontrola klíče
        $key2 = substr(md5($ord->ordid . $ord->orddatec->getTimestamp()), 0, 8);
        if ($key === $key2) {
          $body = 'Feedback k objednávce č. '.$ord->ordcode.':<br />' . nl2br($formVals["feedback"]);
          try {
            $this->mailSend($this->config["SERVER_MAIL"], 'Feedback k objednávce č. '.$ord->ordcode, $body, $this->config["SERVER_MAIL"], array(), (!empty($ord->ordmail) ? $ord->ordmail : NULL));
            $this->flashMessage("Váš vzkaz byl odeslán. Děkujeme!");
          } catch (Nette\InvalidStateException $e) {
            $this->flashMessage("Vzkaz se nepodařilo odeslat.", 'critical');
          }
        }
      }
    }
    $this->redirect('this');
  }

}

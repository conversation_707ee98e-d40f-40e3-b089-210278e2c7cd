<?php
namespace FrontModule;
use dibi;
use Nette;

final class ProductPresenter extends BasePresenter {

  public function renderDetailOld($id) {
    $products = $this->model->getProductsModel();
    $products->setPrcCat($this->userData->usrprccat);
    $products->setCurrency($this->currencies, $this->curId);
    //aktualni polozka katalogu
    $product = $products->load($id);


    if ($product === false) {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    }

    //kontrola platnosti URL
    $urlkey = (!empty($product->prokey) ? $product->prokey : Nette\Utils\Strings::webalize($product->proname));
    $this->redirect(301, 'Product:detail', array('id'=>$id, 'key'=>$urlkey));
  }

  public function renderDetail($id, $key) {
    $products = $this->model->getProductsModel();
    $products->setPrcCat($this->userData->usrprccat);
    $products->setCurrency($this->currencies, $this->curId);
    //aktualni polozka katalogu
    $product = $products->load($id);

    if ($product === false) {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    }

    //kontrola platnosti URL
    $urlkey = (!empty($product->prokey) ? $product->prokey : Nette\Utils\Strings::webalize($product->proname));

    //pokud se zmenil klic presmeruju na novy
    if ($key !== $urlkey) {
      $this->redirect(301, 'Product:detail', array('id' => $id, 'key' => $urlkey));
    }

    if ($product->promasid > 0) {
      $this->basketNamespace->visited[$product->promasid] = $product->promasid;
    } else {
      $this->basketNamespace->visited[$product->proid] = $product->proid;
    }

    $productMasterData = $product;
    if ($product->promasid > 0) {
      $productMas = $products->load($product->promasid);
      if ($productMas) {
        $productMasterData = $productMas;
        //pokud je to podrizena polozka a master položka není skladem nebo není dostupná, skladovou dostupnost a status prevezmu z master polozky
        if ($productMasterData->prostatus > 0 ||  $productMasterData->proaccess > 0) {
          $product->proaccess = $productMasterData->proaccess;
          $product->prostatus = $productMasterData->prostatus;
        }
      } else {
        unset($productMas);
      }
    }

    $this->template->product = $product;

    $this->template->productMasterData = $productMasterData;
    $catalog = $this->model->getCatalogsModel();
    $curCatId = dibi::fetchSingle("SELECT capcatid FROM catplaces WHERE capproid=%i", $productMasterData->proid);
    $catalogData = $catalog->load($curCatId);

    $this->template->catalog = $catalogData;

    if ($catalogData) {
      $idPath = explode('|', trim($catalogData->catpathids, '|'));
      $rootCatId = (int)$idPath[0];
      $menuCatalogL = array();
      $catalogPath = array();
      foreach ($idPath as $catid) {
        $menuCatalogL[$catid] = dibi::fetchAll("SELECT * from catalogs WHERE catmasid=$catid ORDER BY catorder");
        $catalogPath[$catid] = dibi::fetch("SELECT * from catalogs WHERE catid=$catid");
        if ($catalogData->catid == $catid) break;
      }
      $this->template->rootCatId = $rootCatId;
      $this->template->menuCatalogL = $menuCatalogL;
      $this->template->catalogPath = $catalogPath;
      $this->template->thisCatId = $catalogData->catid;
    }

    //naplnim vyrobce
    $this->template->manufacturer = dibi::fetch("SELECT manid, manname FROM manufacturers WHERE manid=%i", $productMasterData->promanid);

    //naplnim access
    $this->template->enum_proaccess = $products->getEnumProAccess();

    //naplnim puvod zbozi
    $this->template->enum_proorigin = $products->getEnumProOrigin();

    //naplnim parametry zbozi
    $this->template->proParams = dibi::fetchAll("SELECT prpname, prpvalue FROM proparams WHERE prpproid=%i", $id, " ORDER BY prpname");

    $this->template->subItems = array();

    //naplnim podrizene polozky
    if ($product->proismaster || isset($productMas)) {
      $pid = isset($productMas) ? $productMas->proid : $product->proid;
      $this->template->subItems = dibi::query($products->getSqlList("prostatus=0 AND promasid=".$pid, "proorder, proname"))->fetchAssoc("proid");
    }

    //naplnim prilohy
    $this->template->files = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $productMasterData->proid);

    //naplnim obrazky
    $images = array();
    for($i=1;$i<=10;$i++){
      $fName = ($productMasterData->propicname != "" ? $productMasterData->propicname : $productMasterData->procode)."_$i".'.jpg';
      $picPath = WWW_DIR."/pic/product/src/";
      if (file_exists($picPath.$fName)) {
        $images[$i]["name"] = $fName;
      }
    }
    $this->template->images = $images;

    //naplnim komentare

    $this->template->comments = dibi::fetchAll("SELECT * FROM comments WHERE cmtproid=%i", $product->proid, " AND cmtreid=0 AND cmtstatus=0 ORDER BY cmtdatec DESC");
    //TOP hodnocení, vypíšu nahoře
    $this->template->commentsTop = dibi::fetchAll("SELECT * FROM comments WHERE cmtproid=%i", $product->proid, " AND cmtreid=0 AND cmtstatus=0 ORDER BY cmtrate DESC, rand() LIMIT 2");


    //podobne zbozi
    if (!empty($productMasterData->prooptionskeywords)) {
      $arr = explode(',', $productMasterData->prooptionskeywords);
      $sql = "";
      foreach ($arr as $value) {
        $value = trim($value);
        if ($value != "") $sql .= "proname LIKE '$value%' OR ";
      }

      if (!empty($sql)) {
        $sql = " proid !=".$productMasterData->proid." AND promasid=0 AND prostatus=0 AND (".substr($sql, 0, -4).") ORDER BY proorder, proname LIMIT 15";
        $this->template->proOptions = dibi::fetchAll($products->getSqlList($sql, ""));
      }
    }

    //prislusenstvi
    $this->template->proAccess = dibi::fetchAll("
      SELECT proid, proname, prodescs, propicname, proismaster, IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) AS proprice,
      ".($this->curId == 1 ? "prodelfree" : "0 AS prodelfree").", protypid, protypid2, protypid3, protypid4, protypid5, proaccess,
      proprice".$this->curId."a AS propricea, proprice".$this->curId."b AS propriceb, proprice".$this->curId."c AS propricec, proprice".$this->curId."d AS propriced,
      proprice".$this->curId."com AS propricecom
      FROM proaccess
      INNER JOIN products ON (proid=acsacsproid)
      WHERE acsproid=%i", $productMasterData->proid
    );

    //zakazu indexovani pokud komentare
    $reid = $this->getParameter("reid");
    if (!empty($reid)) {
      $this->template->pageRobots = "noindex,follow";
    }

    //nactu darky pokud nejaké jsou
    $this->template->proGiftsList = array();
    if (!empty($productMasterData->progifts)) {
      $arr = explode(',', trim($productMasterData->progifts, ','));
      $this->template->proGiftsList = dibi::fetchAll($products->getSql()." WHERE procode IN (%s)", $arr, " ORDER BY proprice DESC");
    }
  }

  public function renderCompare() {
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    $products = array();
    $ids = array();
    foreach ($this->basketNamespace->compare as $key => $id) {
      //produkt
      $products[$id] = $pros->load($id);
      //parametry
      $products[$id]["params"] = dibi::query("SELECT prpname, prpvalue FROM proparams WHERE prpproid=%i", $id)->fetchAssoc("prpname");
      $ids[$id] = $id;
    }
    $this->template->paramsAll = dibi::query("SELECT prpname FROM proparams WHERE prpproid IN %in", $ids)->fetchAssoc('prpname');
    $this->template->products = $products;
  }

  public function renderBetterPrice($id) {
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);
    $product = $pros->load($id);
    if ($product === false) {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    }
    $this->template->product = $product;
  }

  /**
  * prida polozku do porovnani
  *
  * @param integer $proid id polozky
  */
  public function actionCompareAdd($proid) {
    $this->basketNamespace->compare[$proid] = $proid;
    $this->redirect("compare");
  }

  public function actionBookmarkAdd($proid) {
    if ($this->userData->usrid > 0) {
      $boks = $this->model->getBookmarksModel();
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(bokid) FROM bookmarks WHERE bokproid=%i", $proid, " AND bokusrid=%i", $this->userData->usrid);
      if ($cnt === 0) {
        $boks->insert(array(
          'bokproid' => $proid,
          'bokusrid' => $this->userData->usrid,
        ));
      }
    } else {
      $this->flashMessage("Záložky mohou vytářet pouze přihlášení uživatelé.");
      $this->redirect("User:login");
    }
    $this->redirect("User:bookmarks");
  }

  public function actionBookmarkDelete($proid) {
    if ($this->userData->usrid > 0) {
      dibi::query("DELETE FROM bookmarks WHERE bokproid=%i", $proid, " AND bokusrid=%i", $this->userData->usrid);
    } else {
      $this->flashMessage("Záložky mohou mazat pouze přihlášení uživatelé.");
      $this->redirect("User:login");
    }
    $this->redirect("User:bookmarks");
  }

  /**
  * odebere polozku z porovnani
  *
  * @param integer $proid id polozky
  */
  public function actionCompareDelete($proid) {
    unset($this->basketNamespace->compare[$proid]);
    $this->redirect("compare");
  }

  protected function createComponentWatchDogPrice() {
  	$form = $this->createAppForm();
    $form->addText("dogmail", "Email")
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');
    $form->addHidden("dogprice", 1);
    $form->addHidden("dogstore", 0);
    $form->addSubmit("send", "Hlídat cenu");
    $form->onSuccess[] = array($this, 'watchDogSubmitted');
  	return $form;
  }

  protected function createComponentWatchDogStore() {
    $form = $this->createAppForm();
    $form->addText("dogmail", "Email")
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');
    $form->addHidden("dogprice", 0);
    $form->addHidden("dogstore", 1);
    $form->addSubmit("send", "Hlídat naskladnění");
    $form->onSuccess[] = array($this, 'watchDogSubmitted');
    return $form;
  }

  public function watchDogSubmitted($form) {
    if ($form->isSubmitted()) {
      $proid = $this->getParameter("id");
      $formVals = $form->getValues();
      //zjistim jestli uz nema zaznam
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(*) FROM watchdogs WHERE dogprice=%i", (int)$formVals->dogprice, " AND dogstore=%i", (int)$formVals->dogstore, " AND dogmail=%s", $formVals->dogmail, " AND dogproid=%i", $proid);
      $dogid = 0;
      if ($cnt == 0) {
        $dogs = $this->model->getWatchdogsModel();
        $formVals->dogproid = $proid;
        $dogid = $dogs->insert($formVals);
      }
      $this->flashMessage("Hlídání bylo nastaveno.");
      //mailuji info
      $template = $this->createTemplate();
      $pros = $this->model->getProductsModel();
      //sledovany produkt
      $template->product = $pros->load($proid);
      $template->watchPrice = (int)$formVals->dogprice;
      $template->watchStore = (int)$formVals->dogstore;
      //načtu další aktivní sledování
      $template->items = dibi::fetchAll("
        SELECT *
        FROM watchdogs
        INNER JOIN products ON (dogproid=proid)
        WHERE ".($dogid > 0 ? " dogid != $dogid AND " : "")." dogmail=%s", $formVals->dogmail
      );
      $template->setFile(WWW_DIR.'/../templates/Mails/mailWatchdogCreated.latte');
      $this->mailSend($formVals->dogmail, "Hlídání nastaveno", $template);
      $this->redirect('this');
    }
  }

  protected function createComponentRequestForm() {
    //prihlasovaci form
    $form = $this->createAppForm();
    $form->addText('usrname', 'Jméno a příjmení (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno a příjmení.');
    $form->addText('usrmail', 'Váš email (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.');
    $form->addText('usrmobil', 'Telefonní číslo');
    $form->addTextArea('usrnote', 'Poznámka')
      ->setDefaultValue('Mám zájem o zkušební jízdu, nejlépe dne:');

    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"])
      ->setHtmlId('antispam')
      ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);

    $form->addSubmit('submit', 'Odeslat objednávku')->getControlPrototype()->class('btn');
    $form->addHidden('isTestReq', 1);
    $form->onSuccess[] = array($this, 'requestSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  public function requestSubmitted($form) {
    if ($form->isSubmitted()) {
      //odmailuju nove heslo
      $proid = $this->getParameter('id');
      $template = $this->createTemplate();
      $template->product = dibi::fetch("SELECT * FROM products WHERE proid=%s", $proid);
      $template->formVals = $form->getValues();
      if ($template->formVals["isTestReq"] == 1) {
        $template->setFile(WWW_DIR.'/../templates/Mails/mailTestRequest.latte');
        $subj = 'Zkušební jízda';
      } else {
        $subj = 'Dotaz na dostupnost';
        $template->setFile(WWW_DIR.'/../templates/Mails/mailProductRequest.latte');
      }

      if (!$this->mailSend($this->config["SERVER_MAIL"], $subj, $template)) $form->addError('Poptávku se nepodařilo odeslat');
      $this->flashMessage("Email s Vaší poptávkou byl odeslán.");
      $this->redirect('this');
    }
  }

  protected function createComponentBasketAddForm() {
    $pros = $this->model->getProductsModel();
    $id = (string)$this->getParameter('id');
    $form = $this->createAppForm();
    $form->addHidden('proid', $id);
    $form->addText('oricnt', 'Kusy:', 3)
      ->addRule(Nette\Forms\Form::FILLED, 'Vyplňte počet kusů')
      ->addRule(Nette\Forms\Form::INTEGER, 'Počet kusů musí být celé číslo')
      ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Nelze objednat více než 999 kusů', 3)
      ->setDefaultValue(1);

    if ($id > 0) {
      $pro = $pros->load($id);
      if ($this->isEgg($pro)) {
        $form->addSelect('orimonth', 'Měsíc odběru:', $pros->getEnumEggMonths());
      }
    }

    $form->addSubmit('buy', 'Koupit');
    $form->onSuccess[] = [$this, 'basketAddFormSubmitted'];
    return $form;
  }

  public function basketAddFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $this->redirect('Basket:add', $vals["proid"], $vals["oricnt"], $vals["orimonth"]);
    }
  }

  protected function createComponentBasketAddFormVar() {
    $id = (string)$this->getParameter('id');
    $pros = $this->model->getProductsModel();
    $form = $this->createAppForm();
    //$form->getElementPrototype()->class('ajax');
    $pro = $pros->load($id);
    $proMasAccess = 0;
    if ($pro->proismaster || $pro->promasid > 0) {
      $pid = $pro->promasid > 0 ? $pro->promasid : $pro->proid;
      $rows = dibi::fetchAll("SELECT proid,proaccess FROM products WHERE promasid=%i", $pid, " AND prostatus=0 ORDER BY proname");
      $proMas = dibi::fetch("SELECT proid,proaccess FROM products WHERE proid=%i", $pid, " AND prostatus=0");
      if ($proMas) $proMasAccess = $proMas->proaccess;
    } else {
      $rows = dibi::fetchAll("SELECT proid,proaccess FROM products WHERE proid=%i", $id, " AND prostatus=0 ORDER BY proname");
    }
    foreach ($rows as $key => $row) {
      if (($row->proaccess == 0 && $proMasAccess == 0) || $this->config["CHECK_STOCK"] == 0) {
        $com = $form->addContainer($row->proid);
        $com->addHidden('proid', $row->proid);
        $com->addText('oricnt', 'Množství:', 3)
          ->setDefaultValue(0)
          ->setHtmlId('qty_'.$row->proid)
          ->addCondition(Nette\Forms\Form::FILLED)
            ->addRule(Nette\Forms\Form::INTEGER, 'Počet kusů musí být celé číslo')
            ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Nelze objednat více než 999 kusů', 3);
      }
    }

    $form->addSubmit('buy', 'Přidat do košíku');
    $form->onSuccess[] = [$this, 'basketAddFormVarSubmitted'];
    return $form;
  }

  public function basketAddFormVarSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      foreach ($vals as $key => $item) {
        if ((int)$item->oricnt > 0) {
          if (!isset($this->basketNamespace->items[$item->proid])) $this->basketNamespace->items[$item->proid] = 0;
          $this->basketNamespace->items[$item->proid] += $item->oricnt;
        }
      }

      $this->redirect('Basket:default', array('r'=>1));

    }
  }

  protected function createComponentCommentForm() {
    $cmts = $this->model->getCommentsModel();

    $proid = (int)$this->getParameter('id');
    $reid = (int)$this->getParameter('reid');
    $ordId = (int)$this->getParameter('oid');
    $re = (string)$this->getParameter("subj");

    $usrid = $this->userData->usrid;

    $form = $this->createAppForm();

    $form->addHidden("cmtproid", $proid);
    $form->addHidden("cmtreid", $reid);
    $form->addHidden("cmtordid", $ordId);
    $form->addHidden("cmtusrid", $usrid);

    $form->addText('cmtnick', 'Jméno')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addRadioList('cmtrate', 'Hodnocení', $cmts->getEnumCmtRate())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte hodnocení.');


    if ($reid == 0) {
      $form->addCheckbox('cmtsendreply', 'Pošlete mi odpovědi na email');
    } else {
      $form->addHidden('cmtsendreply', FALSE);
    }
    $form->addText('cmtmail', 'Váš email')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát.');

    $form->addText('cmtsubj', 'Titulek');

    if ($reid > 0) {
      $form["cmtsubj"]->setDefaultValue($re);
    }

    $form->addTextArea('cmttext', 'Vaše recenze', 60, 3)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte recenzi.');

    $form->addReCaptcha('recaptcha', $required = FALSE, $message = 'Potvrďte prosím, že nejste robot.');

    $form->addSubmit('submit', 'Odeslat komentář')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'commentSubmitted');
    return $form;
  }

  public function commentSubmitted($form) {
    if ($form->isSubmitted()) {
      /*
      $couCode = $this->appNamespace->countryCode;
      if ($couCode == 'cz' || $couCode == 'sk' || $couCode == '') {
      } else {
        $form->addError("Nekorektně odeslaný formulář.");
        return false;
      }
      */
      //ulozim informace
      $vals = $form->getValues();

      $pros = $this->model->getProductsModel();
      $pros->setPrcCat($this->userData->usrprccat);
      $pros->setCurrency($this->currencies, $this->curId);
      $pro = $pros->load($vals->cmtproid);
      if ((int)$pro->promasid > 0) {
        $pro = $pros->load($pro->promasid);
        $vals->cmtproid = $pro->proid;
      }

      $vals["cmtip"] = $_SERVER["REMOTE_ADDR"];
      $coms = $this->model->getCommentsModel();
      $vals->cmtstatus = 1;
      $comid = $coms->insert($vals);
      $com = $coms->load($comid);
      $template = $this->createTemplate();
      $template->setFile(WWW_DIR.'/../templates/Mails/mailProductCommentNew.latte');
      $template->product = $pro;
      $template->comment = $com;
      $this->mailSend($this->config["SERVER_MAIL"], "Nová recenze", $template);

      $this->flashMessage("Děkujeme za Vaši recenzi.");
      $this->redirect('detail', array('id'=>$pro->proid, 'key'=>Nette\Utils\Strings::webalize($pro->proname)));
    }
  }
}

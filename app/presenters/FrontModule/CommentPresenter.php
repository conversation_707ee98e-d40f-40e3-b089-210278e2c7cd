<?php
namespace FrontModule;
use dibi;
use Nette;

final class CommentPresenter extends BasePresenter {
  /** @persistent */
  public $name = '';

  /** @persistent */
  public $fulltext = '';

  /** @persistent */
  public $cat = array();

  public function renderDefault() {
    $cmts = $this->model->getCommentsModel();
    $and = false;
    $query[] = "
    SELECT * 
    FROM comments 
    LEFT JOIN products ON (cmtproid=proid)
    WHERE cmtreid=0 ";
    if (!empty($this->name)) {
      array_push($query, " AND proname LIKE %~like~", $this->name);
    }

    if (!empty($this->fulltext)) {
      array_push($query, " AND cmttext LIKE %~like~", $this->fulltext);
      $and = true;
    }

    if (count($this->cat) > 0) {
      array_push($query, " AND (cmtcatid IN (%i)", $this->cat, " OR cmtcatid2 IN (%i)", $this->cat, ")");
      $and = true;
    }

    //if ($and == false) array_push($query, " AND cmtid=-1");
    $query[] = " ORDER BY cmtdatec DESC";

    $ds = $cmts->getDataSource($query);
    $rs = dibi::fetchAll($query);
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = 10;
    $paginator->itemCount = $ds->count();
    $rows = $ds->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();

    foreach ($rows as $key => $row) {
      $rows[$key]->res = dibi::fetchAll("SELECT * FROM comments WHERE cmtreid=%i", $row->cmtid, " ORDER BY cmtid DESC");
    }
    $this->template->rows = $rows;

  }

  protected function createComponentCommentForm() {
    $cmts = $this->model->getCommentsModel();
    $reid = (int)$this->getParameter("reid");
    $re = (string)$this->getParameter("subj");

    $form = $this->createAppForm();
    $form->addGroup("Vložte svůj dotaz");
    $form->addHidden("cmtreid", 0);
    $form->addText("cmtnick", "Jméno/přezdívka:", 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše jméno/přezdívku.');
    $form->addText("cmtmail", "Email:", 40)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');
    $form->addText("cmtsubj", "Titulek:", 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte titulek.');
    $form->addTextArea("cmttext", "Text:", 80, 10)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte text.');

    $form->addText("cmtfield1", "", 40);
    $form->addText("cmtfield2", "", 40);
    $form->addText("cmtfield3", "", 40);

    if ($reid > 0) {
      $form["cmtreid"]->setDefaultValue($reid);
      $form["cmtsubj"]->setDefaultValue($re);
    }

    $form->addSubmit('submit', 'Vložit dotaz');
    $form->onSuccess[] = array($this, 'commentFormSubmitted');
    return $form;
  }

  public function commentFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $couCode = $this->appNamespace->countryCode;
      if ($couCode == 'cz' || $couCode == 'sk' || $couCode == '') {
        $vals = $form->getValues();
        $cmts = $this->model->getCommentsModel();
        $cmts->insert($vals);
        $this->flashMessageOk("Děkujeme za Váš dotaz");
      } else {
        $form->addError("Nekorektně odeslaný formulář.");
        return false;
      }
      $this->redirect('default'.(!empty($vals["cmtreid"]) ? '#g'.$vals["cmtreid"] : ""));
    }
  }
}

<?php
namespace FrontModule;
use dibi;
use <PERSON>ure<PERSON>\ShopCertification\Exception;
use Nette;
use <PERSON>\Debugger;

final class BasketPresenter extends BasePresenter {

  /** @persistent */
  public $delid = NULL;

  private $deliveryModeRows;
  private $basketOrderOnOnePage;

  private $delModeTerms;

  protected function startup() {

    parent::startup();
    $this->basketOrderOnOnePage = (bool)$this->neonParameters["app"]["basketOrderOnOnePage"];
    $this->recalc();
  }

  protected function beforeRender() {
    parent::beforeRender();
    $this->template->showMenuLeft = FALSE;
    $pros = $this->model->getProductsModel();
    $this->template->enumEggMonths = $pros->getEnumEggMonths();
  }

  /**
   * vymaze polozku z kosiku
   *
   * @param integer $proid id polozky
   */
  public function actionDelete($proid) {
    if (array_key_exists($proid, $this->basketNamespace->items)) {
      $this->basketDeleteItem($proid);
      $this->recalc();
    }
    $this->redirect('default');
  }

  /**
   * vymaze kupon z kosiku
   *
   * @param integer $proid id polozky
   */
  public function actionDeleteCoupon() {
    $this->basketNamespace->coupon = array();
    $this->recalc(true);
    $this->redirect('default');
  }

  /**
   * prida polozku do kosiku
   *
   * @param integer $proid id polozky
   * @param integer $count pocet kusu
   * @param null $month
   * @throws Nette\Application\AbortException
   * @throws \Dibi\Exception
   */
  public function actionAdd($proid, $count=NULL, $month=NULL) {
    $proid = (int)$proid;
    if ($count === NULL) {
      $count = 1;
    }

    if ((int)$month > 0) {
      $this->basketNamespace->ordmonth = (int)$month;
    }

    $count = (int)$count;
    if (!array_key_exists($proid, $this->basketNamespace->items)) {
      //kontrola zda je zbozi skladem a zda je aktivni
      $products = $this->model->getProductsModel();
      $row = $products->load($proid);
      if ($row) {
        //polozka nalezna v db a je aktivni
        if ((int)$row->prostatus != 0) {
          $this->flashMessageErr("Zboží nejde přidat do košíku, je momentálně vyřazeno z nabídky.");
        } else if ((int)$row->proqty == 0 && $this->config["CHECK_STOCK"] == 1) {
          $this->flashMessageErr("Zboží nejde přidat do košíku, není momentálně skladem.");
        } else {
          if ((int)$row->proqty < $count && $this->config["CHECK_STOCK"] == 1) {
            $this->flashMessageErr(array("Položka byla přídána do košíku, ale v menším počtu, skladem je pouze",$row->proqty,"ks"));
            $count = (int)$row->proqty;
          }
          $this->basketNamespace->items[$proid] = $count;
        }
      }
    }
    $this->redirect('default');
  }

  public function handleBasketChangeCount($proId, $qty=null) {

    if ((int)$qty === 0) {
      unset($this->basketNamespace->items[$proId]);
      $this->recalc();
    } else {
      $products = $this->model->getProductsModel();
      $row = $products->load($proId);
      if ($row) {
        //polozka nalezna v db a je aktivni
        if ((int)$row->prostatus != 0) {
          $this->flashMessageErr("Zboží nejde přidat do košíku, je momentálně vyřazeno z nabídky.");
        } else if ((int)$row->proqty == 0 && $this->config["CHECK_STOCK"] == 1) {
          $this->flashMessageErr("Zboží nejde přidat do košíku, není momentálně skladem.");
        } else {
          if ((int)$row->proqty < $qty && $this->config["CHECK_STOCK"] == 1) {
            $this->flashMessageErr(array("Položka byla přídána do košíku, ale v menším počtu, skladem je pouze",$row->proqty,"ks"));
            $qty = (int)$row->proqty;
          }
          $this->basketNamespace->items[$proId] = $qty;
          $this->recalc();

          $this->template->basketItemsCnt = $this->basketNamespace->itemsCnt;
          $this->template->basketPriceSum = $this->basketNamespace->priceSum;
        }
      }
    }

    if ($this->isAjax()) {
      $this->redrawControl("header");
      $this->redrawControl("basketWindow");
      $this->redrawControl("basket");
      $this->redrawControl("scripts");
    } else {
      $this->redirect('default');
    }
  }

  public function renderDefault() {
    $this->template->step = 1;
    $this->basketNamespace->delid = 0;
    $form = $this->getComponent("basketForm");
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    $this->template->blockPromoRegistrace = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='promo_registrace' AND pagstatus=0");
    if (!$form->isSubmitted()) {
      if (count($this->basketNamespace->items) > 0) {
        $productRows = dibi::query($product->getSqlList("proid IN (".implode(",", array_keys($this->basketNamespace->items)).")"))->fetchAssoc('proid');
        foreach ($productRows as $key => $productRow) {
          $productRows[$key]->proprice = $this->getCouponProductPrice($productRow);
          $productRows[$key]->gifts = array();
          if (!empty($productRow->progifts)) {
            //nactu darky
            $arr = explode(',', trim($productRow->progifts, ','));
            $productRows[$key]->gifts = dibi::fetchAll("SELECT proid, proname, prokey FROM products WHERE procode IN (%s)", $arr);
          }
        }
        $this->basketNamespace->products = $productRows;
      } else {
        $this->basketNamespace->products = array();
      }


      /*
      //zjistim nejblizsi slevu
      $nextDisc = dibi::fetch("SELECT dispercent, disfrom - ".$this->basketNamespace->priceSumVatDisc." AS diff FROM discounts WHERE distypid='volume' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, " AND disstatus=0 AND disfrom > ".$this->basketNamespace->priceSumVatDisc);
      $nextDelsFree = Null;
      $nextDelFreeMas = Null;

      $rows = false;
      if ($this->basketNamespace->delFree == false) {
        //nejblizsi doprava zdarma
        $rows = dibi::query("
        SELECT *, disfrom - ".$this->basketNamespace->priceSumVat." AS diff
        FROM deliverymodes
        INNER JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
        WHERE delmasid=0 AND disfrom > ".$this->basketNamespace->priceSumVat." ORDER BY disfrom")->fetchAssoc("disfrom,delid");
      }

      if ($rows) $nextDelsFree = $rows;

      $this->template->nextDisc = $nextDisc;
      $this->template->nextDelsFree = $nextDelsFree;
      */
      $dels = $this->model->getDeliveryModesModel();
      $dels->setCurrency($this->currencies, $this->curId);
      $dels->setPrcCat($this->userData->usrprccat);
      $delModes = $delModes = $dels->getDelModes($this->basketNamespace);

      $delFreeLimit = NULL;
      $delFreeName = NULL;
      foreach ($delModes as $del) {
        if ($del->delcode !== "OSOBNE") {
          //zjistím limit na dopravu zdarma
          $limit = (double)dibi::fetchSingle("SELECT disfrom FROM discounts WHERE distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, " AND disdelid=%i", $del->delid);

          if ($delFreeLimit === NULL && $limit > 0) {
            $delFreeLimit = $limit;
            $delFreeName = $del->delname;
          }
          if ($limit > 0 && $delFreeLimit > $limit) {
            $delFreeLimit = $limit;
            $delFreeName = $del->delname;
          }
        }
      }

      $this->template->delFreeLimitByBasket = $delFreeLimit;
      $this->template->delFreeNameByBasket = $delFreeName;

      //naplnim access
      $this->template->enum_proaccess = $product->getEnumProAccess();
    }
  }

  public function renderOrderDelMode() {

    $this->template->step = 2;
    if (count($this->basketNamespace->items) === 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }

    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $dels->setPrcCat($this->userData->usrprccat);

    $delModes = $dels->getDelModes($this->basketNamespace);

    //zkotroluji, jestli je košíku´i jiná doprava než osobní odběr
    $cnt = 0;
    $perCnt = 0;
    foreach ($delModes as $row) {
      $cnt ++;
      if ($row->delcode === "OSOBNE") {
        $perCnt ++;
      }
    }
    $this->template->onlyPerDelivery = FALSE;
    if ($cnt === $perCnt && $cnt > 0) {
      $this->template->onlyPerDelivery = TRUE;
    }

    $this->template->delModes= $delModes;

    $this->template->payModes = $dels->getPayModes(0, $this->basketNamespace, "" ,"delmasid,delid");
    $this->template->payModesJs = $dels->getPayModes(0, $this->basketNamespace);
    $this->template->priceSumVat = $this->basketNamespace->priceSumTotalVat;

    $this->template->selectedDeliveryPrice = 0;
    if (!empty($this->basketNamespace->contact["orddelid"])) {
      $currentPaymentMode = $this->template->payModesJs[$this->basketNamespace->contact["orddelid"]];

      if (isset($delModes[$currentPaymentMode->delmasid])) {
        $currentDeliveryMode = $delModes[$currentPaymentMode->delmasid];
        $this->template->selectedDeliveryPrice = $currentDeliveryMode->delprice + $currentPaymentMode->delprice;
      } else {
        $this->basketNamespace->contact["orddelid"] = NULL;
      }
    }

    if (is_array($this->basketNamespace->contact)) {
      if (isset($this->basketNamespace->contact["orddelid"])) {
        $payId = (int)$this->basketNamespace->contact["orddelid"];
        $this->template->payid = $payId;
        if (isset($this->template->payModesJs[$payId])) {
          $payMode = $this->template->payModesJs[$payId];
          $this->template->delid = $payMode->delmasid;
        }
      }
      $form = $this['orderDelModeForm'];
      //$arr = $this->basketNamespace->contact;
      //$form->setDefaults($this->basketNamespace->contact);
    }
  }

  public function renderMakeOrder() {
    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }
    $this->template->productRows = $this->basketNamespace->products;
    $this->template->basket = $this->basketNamespace;
    $basketPrice = $this->basketNamespace->priceSumTotalVat;
    $this->template->delModes = dibi::query("
      SELECT delid, delname, delcode, ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice, delurlparcel, deldesc, deltext".$this->curId." AS deltext
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND delmasid=0 ORDER BY delorder
    ")->fetchAssoc('delid');
    $delId = 0;
    //vezmu prvni dopravu ze seznamu
    if ($delId == 0) {
      $first = current($this->template->delModes);
      $delId = $first->delid;
    }
    $this->delid = $delId;

    $this->template->payModes = dibi::fetchAll("
      SELECT delid, delmasid, delname, delcode, ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice, delurlparcel, deldesc, deltext".$this->curId." AS deltext
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delmasid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delcode!='payonline' AND delstatus=0 AND delmasid=%i", $this->delid, " ORDER BY delmasid, delorder
    ");
    $payMO = dibi::fetch("
      SELECT delid, delmasid, delname, delcode, ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice, delurlparcel, deldesc, deltext".$this->curId." AS deltext
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delmasid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delcode='payonline' AND delstatus=0 AND delmasid=%i", $this->delid, " ORDER BY delmasid, delorder
    ");
    $payModesOnline = Array();
    $payTypeOnline = $this->neonParameters["onlinePayTypes"];
    if (count($payTypeOnline) > 0 && $payMO) {
      foreach ($payTypeOnline as $ikey => $iname) {
        $row = clone $payMO;
        $row->delid = $ikey.'_'.$payMO->delid;
        $row->delname = $iname;
        $payModesOnline[] = $row;
      }
    }
    $this->template->payModesOnline = $payModesOnline;

    $this->template->payModesJs = dibi::query("
      SELECT delid, delmasid, delname, ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice, delurlparcel, deldesc
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delmasid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND delmasid=%i", $this->delid, " ORDER BY delmasid, delorder")->fetchAssoc('delid');
    $this->template->priceSumTotalVat = $basketPrice;

    $LoggedUser = $this->userData;
      if ($LoggedUser->usrid > 0) {
        $defVals = array(
          'ordiname' => $LoggedUser->usriname,
          'ordilname' => $LoggedUser->usrilname,
          'ordifirname' => $LoggedUser->usrifirname,
          'ordistreet' => $LoggedUser->usristreet,
          'ordistreetno' => $LoggedUser->usristreetno,
          'ordicity' => $LoggedUser->usricity,
          'ordipostcode' => $LoggedUser->usripostcode,
          'ordicouid' => $LoggedUser->usricouid,
          'ordic' => $LoggedUser->usric,
          'orddic' => $LoggedUser->usrdic,
          'ordstname' => $LoggedUser->usrstname,
          'ordstlname' => $LoggedUser->usrstlname,
          'ordstfirname' => $LoggedUser->usrstfirname,
          'ordststreet' => $LoggedUser->usrststreet,
          'ordststreetno' => $LoggedUser->usrststreetno,
          'ordstcity' => $LoggedUser->usrstcity,
          'ordstpostcode' => $LoggedUser->usrstpostcode,
          'ordstcouid' => $LoggedUser->usrstcouid,
          'ordtel' => $LoggedUser->usrtel,
          'ordmail' => $LoggedUser->usrmail,
          'shipto' => ($LoggedUser->usrstname != ""),
        );
        $form = $this->getComponent("makeOrderForm");
        $form->setDefaults($defVals);
      }

  }

  public function renderOrderContact() {
    $this->template->step = 3;
    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }
    if (empty($this->basketNamespace->contact["orddelid"])) {
      $this->flashMessage("Vyberte nejprve dopravu a platbu.");
      $this->redirect('Basket:orderDelMode');
    }
    if (is_array($this->basketNamespace->contact)) {
      //nactu kontaktni údaje
      if (empty($this->basketNamespace->contact["ordiname"]) && empty($this->basketNamespace->contact["ordistreet"])) {
        $LoggedUser = $this->userData;
        if ($LoggedUser->usrid > 0) {
          $defVals = array(
            'ordiname' => $LoggedUser->usriname,
            'ordilname' => $LoggedUser->usrilname,
            'ordifirname' => $LoggedUser->usrifirname,
            'ordistreet' => $LoggedUser->usristreet,
            'ordistreetno' => $LoggedUser->usristreetno,
            'ordicity' => $LoggedUser->usricity,
            'ordipostcode' => $LoggedUser->usripostcode,
            'ordicouid' => $LoggedUser->usricouid,
            'ordic' => $LoggedUser->usric,
            'orddic' => $LoggedUser->usrdic,
            'ordstname' => $LoggedUser->usrstname,
            'ordstlname' => $LoggedUser->usrstlname,
            'ordstfirname' => $LoggedUser->usrstfirname,
            'ordststreet' => $LoggedUser->usrststreet,
            'ordststreetno' => $LoggedUser->usrststreetno,
            'ordstcity' => $LoggedUser->usrstcity,
            'ordstpostcode' => $LoggedUser->usrstpostcode,
            'ordstcouid' => $LoggedUser->usrstcouid,
            'ordtel' => $LoggedUser->usrtel,
            'ordmail' => $LoggedUser->usrmail,
            'shipto' => ($LoggedUser->usrstname != ""),
          );
          $form = $this->getComponent("orderContactForm");
          $form->setDefaults($defVals);
        }
      } else {
        $form = $this['orderContactForm'];
        $form->setDefaults($this->basketNamespace->contact);
      }
    }
  }


  public function renderOrderSumarize() {
    $this->template->step = 4;
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $dels->setPrcCat($this->userData->usrprccat);

    if (count($this->basketNamespace->items) === 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }
    if (empty($this->basketNamespace->contact["orddelid"])) {
      $this->flashMessage("Vyberte nejprve dopravu a platbu.");
      $this->redirect('Basket:orderDelMode');
    }
    if (empty($this->basketNamespace->contact["ordiname"])) {
      $this->flashMessage("Vyplňte nejprve Dodací údaje.");
      $this->redirect('Basket:orderContact');
    }

    //doplnim dopravu
    $orddelid = $this->basketNamespace->contact["orddelid"];
    $basketPrice = $this->basketNamespace->priceSumVat;

    //zjistim cenu platby
    $delprice = 0;
    $delname = "";

    $pay = $dels->getPayMode($orddelid, $this->basketNamespace);
    if ($pay) {
      $delprice = $pay->delprice;

      //zjistim cenu dopravy
      $delivery = $dels->getDelMode($pay->delmasid, $this->basketNamespace);
      if ($delivery) {
        $delprice += $delivery->delprice;
        $delname = $delivery->delname." - ".$pay->delname;

        $this->template->delivery = $delivery;
      }

      $this->template->payment = $pay;
    }

    $this->template->productRows = $this->basketNamespace->products;
    $this->template->basket = $this->basketNamespace;
    $this->template->formData = $this->basketNamespace->contact;
  }


  public function basketFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      $datachanged = false;
      foreach ($formVars as $key => $value) {
        if (substr($key, 0, 6) == 'count_')  {
          $proid = substr($key, 6);
          if ($value > 0) {
            $datachanged = true;
            $this->basketEditItem($proid, $value);
          } else {
            $datachanged = true;
            $this->basketDeleteItem($proid);
          }
        }
      }
      $cous = $this->model->getCouponsModel();
      if($form["recalc"]->isSubmittedBy()) {
        if (!empty($formVars["coupon"])) {
          //nactu kupon slevovy
          $cou = $cous->validateCoupon($formVars["coupon"], $this->basketNamespace->priceSumVat, $this->userData->usrmail, TRUE);
          if ($cou["status"] != 'ok') {
            $this->flashMessage($cou["text"], 'danger');
          } else {
            $this->basketNamespace->coupon = $cou["data"];
          }
        }
      }

      //psc pro zjištění dopravy
      $this->basketNamespace->contact["ordregid"] = $formVars["ordregid"];

      $this->recalc();

      //kontroluji platnost kupónu
      if (!empty($this->basketNamespace->coupon)) {
        $cous = $this->model->getCouponsModel();
        $cou = $cous->validateCoupon($this->basketNamespace->coupon->coucode, $this->basketNamespace->priceSumVatDisc, $this->userData->usrmail, TRUE);
        if ($cou["status"] != 'ok') {
          unset($this->basketNamespace->coupon);
          $this->recalc();
          $this->flashMessage($cou["text"], 'danger');
          $this->redirect("this");
        } else {
          $this->basketNamespace->coupon = $cou["data"];
        }
      }

      if($form["recalc"]->isSubmittedBy()) {
        $this->redirect("this");
      } else if($form["makeorder"]->isSubmittedBy()) {
        if ($this->basketOrderOnOnePage) {
          $this->redirect("Basket:makeOrder");
        } else {
          $this->redirect("Basket:orderDelMode");
        }
      }
    }
  }

  protected function createComponentBasketForm() {
    $form = $this->createAppForm();
    $product = $this->model->getProductsModel();
    $ords = $this->model->getOrdersModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    foreach ($this->basketNamespace->items as $key => $count) {
      $row = $product->load($key);
      if ($row->proid > 0  && $row->prostatus === 0) {
        $form->addText("count_".$row->proid, "", 3)
          ->setRequired(TRUE)
          ->addRule(Nette\Forms\Form::INTEGER, "Počet kusů musí být celé číslo")
          ->setDefaultValue($count)->getControlPrototype()->addClass("countInput")->data("proid", $row->proid);
      }
    }

    //slevovy kupon
    if (empty($this->basketNamespace->coupon)) {
      $form->addText("coupon", "Slevový kupón", 10);
      $form->addSubmit('couponAdd', 'Přidat slevu')->getControlPrototype()->class('button');
    }

    $regIdEnum = $ords->getEnumCoolBalikRegId();

    $form->addSelect("ordregid", "Oblast doručení", $regIdEnum)
      ->setPrompt("Vyberte ...")
      ->addRule(Nette\Forms\Form::FILLED, "Vyplňte prosím oblast, do které budeme doručovat. Podle něj Vám můžeme nabídnout nejvhodnější dopravu.");

    if (!empty($this->basketNamespace->contact["ordregid"]) && isset($regIdEnum[$this->basketNamespace->contact["ordregid"]])) {
      $form["ordregid"]->setDefaultValue($this->basketNamespace->contact["ordregid"]);
    }

    $form->addSubmit('recalc', 'Přidat')->getControlPrototype()->class('btn');
    $form->addSubmit('makeorder', 'Vybrat dopravu a platbu');
    $form->onSuccess[] = array($this, 'basketFormSubmitted');
    return $form;
  }

  protected function createComponentOrderDelModeForm() {
    $enums = $this->model->getEnumcatsModel();
    $enumCountries = $enums->getEnumCountries();

    $dels = $this->model->getDeliveryModesModel();
    $ords = $this->model->getOrdersModel();
    $pros = $this->model->getProductsModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $form = $this->createAppForm();

    //bezne platby
    $rows = $dels->getPayModes(0, $this->basketNamespace);
    $payModes = array();
    foreach ($rows as $row) {
      $payModes[$row->delid] = $row->delname;
    }

    $delModes = $dels->getDelModes($this->basketNamespace);

    $delCalc = new \DelTermsCalculator();
    $delTerms = $delCalc->getDelDates($this->basketNamespace->delDateFrom, $this->basketNamespace->delDateTo);

    foreach ($delModes as $delMode) {
      $delCode = $delMode->delcode;
      if ($delCode === 'GEIS') {
        $delCode = \DelTermsCalculator::DEL_TYPE_KURYR;
      }

      if ($this->basketNamespace->eggTypeOrder !== "onlyegg") {
        if (!empty($delMode->deldates)) {
          if ($delMode->deldates === 'pracovnidny') {
            //nageneruji od dneška 10 pracovních dní
            $holidays = dibi::query("SELECT CONCAT(holmonth, '-' ,holday) AS datekey, holmonth, holday FROM holidays ORDER BY holmonth, holday")->fetchAssoc("datekey");
            $arr = [];
            $today = time();
            $date = new \DateInfo($today, $holidays);
            $cnt = 1;
            do {
              if ($date->isWorkDay) {
                $arr[] = $date->dateFormated;
                $cnt++;
              }
              $date = $date->getNextDay();
            } while ($cnt < 31);
          } elseif(strpos($delMode->deldates, "#") !== false) {
            $arr = explode("#", trim($delMode->deldates, '#'));
            $daysArr = array_flip($arr);
            $holidays = dibi::query("SELECT CONCAT(holmonth, '-' ,holday) AS datekey, holmonth, holday FROM holidays ORDER BY holmonth, holday")->fetchAssoc("datekey");
            $arr = [];
            $today = time();
            $date = new \DateInfo($today, $holidays);
            $cnt = 1;
            do {
              if ($date->isWorkDay && isset($daysArr[$date->weekDayIndex])) {
                $arr[] = $date->dateFormated;
                $cnt++;
              }
              $date = $date->getNextDay();
            } while ($cnt < 31);
          } else {
            $arr = explode(",", $delMode->deldates);
          }

          $delTerm = $delCalc->getDelDatesSpecial($arr, $this->basketNamespace->delDateFrom, $this->basketNamespace->delDateTo);
        } else if ($delMode->delcode === 'COOL_BALIK') {
          //podle kraje nageneruji dny
          $ords = $this->model->getOrdersModel();
          $WeekDaysEnum = $ords->getEnumCoolBalikRegIdWeekDays();
          $weekDays = $WeekDaysEnum[$this->basketNamespace->contact["ordregid"]];
          $holidays = dibi::query("SELECT CONCAT(holmonth, '-' ,holday) AS datekey, holmonth, holday FROM holidays ORDER BY holmonth, holday")->fetchAssoc("datekey");
          $arr = [];
          $today = time();
          $date = new \DateInfo($today, $holidays);
          $cnt = 1;
          $cntAll = 1;

          $nowWeekIndex = (int)date('W', $today);
          $nowWeekDayIndex = (int)date('w', $today);
          $nowHour = (int)date('G', $today);

          $fromDayAddNextWeek = 2;
          $fromHourAddNextWeek = 11;

          if (count($weekDays) > 0) {
            $cntAll ++;
            do {
              foreach ($weekDays as $weekDay) {
                $skip = FALSE;

                if ($date->weekIndex === $nowWeekIndex) {
                  if ($nowWeekDayIndex === $fromDayAddNextWeek && $nowHour >= $fromHourAddNextWeek) {
                    $skip = TRUE;
                  } else if ($nowWeekDayIndex > $fromDayAddNextWeek || $nowWeekDayIndex === 0) {
                    $skip = TRUE;
                  }
                }

                if ($date->isWorkDay && $date->weekDayIndex === $weekDay && !$skip) {
                  $arr[] = $date->dateFormated;
                  $cnt ++;
                }
                $date = $date->getNextDay();
                }
            } while ($cnt < 10 || $cntAll > 1000);
          }

          $delTerm = $delCalc->getDelDatesSpecial($arr, $this->basketNamespace->delDateFrom, $this->basketNamespace->delDateTo);

        } else if (!empty($delTerms[$delCode])) {
          $delTerm = $delTerms[$delCode];
        }
      } else {
        $arr = $pros->getEnumMonths();
        if (!empty($this->basketNamespace->ordmonth)) {
          $month = substr("0" . $this->basketNamespace->ordmonth, -2);
          $year = date('Y');
          $date = $year . $month . "01".$year . $month . "01";
          $delTerm[$date] = $arr[$this->basketNamespace->ordmonth] . " " . $year;
        }
      }

      if (!empty($delTerm)) {
        $key = "term_" . $delMode->delid;
        $form->addSelect($key, "Termíny dodání:", $delTerm);
        if (isset($this->basketNamespace->contact[$key]) && isset($delTerm[$key])) {
          $form[$key]->setDefaultValue($this->basketNamespace->contact[$key]);
        }
      }
    }

    $form->addRadioList('orddelid', '', $payModes)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte způsob dodání a platby.');

    $items = $dels->getEnumUlozenkaPlaces();
    $form->addSelect('orddelspec', '', $items)
      ->setPrompt("Vyberte prosím výdejní místo ...");

    $form->addSubmit('submit', 'Zadat dodací údaje');
    $form->onSuccess[] = array($this, 'orderDelModeFormSubmitted');
    return $form;
  }

  public function orderDelModeFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      //unset
      unset($formVars["shipto"]);
      unset($formVars["delmasid"]);



      $payMode = dibi::fetch("SELECT delid, delcode, delname, delmasid FROM deliverymodes WHERE delid=%i", $formVars["orddelid"]);
      if ($payMode) {
        $delMode = dibi::fetch("SELECT delid, delcode, delname, delmasid FROM deliverymodes WHERE delid=%i", $payMode->delmasid);
      }

      if (empty($delMode)) {
        $form->addError("Není správně vybraná doprava.");
      }

      $key = "term_" . $delMode->delid;
      if (isset($formVars[$key])) {
        $detTerms = $formVars[$key];
        $formVars->orddeldatestart = substr($detTerms, 0, 4) . "-" . substr($detTerms, 4, 2). "-" . substr($detTerms, 6, 2);
        $detTerms = substr($detTerms, 8);
        $formVars->orddeldatedel = substr($detTerms, 0, 4) . "-" . substr($detTerms, 4, 2). "-" . substr($detTerms, 6);
      }

      if ($delMode->delcode === 'ULOZENKA' && empty($formVars["orddelspec"])) {
        $form->addError("Prosim vyberte výdejní místo");
      } else if ($delMode->delcode !== 'ULOZENKA' && !empty($formVars["orddelspec"])) {
        unset($formVars["orddelspec"]);
      }


      $this->basketNamespace->contact = array_replace((array)$this->basketNamespace->contact, (array)$formVars);

      if ($form->hasErrors()) {
        return;
      }

      $this->redirect("Basket:orderContact");
    }
  }

  protected function createComponentOrderContactForm() {
    $enums = $this->model->getEnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $form = $this->createAppForm();

    //cena objednaneho zbozi v kosiku
    $basketPrice = $this->basketNamespace->priceSumTotalVat;

    $form->addGroup('Fakturační a dodací adresa');

    $form->addText("ordiname", "Jméno", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addText("ordilname", "Přijmení", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordifirname", "Název firmy", 60);

    $form->addText("ordistreet", "Ulice", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordistreetno", "Číslo popisné", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordicity", "Město, obec", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordipostcode", "PSČ", 6)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.')
      ->addRule(Nette\Forms\Form::LENGTH, 'Prosím vyplňte přesně %d číslic', 5);

    $form->addText("ordtel", "Mobilní telefon", 10)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte telefon.');

    $form->addText("ordmail", "Email", 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addText("ordic", "IČ", 10);
    $form->addText("orddic", "DIČ", 10)
      ->setHtmlId("orddic");

    $form->addCheckbox("ordusrvat", "Jsem plátce DPH")
      ->setHtmlId("ordusrvat");

    $form->addGroup('Fakturační adresa');

    $form->addCheckbox('shipto', 'Chci zadat jinou adresu dodání')
      ->setDefaultValue(FALSE)
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE);

    $form->addCheckbox('onfirm', 'Chci objednat na firmu')
      ->setDefaultValue(FALSE)
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE);

    $form->addText("ordstname", "Jméno", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addText("ordstlname", "Příjmení", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordstfirname", "Název firmy", 60);

    $form->addText("ordststreet", "Ulice", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordststreetno", "Číslo popisné", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordstcity", "Město, obec", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordstpostcode", "PSČ", 6)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.')
        ->addRule(Nette\Forms\Form::LENGTH, 'Prosím vyplňte přesně %d číslic', 5);

    if ($this->userData->usrid > 0) {
      if ($this->userData->usrmaillist == 0) {
        $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(FALSE);
      }
    } else {
      $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(FALSE);
    }

    $form->addTextArea("ordnote", "Vzkaz k objednávce", 60, 3);

    $form->addSubmit('submit', 'Souhrn a objednávka');
    $form->onSuccess[] = array($this, 'orderContactFormSubmitted');
    return $form;
  }

  public function orderContactFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $dels = $this->model->getDeliveryModesModel();
      $formVars = $form->getValues();
      //pokud nemá zašktnuto "jiná dodací adresa tak vymažu data
      if (!$formVars["shipto"]) {
       $formVars["ordstname"] = '';
       $formVars["ordstlname"] = '';
       $formVars["ordstfirname"] = '';
       $formVars["ordststreet"] = '';
       $formVars["ordststreetno"] = '';
       $formVars["ordstcity"] = '';
       $formVars["ordstpostcode"] = '';
      }
      //pokud vybral vlastni_doprava - kontrola zda je zadané psč na trase
      if ((int)$this->basketNamespace->contact["orddelid"] > 0) {
        $pay = $dels->load($this->basketNamespace->contact["orddelid"]);
        $del = $dels->load($pay->delmasid);
        if ($del->delcode === "VLASTNI_PREPRAVA") {
          $postCode = (!empty($formVars["ordstpostcode"]) ? $formVars["ordstpostcode"] : $formVars["ordipostcode"]);
          $postName = (!empty($formVars["ordstcity"]) ? $formVars["ordstcity"] : $formVars["ordicity"]);
          if (!empty($postCode)) {
            $path = dibi::fetch("SELECT * FROM delpathitems WHERE dpidelid=%i", $del->delid, " AND dpipostcode=%s", $postCode);
            if ($path === FALSE) {
              $form->addError("PSČ, které jste zadali neodpovídá vybrané dopravě a rozvozové trase. Změňte vybranou dopravu nebo PSČ u adresy dodání.");
              return;
            }
          }
        }
      }

      //unset
      unset($formVars["antispam"]);
      unset($formVars["shipto"]);
      unset($formVars["delmasid"]);

      if (!empty($this->basketNamespace->ordmonth)) {
        $formVars["orddelmonth"] = $this->basketNamespace->ordmonth;
      }

      $this->basketNamespace->contact = array_replace((array)$this->basketNamespace->contact, (array)$formVars);
      $this->redirect("Basket:orderSumarize");
    }
  }

  protected function createComponentMakeOrderForm() {
    $enums = $this->model->getEnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $form = $this->createAppForm();

    //cena objednaneho zbozi v kosiku
    $basketPrice = $this->basketNamespace->priceSumTotalVat;

    $form->addGroup('Doprava a platba');
    $payModes = dibi::query("
      SELECT delid, delname
      FROM deliverymodes
      WHERE delstatus=0 AND delmasid>0
    ")->fetchPairs("delid", "delname");
    $form->addRadioList('orddelid', '', $payModes)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Způsob dodání a platby.');


    $items = $dels->getEnumUlozenkaPlaces();
    $form->addSelect('orddelspec', '', $items)
      ->setPrompt("Vyberte prosím výdejní místo ...");

    //$form->getElementPrototype()->id = 'makeOrderForm';
    $form->addGroup('Fakturační a dodací adresa');

    $form->addText("ordiname", "Jméno", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

   $form->addText("ordilname", "Přijmení", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordifirname", "Název firmy", 60);

    $form->addText("ordistreet", "Ulice", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordistreetno", "Číslo popisné", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordicity", "Město, obec", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordipostcode", "PSČ", 6)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.');

    $form->addText("ordtel", "Mobilní telefon", 10);

    $form->addText("ordmail", "Email", 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addText("ordic", "IČ", 10);
    $form->addText("orddic", "DIČ", 10)
      ->setHtmlId("orddic");

    $form->addCheckbox("ordusrvat", "Jsem plátce DPH")
      ->setHtmlId("ordusrvat");

    $form->addGroup('Fakturační adresa');

    $form->addCheckbox('shipto', 'Chci zadat jinou adresu dodání')
      ->setDefaultValue(FALSE)
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE);

    $form->addText("ordstname", "Jméno", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addText("ordstlname", "Příjmení", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordstfirname", "Název firmy", 60);

    $form->addText("ordststreet", "Ulice", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordststreetno", "Číslo popisné", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordstcity", "Město, obec", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordstpostcode", "PSČ", 6)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.');
    /*
    $form->addSelect("ordstcouid", "Země", $enumCountries)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte zemi.');
    */

    if ($this->userData->usrid > 0) {
      if ($this->userData->usrmaillist == 0) {
        $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(TRUE);
      }
    } else {
      $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(TRUE);

      $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"],  10)
        ->setOption('description', 'test proti robotum')
        ->setHtmlId('antispam')
        ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);
    }

    $form->addTextArea("ordnote", "Vzkaz k objednávce", 60, 3);

    $form->addSubmit('submit', 'Odeslat objednávku')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'makeOrderFormSubmitted');
    //$form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  protected function createComponentOrderSumarizeForm() {
    $form = $this->createAppForm();

    if ($this->userData->usrid > 0) {
      if ((int)$this->userData->usrmaillist === 0) {
        $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(FALSE);
      }
    } else {
      $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(FALSE);

      //$form->addReCaptcha('recaptcha', $required = FALSE, $message = 'Potvrďte prosím, že nejste robot.');
    }

    $form->addSubmit('submit', 'Závazně objednat');
    $form->onSuccess[] = array($this, 'makeOrderFormSubmitted');
    return $form;
  }


  public function makeOrderFormSubmitted(Nette\Application\UI\Form $form) {
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    if ($form->isSubmitted()) {
      if (count($this->basketNamespace->items) == 0) $this->redirect('Basket:default');

      $LoggedUser = $this->userData;
      $formVars = $form->getValues();
      $formVars = array_merge((array)$formVars, (array)$this->basketNamespace->contact);
      unset($formVars["antispam"]);
      unset($formVars["shipto"]);
      unset($formVars["onfirm"]);
      unset($formVars["delmasid"]);
      unset($formVars["agreement"]);

      foreach ($formVars as $key => $value) {
        if (strpos($key, 'term_') === 0) {
          unset ($formVars[$key]);
        }
      }

      $maillist = false;
      if(isset($formVars["maillist"])) {
        $maillist = $formVars["maillist"];
        unset($formVars["maillist"]);
      }

      $formVars["ordusrid"] = $LoggedUser->usrid;
      $formVars["ordweight"] = $this->basketNamespace->weightSum;
      $formVars["ordcurid"] = $this->curId;

      if (!empty($this->basketNamespace->coupon["coucode"])) {
        //$formVars["orddisccoupon"] = (int)$this->basketNamespace->coupon["couvalue"];
        $formVars["ordcoucode"] = $this->basketNamespace->coupon["coucode"];
      }

      //zjistim jestli neni specialni doprava
      if (!is_numeric($formVars["orddelid"])) {
        //vyseparuju kod platby
        $arr = explode('_', $formVars["orddelid"]);
        $formVars["orddelid"] = (int)$arr[1];
        $formVars["ordpaycode"] = $arr[0];
      }
      $payMode = $dels->load($formVars["orddelid"]);
      if ($payMode) {
        $delMode = $dels->load($payMode->delmasid);
      }

      if (!$payMode || !$delMode) {
        $form->addError("Špatné zadání dopravy a platby");
      }
      if ($delMode->delcode === 'ULOZENKA' && empty($formVars["orddelspec"])) {
        $form->addError("Prosim vyberte výdejní místo");
      }
      if ($form->hasErrors()) {
        return;
      }
      $orders = $this->model->getOrdersModel();
      $orders->setCurrency($this->currencies, $this->curId);

      //ulozim hlavicku objednavky
      $ordid = $orders->insert($formVars);

      //do profilu nakopiruju posledni kontaktni udaje
      $usrs = $this->model->getUsersModel();
      if ($this->userData->usrid > 0) {
        $usr = $usrs->load($this->userData->usrid);
        $usrUpd = array();
        if (empty($usr->usriname)) {
          $usrUpd["usriname"] = $formVars["ordiname"];
          $usrUpd["usrilname"] = $formVars["ordilname"];
          $usrUpd["usrifirname"] = $formVars["ordifirname"];
          $usrUpd["usristreet"] = $formVars["ordistreet"];
          $usrUpd["usristreetno"] = $formVars["ordistreetno"];
          $usrUpd["usricity"] = $formVars["ordicity"];
          $usrUpd["usripostcode"] = $formVars["ordipostcode"];
          $usrUpd["usrtel"] = $formVars["ordtel"];
          $usrUpd["usric"] = $formVars["ordic"];
          $usrUpd["usrdic"] = $formVars["orddic"];
          if (empty($usr->usrstname) && !empty($formVars["ordstname"])) {
            $usrUpd["usrstname"] = $formVars["ordstname"];
            $usrUpd["usrstlname"] = $formVars["ordstlname"];
            $usrUpd["usrstfirname"] = $formVars["ordstfirname"];
            $usrUpd["usrststreet"] = $formVars["ordststreet"];
            $usrUpd["usrststreetno"] = $formVars["ordststreetno"];
            $usrUpd["usrstcity"] = $formVars["ordstcity"];
            $usrUpd["usrstpostcode"] = $formVars["ordstpostcode"];
          }
        }

        if ($maillist)  $usrUpd["usrmaillist"] = 1;

        if (count($usrUpd) > 0) $usrs->update($this->userData->usrid, $usrUpd);
      } else {
        if ($maillist) {
          $usrs = $this->model->getUsersModel();
          //zjistim jestli neni profil s timto mailem
          $usrid = dibi::fetchSingle("SELECT usrid FROM users WHERE usrmail=%s", trim($formVars["ordmail"]));
          if ($usrid > 0) {
            $usrs->update($usrid, array('usrmaillist'=>1));
          } else {
            $usrid = $usrs->insert(array('usrmail'=>trim($formVars["ordmail"]), 'usrmaillist'=>1));
          }
          $orders->update($ordid, array('ordusrid'=>$usrid));
        }
      }

      if ($ordid > 0) {
        $ordItems = $this->model->getOrdItemsModel();

        //vlozim polozky
        $product = $this->model->getProductsModel();
        $product->setPrcCat($LoggedUser->usrprccat);
        $product->setCurrency($this->currencies, $this->curId);
        foreach ($this->basketNamespace->items as $id => $cnt) {
          $ordItemsVals = array();
          $row = $product->load($id);
          if ($row->proid == $id && $row->prostatus == 0) {
            //polozka nalezena v db a je aktivni - vlozim mezi polozky objednavky
            $proprice = $this->getCouponProductPrice($row);

            $ordItemsVals['oriordid'] = $ordid;
            $ordItemsVals['oriproid'] = $id;
            $ordItemsVals['oriprocode'] = $row->procode;
            $ordItemsVals['oriprocode2'] = $row->procode2;
            $ordItemsVals['oritypid'] = 0;
            $ordItemsVals['oriname'] = $row->proname;
            $ordItemsVals['oriprice'] = (double)$proprice;
            $ordItemsVals['oripriceoriginal'] = $row->proprice;
            $ordItemsVals['orivatid'] = $row->provatid;
            $ordItemsVals['orivat'] = (int)(isset($this->config["VATTYPE_".$row->provatid]) ? $this->config["VATTYPE_".$row->provatid] : 0);
            $ordItemsVals['oricredit'] = $row->procredit;
            $ordItemsVals['oriqty'] = $cnt;
            $ordItemsVals['oriprobigsize'] = $row->probigsize;
            $ordItemsVals['oriprooffer'] = $row->prooffer;

            //zjistim jestli neni vyplneno proqty
            $vals = array();
            if ($row->proqty > 0) {
              //odečtu stav skladu
              $vals["proqty"] = MAX(($row->proqty - $cnt), 0);
              $product->update($id, $vals);
            }
            $ordItems->insert($ordItemsVals);
          }
          $ordItemsVals['oriprocode'] = Null;
          $ordItemsVals['oriprocode2'] = Null;
        }
        //vlozim postovne

        $payMode = $dels->load($formVars["orddelid"]);
        $delMode = $dels->load($payMode->delmasid);
        $delPrice = (double)$delMode->delprice + (double)$payMode->delprice;
        $ordItemsVals = array();
        $ordItemsVals['oriordid'] = $ordid;
        $ordItemsVals['oritypid'] = 1;
        $ordItemsVals['oriproid'] = 0;
        $ordItemsVals['oriname'] = "Doprava: ".$delMode->delname." - ".$payMode->delname;
        $ordItemsVals['oriprice'] = ($this->basketNamespace->specDel ? 0 : (double)$delPrice);
        $ordItemsVals['orivatid'] = 0;
        $ordItemsVals['orivat'] = $this->config["VATTYPE_0"];
        $ordItemsVals['oricredit'] = 0;
        $ordItemsVals['oriqty'] = 1;
        $ordItemsVals['oriprobigsize'] = 0;
        $ordItemsVals['oriprooffer'] = 0;
        $ordItems->insert($ordItemsVals);

        $orders->recalcOrder($ordid);
        $this->basketNamespace->ordid = $ordid;

        $order = $orders->load($ordid);

        //odmailuju
        $mailTemplate = $this->createTemplate();
        $mailTemplate->orderRow = $order;
        $delModes = $this->model->getDeliveryModesModel();
        $delModes->setCurrency($this->currencies, $order->ordcurid);

        $enums = $this->model->getEnumcatsModel();
        $mailTemplate->basket = $this->basketNamespace;
        $mailTemplate->payMode = $delModes->load($order->orddelid);
        $mailTemplate->delMode = $delModes->load($mailTemplate->payMode->delmasid);
        $mailTemplate->enum_ulozenka = $delModes->getEnumUlozenkaPlaces();
        $mailTemplate->ordItemRows = dibi::fetchAll("SELECT * FROM orditems WHERE oriordid=%i", $ordid, " ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
        $mailTemplate->ordSpecDel = ((int)dibi::fetchSingle("SELECT COUNT(*) FROM orditems WHERE oriordid=%i", $ordid, " AND oritypid=0 AND (oriprobigsize=1 OR oriprooffer=1)") > 0);
        $mailTemplate->key = substr(md5($ordid.$mailTemplate->orderRow->orddatec), 0, 4);
        $mailTemplate->setTranslator($this->translator);
        $mailTemplate->lang = $this->lang;
        $mailTemplate->user = $this->userData;
        $mailTemplate->enum_usrprccat = $this->getEnumPrcCat();

        $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailOrderCreated.latte');
        try {
          //mailuju zakaznikovi
          $mails = explode(',', $this->config["SERVER_MAILORDERS"]);
          $mailOrders = $mails[0];
          $this->mailSend($order->ordmail, "Objednávka č."." ".$order->ordcode, $mailTemplate, $mailOrders);
          //mailuju obchodnikovi
          foreach ($mails as $mail) {
            $this->mailSend($mail, "Nová objednávka č. ".$order->ordcode, $mailTemplate);
          }
        } catch (Nette\InvalidStateException $e) {
          $this->flashMessageErr("Nepodařilo se ale odeslat informační email o nové objednávce");
        }

        //zjistím způsob platby, pokud je platba předem nastavím staus čeká na platbu
        $payCode = (string)dibi::fetchSingle("SELECT delcode FROM deliverymodes WHERE delid=%i", $order->orddelid);
        if ($payCode === 'paybefore') {
          $orders->update($order->ordid, array('ordstatus'=>2));
          $orders->logStatus($order->ordid, 2, NULL);
          $order->ordstatus = 2;
          //odmailuju změnu stavu
          $mailTemplate = $this->createTemplate();
          $orderRow = $order;
          $mailTemplate->orderRow = $orderRow;
          $delModes = $this->model->getDeliveryModesModel();
          $delModes->setCurrency($this->currencies, $orderRow->ordcurid);
          $mailTemplate->payMode = $delModes->load($orderRow->orddelid);
          $mailTemplate->delMode = $delModes->load($mailTemplate->payMode->delmasid);
          $mailTemplate->enum_ordStatus = $orders->getEnumOrdStatus();
          $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailOrderChanged.latte');
          try {
            $this->mailSend($orderRow->ordmail, "Změna stavu objednávky č. ".$orderRow->ordcode, $mailTemplate);
          } catch (Nette\InvalidStateException $e) {
            $this->flashMessageErr("Nepodařilo se ale odeslat informační email o nové objednávce");
          }
        }

        //vymazu kosik
        $this->basketNamespace->remove();
        $this->basketNamespace = $this->getSession('basket');

        $key = $order->ordid.substr(md5($order->ordid . $order->orddatec->getTimestamp()), 0, 8);
        $this->redirect("Order:status", $key);
      }
    }
  }
}

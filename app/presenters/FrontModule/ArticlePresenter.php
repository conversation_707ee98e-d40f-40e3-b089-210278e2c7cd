<?php
namespace FrontModule;
use dibi;
use Nette;

final class ArticlePresenter extends BasePresenter {

  /** @persistent */
  public $tag;

  public function renderDefault($tag='') {
    $this->tag = $tag;

    $sql[] = "SELECT * FROM articles WHERE artstatus=0 ";

    if (!empty($tag)) {
      $sql[] = " AND arttags LIKE %~like~";
      $sql[] = "|" . $tag . "|";
    }

    $sql[] = " ORDER BY artdate DESC";

    $arts = $this->model->getArticlesModel();
    $dataSource = dibi::dataSource($sql);

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = 20;
    $paginator->itemCount = $dataSource->count();
    $this->template->rows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();

    $this->template->tags = $arts->genEnumTags();
    $this->template->currentTag = $this->tag;
  }

  public function renderDetailOld($id, $key) {
    $articles = $this->model->getArticlesModel();
    $articleData = $articles->load($id);
    if ($articleData) {
      //kontrola platnosti URL
      $urlkey = (!empty($articleData->arturlkey) ? $articleData->arturlkey : Nette\Utils\Strings::webalize($articleData->artname));
      //presmeruju na novy
      $this->redirect(301, 'Article:detail', array('id'=>$id, 'key'=>$urlkey));
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }
  }

  public function renderDetail($id, $key) {
    $articles = $this->model->getArticlesModel();
    $articleData = $articles->load($id);
    if ($articleData) {
      if ($articleData->artstatus == 1) throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
      if ($articleData->artid > 0) {
        $this->template->urlkey = $articleData->arturlkey;
        $this->template->article = $articleData;
      }
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }
    $articles->runCounter($articleData->artid);

    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $articleData->artid);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype IN ('jpg', 'png', 'gif')", $articleData->artid);

    if ($articleData->artid === 2) {
      $sql[] = "SELECT * FROM articles WHERE artstatus=0 AND arttypid=4 ORDER BY artdate DESC";

      $dataSource = dibi::dataSource($sql);

      $paginator = $this['paginator']->paginator;
      $paginator->itemsPerPage = 20;
      $paginator->itemCount = $dataSource->count();
      $this->template->rows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();


      $this->setView("recipes");
    } else if ($articleData->artid === 13) {

      $this->setView("kdeNasNajdete");

    } else if ($articleData->artid === 7) {

      $this->setView("dopravaPlatba");
    }

    if ($articleData->arttypid === 4) {
      $this->template->recipe = $articles->load(2);
    }
  }
}

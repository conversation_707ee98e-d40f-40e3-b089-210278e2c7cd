<?php
namespace FrontModule;
use dibi;
use Nette;

final class SearchPresenter extends BasePresenter {

  /** @persistent */
  public $name = '';

  /** @persistent */
  public $fulltext = '';

  /** @persistent */
  public $t1;

  /** @persistent */
  public $t2;

  /** @persistent */
  public $t3;

  /** @persistent */
  public $t = array(); //typy

  /** @persistent */
  public $m = array(); //vyrobce

  /** @persistent */
  public $pF; //cena od

  /** @persistent */
  public $pT; //cena do

  /** @persistent */
  public $o; //řazeni

  public function renderDefault () {
    $this->name = $this->getParameter("name");
    $this->fulltext = $this->getParameter("fulltext");
    //$this->m = $this->getParameter("m");
    //$this->s = $this->getParameter("s");
    $this->o = $this->getParameter('o');

    $where  = $this->getWhere();
    $orderBy = "";
    switch ($this->o) {
       case '':
         $orderBy = "proorder";
         break;
       case 'na':
         $orderBy = " proname ASC";
         break;
       case 'nd':
         $orderBy = " proname DESC";
         break;
       case 'pa':
         $orderBy = " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) ASC";
         break;
       case 'pd':
         $orderBy = " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) DESC";
         break;
    }

    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $product->setCurrency($this->currencies, $this->curId);
    $dataSource = $product->getDataSource($product->getSqlCatalogList(0, $where, $orderBy));

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $this->template->productsData = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();

    $paginatorTop = $this['paginatorTop']->paginator;
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginatorTop->itemCount = $paginator->itemCount;

    //naplnim filtr vybranymi hodnotami
    if (!empty($this->m)) $arr["m"] = $this->m;
    if (!empty($this->t)) $arr["t"] = $this->t;
    if (!empty($this->o)) $arr["o"] = $this->o;
    if (!empty($this->pF)) $arr["pF"] = $this->pF;
    if (!empty($this->pT)) $arr["pT"] = $this->pT;

    if (!empty($arr)) {
      $form = $this->getComponent("detailSearchForm");
      $form->setDefaults($arr);
    }

    $this->template->o = $this->o;

    //zakazu indexovani pokud filtruje, radi nebo strankuje
    if (!empty($arr) || $paginator->page > 1) {
      $this->template->pageRobots = "noindex,follow";
    }

  }

  public function renderSearchAc($q) {
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    $sql = $product->getSqlList(" prostatus=0 AND promasid=0 AND proname LIKE %~like~", $q, " proname ASC LIMIT 7");
    $this->template->productsData = dibi::fetchAll($sql);
  }

  protected function createComponentDetailSearchForm() {
    $form = $this->createAppForm();
    $form->addGroup();
    $form->addtext("name", "Název:", 40);
    $form->addtext("fulltext", "Fulltext:", 40);
    $where = $this->getWhere();
    $query[] = "SELECT manid, manname 
FROM products 
INNER JOIN manufacturers ON (manid=promanid)
WHERE ";
    $query = array_merge($query, $where);
    array_push($query, "GROUP BY manid
ORDER BY manname");

    $manufacts = dibi::fetchAll($query);
    $form->addGroup("Výrobci");
    $container = $form->addContainer('mans');
    foreach ($manufacts as $row) {
      $container->addCheckbox($row->manid, $row->manname);
    }
    $form->addCheckbox('onstock', "Skladem")
      ->setHtmlId("onstock");
    $form->addSubmit('detailSearch', 'Hledej')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();

      $this->name = $vals["name"];
      $this->fulltext = $vals["fulltext"];
      //zaloguju hledany text
      if (!empty($vals["fulltext"])) {
        $ftx = $this->model->getFulltextlogsModel();
        $ftx->insert(array('ftxtext'=>$vals["fulltext"]));
      }
      $this->s = $vals["onstock"];
      $this->m = array();
      foreach ($vals['mans'] as $key => $val) {
        if ($val) $this->m[$key] = $key;
      }
      $this->redirect('this');

      $this->redirect('Search:default');
    }
  }

  Private function getWhere($manOff = false) {
    $where = array("promasid=0 AND prostatus=0");
    if (!empty($this->name)) {
      array_push($where, " AND proname LIKE %~like~", $this->name);
    }
    if (!empty($this->fulltext)) {
      array_push($where, " AND (proname LIKE %~like~", $this->fulltext, " OR prodescs LIKE %~like~", $this->fulltext, ")");
    }
    if (count($this->m) > 0 && $manOff == false) {
      array_push($where, " AND promanid IN (%i)", $this->m);
    }


    if (count($where) === 0) array_push($where, "proid=-1");
    return $where;
  }
}

<?php
namespace FrontModule;
use dibi;
use Nette;
use <PERSON>\Debugger;

final class FioPaymentPresenter extends BasePresenter {
  private $fioEComConfig = array();
  private $fioECom;

  protected function startup() {
    parent::startup();
    $this->fioEComConfig = $this->neonParameters['fioECom'];
    $this->fioECom = new \fioECom($this->fioEComConfig["Id"], $this->fioEComConfig["CertPass"]);
  }

  public function renderPaymentOk() {
    $params = $_POST;
    Debugger::log($params);
    //oznacim obj jako uhrazenou
    if (empty($params["trans_id"])) {
      $this->template->msg = "Špatné volání stránky.";
      return;
    }
    $ordid = (int)dibi::fetchSingle("SELECT ordid FROM orders WHERE ordpaysesid=%s", $params["trans_id"]);
    if ($ordid > 0) {
      //zjistim vysledek transakce

      $ip = $_SERVER['REMOTE_ADDR'];
      $ret = "";
      if ($this->fioEComConfig["TransType"] == "sms") {
        $ret = $this->fioECom->getTransResult($params["trans_id"], $ip);
      }

      Debugger::log($ret);

      if (substr($ret,8,2)=="OK" || $this->fioEComConfig["TransType"] == "dms") {

        $vals = array(
          'ordstatus'=> 6,
          'ordpaystatus' => 1,
          'orddatepayed' => new \DateTime()
        );
        $ret = false;
        $ords = $this->model->getOrdersModel();
        $ret = $ords->update($ordid, $vals);
        $ords->logStatus($ordid, $vals["ordstatus"], 1, "FIO platba platební kartou");
        $this->template->msg = "Platba byla přijata a objednávka označena jako uhrazena.";
      } else {
        $rows = explode("\n", $ret);
        $arr = explode(": ", $rows[1]);
        if (isset($arr[1])) {
          $resultCode = $arr[1];
          $this->template->msg = $this->fioECom->getResponseMessage($resultCode);
        } else {
          $this->template->msg = "Nastala chyba platba nebyla přijata.";
        }
      }
    }
  }

  public function renderPaymentErr() {
    $this->template->msg = "Nastala chyba platba nebyla přijata.";
  }

  public function renderFioCloseDay() {
    $ret = $this->fioECom->closeDay();
    $this->template->ret = $ret;
  }

}

<?php
namespace FrontModule;

use dibi;
use Model;
use Nette;

abstract class BasePresenter extends \BasePresenter {
  const LOGIN_NAMESPACE = 'user';

  /** p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ý uživatel */
  protected $userData;

  /** ko<PERSON><PERSON> */
  protected $basketNamespace;

  /** app */
  protected $appNamespace;

  /** identita */
  protected $user;

  protected function startup() {
    parent::startup();

    //inicializace session aplikace
    $this->appNamespace = $this->getSession('app');
    $this->appNamespace->setExpiration(NULL, FALSE);
    if (!isset($this->appNamespace->countryRedirectStatus)) $this->appNamespace->countryRedirectStatus = '';
    if (!isset($this->appNamespace->countryCode)) $this->appNamespace->countryCode = $this->visitorCountryCode();

    //zjistím z jaké země přichází
    //nastavení přesměrování
    /*
     * rs = 'r' -> redirect na správný server
     * rs = 's' -> zůstat na aktuálním
     * rs = 'c' -> vymazat cookies
     */
    /*
    $rs = (string)$this->getParameter("rs");
    $this->template->rr = (string)$this->getParameter("rr");
    $couCode = $this->appNamespace->countryCode;

    $target = "";
    if ($rs == 'c') {
      $this->appNamespace->countryRedirectStatus = '';
      $rs = "";
    }
    */
    //\Tracy\Debugger::log("CouCode:$couCode|IP:".$ip."|Status:".$this->appNamespace->countryRedirectStatus);
    /*
    $pathAdd = '';
    if ($this->curId == 1 && $couCode == 'sk') {
      //nesedí země
      if (empty($rs)) {
        //není rs cookie - nastavím na přesměrování
        if ($this->appNamespace->countryRedirectStatus == "") {
          $this->appNamespace->countryRedirectStatus = 'r';
          $pathAdd = '?rr=1';
        }
      } else {
        $this->appNamespace->countryRedirectStatus = $rs;
      }
      $target = "https://www.xxx.sk";
    } else if ($this->curId == 2 && $couCode == 'cz') {
      if (empty($rs)) {
        //není rs cookie - nastavím na přesměrování
        if ($this->appNamespace->countryRedirectStatus == "") {
          $this->appNamespace->countryRedirectStatus = 'r';
          $pathAdd = '?rr=1';
        }
      } else {
        $this->appNamespace->countryRedirectStatus = $rs;
      }
      $target = "https://www.xxx.cz";
    }
    if (!empty($target) && $this->appNamespace->countryRedirectStatus == 'r') {
      //přesměruji
      $path = $this->getHttpRequest()->getUrl()->getPath();
      $this->redirectUrl($target.$path.$pathAdd);
    }
    */

    //ulozim si do sablony zda je prihlaseny admin
    $admin = $this->getUser();
    $admin->getStorage()->setNamespace('admin');
    $this->template->adminLogIn = $admin->isLoggedIn();

    //inicializace kosiku
    $this->basketNamespace = $this->getSession('basket');
    $this->basketNamespace->setExpiration(NULL, FALSE);
    if (!isset($this->basketNamespace->items)) $this->basketNamespace->items = array();
    if (!isset($this->basketNamespace->coupon)) $this->basketNamespace->coupon = array();
    if (!isset($this->basketNamespace->sessionMail)) $this->basketNamespace->sessionMail = "";
    if (!isset($this->basketNamespace->priceSum)) $this->basketNamespace->priceSum = 0;
    if (!isset($this->basketNamespace->priceSumTotal)) $this->basketNamespace->priceSumTotal = 0;
    if (!isset($this->basketNamespace->delFree)) $this->basketNamespace->delFree = FALSE;
    if (!isset($this->basketNamespace->payFree)) $this->basketNamespace->payFree = FALSE;
    if (!isset($this->basketNamespace->compare)) $this->basketNamespace->compare = array();
    if (!isset($this->basketNamespace->visited)) $this->basketNamespace->visited = array();
    if (!isset($this->basketNamespace->products)) $this->basketNamespace->products = array();

    //nactu uzivatele
    $this->user = $this->getUser();
    $this->user->getStorage()->setNamespace(self::LOGIN_NAMESPACE);
    $this->template->identity = $this->user;

    $this->userData = false;
    if ($this->user->isLoggedIn()) {
      $users = $this->model->getUsersModel();
      $this->userData = $users->load($this->user->id);
    }

    if ($this->userData == false) {
      $this->userData = new \DibiRow(array('usrid'=>0, 'usrprccat'=>'a', 'usrmail'=>''));
      if ($this->user->isLoggedIn()) $this->user->logout();
    }
    $this->template->userRow = $this->userData;
  }

  public function handleBasketRefresh() {
    $this->recalc();
    $this->redrawControl("basketWindow");
  }

  protected function beforeRender() {
    $this->template->showMenuLeft = FALSE;

    parent::beforeRender();
    $pros = $this->model->getProductsModel();
    $arts = $this->model->getArticlesModel();

    //články z blogu do patičky
    $lastBlogs = $arts->cacheGet('lastBlogs');
    if ($lastBlogs === FALSE) {
      $lastBlogs = dibi::fetchAll("SELECT * FROM articles WHERE arttypid=1 AND artstatus=0 ORDER BY artdate DESC");
      $arts->cacheSave('lastBlogs', $lastBlogs);
    }
    $this->template->blogs = $lastBlogs;

    $homepageProducts = $pros->cacheGet('homepageProducts');
    if ($homepageProducts === FALSE) {
      $homepageProducts = array();
      //z nastaveni nactu kody zbozi
      $proCodesList = explode(',',$this->config["INDEX_PRODUCTLIST"]);
      $cnt = 0;
      foreach ($proCodesList as $proCode) {
        $proCode = trim($proCode);
        if (!empty($proCode)) {
          $item = $pros->load($proCode, 'code');
          if ($item) {
            $homepageProducts[] = $item;
            $cnt ++;
          }
        }
      }
      $pros->cacheSave('homepageProducts', $homepageProducts);
    }
    $this->template->homepageProducts = $homepageProducts;


    $this->template->basket = $this->basketNamespace;
    $this->template->basketPriceSum = $this->basketNamespace->priceSumVat;
    $this->template->basketItemsCnt = $this->basketNamespace->itemsCnt;
    $this->template->basketProducts = $this->basketNamespace->products;

    //nactu menu katalogu
    $catalog = $this->model->getCatalogsModel();
    $menuCatalog = $catalog->cacheGet('menuCatalog');
    if ($menuCatalog === FALSE) {
      //cache neni musim naplnit
      $menuCatalog = dibi::query("SELECT catid, catmasid, catlevel, catname, catkey, catclass FROM catalogs WHERE catmasid=0 AND catstatus=0 ORDER BY catorder")->fetchAssoc('catid');
      $catalog->cacheSave('menuCatalog', $menuCatalog);
    }
    $this->template->menuCatalog = $menuCatalog;

    $menu = $this->model->getMenusModel();
    $menuTopLeft = $menu->cacheGet('menuTopLeft');
    if ($menuTopLeft === FALSE) {
      //cache neni musim naplnit
      $menuTopLeft = dibi::fetchAll("SELECT * FROM menus LEFT JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=5 AND menstatus=0 ORDER BY menorder");
      $menu->cacheSave('menuTopLeft', $menuTopLeft);
    }
    $this->template->menuTopLeft = $menuTopLeft;

    $menuTopRight = $menu->cacheGet('menuTopRight');
    if ($menuTopRight === FALSE) {
      //cache neni musim naplnit
      $menuTopRight = dibi::fetchAll("SELECT * FROM menus LEFT JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=6 AND menstatus=0 ORDER BY menorder");
      $menu->cacheSave('menuTopRight', $menuTopRight);
    }
    $this->template->menuTopRight = $menuTopRight;

    $menu = $this->model->getMenusModel();
    $menuFooter = $menu->cacheGet('menuFooter');
    if ($menuFooter === FALSE) {
      //cache neni musim naplnit
      $menuFooter = dibi::fetchAll("SELECT * FROM menus LEFT JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=1 AND menstatus=0 ORDER BY menorder");
      $menu->cacheSave('menuFooter', $menuFooter);
    }
    $this->template->menuFooter = $menuFooter;

    $menu = $this->model->getMenusModel();
    $menuShopInformation = $menu->cacheGet('menuShopInformation');
    if ($menuShopInformation === FALSE) {
      //cache neni musim naplnit
      $menuShopInformation = dibi::fetchAll("SELECT * FROM menus INNER JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=1 AND menstatus=0 ORDER BY menorder");
      $menu->cacheSave('menuShopInformation', $menuShopInformation);
    }
    $this->template->menuShopInformation = $menuShopInformation;

    //nactu textove bloky
    $page = $this->model->getPagesModel();
    $textBlocks = $page->cacheGet('textBlocks');
    if ($textBlocks === FALSE) {
      //cache neni musim naplnit
      $textBlocks = dibi::query("SELECT * FROM pages WHERE pagblock=1 AND pagstatus=0")->fetchPairs('pagurlkey', 'pagbody');
      $page->cacheSave('textBlocks', $textBlocks);
    }
    $this->template->textBlocks = $textBlocks;

    //nactu posledni clanky do paticky - Zajimavosti
    $arts = $this->model->getArticlesModel();
    $footerArticles = $arts->cacheGet('footerArticle');
    if ($footerArticles === FALSE) {
      //cache neni musim naplnit
      $footerArticles = dibi::fetchAll("SELECT * FROM articles WHERE artstatus=0 ORDER BY artdate DESC LIMIT 5");
      $arts->cacheSave('footerArticle', $footerArticles);
    }
    $this->template->footerArticles = $footerArticles;

    //nactu minimalni cenu zbozi kdy by mel narok na dopravu zdarma
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $this->template->delFreeLimit = $dels->getDelFreeLimit($this->userData->usrprccat);

    //nactu vyrobce

    $mans = $this->model->getManufacturersModel();
    $manufacturers = $mans->cacheGet('manufacturers');
    if ($manufacturers === FALSE) {
      //cache neni musim naplnit
      $manufacturers = dibi::fetchAll("SELECT * FROM manufacturers WHERE manstatus=0 ORDER BY manname");
      $mans->cacheSave('manufacturers', $manufacturers);
    }
    $this->template->manufacturers = $manufacturers;


    //TOP 10 - nejhledanější patička
    $ftxs = $this->model->getFulltextlogsModel();
    $fulltextSearchs = $ftxs->cacheGet('fulltextSearchs');
    if ($fulltextSearchs === FALSE) {
      //cache neni musim naplnit
      $fulltextSearchs = dibi::fetchAll("SELECT ftxtext FROM fulltextlogs GROUP BY ftxtext ORDER BY COUNT(ftxtext) DESC LIMIT 10");
      $ftxs->cacheSave('fulltextSearchs', $fulltextSearchs);
    }
    $this->template->fulltextSearchs = $fulltextSearchs;

    //naposledy navstivene zbozi
    $this->template->lastVisited = array();
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    $proVisited = array();
    //prehodim poradi prvku
    $arr = array_reverse($this->basketNamespace->visited);
    foreach ($arr as $proid) {
      $p = $pros->load($proid);
      if ($p) {
        $proVisited[] = $p;
      }
      if (count($proVisited) == 4)  break;
    }
    $this->template->proVisited = $proVisited;
    $cmts = $this->model->getCommentsModel();
    $this->template->enu_cmtcatid = $cmts->getEnumCmtCatId();

    //načtu 3 posledni komentare
    $footerComments = $cmts->cacheGet('footerComments');
    if ($footerComments === FALSE) {
      //cache neni musim naplnit
      $footerComments = dibi::fetchAll("SELECT * FROM comments WHERE cmtreid=0 ORDER BY cmtid DESC LIMIT 3");
      $cmts->cacheSave('footerComments', $footerComments);
    }
    $this->template->footerComments = $footerComments;

    //TOP 10 - nejsledovanější patička
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    $footerTopProducts = $product->cacheGet('footerTopProducts');
    if ($footerTopProducts === FALSE) {
      $footerTopProducts = array();
      //z nastaveni nactu kody zbozi
      $proCodesList = explode(',', $this->config["FOOTER_FAVPRODUCTIDS"]);
      $cnt = 0;
      foreach ($proCodesList as $proid) {
        $proid = trim($proid);
        if (!empty($proid)) {
          $item = $product->load($proid);
          if ($item) {
            $footerTopProducts[] = $item;
            $cnt ++;
          }
        }
        if ($cnt == 10) break;
      }
      $product->cacheSave('footerTopProducts', $footerTopProducts);
    }
    $this->template->footerTopProducts = $footerTopProducts;

    //nactu polozky do reklamy v paticce
    $menuIndexs = $this->model->getMenuIndexsModel();
    $footerAdd = $menuIndexs->cacheGet('footerAdd');
    if ($footerAdd === FALSE) {
      $footerAdd = dibi::fetch("
        SELECT *
        FROM menuindexs
        LEFT JOIN pages ON (pagid=meipagid)
        LEFT JOIN catalogs ON (catid=meicatid)
        LEFT JOIN products ON (procode=meiprocode)
        WHERE meimasid=0 AND meistatus=0 AND meitarget='footer'
        GROUP BY meiid
        ORDER BY RAND() LIMIT 1");
      $menuIndexs->cacheSave('footerAdd', $footerAdd);
    }
    $this->template->footerAdd = $footerAdd;

    //Parametry z NEON nastaveni
    if (isset($this->neonParameters)) $this->template->neonParameters = $this->neonParameters;

    //nastavím eCommerce proměnné
    $this->template->eComm = array(
      'proIds' => array(),
      'pageType' => '',
      'totalValue' => 0,
    );

    //načtu fotky z intagramu
    $instangramConfig = $this->neonParameters["instagram"];
    $userId = $instangramConfig["userId"];
    $accessToken = $instangramConfig["accessToken"];

    if (!empty($userId)) {

      $storage = new Nette\Caching\Storages\FileStorage(TEMP_DIR);
      $cache = new Nette\Caching\Cache($storage);

      $result = $cache->load('instagram');

      if ($result === null) {
        try {
          $result = @file_get_contents("https://api.instagram.com/v1/users/$userId/media/recent/?access_token=$accessToken");
          $result = json_decode($result);
          $cache->save('instagram', $result, [
            Nette\Caching\Cache::EXPIRE => '6 hours',
          ]);
        } catch (Exception $e) {

        }
      }

      $limit = 5;
      $i = 0;
      $instagramImages = array();
      if (!empty($result)) {
        foreach ($result->data as $post) {
          if ($i < $limit) {
            $instagramImages[] = array(
              'small' =>$post->images->thumbnail->url,
              'big' =>$post->images->standard_resolution->url,
              'link' =>$post->link,
              'caption' =>$post->caption->text,
            );
          }
          $i++;
        }
      }
      $this->template->instagramImages = $instagramImages;
    }
  }



  public function mainSearchFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $searchParam = array('name'=>'');
      if (isset($vals["fulltext"])) {
        //zaloguju hledany text
        if (!empty($vals["fulltext"])) {
          $ftx = $this->model->getFulltextlogsModel();
          $ftx->insert(array('ftxtext'=>$vals["fulltext"]));
        }
        $searchParam["name"] = $vals["fulltext"];
      }
      $this->redirect('Search:default', $searchParam);
    }
  }

  protected function createComponentSearchForm() {
    $form = $this->createAppForm();
    $form->addtext("fulltext", "", 10)
      ->addRule(Nette\Forms\Form::FILLED, "Vyplňte hledaný text");
    //$form->addImage('quickSearch');
    $form->addSubmit('quickSearch', 'Hledat');

    $button = $form['quickSearch']->getControlPrototype();
    $button->setName('button');

    $form->onSuccess[] = array($this, 'mainSearchFormSubmitted');
    return $form;
  }

  protected function createComponentUserLoginForm() {
    //prihlasovaci form
    $form = $this->createAppForm();
    $form->addText('usrmail', 'Přihlašovací jméno (Váš email)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).');
    $form->addPassword('usrpassw', 'Heslo')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.');
    $form->addSubmit('submit', 'Přihlásit')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'loginFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  public function loginFormSubmitted(Nette\Application\UI\Form $form) {
    $usrs = $this->model->getUsersModel();
    try {
      //nejdříve zkontroluji, zda má nastaveno heslo
      $formVars = $form->getValues();
      $usr = dibi::fetch('SELECT usrid, usrmail, usrpassw, usrmailvcode FROM users WHERE  usrmail=%s', $formVars->usrmail);
      if ($usr && $usr->usrpassw === '') {
        $data = array();
        //přesměruji na průvodce
        //nastavíme verifikační kod
        if (empty($usr["usrmailvcode"])) {
          $data['usrmailvcode'] = $this->getVerifyCode();
        }
        $data['usrmailverified'] = 0;
        $usrs->update($usr->usrid, $data);
        //pošlu email
        $mailTemplate = $this->createTemplate();
        $usr = $usrs->load((int)$usr->usrid);
        $mailTemplate->user = $usr;
        $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/userVerifyAccount.latte');

        if ($this->mailSend($usr->usrmail, 'Aktivace vašeho účtu', $mailTemplate)) {
          //zaloguji odeslání žádosti
          $usrs->logEvent($usr->usrid, Model\UsersModel::EVENT_MAIL_VERIFICATION_SEND);
          $this->flashMessage('Aktivační email byl úspěšně odeslán.');
          $key = substr(md5($usr->usrid . $usr->usrmailvcode), 0, 6);
          $this->redirect('User:verifyAccount', $usr->usrid, $key);
        } else {
          $this->flashMessageErr('Aktivační email se nepodařílo odeslat. Zkuste to prosím později, nebo nás kontaktujte.');
          $this->redirect('Homepage:default');
        }
      }
      $this->user->login($formVars->usrmail, $formVars->usrpassw, self::LOGIN_NAMESPACE);
      $this->user = $this->getUser();
      if ($this->user->isLoggedIn()) {
        $users = $this->model->getUsersModel();
        $usr = $users->load($this->user->id);
      }
      $this->recalc();

      $this->redirect('User:default');
    } catch (Nette\Security\AuthenticationException $e) {
      $this->flashMessageErr($e->getMessage());
      $this->redirect('User:login');
    }
  }

  /**
   * @param $proid
   * @param $qty
   *
   * přidá položku do košíku, upraví množství, nebo vymaže ($qty == 0)
   */
  protected function basketEditItem($proid, $qty) {
    if ($qty == 0) {
      unset($this->basketNamespace->items[$proid]);
    } else {
      $this->basketNamespace->items[$proid] = $qty;
      if (empty($this->userData->usrmail) && !empty($this->basketNamespace->sessionMail)) $email = $this->basketNamespace->sessionMail;

      //pokud se jedná o robota neudělám nic
      $ret = preg_match('/bot|spider|crawler|curl|^$/i', $_SERVER['HTTP_USER_AGENT']);
      if ($ret == 1) return;
    }
  }

  /**
   * @param $proid
   * @param $qty
   *
   * přidá položku do košíku, upraví množství, nebo vymaže ($qty == 0)
   */
  protected function basketDeleteItem($proid) {
    unset($this->basketNamespace->items[$proid]);
    if (!empty($this->basketNamespace->coupon->products[$proid])) $this->basketNamespace->coupon->products[$proid]["used"] = FALSE;
  }

  protected function getCouponProductPrice($product) {
    $proprice = $product->proprice;
    if (isset($this->basketNamespace->coupon->products[$product->proid]))  {
      $proprice = $product->proprice - $this->basketNamespace->coupon->products[$product->proid]["prodiscount"];
    }
    return (double)$proprice;
  }


  /**
  * prepocita obsah kosiku
  */
  public function recalc() {
    //kontrola zda vsechny polozky existuji - nejsou vyrazeny z nabidky
    $priceSum = 0;
    $priceSumVat = 0;
    $priceSumTotal = 0;
    $priceSumTotalVat = 0;
    $priceSumVatDisc = 0;
    $weightSum = 0;
    $products = array();
    $items = 0;
    $eggTypeCnt = 0;

    $vatType = (string)$this->config["PRICEVAT"];

    $coupon = $this->basketNamespace->coupon;

    $this->basketNamespace->specDel = false;

    //povolené dopravy pro položky v košíku
    $this->basketNamespace->delIds = array();
    $this->basketNamespace->delCodes = array();
    $delIdsAll = array();
    $proDelIds = array();

    //datum od do kdy je možno produkt objednat
    $this->basketNamespace->delDateFrom = NULL;
    $this->basketNamespace->delDateTo = NULL;

    $this->basketNamespace->delFree = false;
    $this->basketNamespace->payFree = false;

    if (count($this->basketNamespace->items) > 0) {
      $product = $this->model->getProductsModel();
      $product->setPrcCat($this->userData->usrprccat);
      $product->setCurrency($this->currencies, $this->curId);
      $setunset = false;
      foreach ($this->basketNamespace->items as $key => $value) {
        $row = $product->load($key);
        if (!$row) {
          $this->basketDeleteItem($key);
          continue;
        }
        if (isset($coupon->products[$row->proid])) {
          $coupon->products[$row->proid]["used"] = TRUE;
          $coupon->products[$row->proid]["proname"] = $row->proname;
          $coupon->products[$row->proid]["proprice"] = $row->proprice;
          $row->proprice = $this->getCouponProductPrice($row);
        }
        if ($row->proid == $key && $row->prostatus == 0) {
          //polozka nalezna v db a je aktivni
          $products[$key] = $row;
          //zkontroluju pocet skladem
          if ($this->config["CHECK_STOCK"] == 1) {
            if ((int)$value > (int)$row->proqty) {
              if ((int)$row->proqty == 0) {
                $this->basketDeleteItem($row->proid);
                $setunset = true;
              } else {
                $value = (int)$row->proqty;
                $this->basketNamespace->items[$key] = $value;
                $this->flashMessageErr("U položky " . $row->proname . " je skladem pouze " . $row->proqty . "ks. Počet kusů ve vaší objednávce byl snížen.");
              }
            }
          } else if ((int)$row->proqty > 0) {
            if ($row->proqty < (int)$value) {
              $this->basketNamespace->items[$key] = $row->proqty;
              $this->flashMessageErr("Skladem je dostupné pouze ".$row->proqty." kusů. Počet kusů ve vaší objednávce byl snížen.");
            }
          }

          $weightSum += $row->proweight * $value;
          $items += $value;
          $itemPriceSumVat = 0;

          if ($vatType == 'inclvat') {
            $itemPriceSumVat = $row->proprice * $value;
            $vatLevel = (int)$this->config["VATTYPE_".$row->provatid];
            $priceSum += $row->proprice / (1+($vatLevel / 100)) * $value;
          } else {
            $priceSum += $row->proprice * $value;
            $itemPriceSumVat = $this->getPriceVat($row->proprice, $row->provatid) * $value;
          }
          $priceSumVat += $itemPriceSumVat;
          if ($row->pronotdisc == 0) {
            $priceSumVatDisc += $itemPriceSumVat;
          }

          //zjistím, jestli u produktu je nějaké omezení dopravy
          if (!empty($row->prodelids)) {
            $arr = explode(",", $row->prodelids);
            foreach ($arr as $delId) {
              $delIdsAll[$delId] = $delId;
              $proDelIds[$row->proid][$delId] = $delId;
            }
          }

          //zjistim jestli není vyplněno datum od / do
          if (!empty($row->prodatefrom)) {
            if (!empty($this->basketNamespace->delDateFrom)) {
              $this->basketNamespace->delDateFrom = dibi::fetchSingle("SELECT IF(DATE('" . $this->basketNamespace->delDateFrom . "')>DATE('" . $row->prodatefrom . "'), DATE('" . $this->basketNamespace->delDateFrom . "'), DATE('" . $row->prodatefrom . "'))");
            } else {
              $this->basketNamespace->delDateFrom = $row->prodatefrom;
            }
          }
          if (!empty($row->prodateto)) {
            if (!empty($this->basketNamespace->delDateTo)) {
              $this->basketNamespace->delDateTo = dibi::fetchSingle("SELECT IF(DATE('" . $this->basketNamespace->delDateTo . "')<DATE('" . $row->prodateto . "'), DATE('" . $this->basketNamespace->delDateTo . "'), DATE('" . $row->prodateto . "'))");
            } else {
              $this->basketNamespace->delDateTo = $row->prodateto;
            }
          }

        } else {
          //polozka nenalezena nebo neni aktivni
          $setunset = true;
          $this->basketDeleteItem($row->proid);
        }

        //pokud ma polozka dopravu zdarma nebo z kuponu ma ji cely kosik
        if (($row->prodelfree === 1 && ($this->userData->usrprccat === 'a')) || !empty($coupon->coudelfree)) {
          $this->basketNamespace->delFree = TRUE;
        }
        //platba  zdarma z kuponu
        if (!empty($coupon->coupayfree)) {
          $this->basketNamespace->payFree = TRUE;
        }

        //počet objednaných kusů
        if (!empty($this->basketNamespace->items[$key])) {
          $products[$key]->qty = $this->basketNamespace->items[$key];
        }

        if ($this->isEgg($row)) {
          $eggTypeCnt++;
        }
      }
      //zjistim slevu - sleva se pocita jen pro cenovou kategorii A
      $discount = (double)0;

      //zjistim slevu nastavenou zakaznikovi
      if ($this->userData->usrid > 0) {
        $discount = (double)$this->userData->usrdiscount;
      }

      if (!empty($this->basketNamespace->coupon["couvalue"])) {
        if ($coupon["couvalueunit"] == 'Kč') {
          $this->basketNamespace->discountPer = 0;
          $this->basketNamespace->discountVal = round((double)$coupon["couvalue"], 0);
        }  else if ($coupon["couvalueunit"] == '%') {
          $this->basketNamespace->discountPer = $coupon["couvalue"];
          $this->basketNamespace->discountVal = round($priceSumVatDisc * ($coupon["couvalue"] / 100), 0);
        }
      } else {
        //zjistim mnozstevni slevu
        $disc = (double)dibi::fetchSingle("SELECT dispercent FROM discounts WHERE distypid='volume' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, " AND $priceSumVatDisc BETWEEN disfrom AND disto AND disstatus=0");
        if ($disc > $discount) $discount = $disc;

        $this->basketNamespace->discountPer = $discount;
        $this->basketNamespace->discountVal = round($priceSumVatDisc * ($discount / 100), 0);
      }

      if ($vatType == 'inclvat' && $this->basketNamespace->discountVal > 0) {
        $priceSumTotal = $priceSum - ($this->basketNamespace->discountVal / (1+($vatLevel / 100)));
        $priceSumTotalVat = $priceSumVat - $this->basketNamespace->discountVal;
      } else {
        $priceSumTotal = $priceSum - $this->basketNamespace->discountVal;
        $priceSumTotalVat = $priceSumVat - $this->getPriceVat($this->basketNamespace->discountVal, 0);
      }

      if ($setunset) $this->flashMessage("Některé položky byly z košíku odstraněny, neboť byly vyřazeny z nabídky, nebo již nejsou skladem.");
    }

    //zjistím průnik doprav zboží co je v košíku
    if (count($proDelIds)) {
      foreach ($proDelIds as $proDels) {
        $delIdsAll = array_intersect_key($delIdsAll, $proDels);
      }
    }

    //povolené dopravy pro poloýky v košíku
    $this->basketNamespace->delIds = $delIdsAll;
    if (count($delIdsAll) > 0) {
      $this->basketNamespace->delCodes = dibi::fetchPairs("SELECT delcode, delid FROM deliverymodes WHERE delid in (%i)", $delIdsAll);
    }

    if(!empty($this->basketNamespace->contact["orddelid"]) && $this->basketNamespace->specDel == False) {
      //pitva orddelid
      $orddelid = $this->basketNamespace->contact["orddelid"];
      if (!is_numeric($orddelid)) {
        $arr = explode('_', $orddelid);
        $orddelid = 0;
        if (!empty($arr[1])) $orddelid = (int)$arr[1];
      }

      //zjistim cenu platby
      $delprice = 0;
      $pay = dibi::fetch("
        SELECT delmasid, IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSumVatDisc.", 0, delprice".$this->curId.$this->userData->usrprccat.") AS delprice
        FROM deliverymodes
        LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
        WHERE delid=%i", $orddelid
      );
      if ($pay) {
        $delprice = $pay->delprice;

        //zjistim cenu dopravy
        $delivery = dibi::fetch("
          SELECT delmasid, delcode, ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSumVatDisc.", 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice
          FROM deliverymodes
          LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
          WHERE delid=%i", $pay->delmasid
        );
        if ($delivery) {
          if ($delivery->delcode === "VLASTNI_PREPRAVA") {
            $dels = $this->model->getDeliveryModesModel();
            $this->basketNamespace->priceSumVat = $priceSumVat;
            $delivery = $dels->getDelMode($pay->delmasid, $this->basketNamespace);
          }

          $delprice = $delivery->delprice;
        }
      }

      $priceSumTotal += $delprice;
      $priceSumTotalVat += $delprice;
    }

    $this->basketNamespace->coupon = $coupon;

    $this->basketNamespace->priceSum = $priceSum;
    $this->basketNamespace->priceSumVat = $priceSumVat;
    $this->basketNamespace->priceSumTotal = $priceSumTotal;
    $this->basketNamespace->priceSumTotalVat = $priceSumTotalVat;
    $this->basketNamespace->priceSumVatDisc = $priceSumVatDisc;
    $this->basketNamespace->weightSum = $weightSum;
    $this->basketNamespace->itemsCnt = $items;
    $this->basketNamespace->products = $products;

    if ($eggTypeCnt === 0) {
      $this->basketNamespace->eggTypeOrder = "noegg";
    } else if ($eggTypeCnt > 0) {
      $this->basketNamespace->eggTypeOrder = (count($this->basketNamespace->items) > $eggTypeCnt ? "mix" : "onlyegg");
    }
  }

  public function flashMessage($message, $type = "info") {
    if (is_array($message)) {
      $cnt = 1;
      $tMessage = "";
      foreach ($message as $key => $value) {
        if ($cnt % 2 == 0) {
          $tMessage .= ' '.$value.' ';
        } else {
          $tMessage .= $this->translator->translate($value);
        }
        $cnt++;
      }
    } else {
      $tMessage = $this->translator->translate($message);
    }
    return parent::flashMessage($tMessage, $type);
  }

  protected function createAppForm() {
    $form = new Nette\Application\UI\Form();
    $form->setTranslator($this->translator);
    return $form;
  }

  public function contactFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();
      unset($formVals["antispam"]);
      $id = $this->getParameter('id');
      $pages = $this->model->getPagesModel();
      $pageData = $pages->load($id, 'urlkey');
      $body = 'Nový dotaz, vzkaz:<br />
      '.($pageData ? 'Stránka: '.$pageData->pagname.' ('.$this->presenter->link('//:Front:Page:detail', $id).')<br />' :'').'
      Jméno, Přijmení: '.$formVals["conname"].'<br />
      Email: '.$formVals["conmail"].'<br />
      Mobil: '.$formVals["congsm"].'<br />
      Poznámka: <br />
      '.nl2br($formVals["connote"]);
      $mail = new Nette\Mail\Message();
      $mail->setFrom($this->config["SERVER_NAME"].' <'.$this->config["SERVER_MAIL"].'>');
      $mail->addReplyTo($formVals["conmail"]);
      $mail->addTo($this->config["SERVER_MAIL"]);
      $mail->setSubject('Nový dotaz, vzkaz');
      $mail->setHtmlBody($body);
      try {
        // Použití SMTP maileru
        $this->mailMail($mail);
        $this->flashMessage("Váš vzkaz byl přijat. Děkujeme!");
        $this->redirect('this');
      } catch (Nette\InvalidStateException $e) {
        $someerr = true;
        $form->addError("Vzkaz se nepodařilo odeslat.");
      }
    }
  }

  protected function createComponentContactForm() {
    $form = $this->createAppForm();

    $form->addText('conname', 'Jméno, příjmení', 30);

    $form->addText('conmail', "Platná emailová adresa (nutno vyplnit)", 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte platný email')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát.');

    $form->addText('congsm', 'Telefon (mobil) pro rychlejší kontakt', 20);

    $form->addTextArea('connote', "Poznámka, dotaz či přání", 50, 8);

    $form->addReCaptcha('recaptcha', $required = FALSE, $message = 'Potvrďte prosím, že nejste robot.');

    //$form->setRenderer(new ScaffoldingRenderer);

    $form->addSubmit('save', 'Odeslat')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'contactFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }

  public function visitorCountryCode() {
    $ip = $_SERVER["REMOTE_ADDR"];
    if(filter_var(@$_SERVER['HTTP_X_FORWARDED_FOR'], FILTER_VALIDATE_IP))
      $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    if(filter_var(@$_SERVER['HTTP_CLIENT_IP'], FILTER_VALIDATE_IP))
      $ip = $_SERVER['HTTP_CLIENT_IP'];

    $file = @file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $ip);
    $arr =  unserialize($file);
    $result = (string)$arr["geoplugin_countryCode"];

    return strtolower($result);
  }

  /**
   * vrací data pro našeptávač PSČ
   *
   * @param $query
   * @throws \Dibi\Exception
   */
  public function renderPostCodesAc($query) {
    $this->template->setFile(APP_DIR . "/../templates/json/postCodesAc.latte");
    $this->template->productsData = dibi::fetchAll("SELECT pocpostcode, pocplace1 FROM postcodes WHERE pocpostcode LIKE %~like~", $query, "OR pocplace1 LIKE %~like~", $query);
  }
/**
  * Create items paginator
  *
  * @return \IPub\VisualPaginator\Components\Control
  */
  function createComponentPaginator(){
    // Init visual paginator
		$control = new \IPub\VisualPaginator\Components\Control;
		$control->setTemplateFile(WWW_DIR . '/../templates/FrontModule/@pagination.latte');
		$control->disableAjax();
		return $control;
  }

  function createComponentPaginatorTop(){
    // Init visual paginator
		$control = new \IPub\VisualPaginator\Components\Control;
		$control->setTemplateFile(WWW_DIR . '/../templates/FrontModule/@paginationTop.latte');
		$control->disableAjax();
		return $control;
  }

}

<?php
namespace FrontModule;
use dibi;
use Model\ProductsModel;
use Nette;

final class CatalogPresenter extends BasePresenter {
  /** @persistent */
  public $t1;

  /** @persistent */
  public $t2;

  /** @persistent */
  public $t3;

  /** @persistent */
  public $t = array(); //typy

  /** @persistent */
  public $m = array(); //vyrobce

  /** @persistent */
  public $pF; //cena od

  /** @persistent */
  public $pT; //cena do

  /** @persistent */
  public $o; //řazeni

  public $productsRows = array();


  public function renderDefault() {
    $pros = $this->model->getProductsModel();
    $this->template->catalogMainItems = dibi::query("SELECT * FROM catalogs WHERE catmasid=0 AND catstatus=0 ORDER BY catorder")->fetchAssoc("catid");
    $this->template->productsData = dibi::fetchAll($pros->getSqlCatalogList(0, array("prostatus=0"), "proorder"));
  }

  public function renderDetailOld($id, $key) {
    $catalog = $this->model->getCatalogsModel();

    //aktualni polozka katalogu
    $catalogData = $catalog->load($id);
    if ($catalogData === false) {
      throw new Nette\Application\BadRequestException('Katalog nenalezen', '404');
    }

    //kontrola platnosti URL
    $urlkey = (!empty($catalogData->catkey) ? $catalogData->catkey : Nette\Utils\Strings::webalize($catalogData->catname));
    //presmeruju na novy
    $this->redirect(301, 'Catalog:detail', array('id'=>$id, 'key'=>$urlkey));
  }

  public function renderDetail($id, $key) {
    $catalog = $this->model->getCatalogsModel();

    //aktualni polozka katalogu
    $catalogData = $catalog->load($id);

    if ($catalogData === false) {
      throw new Nette\Application\BadRequestException('Katalog nenalezen', '404');
    }

    //kontrola platnosti URL
    $urlkey = (!empty($catalogData->catkey) ? $catalogData->catkey : Nette\Utils\Strings::webalize($catalogData->catname));
    //pokud se zmenil klic presmeruju na novy
    if ($key !== $urlkey) {
      $this->redirect(301, 'Catalog:detail', array('id'=>$id, 'key'=>$urlkey));
    }

    $this->template->catalogData = $catalogData;

    //id aktualni kategorie
    $this->template->thisCatId = $catalogData->catid;
    $idPath = explode('|', trim($catalogData->catpathids, '|'));

    $rootCatId = 0;
    if (isset($idPath[1])) {
      $rootCatId = (int)$idPath[0];
    }

    $this->template->masterCatId = $idPath[0];
    $catalogPath = array();
    $breadcrumbs = array();

    $breadcrumbs[] = array(
        "url" => $this->link("Catalog:default"),
        "title" => 'e-shop'
      );

    $catNames = array();
    foreach ($idPath as $catid) {
      $row = dibi::fetch("SELECT * from catalogs WHERE catid=$catid");
      $breadcrumbs[] = array(
        "url" => $this->link("Catalog:detail", $row->catid, (!empty($row->catkey) ? $row->catkey : Nette\Utils\Strings::webalize($row->catname))),
        "title" => $row->catname
      );
      $catalogPath[$catid] = $row;
      $catNames[] = $row->catname;
    }

    $this->template->catNameFull = implode(" - ", $catNames);


    $this->template->breadcrumbs = $breadcrumbs;

    $this->template->catalogPath = $catalogPath;
    $this->template->rootCatId = $rootCatId;

    if (isset($catalogPath[$rootCatId])) {
      $this->template->rootCatalog = $catalogPath[$rootCatId];
    }

    //k aktualni kategorii načtu podkategorie
    $this->template->thisCatId = $catalogData->catid;

    $catalogSubItems = dibi::query("SELECT * FROM catalogs WHERE catmasid=%i", $catalogData->catid, " AND catstatus=0 ORDER BY catorder")->fetchAssoc("catid");
    $this->template->catalogSubItems = $catalogSubItems;

    //naplnim si do katalogu prislusne zbozi
    $product = $this->model->getProductsModel();
    //sestavim WHERE
    switch ($this->o) {
       case '':
         $orderBy = "proorder";
         break;
       case 'os':
         $orderBy = " proaccess ASC";
         break;
      case 'na':
        $orderBy = " proname ASC";
        break;
       case 'nd':
         $orderBy = " proname DESC";
         break;
       case 'pa':
         $orderBy = " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) ASC";
         break;
       case 'pd':
         $orderBy = " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) DESC";
         break;
    }
    //nactu do filtru podminky
    $where = array();

    //filtrování podle tagů
    $filterEmpty = TRUE;
    for ($i = 1; $i <= 3; $i++) {
      $valName = 't' . $i;
      $values = $this->getTagValues($this->$valName, '-');
      if (count($values) > 0) {
        if (count($where) > 0) {
          $where[] = " AND ";
        }
        $where[] = "(";
        $cnt = 0;
        foreach ($values as $k => $val) {
          $filterEmpty = FALSE;
          $cnt++;
          $where[] = "protag" . $i . " LIKE %~like~";
          $where[] = "|$k|";
          if (count($values) > $cnt) {
            $where[] = " OR ";
          }
        }
        $where[] = ")";
      }
    }

    $this->template->filterEmpty = $filterEmpty;

    //vyrobce
    $manwhere = "";
    //vyrobci
    if (count($this->m) > 0) {
      $manwhere = implode(',', $this->m);
    }

    //nefiltrovat podle vyrobce pokud prislusenstvi
    if (!empty($manwhere)) {
      $where[] = " AND promanid IN (%i)";
      $where[] = $manwhere;
    }

    //typy
    if (count($this->t) > 0) {
      $tWhere = "";
      foreach ($this->t as $k => $value) {
        switch ($k) {
          case 'sk': //Skladem
            $tWhere .= "proaccess=0 OR ";
            break;
          case 'no': //Novinky
            $tWhere .= "protypid2=1 OR ";
            break;
          case 'ak': //Akce
            $tWhere .= "protypid=1 OR ";
            break;
          case 'dz': //Doprava zdarma
            $tWhere .= "prodelfree=1 OR ";
            break;
          case 'ti': //tip
            $tWhere .= "protypid3=1 OR ";
            break;
          case 'zd': //zlate dny
            $tWhere .= "protypid4=1 OR ";
            break;
        }
      }
      if (!empty($tWhere)) {
        $tWhere = " AND (".substr($tWhere, 0, -4).")";
      }
      $where[] = $tWhere;
    }

    //cena od
    if (!empty($this->pF)) {
     $where[] = " AND IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a)>=".(int)$this->pF;
    }
    //cena do
    if (!empty($this->pT)) {
      $where[] = " AND IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a)<=".(int)$this->pT;
    }

    $dataSource = $product->getDataSource($product->getSqlCatalogList($id, $where, $orderBy));

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $this->template->productsData = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();

    $paginatorTop = $this['paginatorTop']->paginator;
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginatorTop->itemCount = $paginator->itemCount;

    //statistika prodejnosti
    $this->template->saleStatProducts = array();
    if (isset($this->template->rootCatalog)) {
      $cacheKey = 'catalogSaleStats_'.$this->template->rootCatalog->catid.$this->curId.$this->userData->usrprccat;
      $catalogSaleStats = $catalog->cacheGet($cacheKey);
      if ($catalogSaleStats === FALSE) {
        $catalogSaleStats = array();
        if (!empty($catalogData->catsalestat)) {
          $proCodesList = explode(',',trim($catalogData->catsalestat, ','));
          $cnt = 0;
          foreach ($proCodesList as $proId) {
            $cnt++;
            $proId = trim($proId);
            if (!empty($proId)) {
              $item = $product->load($proId);
              if ($item) $catalogSaleStats[] = $item;
            }
            if ($cnt>=3) break;
          }
        } /*else {
          //vezmu ze statistiky prodejnosti
          $rows = dibi::fetchAll("SELECT prsproid  FROM products_salestat WHERE prscatpathids LIKE '%|".$catalogData->catid."|%' ORDER BY prscnt DESC LIMIT 3");
          foreach ($rows as $key => $row) {
            $catalogSaleStats[] = $product->load($row->prsproid);
          }
        }*/
        $catalog->cacheSave($cacheKey, $catalogSaleStats);
      }
      $this->template->saleStatProducts = $catalogSaleStats;
    }

    //naplnim filtr vybranymi hodnotami
    if (!empty($this->m)) $arr["m"] = $this->m;
    if (!empty($this->t)) $arr["t"] = $this->t;
    if (!empty($this->o)) $arr["o"] = $this->o;
    if (!empty($this->pF)) $arr["pF"] = $this->pF;
    if (!empty($this->pT)) $arr["pT"] = $this->pT;

    if (!empty($arr)) {
      $form = $this->getComponent("catalogSearchForm");
      $form->setDefaults($arr);
    }

    $this->template->o = $this->o;

    //zakazu indexovani pokud filtruje, radi nebo strankuje
    if (!empty($arr) || $paginator->page > 1) {
      $this->template->pageRobots = "noindex,follow";
    }

    //sestavim popisek co ma ve filtru
    //vyrobci
    $arr = array();
    If (!empty($manwhere)) {
      $arr["m"] = dibi::query("SELECT manid, manname FROM manufacturers WHERE manid IN (".$manwhere.")")->fetchPairs("manid", "manname");
    }
    //forma
    if (count($this->t) > 0) {
      $a = $this->getEnumTypes();
      $aar = array();
      foreach ($this->t as $k => $value) {
        $aar[$k] = $a[$k];
      }
      $arr["t"] = $aar;
    }
    If (!empty($this->pF)) $arr["pF"] = $this->pF;
    If (!empty($this->pT)) $arr["pT"] = $this->pT;
    $this->template->formVals = $arr;
  }

  protected function createComponentCatalogSearchForm() {
    $form = $this->createAppForm();

    $pros = $this->model->getProductsModel();
    $catid = $this->getParameter('id');
    //nactu vyrobce v katalogu
    $manufacts = dibi::query("
      SELECT manid, manname
      FROM products
      INNER JOIN catplaces ON (capproid=proid)
      INNER JOIN catalogs ON (capcatid=catid)
      INNER JOIN manufacturers ON (manid=promanid)
      WHERE catpathids LIKE '%|$catid|%'
      GROUP BY manid
      ORDER BY manname
    ")->fetchPairs("manid", "manname");
    $container = $form->addContainer('m');
    foreach ($manufacts as $k=>$name) {
      $container->addCheckbox($k, $name);
    }

    //$arr = $this->getEnumOrderBy();
    //$form->addSelect("o", "", $arr);
    //if (!empty($this->o)) $form["o"]->setDefaultValue($this->o);

    $arr = $this->getEnumTypes();
    $container = $form->addContainer('t');
    foreach ($arr as $k=>$name) {
      $container->addCheckbox($k, $name);
    }

    $form->addText("pF", 'Cena od', 5);
    $form->addText("pT", 'Cena do', 5);

    for ($i = 1; $i <= 3; $i++) {
      $enumName = 'getEnumProTag' . $i;
      $varName = 't' . $i;
      $arr = $pros->$enumName();
      $con = $form->addContainer('protag'.$i);
      $values = array();
      if (!empty($this->$varName)) {

        $values = $this->getTagValues($this->$varName, '-');
      }

      foreach ($arr as $key => $label) {
        $con->addCheckbox($key, $label);
        $con[$key]->setDefaultValue(isset($values[$key]));
      }
    }

    $form->addSubmit('search', 'Filtrovat')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'catalogSearchFormSubmitted');
    return $form;
  }

  public function catalogSearchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $this->m = array();
      foreach ($vals['m'] as $key => $val) {
        if ($val) $this->m[$key] = $key;
      }
      $this->t = array();
      foreach ($vals['t'] as $key => $val) {
        if ($val) $this->t[$key] = $key;
      }
      //$this->o = $vals->o;
      $this->pF = $this->formatNumberMySQL($vals->pF);
      $this->pT = $this->formatNumberMySQL($vals->pT);

      for ($i = 1; $i <= 3; $i++) {
        $varName = 't'.$i;
        $this->$varName = '';
        foreach ($vals["protag".$i] as $key => $value) {
          if ($value) {
            $this->$varName .= "$key-";
          }
        }
        $this->$varName = trim($this->$varName, '-');
      }

      $this->redirect('this');
    }
  }

  /**
  * číselínk pro filtrování OrderBy
  *
  */
  private function getEnumOrderBy() {
    return array(
       '' => 'Výchozí řazení',
       'pa' => 'Řadit cenu od nejnižší',
       'pd' => 'Řadit cenu od nejvyšší',
       'na' => 'Řadit podle názvu A-Z',
       'nd' => 'Řadit podle názvu Z-A',
     );
  }

  /**
  * číselínk pro filtrování Types
  *
  */
  private function getEnumTypes() {
    return array(
      'sk' => 'Skladem',
      'ak' => 'Akce',
      'no' => 'Novinka',
      'ti' => 'Tip',
      'zd' => 'Zlaté dny',
      'dz' => 'Doprava zdarma',
    );
  }

}

<?php
namespace FrontModule;
use dibi;
use Nette\Application\BadRequestException;

final class GoPayPresenter extends BasePresenter {

  /** @var $goPayApi  \GoPayApi */
  private $goPayApi;

  protected function startup() {
    parent::startup();
    $goPayConfig = $this->neonParameters['goPay'];

    $this->goPayApi = new \GoPayApi($goPayConfig);
  }

  public function actionCreate($ordId, $key) {
    $ords = $this->model->getOrdersModel();
    $ord = $ords->load($ordId);

    $key2 = substr(md5($ord->ordid . $ord->ordcode), 0, 6);

    if ($key !== $key2) {
      throw new BadRequestException('Stránka nenalezena', '404');
    }

    if ($ord) {
      if ((int)$ord->ordpaystatus === 1) {
        $this->flashMessageInfo("Tato objednávka je již nastavena jako uhrazena.");
      } else {
        $ord->items = dibi::fetchAll("SELECT * FROM orditems WHERE oriordid=%i", $ord->ordid);
        $ret = $this->goPayApi->createPayment($ord);
        if ($ret !== FALSE) {
          $url = $ret['gw_url'];
          $ords->update($ord->ordid, ["ordpaysesid" => $ret['id']]);
          $this->redirectUrl($url);
        }
      }
      $this->redirect("Order:status", $this->getOrderStatusKey($ord));
    }
  }

  public function renderNotify($id) {
    $ret = $this->goPayApi->getStatus($id);
    $this->updateOrderGoPayStatus($ret);
    $this->terminate();
  }

  public function renderReturn($id) {
    $ret = $this->goPayApi->getStatus($id);
    $this->updateOrderGoPayStatus($ret, TRUE);
  }
}

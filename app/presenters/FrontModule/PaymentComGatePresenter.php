<?php

namespace FrontModule;
use dibi;
use Model\OrdersModel;
use Nette;
use LZaplata\Comgate\Service;
use Tracy\Debugger;


final class PaymentComGatePresenter extends BasePresenter {

  /** @var Service @inject */
  public $comgate;

  public function renderReturn() {
    $id = $this->getParameter('id');
    $refId = $this->getParameter('refId');
    $ords = $this->model->getOrdersModel();
    $ord = $ords->load($refId, 'payrefid');
    $this->template->payment = "err";
    if ($ord) {
      if ($ord->ordpaystatus === 1) {
        $this->template->payment = "ok";
      }
    }
  }

  public function renderStartPayment($ordid, $key) {
    $ords = $this->model->getOrdersModel();
    $ord = $ords->load($ordid);
    $key2 = substr(md5($ord->ordid.$ord->orddatec), 0, 6);
    if ($key2 != $key) {
      $this->flashMessageErr("Tento požadavek na otevření platební brány je neplatný.");
      $this->redirect("default");
    }

    $data = array(
      'country' => 'CZ',
      'description' => 'platba za obj. č. '.$ord->ordcode,
      'refId' => $ord->ordcode,
      'email' => $ord->ordmail
    );

    //načtu kód cílové země
    if (!empty($ord->ordicouid)) {
      $couCode = (string)dibi::fetchSingle("SELECT enuname2 FROM enumcats WHERE enuid=%i", $ord->ordicouid);
    }
    if (!empty($couCode)) {
      $data['country'] = $couCode;
    }

    $url = "";
    try {
      $this->comgate->setCurrency($this->curKey);
      $payment = $this->comgate->createPayment($ord->ordpricevat, $data);
      $payment->refId = $ord->ordcode;

      $payId = $payment->getPayId();
      $refId = $payment->refId;
      $ords->update($ordid, array(
        'ordpaymentid'=>$payId,
        'ordpayrefid'=>$refId
      ));
      $response = $payment->send();
      $url = $response->getRedirectUrl();
    } catch (\Exception $e) {
      $this->flashMessageErr($e->getMessage());
      $this->redirect('return');
    }
    if (!empty($url)) {
      $this->redirectUrl($url);
    }
    $this->terminate();
  }

  public function renderStatus() {
    $response = $this->comgate->getReturnResponse();
    $status = (string)$_POST["status"];
    if ($status !== 'PAID') {
      echo("code=0&message=OK");
      $this->terminate();
    }
    if ($response->isOk()) {
      $payId = $response->getPayId();
      if ($status === 'PAID') {
        $ords = $this->model->getOrdersModel();
        $ord = $ords->load($payId, 'paymentid');
        if ($ord && $ord->ordpaystatus === 0) {
          $vals = array(
            'ordstatus'=> 6,
            'ordpaystatus' => 1,
            'orddatepayed' => new \DateTime()
          );
          $ords->update($ord->ordid, $vals);
          $ords->logStatus($ord->ordid, $vals["ordstatus"], 1, "ComGate platba platební kartou");
        }
      }
    }
    echo("code=0&message=OK");
    $this->terminate();
  }
}

<?php
namespace FrontModule;
use dibi;
use Nette;

final class HomepagePresenter extends BasePresenter {

  /********************* view default *********************/

  public function renderDefault() {
    //seznam produktu na uvodni strance
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);

    //novinky
    $rows = $this->model->getArticlesModel();
    $homepageNews = $rows->cacheGet('homepageNews');
    if ($homepageNews === FALSE) {
      $homepageNews = dibi::fetchAll("SELECT artid, artname, arttitle, arturlkey, artdesc, artdate FROM articles ORDER BY artdate DESC LIMIT 4");
      $rows->cacheSave('homepageNews', $homepageNews);
    }
    $this->template->news = $homepageNews;


    //nactu polozky do slideru
    $menuIndexs = $this->model->getMenuIndexsModel();
    $homepageSlides = $menuIndexs->cacheGet('homepageSlides');
    if ($homepageSlides === FALSE) {
      $homepageSlides = dibi::fetchAll("
        SELECT *
        FROM menuindexs
        LEFT JOIN pages ON (pagid=meipagid)
        LEFT JOIN catalogs ON (catid=meicatid)
        LEFT JOIN products ON (procode=meiprocode)
        WHERE meimasid=0 AND meistatus=0 AND meitarget='index'
        ORDER BY meiorder");
      $menuIndexs->cacheSave('homepageSlides', $homepageSlides);
    }
    $this->template->menuIndexs = $homepageSlides;

    //nactu polozky do rekamy
    $footerAdd = $menuIndexs->cacheGet('footerAdd');
    if ($footerAdd === FALSE) {
      $footerAdd = dibi::fetch("
        SELECT *
        FROM menuindexs
        LEFT JOIN pages ON (pagid=meipagid)
        LEFT JOIN catalogs ON (catid=meicatid)
        LEFT JOIN products ON (procode=meiprocode)
        WHERE meimasid=0 AND meistatus=0 AND meitarget='footer'
        ORDER BY meiorder");
      $menuIndexs->cacheSave('footerAdd', $footerAdd);
    }
    $this->template->footerAdd = $footerAdd;

    //naplnim access
    $this->addEnum("proaccess");
  }
}

<?php
namespace FrontModule;
use dibi;
use Model\CouponsModel;
use Model\UsersModel;
use Nette;

final class UserPresenter extends BasePresenter {

  protected function startup() {
    parent::startup();
    // autorizace zakaznika
    if ($this->action != "login" && $this->action != 'add' && $this->action != 'sendPassword' && $this->action != 'verifyAccount') {
      if (!$this->user->isLoggedIn()) {
        if ($this->user->getLogoutReason() === Nette\Security\IUserStorage::INACTIVITY) {
          $this->flashMessage('Byl/a jste odhl<PERSON>en/a z důvodu delší neaktivity.');
        }
        $this->redirect('login');
      }
    }
  }

  public function actionLogin() {
    if ($this->user->isLoggedIn()) {
      $this->redirect('default');
    }
  }

  /**
   * mailuje verifikační kód
   *
   * @param bool $genNewCode
   * @throws Nette\Application\AbortException
   */
  public function actionSendVerification() {
    $this->sendVerification();
    $this->redirect('default');
  }

  private function sendVerification($genNewCode=TRUE) {
    $usrs= $this->model->getUsersModel();
    if (!$this->userData->usrid === 0) {
      $this->flashMessage('Nejdříve se prosím přihlašte.');
      $this->redirect('login');
    }
    if ($genNewCode) {
      $this->userData = $usrs->load($this->userData->usrid);
      if (empty($this->userData->usrmailvcode)) {
        $usrs->update($this->userData->usrid, array(
          'usrmailvcode' => $this->getVerifyCode(),
          'usrmailverified' => 0
        ));
        $this->userData = $usrs->load($this->userData->usrid);
      }
    }
    $mailTemplate = $this->createTemplate();
    $mailTemplate->user = $this->userData;
    $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/userVerify.latte');

    $this->mailSend($this->userData->usrmail, 'Ověření emailu', $mailTemplate);
    //zaloguji odeslání žádosti
    $usrs->logEvent($this->userData->usrid, UsersModel::EVENT_MAIL_VERIFICATION_SEND);
    $this->flashMessage('Ověřovací email byl úspěšně odeslán.');
  }

  public function actionMv($k) {
    $arr = explode('-', $k);
    $usrid = 0;
    $isVerified = false;
    if (isset($arr[0])) {
      $usrid = $arr[0];
    }
    $usrs= $this->model->getUsersModel();
    $usr = $usrs->load($usrid);
    if ($usr && isset($arr[1]) && !empty($usr->usrmailvcode)) {
      $key = substr(md5($usr->usrid . $usr->usrmailvcode), 0, 6);
      if ($key === $arr[1]) {
        $usrs->update($usr->usrid, array(
          'usrmailvcode' => NULL,
          'usrmailverified' => 1
        ));
        $usrs->logEvent($usr->usrid, UsersModel::EVENT_MAIL_VERIFIED);
        $isVerified = TRUE;
      }
    }

    if ($isVerified) {
      $this->flashMessage("Váš email byl úspěšně ověřen. Děkujeme.");
    } else {
      $this->flashMessageErr("Váš email se nepodařilo ověřit.");
    }

    if ($this->userData->usrid > 0) {
      $this->redirect('default');
    }
    $this->redirect('Homepage:default');
  }

  public function actionLogout() {
    $user = $this->user;
    $user->logout();
    $this->flashMessage("Odhlášení proběhlo úspěšně.");
    $this->redirect('login');
  }

  /**
   * kopie objednávky do košíku
   *
   * @param $ordId
   * @throws Nette\Application\AbortException
   * @throws \Dibi\Exception
   */
  public function actionCopyOrder($ordId) {
    $rows = dibi::fetchAll("SELECT oriproid, oriqty FROM orditems WHERE oriordid=%i", $ordId, " AND oritypid=0 AND oriproid > 0");
    if ($rows !== FALSE) {
      foreach ($rows as $row) {
        $this->basketEditItem($row->oriproid, $row->oriqty);
      }
      $this->flashMessage("Položky vybrané objednávky byly vloženy do košíku. Původní zboží v košíku (pokud jste nějaké měli) bylo v košíku ponecháno.");
      $this->redirect("Basket:default");
    }
  }

  public function actionWatchDogStore($proid) {
    $pro = dibi::fetch("SELECT proid, proname, prokey FROM products WHERE proid=%i", $proid);
    if ($pro) {
      if ($this->userData->usrid > 0) {
        $dogid = (int)dibi::fetchSingle("SELECT dogid FROM watchdogs WHERE dogstore=1 AND dogproid=%i", $pro->proid, " AND dogusrid=%i", $this->userData->usrid);
        $dogs = $this->model->getWatchdogsModel();
        if ($dogid > 0) {
          //dog je, tak ho smazu
          $dogs->delete($dogid);
        } else {
          //dog neni, vytvorim
          $dogs->insert(array(
            'dogproid'=> $pro->proid,
            'dogstore'=> 1,
            'dogusrid'=> $this->userData->usrid,
          ));
        }
      }
      $urlkey = (!empty($pro->prokey) ? $pro->prokey : \Nette\Utils\Strings::webalize($pro->proname));
      $this->redirect('Product:detail', $pro->proid, $urlkey);
    } else {
      throw new NBadRequestException('Položka nenalezena', '404');
    }
  }

  public function actionWatchDogPrice($id) {
    $pro = dibi::fetch("SELECT proid, proname, prokey FROM products WHERE proid=%i", $id);
    if ($pro) {
      if ($this->userData->usrid > 0) {
        $dogid = (int)dibi::fetchSingle("SELECT dogid FROM watchdogs WHERE dogprice=1 AND dogproid=%i", $pro->proid, " AND dogusrid=%i", $this->userData->usrid);
        $dogs = $this->model->getWatchdogsModel();
        if ($dogid > 0) {
          //dog je, tak ho smazu
          $dogs->delete($dogid);
        } else {
          //dog neni, vytvorim
          $dogs->insert(array(
            'dogproid'=> $pro->proid,
            'dogprice'=> 1,
            'dogusrid'=> $this->userData->usrid,
          ));
        }
      }
      $urlkey = (!empty($pro->prokey) ? $pro->prokey : \Nette\Utils\Strings::webalize($pro->proname));
      $this->redirect('Product:detail', $pro->proid, $urlkey);
    } else {
      throw new NBadRequestException('Položka nenalezena', '404');
    }
  }

  /********************* view default *********************/

  public function renderDefault() {
    $usrs = $this->model->getUsersModel();
    $ords = $this->model->getOrdersModel();

    $ords->recalcLoyaltyDiscount($this->userData->usrid);

    $usr = $usrs->load($this->userData->usrid);
    $this->template->userRow = $usr;

    //otevrene objednavky
    $this->template->openedOrders = dibi::fetchAll("SELECT * FROM orders WHERE ordusrid=%i", $usr->usrid, " AND ordstatus IN (0,1,2,6,8) ORDER BY ordid DESC");
    //uzavrene objednavky
    $this->template->closedOrders = dibi::fetchAll("SELECT * FROM orders WHERE ordusrid=%i", $usr->usrid, " AND ordstatus IN (3,4) ORDER BY ordid DESC");
    $orders = $this->model->getOrdersModel();
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();

    //věrnostní systém
    if ($usr) {
      $sum = (double)dibi::fetchSingle("
        SELECT SUM(oriprice*oriqty) FROM orders
        INNER JOIN orditems ON oriordid=ordid
        WHERE ordusrid=%i", $usr->usrid, " AND
        ordstatus IN (3,4)");
      $this->template->ordersSum = $sum;
    }
  }

  /********************* view add, edit *********************/
  public function renderAdd() {
    if ($this->user->isLoggedIn()) $this->redirect('default');
    $form = $this->getComponent('userAddForm');
    $form['save']->caption = 'Registrovat';
    $this->template->form = $form;
    $this->template->blockPromoRegistrace = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='promo_registrace' AND pagstatus=0");
  }

  public function renderEdit() {
    $id = $this->getUser()->getIdentity()->id;
    $form = $this->getComponent("userEditForm");

    if (!$form->isSubmitted()) {
      $user = $this->model->getUsersModel();
      $userRow = $user->load($id);
      if (!$userRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }


      $userRow["stadr"] = ($userRow["usrstname"] != "");
      $form->setDefaults($userRow);
    }
  }

  public function renderVerifyAccount($id, $key) {
    $usrs= $this->model->getUsersModel();
    $usr = $usrs->load($id);
    $verifyReady = FALSE;
    if ($usr) {
      $key2 = substr(md5($usr->usrid . $usr->usrmailvcode), 0, 6);
      if ($key === $key2) {
        $verifyReady = TRUE;
      }
    }
    if (!$verifyReady) {
      $this->flashMessage("Ověření není možné. Nepodařilo se identifikovat Váš zákaznický účet.");
      $this->redirect('Homepage:default');
    }
    //je to OK, mohu ověřovat
    $this->template->user = $usr;

  }

  public function renderOrder($id) {
    $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
    if ($order===false) throw new Nette\Application\BadRequestException('Objednávka nenalezena', '404');
    //kontrola zda tato objednavka patri prihlasenemu
    if ($this->userData->usrid != $order->ordusrid) throw new Nette\Application\BadRequestException('Objednávka nenalezena', '404');
    $this->template->order = $order;

    $enums = $this->model->getEnumcatsModel();
    $delModes = $this->model->getDeliveryModesModel();
    $delModes->setCurrency($this->currencies, $order->ordcurid);
    $this->template->payMode = $delModes->load($order->orddelid);
    $this->template->delMode = $delModes->load($this->template->payMode->delmasid);
    $this->template->enum_countries = $enums->getEnumCountries();
    $this->template->ordItems = dibi::fetchAll("
      SELECT * from orditems
      INNER JOIN products ON (oriproid=proid)
      WHERE oriordid=%i", $order->ordid, "
      ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END
    ");

    if (!empty($order->ordcoucode)) {
      $cous = $this->model->getCouponsModel();
      $this->template->discountCoupon = $cous->load($this->template->order->ordcoucode, 'code');
    }

  }

  public function renderBookmarks() {
    $this->template->bookmarks = dibi::fetchAll("
      SELECT proid, protypid, promasid, proismaster, protypid2, protypid3, protypid4, protypid5, prokey, procode, proname AS proname1,
      concat(proname, ' ', proname2) AS proname, proname2, propicname, prodescs, proaccess, proaccesstext, proqty, proprice".$this->curId."com AS pricecom,
      IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) AS proprice,
      proprice".$this->curId."a AS propricea, proprice".$this->curId."b AS propriceb, proprice".$this->curId."c AS propricec, proprice".$this->curId."d AS propriced,
      proprice".$this->curId."com AS propricecom, provatid, prostatus, proorder, manname, prorating, ".($this->curId == 1 ? "prodelfree" : "0 AS prodelfree").", pronotdisc
      FROM bookmarks
      INNER JOIN products ON (bokproid=proid)
      INNER JOIN manufacturers ON (promanid=manid)
      WHERE bokusrid=%i", $this->userData->usrid);
  }

  public function sendPasswordFormSubmitted($form) {
    if ($this->user->isLoggedIn()) $this->redirect('login');

    //kontrola zda takovy email existuje v DB
    if ($form['submit']->isSubmittedBy()) {
      $user = $this->model->getUsersModel();
      $formVars = $form->getValues();
      $dataRow = $user->load($formVars["usrmail"], 'mail');
      if ($dataRow["usrid"] > 0) {
        //zmenim heslo a poslu email
        $newPassw = $this->getVerifyCode();
        $vals = array('usrpassw' => $this->passwordHash($newPassw));
        if ($user->update($dataRow->usrid, $vals)) {
          //odmailuju nove heslo
          $this->template->newPassword = $newPassw;
          $this->template->setFile(WWW_DIR.'/../templates/Mails/mailUserSendPassword.latte');

          if (!$this->mailSend($dataRow->usrmail, $this->translator->translate("Žádost o nové heslo"), $this->template)) $form->addError('Nové heslo se nepodařilo odeslat');
          $this->flashMessage("Email s novým heslem byl odeslán.");
          $this->redirect('login');
        } else {
          $form->addError('Nové heslo se nepodařilo nastavit');
        }

      } else {
        $form->addError('Zadaný email nebyl nalezen');
      }
    }
  }

  public function userFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $id = 0;
      if ($this->user->isLoggedIn()) {
        $id = $this->user->getIdentity()->id;
      }
      $formVars = $form->getValues();
      //kontrola duplicity emailu
      $cnt = dibi::fetchSingle("SELECT COUNT(usrid) FROM users WHERE usrmail='".$formVars["usrmail"]."'".($id > 0 ? " AND usrid != $id" : ""));
      if ($cnt > 0) {
        $form->addError("Účet s tímto emailem už existuje. Pokud jste zapomněli heslo, požádejte si o heslo nové.");
        return;
      }
      $user = $this->model->getUsersModel();
      if ($id > 0) {
        //editace zaznamu

        //zjistim jestli nebyl zmenen email
        $mailNewCode = false;
        $userRow = $user->load($id);
        if (isset($formVars["usrmail"]) && $formVars["usrmail"] != $userRow->usrmail) {
          $mailNewCode = true;
          $formVars["usrmailvcode"] = NULL;
          $formVars["usrmailverified"] = 0;
        }

        //zjistim jestli nebyl zmenen status zasílání emailů
        if ($formVars["usrmaillist"] != $userRow->usrmaillist) {
          if ($formVars["usrmaillist"] == FALSE) {
            $user->logEvent($id, \Model\UsersModel::EVENT_MAILLIST_REM);
          } else if ($formVars["usrmaillist"] == TRUE) {
            $user->logEvent($id, \Model\UsersModel::EVENT_MAILLIST_ADD);
          }
        }

        // kontrola pokud chce zmenit heslo
        $passw_changed = false;
        if ($formVars["usrpassw_old"] != "") {
          if (!$this->passwordVerify($formVars["usrpassw_old"], $userRow["usrpassw"])) {
            $this->flashMessageErr("Původní heslo jste nevyplnil/a správně. Heslo nebylo změněno");
            unset($formVars["usrpassw"]);
          } else {
            $formVars["usrpassw"] = $this->passwordHash($formVars["usrpassw"]);
            $passw_changed = true;
          }
        } else {
          unset($formVars["usrpassw"]);
        }

        unset($formVars["usrpassw_old"]);
        unset($formVars["usrpassw2"]);
        unset($formVars["stadr"]);

        if ($user->update($id, $formVars)) {
          $this->flashMessage('Vaše údaje byly aktualizovány.');

          //mailuji žádost o verifikaci emailu
          if ($mailNewCode) {
            $this->flashMessage('Váš email byl změněn. Je nutné ho ověření.');
            $this->sendVerification(TRUE);
          }
          if ($passw_changed) {
            $this->flashMessage('Heslo bylo změněno, prosím znovu se přihlašte.');
            $this->redirect('logout');
          } else {
            $this->redirect('default');
          }
        }
      } else {
        //novy zazanam
        $formVars = $form->getValues();
        $formVars["usrdiscount"] = $this->config["DEFAULT_DISCOUT"];
        $formVars["usrprccat"] = 'a';
        $mailTemplate = $this->createTemplate();
        //ulozim si do sablony hesle nez se zaheshuje
        $mailTemplate->usrpassw = $formVars["usrpassw"];

        //uklidim promenne ktere nejsou v objektu user
        unset($formVars["usrpassw2"]);
        unset($formVars["antispam"]);
        unset($formVars["usrgdpr"]);

        //naplnim overovaci kody
        $formVars["usrmailvcode"] = $this->getVerifyCode();
        $formVars["usrmailverified"] = 0;
        $formVars["usrpassw"] = $this->passwordHash($formVars["usrpassw"]);

        //ulozim novou registraci
        $id = $user->insert($formVars);

        if ($id > 0) {
          //zaloguji, regiistraci
          $user->logEvent($id, UsersModel::EVENT_REGISTER);
          //zaloguji souhlas s ucováním údajů
          $user->logEvent($id, UsersModel::EVENT_GDPR);
          if ($formVars["usrmaillist"]) {
            //zaloguji přihlášení do maillingu
            $user->logEvent($id, UsersModel::EVENT_MAILLIST_ADD);
          }

          //naplnim row
          $userRow = $user->load($id);

          $mailTemplate->userRow = $userRow;
          $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailUserMailAdd.latte');

          $this->mailSend($userRow->usrmail, $this->translator->translate("Registrace"), $mailTemplate);
          $mailTemplate->usrpassw = "";
          $this->mailSend($this->config["SERVER_MAIL"], $this->translator->translate("Nová registrace"), "Pravě se registroval nový uživatel: ".$userRow->usriname." ".$userRow->usrilname.' <a href="mailto:'.$userRow->usrmail.'">'.$userRow->usrmail.'</a>');

          //prihlasim
          $this->user->login($form['usrmail']->getValue(), $form['usrpassw']->getValue(), self::LOGIN_NAMESPACE);
          $this->userData = $userRow;
          $this->sendVerification(FALSE);
        }
        $this->flashMessage('Vaše registrace byla přijata a nyní jste přihlášen/a na svůj účet. Na uvedený email jsme poslali ověřovací email. Prosím dle pokynů v tomto emailu dokončete registraci.');
        $this->redirect('default');
      }
    }
  }

  /********************* facilities *********************/

  protected function createComponentSendPasswordForm() {
    //prihlasovaci form
    $form = $this->createAppForm();
    $form->addText('usrmail', 'Email:')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addReCaptcha('recaptcha', $required = FALSE, $message = 'Potvrďte prosím, že nejste robot.');

    $form->addSubmit('submit', 'Zaslat heslo')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'sendPasswordFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  /**
  * editace, registrace novych zakazniku
  */
  protected function createComponentUserAddForm() {
    $form = $this->createAppForm();

    //prihlasovaci udaje
    $form->addText('usrmail', 'Váš email', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addText('usriname', 'Jméno', 30);
    $form->addText('usrilname', 'Přijmení', 30);

    $form->addPassword('usrpassw', 'Heslo', 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);
    $form->addPassword('usrpassw2', 'Heslo podruhé', 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo podruhé.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["usrpassw"]);

    $form->addCheckbox("usrmaillist", "Chci dostávat informace o novinkách a akčních nabídkách.");
    $form->addCheckbox("usrgdpr", "")
      ->addCondition(Nette\Forms\Form::EQUAL, FALSE)
        ->addRule(Nette\Forms\Form::FILLED, 'Pro registraci je nutné souhlasit se zpracováním osobních údajů.');

    $form->addReCaptcha('recaptcha', $required = FALSE, $message = 'Potvrďte prosím, že nejste robot.');

    $form->addSubmit('save', 'Zaregistrovat se')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'userFormSubmitted');

    return $form;
  }

  protected function createComponentUserEditForm() {
    $user = $this->model->getUsersModel();
    $enums = $this->model->getEnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $form = $this->createAppForm();

    //prihlasovaci udaje
    $form->addGroup('Přihlašovací údaje');
    $form->addText('usrmail', 'Email', 30)
      ->setOption('description', ' slouží zároveň jako přihlašovací jméno')
      ->setEmptyValue('@')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');
    $form->addCheckbox("usrmaillist", "Chci dostávat informace o novinkách a akčních nabídkách");

    //Adresa dodani
    $form->addGroup('Fakturační adresa');
    $form->addText('usriname', 'Jméno', 30);
    $form->addText('usrilname', 'Přijmení', 30);
    $form->addText('usrifirname', 'Název firmy', 30);
    $form->addText('usristreet', 'Ulice', 30);
    $form->addText('usristreetno', 'Číslo popisné', 30);
    $form->addText('usricity', 'Město, obec', 30);
    $form->addText('usripostcode', 'PSČ', 6);
    $form->addHidden("usricouid", 1);
    $form->addText('usrtel', 'Telefon', 20);
    $form->addText('usric', 'IČ', 15)
      ->setOption('description', ' 10 číslic, bez mezer');
    $form->addText('usrdic', 'DIČ', 15);

    //fakturacni adresa
    $form->addGroup('Adresa dodání')
      ->setOption('embedNext', TRUE);

    $form->addCheckbox('stadr', 'Chci vyplnit adresu dodání (vyplňte jen pokud je jiná než adresa fakturační).')
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE)
        ->toggle('sendBox');

    // subgroup
    $form->addGroup()
      ->setOption('container', Nette\Utils\Html::el('div')->id('sendBox'));

    $form->addText('usrstname', 'Jméno', 30)
      ->addConditionOn($form["stadr"], Nette\Forms\Form::EQUAL, true)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte jméno");

    $form->addText('usrstlname', 'Přijmení', 30)
      ->addConditionOn($form["stadr"], Nette\Forms\Form::EQUAL, true)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte příjmení");

    $form->addText('usrstfirname', 'Název firmy', 30)
      ->addConditionOn($form["stadr"], Nette\Forms\Form::EQUAL, true)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte název firmy");

    $form->addText('usrststreet', 'Ulice', 30)
      ->addConditionOn($form["stadr"], Nette\Forms\Form::EQUAL, true)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte ulici");

    $form->addText('usrststreetno', 'Číslo popisné', 30)
      ->addConditionOn($form["stadr"], Nette\Forms\Form::EQUAL, true)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte číslo popisné.");

    $form->addText('usrstcity', 'Město, obec', 30)
      ->addConditionOn($form["stadr"], Nette\Forms\Form::EQUAL, true)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte obec/město");

    $form->addText('usrstpostcode', 'PSČ', 6)
      ->addConditionOn($form["stadr"], Nette\Forms\Form::EQUAL, true)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte PSČ");

    $form->addHidden("usrstcouid", 1);

    $form->addGroup('Změna hesla');
    $form->addPassword('usrpassw_old', 'Původní heslo', 20)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);
    $form->addPassword('usrpassw', 'Nové heslo', 20)
      ->addConditionOn($form["usrpassw_old"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte nové heslo.')
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);
    $form->addPassword('usrpassw2', 'Heslo podruhé', 20)
      ->addConditionOn($form["usrpassw_old"], Nette\Forms\Form::FILLED)
        ->setRequired()
        ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Nové heslo', $form["usrpassw"])
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);

    $form->addGroup();
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'userFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  protected function createComponentSetVerifyCodeForm() {
    //prihlasovaci form
    $form = $this->createAppForm();
    $form->addText('usrmailvcode', 'Ověřovací kód:')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');
    $form->addSubmit('submit', 'Ověřit email')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'setVerifyCodeFormSubmitted');
    return $form;
  }

  public function setVerifyCodeFormSubmitted($form) {
    if ($this->userData->usrid == 0) {
      $this->flashMessage('Nejprve se prosím přihlašte.');
      $this->redirect('login');
    }
    //kontrola zda takovy email existuje v DB
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      if ($formVars->usrmailvcode === $this->userData->usrmailvcode) {
        $usrs= $this->model->getUsersModel();
        $usrs->update($this->userData->usrid, array(
          'usrmailverified' => 1,
          'usrmailvcode' => NULL
        ));
        $usrs->logEvent($this->userData->usrid, UsersModel::EVENT_MAIL_VERIFIED);
        $this->flashMessage('Email byl úspěšně ověřen.');
      } else {
        $this->flashMessageErr('Email se nepodařilo ověřit.');
      }
      $this->redirect('default');
    }
  }

  protected function createComponentVerifyAccountForm() {
    //prihlasovaci form
    $form = $this->createAppForm();
    $form->addText('usrmailvcode', 'Ověřovací kód:')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte pole %label.');

    $form->addPassword('usrpassw', 'Vaše nové heslo', 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše nové heslo.')
      ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);

    $form->addPassword('usrpassw2', 'Vaše nové heslo podruhé pro kontrolu', 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo podruhé.')
      ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["usrpassw"]);

    /*
    $arr = array(
      1 => 'Jsem MLADŠÍ než 16 let.',
      0 => 'Jsem STARŠÍ než 16 let.',
    );

    $form->addRadioList('usragelimit', '', $arr)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte pole %label.')
      ->addCondition(Nette\Forms\Form::EQUAL, 1)
        ->toggle('agelimit');

    $form->addText('usrreprename', 'Jméno a příjmení:')
      ->addConditionOn($form['usragelimit'], Nette\Forms\Form::EQUAL, 1)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte pole %label.');

    $form->addText('usrrepreaddress', 'Adresa:')
      ->addConditionOn($form['usragelimit'], Nette\Forms\Form::EQUAL, 1)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte pole %label.');

    $form->addText('usrrepremail', 'Email:')
      ->addConditionOn($form['usragelimit'], Nette\Forms\Form::EQUAL, 1)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usrrepretel', 'Telefon:')
      ->addConditionOn($form['usragelimit'], Nette\Forms\Form::EQUAL, 1)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    */

    $form->addCheckbox("usrgdpr", "")
      ->addCondition(Nette\Forms\Form::EQUAL, FALSE)
        ->addRule(Nette\Forms\Form::FILLED, 'Pro registraci je nutné souhlasit se zpracováním osobních údajů.');

    $form->addCheckbox("usrmaillist", "Chci dostávat informace o novinkách a akčních nabídkách.");

    $form->addSubmit('submit', 'Uložit')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'verifyAccountFormSubmitted');

    return $form;
  }


  public function verifyAccountFormSubmitted($form) {
    //kontrola kódu
    if ($form->isSubmitted()) {
      $usrs= $this->model->getUsersModel();
      $usrid = $this->getParameter('id');
      $usr = $usrs->load($usrid);
      $formVars = $form->getValues();
      if ($formVars->usrmailvcode === $usr->usrmailvcode) {
        $usrs= $this->model->getUsersModel();
        $vals = array(
          'usrpassw' => $this->passwordHash($formVars->usrpassw),
          'usrmailverified' => 1,
          'usrmailvcode' => NULL,
          'usrmaillist' => $formVars->usrmaillist
        );
        if ($usrs->update($usr->usrid, $vals)) {
          $usrs->logEvent($usr->usrid, UsersModel::EVENT_MAIL_VERIFIED);
          $usrs->logEvent($usr->usrid, UsersModel::EVENT_GDPR);
          if ($formVars->usrmaillist) {
            $usrs->logEvent($usr->usrid, UsersModel::EVENT_MAILLIST_ADD);
          }
          $this->flashMessage('Nové heslo je nastaveno a Váš email je ověřený. Můžete se přihlásit.');
        }

        $this->redirect('login');
      } else {
        $this->flashMessageErr('Email se nepodařilo ověřit. Nelze nastavit ani nové heslo.');
      }
      $this->redirect('default');
    }
  }

  protected function createComponentUserDeleteForm() {
    //prihlasovaci form
    $form = $this->createAppForm();

    $form->addPassword('usrpassw', 'Vaše platné přihlašovací heslo', 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addSubmit('submit', 'Zrušit účet')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'userDeleteFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  public function userDeleteFormSubmitted($form) {
    if ($form->isSubmitted()) {
      if ($this->userData->usrid === 0) {
        $this->flashMessage('Nejprve se prosím přihlašte.');
        $this->redirect('login');
      }
      $usrs= $this->model->getUsersModel();
      $usrid = $this->userData->usrid;
      $usr = $usrs->load($usrid);
      $formVars = $form->getValues();

      //ověřím heslo
      if (!Nette\Security\Passwords::verify($formVars['usrpassw'], $usr->usrpassw)) {
        $this->flashMessage('Zadal/a jste neplatné heslo. Účet není možné zrušit.');
        $this->redirect('edit');
      } else {
        $usrs->clearPersonalData($this->userData->usrid);
        $this->user->logout();
        $this->flashMessage("Váš účet byl vymazán");
        $this->redirect('Homepage:default');
      }
    }
  }

  protected function getVerifyCode($length = 6) {
    $base = "0123456789";
    $max = strlen($base)-1;
    $string = "";
    mt_srand((double)microtime()*1000000);
    while (strlen($string) < $length) {
      $string .= $base[mt_rand(0, $max)];
    }
    return $string;
  }
}

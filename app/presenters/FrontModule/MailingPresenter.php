<?php
namespace FrontModule;
use dibi;
use Model\UsersModel;
use Nette;

final class MailingPresenter extends BasePresenter {

  public function renderClick($typid, $mamid, $usrid, $par1=NULL) {
    $this->setLayout(NULL);
    $mass = $this->model->getMailingStatsModel();
    if ($typid != 4) $mass->runCounterClick($mamid);
    if (empty($usrid)) $this->redirect("Homepage:default");
    if ($typid == 0) {
      //Homepage
      $data = array(
          'mastypid'=> $typid,
          'masmamid'=> $mamid,
          'masusrid'=> $usrid,
          'masproid'=> NULL,
        );
        $mass->insert($data);
      $this->redirect('Homepage:default');
    } else if ($typid == 1) {
      //detail produktu
      $proid = (int)$par1;
      //nactu produkt
      $product = dibi::fetch("SELECT proid, prokey, proname FROM products WHERE proid=%i", $proid);
      if (!$product) {
        $this->redirect('detail', $mamid);
      }
      //zapisu do statistiky
      //pokud neexistuje uz zaznam
      $data = array(
        'mastypid'=> $typid,
        'masmamid'=> $mamid,
        'masusrid'=> $usrid,
        'masproid'=> $proid,
      );
      $mass->insert($data);
      //presmeruju na detail zbozi
      $this->redirect('Product:detail', array('id'=>$product->proid, 'key'=>(!empty($product->prokey) ? $product->prokey : Nette\Utils\Strings::webalize($product->proname))));
    } else if ($typid == 2) {
      //klik na odhlaseni z maillistu
      $data = array(
          'mastypid'=> $typid,
          'masmamid'=> $mamid,
          'masusrid'=> $usrid,
          'masproid'=> NULL,
        );
        $mass->insert($data);

      $this->redirect('Mailing:logout', $usrid, $par1);
    } else if ($typid == 3) {
      //klik na detailmaillistu
      //neloguju, loguje se zobrazeni detailu
      $this->redirect(':Front:Mailing:detail', $mamid, $usrid, $par1);
    } else if ($typid == 4) {
      //zobrazeni emailu
      $data = array(
          'mastypid'=> $typid,
          'masmamid'=> $mamid,
          'masusrid'=> $usrid,
          'masproid'=> NULL,
        );
      $mass->insert($data);
      $mass->runCounterView($mamid);
      $this->terminate();
    } else if ($typid == 5) {
      //klik na vygenerovani slevoveho kuponu
      $data = array(
          'mastypid'=> $typid,
          'masmamid'=> $mamid,
          'masusrid'=> $usrid,
          'masproid'=> NULL,
        );
      $mass->insert($data);
      $this->redirect(':Front:Mailing:coupon', $mamid, $usrid, $par1);
    }
  }

  public function renderDetail($id, $usrid, $key) {
    $pros = $this->model->getProductsModel();
    $mails = $this->model->getMailsModel();
    $mailings = $this->model->getMailingsModel();
    $mass = $this->model->getMailingStatsModel();

    $mailing = $mailings->load($id);
    if ($mailing === false) throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');

    $user = dibi::fetch("SELECT usrid, usrmail, usrdatec FROM users WHERE usrid=%i", $usrid);
    $uKey = md5($user->usrid.$user->usrdatec);
    if ($uKey != $key) throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');



    //zaloguju zobrazeni emailu
    $data = array(
        'mastypid'=> 3,
        'masmamid'=> $id,
        'masusrid'=> $usrid,
        'masproid'=> NULL,
      );
    $mass->insert($data);
    $mass->runCounterView($id);

    //naplnim produkty
    $arrPro = explode("\n", trim($mailing->mamproducts));
    $arrDesc1 = explode("\n", trim($mailing->mamdesc1));
    $arrDesc2 = explode("\n", trim($mailing->mamdesc2));
    $arrDesc3 = explode("\n", trim($mailing->mamdesc3));

    $cnt = 0;
    $products = array();
    foreach ($arrPro as $proid) {
      $pro = $pros->load($proid);
      if ($pro) {
        $pro->promamdesc1 = (isset($arrDesc1[$cnt]) ? $arrDesc1[$cnt] : "");
        $pro->promamdesc2 = (isset($arrDesc2[$cnt]) ? $arrDesc2[$cnt] : "");
        $pro->promamdesc3 = (isset($arrDesc3[$cnt]) ? $arrDesc3[$cnt] : "");
        $products[$cnt] = $pro;
        $cnt ++;
      }
    }
    $this->setLayout(NULL);
    $this->template->products = $products;
    $this->template->setFile(WWW_DIR.'/../templates/Mails/mailing.latte');
    //adresati
    $this->template->mailing = $mailing;

    $this->template->usrid = $user->usrid;
    $this->template->usrmail = $user->usrmail;
    $this->template->loKey = $key;
    $this->template->mamid = $mailing->mamid;
    $this->template->isMail = FALSE;
  }

  public function renderLogOut($usrid, $key) {
    $usrs = $this->model->getUsersModel();
    $user = $usrs->load($usrid);
    $myKey = md5($user->usrid.$user->usrdatec);
    if ($myKey == $key) {
      //odhlasim
      $usrs->update($usrid, array('usrmaillist'=>0));
      $usrs->logEvent($usrid, \Model\UsersModel::EVENT_MAILLIST_REM);
      $this->flashMessage("Odhlášení z odběru novinek proběhlo úspěšně.");
    } else {
      $this->flashMessage("Odhlášení z odběru novinek se nepovedlo.");
    }
    $this->redirect("Homepage:default");
  }

  public function renderCoupon($mamid, $usrid, $key) {
    $usrs= $this->model->getUsersModel();
    $user = $usrs->load($usrid);
    $myKey = md5($user->usrid.$user->usrdatec);
    if ($myKey == $key) {
      //vytvorim slevovy kupon
      $mailings= $this->model->getMailingsModel();
      $mailing = $mailings->load($mamid);
      $cous= $this->model->getCouponsModel();

      try {
        dibi::begin();
        $cou = $cous->getMailingCoupon($mailing, $user);
        if ($cou->coumailcnt<3) {
          //odmailuju jen pokud bylo odmailovano max 3x
          $template = $this->getTemplate();
          $template->coupon = $cou;
          $this->template->setFile(NEnvironment::expand('%wwwDir%/../templates/Mails/mailUserSendPassword.phtml'));
          if ($this->mailSend($cou->coumail, $this->translator->translate("Slevový kupón"), $this->template)) {
            $this->flashMessage("Slevový kupón byl odmailován na Váš email.");
            //pocitadlo mailovani
            $cous->update($cou->couid, array('coumailcnt'=>($cou->coumailcnt+1)));
          }
          dibi::commit();
        } else {
          $this->flashMessage("Slevový kupón lze odmailovat maximálně 3x.");
        }
      } catch (Exception $e) {
        $this->flashMessage("Odmailování kupónu se nepovedlo.");
        dibi::rollback();
      }
    } else {
      $this->flashMessage("Odmailování kupónu se nepovedlo.");
    }
    $this->redirect("Homepage:default");
  }
}
?>

<?php
namespace FrontModule;
use dibi;
use Nette;

final class PagePresenter extends BasePresenter {

  /********************* view default ********************
   *
   * @param $id
   * @param $key
   * @throws Nette\Application\AbortException
   * @throws Nette\Application\BadRequestException
   * @throws \Dibi\Exception
   */
  public function renderDetail($id, $key) {
    $pages = $this->model->getPagesModel();
    $pageData = $pages->load($id);
    if ($pageData) {
      //kontrola platnosti URL
      $urlkey = (!empty($pageData->pagurlkey) ? $pageData->pagurlkey : Nette\Utils\Strings::webalize(empty($pageData->pagtitle) ? $pageData->pagname :$pageData->pagtitle));
      //pokud se zmenil klic presmeruju na novy
      if ($key !== $urlkey) {
        $this->redirect(301, 'Page:detail', array('id'=>$id, 'key'=>$urlkey));
      }

      if ($pageData->pagid > 0) {
        $this->template->urlkey = $pageData->pagurlkey;
        $this->template->page = $pageData;
      }

      //pokud existuje šablona natvrdo použiji tuto
      $templateFileName = APP_DIR . "/../templates/FrontModule/page/" . $urlkey . ".latte";
      if (file_exists($templateFileName)) {
        $this->template->templateFileName = $urlkey . ".latte";
        $this->setView("template");
      }
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }

    if ($pageData->pagblock === 1) {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }

    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $pageData->pagid);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype IN ('jpg', 'png', 'gif')", $pageData->pagid);
  }
}

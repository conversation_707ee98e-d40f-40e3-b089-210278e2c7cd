<?php

/**
 * https://doc.gopay.com/cs/
 *
 */

/**
URL prodejního místa: https://www.pstrosivejce.cz/
evidenční číslo inplementace: EVC 1310955602
Test GoID: 8604807442
Test SecureKey: Yrua7Fk7N43ZRpY6UyZFy8Cs
Test ClientID: 1447239035
Test ClientSecret: LKzGYPW7
Test uživatelské jméno: testUser8604807442
Test heslo: P8477539
https://gw.sandbox.gopay.com/cs/prihlaseni-obchodnik
*/

use Contributte\GopayInline\Api\Entity\PaymentFactory;
use Contributte\GopayInline\Api\Lists\Currency;
use Contributte\GopayInline\Api\Lists\Language;
use Contributte\GopayInline\Api\Lists\PaymentInstrument;
use Contributte\GopayInline\Api\Lists\Scope;
use Contributte\GopayInline\Api\Lists\SwiftCode;
use Contributte\GopayInline\Client;
use Contributte\GopayInline\Config;

class GoPayException extends \Exception {}

  class GoPayApi {

    private $goId;
    private $clientId;
    private $clientSecret;
    private $mode;
    private $token;
    private $returnUrl;
    private $notifyUrl;

    const GOPAY_STATUS_CREATED = 'CREATED'; //Platba založena
    const GOPAY_STATUS_PAYMENT_METHOD_CHOSEN = 'PAYMENT_METHOD_CHOSEN'; //Platební metoda vybrána
    const GOPAY_STATUS_PAID = 'PAID'; //Platba zaplacena
    const GOPAY_STATUS_AUTHORIZED = 'AUTHORIZED'; //Platba předautorizována
    const GOPAY_STATUS_CANCELED = 'CANCELED'; //Platba zrušena
    const GOPAY_STATUS_TIMEOUTED = 'TIMEOUTED'; //Vypršelá platnost platby
    const GOPAY_STATUS_REFUNDED = 'REFUNDED'; //Platba refundována
    const GOPAY_STATUS_PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED'; //Platba částečně refundována

    /** @var Contributte\GopayInline\Client */
    private $client;

    /**
     * @param array $config
     * @throws GoPayException
     */
    public function __construct($config) {
      if (empty($config["goId"]) || empty($config["clientId"]) || empty($config["clientSecret"]) || empty($config["returnUrl"]) || empty($config["notifyUrl"])) {
        throw new GoPayException('Nenalezeno nastaveni');
      }

      if (empty($config["mode"])) {
        $config["mode"] = "TEST";
      }

      $this->goId = (string)$config["goId"];
      $this->clientId = (string)$config["clientId"];
      $this->clientSecret = (string)$config["clientSecret"];
      $this->mode = (string)$config["mode"];
      $this->returnUrl = (string)$config["returnUrl"];
      $this->notifyUrl = (string)$config["notifyUrl"];

      $this->client = new Client(new Config($this->goId, $this->clientId, $this->clientSecret, $mode = $this->mode));
    }

    /**
     * otevře platební bránu
     *
     * @param array $order
     * @return array|FALSE
     */
    public function createPayment($order) {
      // Payment data
      $phone = substr(str_replace(" ", "", trim($order->ordtel)), -9);
      $phone = "+420" . $phone;
      //načtu položky obj.
      $items = array();
      foreach ($order->items as $key => $row) {
        $items[] = ['name' => $row->oriname, 'amount' => round($row->oriprice * $row->oriqty)];
      }

      $payment = [
        'payer' => [
          'default_payment_instrument' => PaymentInstrument::PAYMENT_CARD,
          'allowed_payment_instruments' => [PaymentInstrument::PAYMENT_CARD],
          'default_swift' => SwiftCode::FIO_BANKA,
          'allowed_swifts' => self::getAlowedSwifts(),
          'contact' => [
            'first_name' => $order->ordiname,
            'last_name' => $order->ordilname,
            'email' => $order->ordmail,
            'phone_number' => $phone,
            'city' => $order->ordicity,
            'street' => $order->ordistreet . " " . $order->ordistreetno,
            'postal_code' => $order->ordipostcode,
            'country_code' => 'CZE',
          ],
        ],
        'target' => [
          'goid' => $this->goId
        ],
        'amount' => $order->ordpricevat,
        'currency' => Currency::CZK,
        'order_number' => $order->ordcode,
        'order_description' => 'Objednávka č: ' . $order->ordcode,
        'items' => $items,
        'additional_params' => [
          ['name' => 'Zákazník', 'value' => $order->ordiname . " " . $order->ordilname . ", " . $order->ordicity . ", " . $order->ordistreet . " " . $order->ordistreetno],
          ['name' => 'Email', 'value' => $order->ordmail],
        ],
        'callback' => [
          'return_url' => $this->returnUrl,
          'notify_url' => $this->notifyUrl,
        ],
        'lang' => Language::CZ,
      ];

      // Create payment request
      try {
        //autorizace
        $this->token = $this->client->authenticate(['scope' => Scope::PAYMENT_CREATE]);
        $response = $this->client->payments->createPayment(PaymentFactory::create($payment));
        if ($response->isSuccess()) {
          return $response->getData();
        }
        $this->error = $response->getError();
        return false;
      } catch (GoPayException $e) {
        $this->error = $e->getMessage();
      }
    }

    public function getStatus($id) {
      // Verify payment
      $this->token = $this->client->authenticate(['scope' => Scope::PAYMENT_ALL]);
      $response = $this->client->payments->verify($id);

      if ($response->isSuccess()) {
        return $response->getData();
      }

      return false;
    }

    /**
     * vrátí popis stavu platby
     * @param $payStatus
     * @return string
     */
    public static function getPayStatusDesc($payStatus) {
      $arr = array(
        self::GOPAY_STATUS_CREATED => 'Platba založena',
        self::GOPAY_STATUS_PAYMENT_METHOD_CHOSEN => 'Platební metoda vybrána',
        self::GOPAY_STATUS_PAID => 'Platba zaplacena',
        self::GOPAY_STATUS_AUTHORIZED => 'Platba předautorizována',
        self::GOPAY_STATUS_CANCELED => 'Platba zrušena',
        self::GOPAY_STATUS_TIMEOUTED => 'Vypršelá platnost platby',
        self::GOPAY_STATUS_REFUNDED => 'Platba refundována',
        self::GOPAY_STATUS_PARTIALLY_REFUNDED => 'Platba částečně refundována',
      );
      return $arr[$payStatus];
    }

    private static function getAlowedSwifts() {
		return [
			SwiftCode::AIR_BANK,
			SwiftCode::CESKA_SPORITELNA,
			SwiftCode::CSOB,
			SwiftCode::EQUA_BANK,
			SwiftCode::ERA,
			SwiftCode::FIO_BANKA,
			SwiftCode::KOMERCNI_BANKA,
			SwiftCode::MBANK,
			SwiftCode::MONETA_MONEY_BANK,
			SwiftCode::OBER_BANK,
			SwiftCode::RAIFFEISENBANK,
			SwiftCode::SBER_BANK,
			SwiftCode::UNICREDIT_BANK_CZ,
		];
    }
  }

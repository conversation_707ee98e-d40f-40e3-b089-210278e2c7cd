<?php
/**
* Heureka Kosik API
  * http://sluzby.heureka.cz/napoveda/kosik-api/
* <AUTHOR>
*/
class HeurekaKosikApi {

  const API_VERSION = 1;
  const API_URL = 'https://ssl.heureka.cz/api/cart';

  private $apiUrlFull = '';
  private $apiId = '';
  private $curId = 1;

  private $ulozenky = array();

  public function __construct($serverId, $apiId) {
    $this->apiId = $apiId;
    $this->apiUrlFull = self::API_URL.'/'.$this->apiId.'/'.self::API_VERSION;
  }

  /**
  * vraci TRUE/FALSE zda je sluzba aktivni
  *
  */
  public function isApiOn() {
    return (!empty($this->apiId));
  }

  private function getUlozenky() {
    if (count($this->ulozenky)==0) {
      //nabrani ulozenek
      $dels = new DeliveryModesModel();
      $this->ulozenky = $dels->getEnumUlozenky();
    }
    return $this->ulozenky;
  }

  public function setErrorMsg($msg) {
    NDebugger::log($msg);
    header("HTTP/1.0 500 Internal Server Error");
    $data = array(
      "id" => 500,
      "msg" => $msg,
    );
    echo json_encode($data);
    die();
  }

  /**
  * GET products/availability
  * Metoda vrací aktuální data o požadovaných produktech.
  *
  * @param array $params
  * @return array $jsonItems
  */
  public function getProductsAvailability($params) {
    $dels = new DeliveryModesModel();
    $delFreeLimit = $dels->getDelFreeLimit();
    $codes = array();
    $count = array();
    $jsonItems = array();
    $sum = 0;
    foreach ($params as $item) {
      $row = dibi::fetch("SELECT proid, procode, proname, proaccess, proqty, proprice".$this->curId."a AS propricea, prostatus FROM products WHERE procode=%s", $item["id"]);
      //kolik chce kusu
      $cnt = (int)$item["count"];
      //pokud chce vice kusu nez mame skladem dam mu jen to co mame
      if ($cnt > (int)$row->proqty)  $cnt = (int)$row->proqty;

      //$cnt = 1;
      $item = array(
        'id' => (string)$row->procode,
        'count' => (int)$cnt,
        'available' => (boolean)($row->prostatus==0),
        'delivery' => (int)$row->proaccess,
        'name' => (string)$row->proname,
        'price' => (float)$row->propricea,
        'priceTotal' => (float)$row->propricea * (int)$cnt,
      );
      //pokud je doprava zdarma dam related
      if ($row->propricea > $delFreeLimit) {
        $item["related"] = array("title"=>"Doprava zdarma");
      }
      $jsonItems["products"][] = $item;
      $sum += (float)$row->propricea;

      $codes[] = $item["id"];
      $count[$item["id"]] = $item["count"];
    }
    $jsonItems["priceSum"] = $sum;

    return ($jsonItems);
  }

  /**
  * GET payment/delivery
  * Vrátí možnosti dopravy a platby.
  *
  * @param array $params
  * @return array $jsonItems
  */
  public function getPaymentDelivery($params) {
    //nactu dopravy
    $jsonItems = array();

    //zjistim jestli ma narok na dopravu zdarma
    //zjistim cenu objednavky
    $proPriceSum = 0;
    foreach ($params as $item) {
      $proPriceSum += (float)dibi::fetchSingle("SELECT proprice".$this->curId."a AS propricea FROM products WHERE procode=%s", $item["id"]) * (int)$item["count"];
    }
    $dels = new DeliveryModesModel();
    $rows = dibi::fetchAll("SELECT * FROM deliverymodes WHERE delmasid=0 AND delstatus=0 AND delcode NOT IN ('GEIS', 'CESKA_POSTA_BALIKOMAT', 'ZASILKOVNA') ORDER BY delorder"); //vyradim geis point
    $jsonItems = array();
    $binding = array();
    $cnt = 0;
    $bindingCnt = 0;
    $uloDelId = 0;
    foreach ($rows as $item) {
      $delPrice = (($proPriceSum >= $item->dellimitfrom && $item->dellimitfrom > 0) ? 0 :(float)$item->delprice);
      if ($item->delcode == 'ULOZENKA') {
        $uloDelId = $item->delid;
        $ulozenky = $this->getUlozenky();
        //platby - budou spolecne pro vsechny ulozenky
        $irows = dibi::fetchAll("SELECT * FROM deliverymodes WHERE delmasid=%i", $item->delid, " AND delstatus=0 AND (delcode NOT IN ('cetelem','cofidis','creditcard') OR delcode IS NULL)");
        foreach ($ulozenky as $key => $ulozenka) {
          $uloId = (int)$ulozenka["id"] + 10000;
          $jsonItem = array();
          $jsonItem = array(
           "id" => $uloId,
           "type" => 1,
           "name" => 'Uloženka '.$ulozenka["nazev"],
           "price" => (double)$delPrice,
           "description" => (string)$ulozenka["ulice"].', '.$ulozenka["obec"]
          );
          $jsonItem["store"] = array(
            "id" => $ulozenka["id"],
            "type" => 2,
          );
          foreach ($irows as $iitem) {
            $jsonItemPay = array();
            $bindingCnt ++;
            $binding[] = array(
              "id" => (int)$bindingCnt,
              "transportId" => (int)$uloId,
              "paymentId" => (int)$iitem->delid,
            );
          }
          $jsonItems["transport"][] = $jsonItem;
        }
      } else {
        $jsonItem = array();
        $jsonItem = array(
           "id" => $item->delid,
           "type" => $dels->getHeurekaKosikDelTypId($item->delcode),
           "name" => $item->delname,
           "price" => (double)$delPrice,
           "description" => (string)$item->deldesc
        );
        if (!empty($item->deldepid)) {
          $jsonItem["store"] = array(
            "id" => $item->deldepid,
            "type" => $dels->getHeurekaKosikDepTypId($item->delcode),
          );
        }
        $jsonItems["transport"][] = $jsonItem;
      }
      //nactu platby
      $irows = dibi::fetchAll("SELECT * FROM deliverymodes WHERE delmasid=%i", $item->delid, " AND delstatus=0 AND (delcode NOT IN ('cetelem','cofidis','creditcard') OR delcode IS NULL)");
      foreach ($irows as $iitem) {
        $bindingCnt ++;
        $price = (($proPriceSum >= $iitem->dellimitfrom && $iitem->dellimitfrom > 0) ? 0 :(float)$iitem->delprice);
        $jsonItem = array();
        $jsonItem = array(
           "id" => (int)$iitem->delid,
           "type" => (int)$dels->getHeurekaKosikPayTypId($iitem->delcode),
           "name" => (string)$iitem->delname,
           "price" => $price,
        );
        if ($iitem->delmasid != $uloDelId) {
          $binding[] = array(
            "id" => (int)$bindingCnt,
            "transportId" => (int)$iitem->delmasid,
            "paymentId" => (int)$iitem->delid,
          );
        }
        $jsonItems["payment"][] = $jsonItem;
      }
    }
    $jsonItems["binding"] = $binding;
    return ($jsonItems);
  }

  /**
  * GET order/status
  * Vrátí stav objednávky v obchodě.
  *
  * @param mixed $ordid
  */
  public function getOrderStatus($ordid) {
    $orders = new OrdersModel();
    $ordStatus = (int)dibi::fetchSingle("SELECT ordstatus FROM orders WHERE ordid=%i", $ordid);
    $ordStatus = $orders->getHeurekaKosikOrdStatusId($ordStatus);
    return array(
      "order_id" => (int)$ordid,
      "status"  => (int)$ordStatus,
    );
  }

  /**
  * POST order/send
  * Odeslání objednávky do obchodu.
  *
  * @param array $params
  */
  public function postOrderSend($params) {
    $orders = new OrdersModel();
    $ordItems = new OrdItemsModel();
    $dels = new DeliveryModesModel();
    //zalozim objednavku
    //podle emailu zjistum jestli uz nema ucet
    $usrid = (int)dibi::fetchSingle("SELECT usrid FROM users WHERE usrmail=%s", $params["customer"]["email"]);

    if ($params["paymentId"] == 0) {
      $delId = (int)$params["deliveryId"];
      if ($delId > 0) {
        if ($delId > 10000) {
          //zjistim ID ulozenky
          $delId = (int)dibi::fetchSingle("SELECT delid FROM deliverymodes WHERE delcode='ULOZENKA'");
        }
        //nactu platbu ktera je platba predem pro danou dopravu
        $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delmasid=%i", $delId, " AND delcode='paybefore'");
      }
    } else {
      $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $params["paymentId"]);
    }
    $delSpec = NULL;
    if ($payMode) {
      $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);
      if ($delMode === FALSE) $this->setErrorMsg("deliveryId: ".$payMode->delmasid."(".$params["deliveryId"].") nenalezen.");
      //pokud je to ulozenka dotahnu ID depa
      $ulozenky = $dels->getEnumUlozenky();
      $ulId = (int)$params["deliveryId"] - 10000;
      if (isset($ulozenky[$ulId]['zkratka'])) $delSpec = $ulozenky[$ulId]['zkratka'];
    } else {
      $this->setErrorMsg("paymentId: ".$params["paymentId"]." nenalezen. (email: ".$params["customer"]["email"].", deliveryId: ".$params["deliveryId"]."/$delId)");
    }
    if (!isset($params["customer"]["ic"])) $params["customer"]["ic"] = NULL;
    if (!isset($params["customer"]["dic"])) $params["customer"]["dic"] = NULL;
    $vals = array(
      "ordusrid" => $usrid,
      "orddelid" => $payMode->delid,
      "orddelspec" => $delSpec,
      "ordtype" => 3,
      "ordmail" => $params["customer"]["email"],
      "ordtel" => $params["customer"]["phone"],
      "ordiname" => $params["customer"]["firstname"],
      "ordilname" => $params["customer"]["lastname"],
      "ordifirname" => $params["customer"]["company"],
      "ordistreet" => $params["customer"]["street"],
      "ordicity" => $params["customer"]["city"],
      "ordipostcode" => $params["customer"]["postCode"],
      "ordicouid" => 1,
      "ordic" => $params["customer"]["ic"],
      "orddic" => $params["customer"]["dic"],
      "ordusrvat" => (!empty($params["customer"]["dic"]) ? 1 : 0),
      "ordstname" => $params["deliveryAddress"]["firstname"],
      "ordstlname" => $params["deliveryAddress"]["lastname"],
      "ordstfirname" => $params["deliveryAddress"]["company"],
      "ordststreet" => $params["deliveryAddress"]["street"],
      "ordstcity" => $params["deliveryAddress"]["city"],
      "ordstpostcode" => $params["deliveryAddress"]["postCode"],
      "ordstcouid" => 1,
      "ordnote" => (string)$params["note"],
      "ordpricevat" => (double)$params["productsTotalPrice"]+(double)$params["deliveryPrice"]+(double)$params["paymentPrice"],
      "ordnote" => $params["note"],
    );
    try {
      $id = $orders->insert($vals);
    } catch (Exception $e) {
      $this->setErrorMsg("Err heureka kosik-nova objednavka: ".$e->getMessage());
    }
    if ($id > 0) {
      //vlozim polozky
      foreach ($params["products"] as $item) {
        //nactu product
        $pro = dibi::fetch("SELECT proid, procode, proname, provatid FROM products WHERE procode=%s", $item["id"]);
        if ($pro) {
          $vals = array(
            "oriordid" => $id,
            "oriproid" => $pro->proid,
            "oriprocode" => $pro->procode,
            "oritypid" => 0,
            "oriname" => $pro->proname,
            "oriprice" => $item["price"],
            "oriqty" => $item["count"],
            "orivatid" => $pro->provatid,
          );
          try {
            $ordItems->insert($vals);
          } catch (Exception $e) {
            //vymazu obj.
            $orders->delete($id);
            $this->setErrorMsg("Err heureka kosik-nova položka: ".$e->getMessage());
          }
        } else {
          $orders->delete($id);
          $this->setErrorMsg("Err heureka kosik-nova položka nenalezena. procode:".$item["id"]);
        }
      }
      //doprava
      $vals = array(
        "oriordid" => $id,
        "oriproid" => 0,
        "oriprocode" => '',
        "oritypid" => 1,
        "oriname" => "Doprava: ".$delMode->delname." - ".$payMode->delname,
        "oriprice" => (float)$params["deliveryPrice"] + (float)$params["paymentPrice"],
        "oriqty" => 1,
        "orivatid" => 0,
      );
      try {
        $ordItems->insert($vals);
      } catch (Exception $e) {
        //vymazu obj.
        $orders->delete($id);
        $this->setErrorMsg("Err heureka kosik-vlozeni dopravy: ".$e->getMessage());
      }
    }
    $ord = $orders->load($id);
    return array(
      "order_id" => (int)$ord->ordid,
      "internal_id" => (string)$ord->ordcode,
      "variableSymbol" => (int)$ord->ordcode,
    );
  }

  /**
  * PUT order/cancel
  * Nastavení objednávky na storno
  *
  * @param array $params
  */
  public function putOrderCancel($params) {
    $formVals["ordstatus"] = 5;
    $id = $params["order_id"];
    $orders = new OrdersModel();
    $order = $orders->load($id);
    if (!$order) {
      $this->setErrorMsg("Objednávka nenalezena. ID:".$id);
    }
    if ($order->ordstatus != $formVals["ordstatus"]) {
      //pokud storno vymazu cislo fa a datum vystaveni
      if ($formVals["ordstatus"] == 5) {
        $formVals["ordinvcode"] = Null;
        $formVals["ordinvdate"] = Null;
      }
      $orders->update($id, $formVals);
      //vystornuju vsechny polozky
      //vymazu blokaci zbozi
      $items = dibi::fetchAll("SELECT oriid FROM orditems WHERE oritypid=0 AND oriordid=%i", $order->ordid);
      $stores = new StoresModel();
      foreach ($items as $row) {
        $stores->ordItemBlockDelete($row->oriid);
      }
      $orders->logStatus($id, $formVals["ordstatus"], 6, (!empty($params["reason"]) ? "reason:".$params["reason"] : ""));
      $order = $orders->load($id);
    }
    return array(
      "status" => ($order->ordstatus == $formVals["ordstatus"]),
    );
  }

  /**
  * PUT payment/status
  * nastaví stav platby (-1 = nezaplaceno, 1 = zaplaceno)
  *
  * @param array $params
  */
  public function putPaymentStatus($params) {
    $formVals["ordpaystatus"] = ($params["status"] == 1 ? 1 : 0);
    $id = (int)$params["order_id"];
    $orders = new OrdersModel();
    $order = $orders->load($id);
    if (!$order) {
      $this->setErrorMsg("Objednávka nenalezena. ID:".$id);
    }
    if ($order->ordpaystatus != $formVals["ordpaystatus"]) {
      if ($formVals["ordpaystatus"] == 1) {
        $formVals["ordstatus"] = 6;
        if (!empty($params["date"])) {
          $formVals["orddatepayed"] = $params["date"];
        } else {
          $formVals["orddatepayed"] = new DateTime();
        }
      }
      $orders->update($id, $formVals);
      if ($formVals["ordpaystatus"] == 1) $orders->logStatus($id, $formVals["ordstatus"], 6, $params["date"]);
      $order = $orders->load($id);
    }
    return array(
      "status" => ($order->ordpaystatus == $formVals["ordpaystatus"]),
    );
  }

  /**
  * GET shop/status
  * Informace o aktivaci obchodu v Košíku.
  */
  public function getShopStatusFromHeureka() {
    $str = file_get_contents($this->apiUrlFull.'/shop/status/');
    return(json_decode($str));
  }

  /**
  * GET payment/status
  * získání stavu objednávky na Heurece
  *
  * @param integer $ordid
  */
  public function getPaymentStatusFromHeureka($ordid) {
    $str = file_get_contents($this->apiUrlFull.'/payment/status?order_id='.$ordid);
    return(json_decode($str));
  }

  /**
  * GET order/status
  * Informace o stavu objednávky a interním čísle objednávky na Heurece.
  *
  * @param integer $ordid
  */
  public function getOrderStatusFromHeureka($ordid) {
    $str = file_get_contents($this->apiUrlFull.'/order/status?order_id='.$ordid);
    return(json_decode($str));
  }

  /**
  * PUT order/status
  * Nastavení stavu objednávy na Heurece.
  *
  * @param integer $ordid
  */
  public function putOrderStatusToHeureka($order) {
    $orders = new OrdersModel();
    $data = array(
      "order_id" => $order->ordid,
      "status" => $orders->getHeurekaKosikOrdStatusId($order->ordstatus),
    );
    $ch = curl_init();
    // set URL and other appropriate options
    curl_setopt($ch, CURLOPT_URL, $this->apiUrlFull."/order/status/");
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    $ret = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    $arr = json_decode($ret);

    if ($ret === FALSE) $this->setErrorMsg("cUrl error: $err");
    return($arr);
  }

  /**
  * PUT payment/status
  * Nastavení stavu platby na Heurece.
  *
  * @param integer $ordid
  */
  public function putPaymentStatusToHeureka($order) {
    $orders = new OrdersModel();
    $payDate = FALSE;
    if ($order->ordpaystatus == 1) {
      if (!empty($order->orddatepayed)) {
        $payDate = $order->orddatepayed;
      } else {
        $payDate = $orders->getPayDate($order->ordid);
      }
    }

    $data = array(
      "order_id" => $order->ordid,
      "status" => ($order->ordpaystatus == 1 ? 1 : -1),
      "date" => ($payDate === FALSE ? NULL : (string)$payDate),
    );

    $ch = curl_init();
    // set URL and other appropriate options
    curl_setopt($ch, CURLOPT_URL, $this->apiUrlFull."/payment/status/");
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    $ret = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    if ($ret === FALSE) $this->setErrorMsg("cUrl error: $err");
    return(json_decode($ret));
  }

  /**
  * POST order/note
  * Zaslání poznámky, které obchod vytvořil při procesu vyřizování objednávky.
  *
  * @param integer $ordid
  */
  public function putOrderNoteToHeureka($ordid, $note) {
    $data = array(
      "order_id" => $ordid,
      "note" => ($note),
    );

    $ch = curl_init();
    // set URL and other appropriate options
    curl_setopt($ch, CURLOPT_URL, $this->apiUrlFull."/order/note/");
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    $ret = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    if ($ret === FALSE) $this->setErrorMsg("cUrl error: $err");
    return(json_decode($ret));
  }

  /**
  * POST order/invoice
  * Zaslaní faktury (dokladu) k objednávce.
  *
  * @param integer $ordid
  */
  public function postOrderInvoiceToHeureka($order, $invoiceFilePath) {
    return(true);
    $invoiceFilePath = realpath($invoiceFilePath);
    if(!file_exists($invoiceFilePath)) $this->setErrorMsg('POST order/invoice: PDF soubor faktury se nepodarilo nacist');
    $filesize = filesize($invoiceFilePath);
    $file = $this->curlFileCreate($invoiceFilePath, 'application/pdf', $order->ordinvcode.".pdf");
    $data = array(
      "order_id" => $order->ordid,
      "invoice" => $file,
    );

    $headers = array("Content-Type:multipart/form-data");

    $ch = curl_init();
    // set URL and other appropriate options
    curl_setopt($ch, CURLOPT_URL, $this->apiUrlFull."/order/invoice");
    //curl_setopt($ch, CURLOPT_URL, "http://localhost/mobil-bar.cz/www/curl.php");
    curl_setopt($ch, CURLOPT_POST, TRUE);
    //curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_INFILESIZE, $filesize);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    $ret = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    if ($ret === FALSE) $this->setErrorMsg("cUrl error: $err");
    return(json_decode($ret));
  }

  private function curlFileCreate($filename, $mimetype = '', $postname = '') {
    if (!function_exists('curl_file_create')) {
      return "@$filename;filename="
          . ($postname ?: basename($filename))
          . ($mimetype ? ";type=$mimetype" : '');
    } else {
      return curl_file_create($filename, $mimetype, $postname);
    }
  }
}
?>

<?php

  use Nette\DateTime;

  class FioException extends \Exception { }

  class FioApi {

    /** @var string */
    const FIO_API_VERSION = '1.2.8';

    /** @var int [s] */
    const API_INTERVAL = 30;

    /** @var string */
    const REST_URL = 'https://fioapi.fio.cz/rest/';

    /** @var string */
    const REST_URL_WRITE = 'https://fioapi.fio.cz/rest/import/';

    const PAYMENT_STANDARD = 431001;
    const PAYMENT_INKASO = 431022;

    /**
     * Secure token
     *
     * @var string
     */
    private $token;

    /** @var string */
    private $accountNo;

    /** @var string */
    private $accountBankCode;

    /** @var string */
    public $errMsg;

    /** @var array */
    private $paymentData = array();


    /**
     * @param array $config (token, account)
     * @throws FioException
     */
    public function __construct($config) {
      if (empty($config["token"]) || empty($config["account"])) {
        throw new FioException('Nenalezeno nastaveni (token, account).');
      }
      $this->token = $config["token"];
      $account = $config["account"];
      $arr = explode('/', $account);
      $this->accountNo = $arr[0];
      $this->accountBankCode = $arr[1];
    }

    /**
     * Pohyby na účtu za určené období
     *
     * @param string|int|DateTime $from
     * @param string|int|DateTime $to
     * @return array
     */
    public function getMovements($from = '-2 day', $to = 'now') {
      $format = 'json';
      $this->requestUrl = self::REST_URL . sprintf('periods/%s/%s/%s/transactions.%s', $this->token, \Nette\Utils\DateTime::from($from)->format('Y-m-d'), \Nette\Utils\DateTime::from($to)->format('Y-m-d'), $format);
      $data = $this->download($this->requestUrl);
      $json = json_decode($data);

      $map = array(
        22 => 'moveId',
        0 => 'moveDate',
        1 => 'amount',
        14 => 'currency',
        //2 => 'toAccount',
        //10 => 'toAccountName',
        //3 => 'bankCode',
        //12 => 'bankName',
        //4 => 'constantSymbol',
        5 => 'variableSymbol',
        //6 => 'specificSymbol',
        //7 => 'userNote',
        //16 => 'message',
        //8 => 'type',
        //9 => 'performed',
        //18 => 'specification',
        //25 => 'comment',
        //26 => 'bic',
        //17 => 'instructionId',
        );

      if (!$json->accountStatement->transactionList) {
        // There are no transactions
        return NULL;
      }
      $ret = array();
      foreach ($json->accountStatement->transactionList->transaction as $row) {
        $out = array();
        $out["source"] = "fio";
        foreach ($row as $column) {
          if ($column) {
            if (isset($map[$column->id])) {
              $out[$map[$column->id]] = $column->value;
            }
          }
        }
        if ($out["currency"] === 'CZK' && $out["amount"] > 0) {
          $ret[] = $out;
        }
      }
      return $ret;
    }

    /**
    * stahne soubor GET
    *
    * @param string $url
    * @return string
    */
    private function download($url) {
      $curl_handle=curl_init();
      curl_setopt($curl_handle, CURLOPT_URL, $url);
      curl_setopt($curl_handle, CURLOPT_CONNECTTIMEOUT, 2);
      curl_setopt($curl_handle, CURLOPT_CUSTOMREQUEST, 'GET');
      curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, TRUE);
      curl_setopt($curl_handle, CURLOPT_SSL_VERIFYPEER, FALSE);
      curl_setopt($curl_handle, CURLOPT_USERAGENT, 'FIOAPI');
      $ret = curl_exec($curl_handle);
      $err = curl_error($curl_handle);
      curl_close($curl_handle);
      if ($ret === FALSE) {
        $this->setErrorMsg("cUrl error: $err");
      }
      return $ret;
    }

    /**
    * POST paments
    *
    * @param mixed $data
    */
    public function addPayment($data) {
      //validuju data

      $data['accountFrom'] = $this->accountNo;
      $data['currency'] = 'CZK';
      //pridam data
      $this->paymentData[] = $data;
    }

    /**
    * POST payments do FIO
    *
    */
    private function getXml() {
      if (count($this->paymentData) === 0) {
        return "";
      }
      $temp = ini_get('upload_tmp_dir');


      $temp = @realpath($temp);
      if (!$temp || !is_writable($temp)) {
          throw new FioException('Temporary directory must exists and writeable.');
      }

      $this->temp = $temp;

      $trans = "";
      foreach ($this->paymentData as $row) {
        $trans .= '
  <DomesticTransaction>
    <accountFrom>'.$row["accountFrom"].'</accountFrom>
    <currency>'.$row["currency"].'</currency>
    <amount>'.$row["amount"].'</amount>
    <accountTo>'.$row["accountTo"].'</accountTo>
    <bankCode>'.$row["bankCode"].'</bankCode>
    <ks>'.$row["ks"].'</ks>
    <vs>'.$row["vs"].'</vs>
    <ss>'.$row["ss"].'</ss>
    <date>'.$row["date"].'</date>
    <messageForRecipient>'.$row["messageForRecipient"].'</messageForRecipient>
    <comment>'.$row["comment"].'</comment>
    <paymentType>'.$row["paymentType"].'</paymentType>
  </DomesticTransaction>';
      }
      $xml = '<?xml version="1.0" encoding="UTF-8"?>
<Import xmlns:xsi="https://www.w3.org/2001/XMLSchema-instance"
xsi:noNamespaceSchemaLocation="https://www.fio.cz/schema/importIB.xsd">
<Orders>
'.$trans.'
</Orders>
</Import>';
      $filename = $this->temp . DIRECTORY_SEPARATOR . md5(microtime(TRUE)) . '.xml';
      file_put_contents($filename, $xml);
      register_shutdown_function(function() use ($filename) {
          @unlink($filename);
      });
      return($filename);
    }

    /**
     * @param string $filename
     * @return XMLResponse
     * @throws FioException
     */
    public function send() {
      if (count($this->paymentData) == 0) return FALSE;
      $filename = $this->getXml();
      $filesize = filesize($filename);
      $file = curl_file_create($filename);
      $data = array(
        'type' => 'xml',
        'token' => $this->token,
        'lng' => 'cs',
        'file' => $file
      );

      $ch = curl_init();
      // set URL and other appropriate options
      curl_setopt($ch, CURLOPT_URL, self::REST_URL_WRITE);
      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
      curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
      curl_setopt($ch, CURLOPT_HEADER, 0);
      curl_setopt($ch, CURLOPT_POST, TRUE);
      curl_setopt($ch, CURLOPT_VERBOSE, 0);
      curl_setopt($ch, CURLOPT_TIMEOUT, 60);
      curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: multipart/form-data; charset=utf-8;'));
      curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
      curl_setopt($ch, CURLOPT_INFILESIZE, $filesize);

      $ret = curl_exec($ch);
      $err = curl_error($ch);
      curl_close($ch);
      if ($ret === FALSE) $this->setErrorMsg("cUrl error: $err");
      return($ret);
    }

    private function setErrorMsg($msg) {
      $this->errMsg = $msg;
    }

  }
?>

<?php

class EcomailApi {

    const API_KEY = '6003f7a2004816003f7a20048f';

    private $ecomail;

    public function __construct() {
      //odešlu ro ecomailu
      $this->ecomail = new \Ecomail(self::API_KEY);
    }

    public function logNewOrder($listId, $order) {

     $data = [
        'subscriber_data' => [
          'name' => $order->ordiname,
          'surname' => $order->ordilname,
          'email' => $order->ordmail,
          'company' => $order->ordifirname,
          'city' => $order->ordicity,
          'street' => $order->ordistreet . " " . $order->ordistreetno,
          'zip' => $order->ordipostcode,
          'phone' => $order->ordtel,
          'custom_fields' => [
            'SHOP' => 'pstrosivejce.cz',
            'ORD_VALUE' => (int)$order->ordpricevat,
          ],
        ],
        'trigger_autoresponders' => false,
        'update_existing' => true,
        'resubscribe' => false,
      ];

      $trans = array();
      $trans["transaction"] = [
        "order_id"=> $order->ordcode,
        "email"=> $order->ordmail,
        "shop"=> "pstrosivejce.cz",
        "amount"=> $order->ordpricevat,
        "city"=> $order->ordicity,
        "timestamp"=> time(),
      ];

      foreach ($order->items as $row) {
        $trans["transaction_items"][] = [
          "code" => $row->oriprocode,
          "title" => $row->oriname,
          "price" => $row->oriprice,
          "amount" => $row->oriqty
        ];
      }

     try {
       if (IS_PRODUCTION) {
         $this->ecomail->addSubscriber($listId, $data);
         $this->ecomail->createNewTransaction($trans);
       }
       return true;
     } catch (Exception $e) {
       return false;
     }
    }

  }

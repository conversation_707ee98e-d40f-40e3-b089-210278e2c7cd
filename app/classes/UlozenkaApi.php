<?php
/**
* Ulozenka API
* http://docs.ulozenkav3.apiary.io/
* <AUTHOR>
*/
class UlozenkaException extends \Exception { }

class UlozenkaApi {
  //const API_URL = 'https://api.ulozenka.cz/v3';
  const API_URL = 'https://api.ulozenka.cz/v3';

  private $apiId;
  private $shopId;

  public $errMsg = "";

  public $activeServices = array(
    1 => 'Uloženka',
    //5 => 'DPD ParcelShop',

  );

  public $serviceShortcuts = array();

  public function __construct($config) {
    $this->shopId = $config["shopId"];
    $this->apiId = $config["apiKey"];

    $this->serviceShortcuts = dibi::query("SELECT uloshortcut, uloid2 FROM ulozenkapoints WHERE ulostatus=0")->fetchPairs("uloshortcut", "uloid2");
  }

  /**
  * vraci TRUE/FALSE zda je sluzba aktivni
  *
  */
  public function isApiOn() {
    return TRUE;
  }

  /**
   * POST consignments
   * /v3/consignments{?timeFrom,updatedFrom,limit,offset}
   *
   * Vytvoření nové zásilky
   *
   * @param mixed $order
   * @return int parcelId
   * @throws \Dibi\Exception
   */
  public function postParcelAdd($order) {
    if (!empty($order->ordparcode)) {
      $this->errMsg = "Objednávka č. ".$order->ordcode." už byla odeslaná/má přiřazené číslo balíku.";
      return FALSE;
    }
    $phone = trim($order->ordtel);
    $phone = str_replace(' ', '', $phone);
    $phone = "+420".substr($phone, -9);
    //nactu zpusob platby
    $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    //pokud je jina dodaci adresa
    if (!empty($order->ordstname)) {
      $order->ordiname = $order->ordstname;
      $order->ordilname = $order->ordstlname;
      $order->ordifirname = $order->ordstfirname;
      $order->ordistreet = $order->ordststreet;
      $order->ordistreetno = $order->ordststreetno;
      $order->ordicity = $order->ordstcity;
      $order->ordipostcode = $order->ordstpostcode;
    }
    $arr = array(
      "transport_service_id" => 1,
      "address_state" => "CZE",
      "destination_branch_id" =>  $this->serviceShortcuts[$order->orddelspec],
      "order_number" => (string)$order->ordcode,
      "parcel_count" => 1,
      "cash_on_delivery" => ($payMode->delcode=='cash' ? (double)$order->ordpricevat : ""),
      "currency" => "CZK",
      "variable" => (string)$order->ordcode,
      "password" => "",
      "customer_name" => (string)$order->ordiname,
      "customer_surname" => (string)$order->ordilname,
      "company_name" => (string)$order->ordifirname,
      "customer_phone" => (string)$phone,
      "customer_email" => (string)$order->ordmail,
      "address_street" => (string)$order->ordistreet.' '.$order->ordistreetno,
      "address_town" => (string)$order->ordicity,
      "address_zip" => (string)$order->ordipostcode,
      "allow_card_payment" => 0,
      "require_full_age" => 0,
      "note" => "křehké".(!empty($order->ordnotedel) ? ", ".$order->ordnotedel : "")
    );
    $json = json_encode($arr);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, self::API_URL."/consignments");
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    curl_setopt($ch, CURLOPT_POST, TRUE);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "X-Shop: ".$this->shopId,
      "X-Key: ".$this->apiId,
    ));
    $response = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    if ($response === FALSE) {
      $this->errMsg = "cUrl error: $err";
      return FALSE;
    } else {
      $res = json_decode($response);
      if (isset($res->code)) {
        $code = $res->code;
        if (substr($code, 0, 1) == '2') {
          $id = (int)$res->data[0]->id;
          return $id;
        }

        if (is_array($res->errors)) {
          $msg = "";
          foreach ($res->errors as $key => $err) {
            $msg .= $err->description . " ";
          }
          $this->errMsg = "postParcelAdd error. " . $msg;
        } else {
          $this->errMsg = "postParcelAdd error. Code=".$code.", OrdId=".$order->ordid;
        }
        return FALSE;
      }
    }
  }

  /**
  * POST labels
  * /v3/labels
  *
  * vrati sadu stitku
  *
  * @param array $consignments cisla baliku
  * @param string $format format stitku [pdf|zpl]
  * @param int $format $firstPosition pozice prvniho stitku na strance
  * @return tiskova sestava podle formatu
  */
  public function postLabels($consignments, $format="pdf", $firstPosition=1) {
    if (empty($format)) $format = 'pdf';
    if (empty($firstPosition)) $firstPosition = 1;

    $fields = array(
      'type' => $format,
      'first_position' => $firstPosition,
      'labels_per_page' => 4,
      'consignments' => $consignments,
    );
    $jsonFields = json_encode($fields);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, self::API_URL."/labels");
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    curl_setopt($ch, CURLOPT_POST, TRUE);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonFields);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "X-Shop: ".$this->shopId,
      "X-Key: ".$this->apiId,
    ));
    $response = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    if ($response === FALSE) {
      $this->errMsg = "cUrl error: $err";
      return FALSE;
    } else {
      $res = json_decode($response);
      if (isset($res->code)) {
        $code = $res->code;
        if (substr($code, 0, 1) == '2') {
          $file = $res->data[0]->labels;
          return $file;
        } else {
          $this->errMsg = 'postLabels error: '.$res->errors[0]->description;
          return FALSE;
        }
      }
    }
  }


  /**
  * GET statuses
  * /v3/statuses/{id}
  *
  * Zjistí stav zásilky
  *
  * @param int $id ID balíku
  * @return int parcelId
  */
  public function getConsignments($id) {
    if (empty($id)) return FALSE;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, self::API_URL."/consignments/".$id);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "X-Shop: ".$this->shopId,
      "X-Key: ".$this->apiId,
    ));
    $response = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    if ($response === FALSE) {
      $this->errMsg = "cUrl error: $err";
      return FALSE;
    } else {
      $res = json_decode($response);
      if (isset($res->code)) {
        $code = $res->code;
        if (substr($code, 0, 1) == '2') {
          $id = (int)$res->data[0]->status->id;
          $name = $res->data[0]->status->name;
          return array('id'=>$id, 'text'=>$name);
        } else {
          $str = $res->errors[0]->description;
          $this->errMsg = 'getParcelStatus error. '.$str.', ParcelId='.$id;
          return FALSE;
        }
      }
    }
  }

  public function updateBranches() {
    dibi::query("DELETE FROM ulozenkapoints");
    dibi::query("ALTER TABLE ulozenkapoints AUTO_INCREMENT=1");
    $ulos = $this->model->getUlozenkapointsModel();
    foreach ($this->activeServices as $key => $service) {
      $url = self::API_URL."/transportservices/".$key."/branches?shopId=".$this->shopId;
      $json = file_get_contents($url);
      $jsonDecoded = json_decode($json);
      foreach ($jsonDecoded->data->destination as $row) {
        //jen mista v CR
        $openingHours = "";
        if (isset($row->opening_hours->regular->monday->hours[0]->open)) {
          $hours = $row->opening_hours->regular->monday->hours[0];
          $openingHours .= "Pondělí: $hours->open - $hours->close <br>";
        }
        if (isset($row->opening_hours->regular->tuesday->hours[0]->open)) {
          $hours = $row->opening_hours->regular->tuesday->hours[0];
          $openingHours .= "Úterý: $hours->open - $hours->close <br>";
        }
        if (isset($row->opening_hours->regular->wednesday->hours[0]->open)) {
          $hours = $row->opening_hours->regular->wednesday->hours[0];
          $openingHours .= "Středa: $hours->open - $hours->close <br>";
        }
        if (isset($row->opening_hours->regular->thursday->hours[0]->open)) {
          $hours = $row->opening_hours->regular->thursday->hours[0];
          $openingHours .= "Čtvrtek: $hours->open - $hours->close <br>";
        }
        if (isset($row->opening_hours->regular->friday->hours[0]->open)) {
          $hours = $row->opening_hours->regular->friday->hours[0];
          $openingHours .= "Pátek: $hours->open - $hours->close <br>";
        }
        if (isset($row->opening_hours->regular->saturday->hours[0]->open)) {
          $hours = $row->opening_hours->regular->saturday->hours[0];
          $openingHours .= "Sobota: $hours->open - $hours->close <br>";
        }
        if (isset($row->opening_hours->regular->sunday->hours[0]->open)) {
          $hours = $row->opening_hours->regular->sunday->hours[0];
          $openingHours .= "Neděle: $hours->open - $hours->close <br>";
        }
        $navigation = "";
        if (!empty($row->navigation->general)) $navigation .= $row->navigation->general."<br>";
        if (!empty($row->navigation->car)) $navigation .= "Autem: ".$row->navigation->car."<br>";
        if (!empty($row->navigation->public_transport)) $navigation .= "Hromadná doprava: ".$row->navigation->public_transport."<br>";
        if (!empty($navigation)) $navigation = "<p>$navigation</p>";
        $status = ((int)$row->active == 1 ? 0 : 1);
        $data = array(
            'uloid2' => $row->id,
            'uloshortcut' => (string)$row->shortcut,
            'uloname' => (string)$row->name,
            'ulostreet' => (string)$row->street.' '.$row->house_number,
            'ulocity' => (string)$row->town,
            'ulopostcode' => (string)$row->zip,
            'ulocountry' => (string)$row->country,
            'uloemail' => (string)$row->email,
            'ulophone' => (string)$row->phone,
            'uloopeninghours' => $openingHours,
            'ulonavigation' => $navigation,
            'ulostatus' => $status,
            'ulourl' => (string)$row->_links->website->href,
            'ulourlphoto' => (string)$row->_links->picture->href,
            'ulourlself' => (string)$row->_links->self->href,
            'ulogpsn' => (string)$row->gps->latitude,
            'ulogpse' => (string)$row->gps->longitude,
          );
        if ($status == 0) $ulos->insert($data);
      }
    }
  }

  public function setErrorMsg($msg) {
    Tracy\Debugger::log($msg);
    header("HTTP/1.0 500 Internal Server Error");
    $data = array(
      "id" => 500,
      "msg" => $msg,
    );
    echo json_encode($data);
    die();
  }

}
?>

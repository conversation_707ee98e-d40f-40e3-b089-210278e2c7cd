<?php

class BoxxiApi {
  const API_URL = 'https://api.boxxi-logistics.cz/v1/';

  public string $errMsg = "";

  /** @var ModelFactory */
  protected ModelFactory $model;

  public function __construct($config, $model) {
    $this->model = $model;

    $this->apiKey = $config["apiKey"];
  }

  /**
   * POST consignments
   * /v3/consignments{?timeFrom,updatedFrom,limit,offset}
   *
   * Vytvoření nové zásilky
   *
   * @param $order
   * @return array|false
   * @throws \Dibi\Exception
   */
  public function postParcelAdd($order) {
    if (!empty($order->ordparcode)) {
      $this->errMsg = "Objednávka č. ".$order->ordcode." už byla odeslaná/má přiřazené číslo balíku.";
      return FALSE;
    }

    $phone = trim($order->ordtel);
    $phone = str_replace(' ', '', $phone);

    $phonePrefix = $order->ordicouid == 1 ? '+420' : '+421';

    if (!empty($phone)) {
      $phone = $phonePrefix . substr($phone, -9);
    }
    //nactu zpusob platby
    $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);
    //pokud je jina dodaci adresa
    if (!empty($order->ordstname) && !empty($order->ordstlname)) {
      $order->ordiname = $order->ordstname;
      $order->ordilname = $order->ordstlname;
      $order->ordifirname = $order->ordstfirname;
      $order->ordistreet = $order->ordststreet;
      $order->ordistreetno = $order->ordststreetno;
      $order->ordicity = $order->ordstcity;
      $order->ordipostcode = $order->ordstpostcode;
      $order->ordicouid = $order->ordstcouid;
    }

    $ordweight = 2;
    $parcelsCount = 1;
    if (!empty($order->ordweight)) {
      $ordweight = round((double)$order->ordweight/$parcelsCount, 1);
    }

    $addressFrom = [
      "Country"=> "CZ",
      "ContactName"=> "Pavel Wieder",
      "PhoneNumber"=> "*********",
      "Email"=> "<EMAIL>",
      "CompanyName"=> "Pštrosí Vršek Wieder",
      "Street"=> "Služovice 154",
      "StreetLine1"=> "",
      "StreetLine2"=> "",
      "City"=> "Služovice",
      "PostalCode"=> "74728",
      "Note"=> "",
      "OpenTime"=> "",
      "CloseTime"=> ""
    ];

    $addressTo = [
      "Country"=> "CZ",
      "ContactName"=> $order->ordiname . " " . $order->ordilname,
      "PhoneNumber"=> (string)$phone,
      "Email"=> (string)$order->ordmail,
      "CompanyName"=> (string)$order->ordifirname,
      "Street"=> $order->ordistreet . " " . $order->ordistreetno,
      "StreetLine1"=> "",
      "StreetLine2"=> "",
      "City"=> (string)$order->ordicity,
      "PostalCode"=> $order->ordipostcode,
      "Note"=> "",
      "OpenTime"=> "",
      "CloseTime"=> ""
    ];

    $arr = [
      "AddressFrom" => $addressFrom,
      "AddressTo" => $addressTo,
      "DeliverAt" => $order->orddeldatedel->format("Y-m-d"),
      "PreparedForPickup" => false,
      "ClientOrderNumber" => (string)$order->ordcode,
      "Currency" => ($order->ordicouid == 1 ? 'CZK' : 'EUR'),
      "PackageCount" => 1,
      "EveningDelivery" => true,
      "TotalWeight" => $ordweight,
      "Temperature" => "Cold",
      "Note" => "",
    ];

    //kontrola povinnych poli
    $emptyFields = array();
    if (empty($arr["ClientOrderNumber"])) $emptyFields[] = "Kód obj.";
    if (empty($arr["DeliverAt"])) $emptyFields[] = "Termín dodání.";
    if (empty($arr["AddressTo"]["ContactName"])) $emptyFields[] = "Jméno příjmení";
    if (empty($arr["AddressTo"]["Email"])) $emptyFields[] = "Email";
    if (empty($arr["AddressTo"]["PhoneNumber"])) $emptyFields[] = "Telefon";
    if (empty($arr["AddressTo"]["Street"])) $emptyFields[] = "Ulice";
    if (empty($arr["AddressTo"]["City"])) $emptyFields[] = "Město";
    if (empty($arr["AddressTo"]["PostalCode"])) $emptyFields[] = "PSČ";
    if (count($emptyFields)) {
      $this->errMsg = "Nejsou vyplněny tyto povinné pole:".implode(", ", $emptyFields);
      return false;
    }

    if ($payMode->delcode == 'dobirka' || $payMode->delcode == 'cash') {
      $arr["CodAmount"] = (double)$order->ordpricevat;
    } else if ($payMode->delcode == 'cofidis' || $payMode->delcode == 'cetelem') {
      $arr["CodAmount"] = (double)$order->ordfirstpay;
    }

    $json = json_encode($arr);

    $packet = $this->curlQuery("shipments", $json);


    if ($packet !== false) {
      $data = json_decode($packet, TRUE);
      if (isset($data["ShipmentNumber"]) && isset($data["PackageNumbers"]) && count($data["PackageNumbers"]) > 0) {
        return [
          "ShipmentNumber" => $data["ShipmentNumber"],
          "PackageNumbers" => $data["PackageNumbers"]
        ];
      }
    }

    return false;
  }

  public function getParcelStatus($order) {
    $path = "shipments/" . $order->ordparid . "/state";

    $response = $this->curlQuery($path, NULL, "GET");

    if ($response !== false) {
      $data = json_decode($response, TRUE);
      if (isset($data["Status"])) {
        return (string)$data["Status"];
      }
    }

    return false;
  }

  private function curlQuery($path, $data, $type="POST") {

    $headers[] = 'X-Api-key: '. $this->apiKey;

    $ch = curl_init($this::API_URL . $path);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    if ($type === "POST") {
      $headers[] = "Content-Type: application/json";
      curl_setopt($ch, CURLOPT_POST, 1);
      //curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
      if (!empty($data)) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
      }
    } else {
      curl_setopt($ch, CURLOPT_POST, 0);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
    }

    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    try {
      $response = curl_exec($ch);
    } catch (\Exception $e) {
      \Tracy\Debugger::log($response);
      \Tracy\Debugger::log($e->getMessage());
      $this->errMsg = "Nastala chyba:" . $e->getMessage();
      return FALSE;
    }

    $errors = curl_error($ch);

    if (!empty($errors)) {
      $this->errMsg = "Nastala chyba:" . $errors;
      \Tracy\Debugger::log($response);
      \Tracy\Debugger::log($errors);
      return FALSE;
    }

    curl_close($ch);

    return $response;
  }
}

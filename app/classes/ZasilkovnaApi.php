<?php
/**
* Zasilkovna API
* http://www.zasilkovna.cz/popis-api
* <AUTHOR>
*/
class ZasilkovnaException extends \Exception { }

class ZasilkovnaApi {

  /**
   * @var array nastavení pro Zásilkovnu
   */
  private $config = array();

  /**
   * @var \Salamek\Zasilkovna\ApiRest
   */
  private $api;

  /**
   * @var \Salamek\Zasilkovna\Branch
   */
  private $branch;

  /**
   * @var \Salamek\Zasilkovna\Label
   */
  private $label;

  /**
   * @var array chybové zprávy
   */
  public $errMsgs = array();



  public function __construct($config) {
    $this->config = $config;
    $this->api = new Salamek\Zasilkovna\ApiRest($this->config["apiPassword"], $this->config["apiKey"]);
  }

  /**
  * vraci TRUE/FALSE zda je sluzba aktivni
  *
  */
  public function isApiOn() {
    return TRUE;
  }

  /**
   * Vytvoření nové <PERSON>
   *
   * @param mixed $order
   * @return \Salamek\Zasilkovna\Model\PacketAttributes
   * @throws \Dibi\Exception
   */
  public function createPacket($order) {
    if (!empty($order->ordparcode)) {
      $this->errMsg = "Objednávka č. ".$order->ordcode." už byla odeslaná/má přiřazené číslo balíku.";
      return FALSE;
    }

    $phone = trim($order->ordtel);
    $phone = str_replace(' ', '', $phone);
    $phone = "+420".substr($phone, -9);
    //nactu zpusob platby
    $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    //pokud je jina dodaci adresa
    if (!empty($order->ordstname)) {
      $order->ordiname = $order->ordstname;
      $order->ordilname = $order->ordstlname;
      $order->ordifirname = $order->ordstfirname;
      $order->ordistreet = $order->ordststreet;
      $order->ordistreetno = $order->ordststreetno;
      $order->ordicity = $order->ordstcity;
      $order->ordipostcode = $order->ordstpostcode;
    }

    $cod = NULL;
    if ($payMode->delcode === 'dobirka' || $payMode->delcode === 'cash') {
      $cod = (double)$order->ordpricevat;
    }

    $transporterPackage = new \Salamek\Zasilkovna\Model\PacketAttributes(
      $order->ordcode,
      $order->ordiname,
      $order->ordilname,
      (double)$order->ordpricevat,
      $order->orddelspeczasilkovna,
      null,
      (string)$order->ordifirname,
      (string)$order->ordmail,
      $phone,
      'CZK',
      $cod,
      (double)$order->ordweight,
      $this->config["eshoName"],
      false,
      $order->ordistreet,
      $order->ordistreetno,
      $order->ordicity,
      $order->ordipostcode
    );

    try {
      $id = $this->api->createPacket($transporterPackage);
      $transporterPackage->setId($id);
    } catch(\Salamek\Zasilkovna\Exception\RestFault $e) {
      $this->errMsg = "postParcelAdd error. Kód obj.=".$order->ordcode." : ".$e->getMessage();
    }
    return $transporterPackage;
  }

  /**
   * POST labels
   * /v3/labels
   *
   * vrati sadu stitku
   *
   * @param array $consignments cisla baliku
   * @throws Exception
   */
  public function generateLabels($consignments) {
    $this->branch = new \Salamek\Zasilkovna\Branch($this->config["apiKey"], new \Salamek\Zasilkovna\Model\BranchStorageSqLite()); // There are multiple implementations of IBranchStorage BranchStorageSqLite using SQLite, BranchStorageFile using file in /tmp and BranchStorageMemory using simple variable (SLOW), You can implement your own by implementing IBranchStorage interface
    $this->label = new \Salamek\Zasilkovna\Label($this->api, $this->branch);
    try {
      $this->label->generateLabels($consignments, \Salamek\Zasilkovna\Enum\LabelDecomposition::QUARTER);
    } catch(SoapFault $e) {
      $this->errMsg = "postParcelAdd error: ".$e->getMessage();
    }
  }

  public function updateBranches() {
    $this->branch = new \Salamek\Zasilkovna\Branch($this->config["apiKey"], new \Salamek\Zasilkovna\Model\BranchStorageFile()); // There are multiple implementations of IBranchStorage BranchStorageSqLite using SQLite, BranchStorageFile using file in /tmp and BranchStorageMemory using simple variable (SLOW), You can implement your own by implementing IBranchStorage interface
    dibi::query("DELETE FROM zasilkovnapoints");
    dibi::query("ALTER TABLE zasilkovnapoints AUTO_INCREMENT=1");
    $zass = $this->model->getZasilkovnapointsModel();

    $list = $this->branch->getBranchList();
    foreach ($list as $row) {
      if ((string)$row["country"] !== 'cz' && (string)$row["country"] !== 'sk') {
        continue;
      }
      $openingHours = "";
      if (isset($row->openingHours->regular->monday)) {
        $hours = $row->openingHours->regular->monday;
        if (is_string($hours)) $openingHours .= "Pondělí: $hours <br>";
      }
      if (isset($row->openingHours->regular->tuesday)) {
        $hours = $row->openingHours->regular->tuesday;
        if (is_string($hours)) $openingHours .= "Úterý: $hours <br>";
      }
      if (isset($row->openingHours->regular->wednesday)) {
        $hours = $row->openingHours->regular->wednesday;
        if (is_string($hours)) $openingHours .= "Středa: $hours <br>";
      }
      if (isset($row->openingHours->regular->thursday)) {
        $hours = $row->openingHours->regular->thursday;
        if (is_string($hours)) $openingHours .= "Čtvrtek: $hours <br>";
      }
      if (isset($row->openingHours->regular->friday)) {
        $hours = $row->openingHours->regular->friday;
        if (is_string($hours)) $openingHours .= "Pátek: $hours <br>";
      }
      if (isset($row->openingHours->regular->saturday)) {
        $hours = $row->openingHours->regular->saturday;
        if (is_string($hours)) $openingHours .= "Sobota: $hours <br>";
      }
      if (isset($row->openingHours->regular->sunday)) {
        $hours = $row->openingHours->regular->sunday;
        if (is_string($hours)) $openingHours .= "Neděle: $hours <br>";
      }
      $navigation = "";
      if (!empty($row->directions)) $navigation .= $row->directions."<br>";
      if (!empty($row->directionsCar)) $navigation .= "Autem: ".$row->directionsCar."<br>";
      if (!empty($row->directionsPublic)) $navigation .= "Hromadná doprava: ".$row->directionsPublic."<br>";

      $data = array(
          'zasid2' => $row["id"],
          'zasname' => (string)$row["place"].', '.(string)$row["nameStreet"],
          'zasstreet' => (string)$row["street"],
          'zascity' => (string)$row["city"],
          'zaspostcode' => (string)$row["zip"],
          'zascountry' => (string)$row["country"],
          'zasopeninghours' => $openingHours,
          'zasnavigation' => $navigation,
          'zasstatus' => 0,
          'zasurl' => (string)$row["url"],
          'zasurlphoto' => (string)$row["photos"][0]["normal"],
          'zasgpsn' => (string)$row["latitude"],
          'zasgpse' => (string)$row["longitude"],
        );
      $zass->insert($data);
    }
  }
}

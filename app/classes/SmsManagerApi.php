<?php
/**
* SmsManager API
* https://smsmanager.cz/api/
* <AUTHOR>
*/

class SmsManagerApi {

  private $apiKey;
  private $apiUrl = 'https://http-api.smsmanager.cz/Send';
  private $gateway = 'high';

  public $lastErrorDesc;

  public function __construct($apiKey) {
    $this->apiKey = $apiKey;
  }

  /**
  * vraci TRUE/FALSE zda je sluzba aktivni
  *
  */
  public function isApiOn() {
    return TRUE;
  }

  public function sendSms($phoneNumber, $text) {
    $phoneNumber = trim($phoneNumber);
    $phoneNumber = str_replace(' ', '', $phoneNumber);
    $phoneNumber = substr($phoneNumber, -9);
    $phoneNumber = '00420'.$phoneNumber;

    $data = [
      "apikey"=>$this->apiKey,
      "number"=>$phoneNumber,
      "message"=>$text,
      "gateway"=>$this->gateway,
      //"sender"=>"pstrosivejce.cz",
    ];

    $ch = curl_init($this->apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

    // execute!
    $response = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    if ($response === FALSE) {
      $this->lastErrorDesc = $err;
    } else {
      \Tracy\Debugger::log($response);
      $responseArr = explode("|", trim($response, "|"));
      if (is_array($responseArr) && count($responseArr) > 2) {
        if ($responseArr[0] === 'OK') {
          return true;
        } else if ($responseArr[0] === 'ERROR') {
          $errCode = $responseArr[1];
          $this->lastErrorDesc = "Neznámá chyba";
          if (isset($this->errorCodes[$errCode])) {
            $this->lastErrorDesc = $this->errorCodes[$errCode];
          }
          return FALSE;
        }
      }
    }
    return FALSE;
  }
}

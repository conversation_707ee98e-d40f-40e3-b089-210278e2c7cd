<?php

require LIBS_DIR . '/geis-api/' . 'Geis.php';

class GeisException extends Exception { }

class GeisApi {

  /** @var Geis */
  private $service;

  /**
   * @var string číslo klienta
   */
  private $clientNumber;
  /**
   * @var string telefonní číslo
   */
  private $phone;

  /**
   * @var string email odesílatele
   */
  private $email;

  /**
   * do kolika hodin (HHMM) objednat svoz, aby přijel ještě týž den
   */
  private $pickupDeadLine;

  public function __construct($config) {

    $this->clientNumber = $config["customerCode"];

    try {

      if (empty($this->clientNumber)) {
        throw new GeisException("Služba není řádně nastavena");
      }

      // přihlašovací údaje ke službě G-Service musíte vyžádat u Geis, jsou jiné než u GClienta
      $this->service = new \Geis(
        $clientNumber = $this->clientNumber, $clientPassword = $config["password"],
        $service = 'CZ', // jiné hodnoty: SK, PL, PL2 (if you use etlogistik.com)
        $testService = FALSE // pokud probíhá jen testování
      );
    } catch (Exception $e) {
      throw new GeisException($e);
    }

    $this->phone = $config["phone"];
    $this->email = $config["email"];
    $this->pickupDeadLine = $config["pickupDeadLine"];
  }

  public function isApiOn() {
    return !empty($this->clientNumber);
  }

  public function getServiceStatus() {
    $status = array();

    $status["status"] = $this->service->IsHealthy();
    $status["nextPossiblePickup"] = $this->service->getNextPossiblePickup($this->pickupDeadLine);

    $wrap = $this->service->WrapList();
    $status["wraps"] = $wrap->Wrap;

    $service = $this->service->ServiceList();
    $status["services"] = $service->Service;

    $statuses = $this->service->StatusList();
    $status["statuses"] = $statuses->Status;

    return $status;
  }

  /**
   * vrací nejbližší možný termín pro svoz
   */
  public function getNextPossiblePickup() {
    return $this->service->getNextPossiblePickup($this->pickupDeadLine);
  }

  /**
   * vytvoří svoz
   *
   * @param $date
   * @param $parcelsCnt
   */
  public function createPickup($date, $parcelsCnt) {
    $this->service->CreatePickup($date,$this->phone, $this->email, $pocetBaliku = $parcelsCnt);
  }

  public function postParcel($ord, $pickupDate) {

    //pokud je jina dodaci adresa
    if (!empty($ord->ordstname)) {
      $ord->ordiname = $ord->ordstname;
      $ord->ordilname = $ord->ordstlname;
      $ord->ordifirname = $ord->ordstfirname;
      $ord->ordistreet = $ord->ordststreet;
      $ord->ordistreetno = $ord->ordststreetno;
      $ord->ordicity = $ord->ordstcity;
      $ord->ordipostcode = $ord->ordstpostcode;
    }

    $receiver = Geis::getFullAddress(
      $ord->ordiname . " " . $ord->ordilname . (!empty($ord->ordifirname) ? ", " . $ord->ordifirname : ""),
      $ord->ordistreet . " " . $ord->ordistreetno,
      $ord->ordicity,
      str_replace(" ", "", $ord->ordipostcode),
      'CZ',
      $ord->ordmail,
      $ord->ordtel
    );
    // Seznam balíků v zásilce (obvykle 1)
    // rozměry není nutno zadávat - popřípadě uvádět v metrech a stačí jen u prvního balíku
    $packages = array();
    $packages[] = array($ord->ordweight, 0, 0, 0, NULL); // hmotnost kg

    //zjistim jestli se jedna o dobirku
    //nactu zpusob platby
    $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $ord->orddelid);
    $cod = NULL;
    if ($payMode->delcode==='cash' || $payMode->delcode==='dobirka') {
      $cod = array($ord->ordpricevat, 'CZK', $ord->ordcode);
    }
    $ret = $this->service->InsertExport(
      $pickupDate, // den svozu
      $receiver, // příjemce zásilky
      $packages,
      '', // poznámka řidiči
      $cod, // dobírka, měna, variabilní symbol
      true, // zda poslat příjemci balíku potvrzovací email
      $ord->ordcode, // reference zásilky (volitelně)
      true, // zda jde o soukromou adresu (B2C)
      '' // Geis Pickup Point - ID destinace
    );
    return $ret->PackNumber;
  }


  public function ParcelDetail($pacelNumber) {
    return $this->service->ShipmentDetail($pacelNumber);
  }

  public function DeleteShipment($parcels) {
    return $this->service->DeleteShipment($parcels);
  }

  public function PrintLabel($parcels, $position=1) {
    $ret = $this->service->PrintLabel($parcels, $position);
    $this->downloadPdf($ret);
    return true;
  }

  public function PrintPickupList($date) {
    $ret = $this->service->PrintPickupList($date); // vytiskne PDF
    $this->downloadPdf($ret);
    return true;
  }

  private function downloadPdf($file) {
    header('Content-Disposition: attachment;filename="geis_labels.pdf"');
    header('Content-Type: application/force-download');
    header('Content-Length: ' . (strlen($file)));
    echo $file;
  }
}

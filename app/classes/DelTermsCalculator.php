<?php


class DelTermsCalculator {

  const DEL_TYPE_OSOBNE = "OSOBNE";
  const DEL_TYPE_KURYR = "KURYR";
  const DEL_TYPE_ROZVOZ = "VLASTNI_PREPRAVA";


  /** @var date */
  private $todayDate;

  /** @var DateInfo */
  private $todayDateInfo ;

  /** @var array */
  private $holidays;

  /** @var array */
  private $config;

  public function __construct($holidays = array()) {
    $this->config = array(
      self::DEL_TYPE_KURYR =>
        array(
          'termsCount' => 18,
        ),
      self::DEL_TYPE_ROZVOZ =>
        array(
          'termsCount' => 10,
        ),
      self::DEL_TYPE_OSOBNE =>
        array(
          'termsCount' => 18,
        ),
    );


    if (empty($holidays)) {
      //nactu svatky
      $this->holidays = dibi::query("SELECT CONCAT(holmonth, '-' ,holday) AS datekey, holmonth, holday FROM holidays ORDER BY holmonth, holday")->fetchAssoc("datekey");
    }

    //vyplnim dnesni datum
    $this->todayDate = time();
    $this->todayDateInfo = new DateInfo($this->todayDate, $this->holidays);
  }

  /**
   * vraci nejbližší termíny dodání pro jednotlivé typy doprav
   *
   * @param $dateFrom
   * @param $dateTo
   * @return array
   */
  public function getDelDates($dateFrom, $dateTo) {
    $delTerms = array();
    $arr = $this->getEnumDelTypes();
    foreach ($arr as $key => $row) {
      $delTerms[$key] = $this->getDelDatesByType($key, $dateFrom, $dateTo);
    }


    return $delTerms;
  }


  /***
   *
   * @param $dates
   * @param $dateFrom
   * @param $dateTo
   * @return array
   */
  public function getDelDatesSpecial($dates, $dateFrom, $dateTo) {
    $curDate = $this->todayDateInfo;
    $delTerms = array();

    foreach ($dates as $date) {
      $dateInfo = new dateInfo(strtotime($date), $this->holidays);
      $key = $dateInfo->data . $dateInfo->data;
      $delTerms[$key] = $dateInfo->label;
    }

    $delTerms = $this->checkLimits($delTerms, $dateFrom, $dateTo);

    return $delTerms;
  }



  /***
   *
   * @param $delType [VLASTNI_PREPRAVA|GEIS|OSOBNE]
   * @param $dateFrom
   * @param $dateTo
   * @return array
   */
  public function getDelDatesByType($delType, $dateFrom, $dateTo) {
    $curDate = $this->todayDateInfo;

    //pro rozvoz/odběrná místa může objednat v Ut do 12:00 na St
    if ($delType === self::DEL_TYPE_OSOBNE || $delType === self::DEL_TYPE_ROZVOZ) {
      //zjisím jestli není Út po 12:00
      If (($curDate->weekDayIndex === 2) && (int)date('G', $curDate->date) > 12) {
        //posunu začátek o den, středu už nestihne
        $curDate = $curDate->getNextDay();
      }
    }
    $delTerms = array();
    $startDate = NULL;
    while (TRUE) {
      //kolik termínů mám vrátit?
      $cntConfig = (int)$this->config[$delType]["termsCount"];
      $c = count($delTerms);
      if ($cntConfig <= $c) {
        break;
      }
      //najdu nejbližší datum pro svoz
      $startDate = $this->getStartDate($curDate, $delType);
      $endDate = $this->getEndDate($startDate, $delType);
      if ($endDate->isWorkDay) {
        $key = $startDate->data . $endDate->data;
        $delTerms[$key] = $endDate->label;
        if ($delType === self::DEL_TYPE_OSOBNE) {
          //pokud další den je taky pracovní může si převzít na odběrném místě i následující den
          $nextDate = $endDate->getNextDay();
          if ($nextDate->isWorkDay) {
            $key = $startDate->data . $nextDate->data;
            $delTerms[$key] = $nextDate->label;
          }
        }
      }

      $curDate = $startDate;
      $startDate = NULL;
    }

    $delTerms = $this->checkLimits($delTerms, $dateFrom, $dateTo);

    return $delTerms;
  }

  /**
   * vrací datum počátku přepravy (svoz/chystání vlastní přepravy)
   *
   * @param $date DateInfo
   * @param $delType
   * @return DateInfo
   */
  private function getStartDate(DateInfo $date, $delType) {
    if ($delType === self::DEL_TYPE_KURYR) {
      $daysBefore = 2;
      $cntDays = 0;
      while (TRUE) {
        $cntDays++;
        $date = $date->getNextDay();
        //je to 1 nebo 3 a je to pracovní den
        if (($cntDays >= $daysBefore) && ($date->weekDayIndex === 1 || $date->weekDayIndex === 3) && $date->isWorkDay) {
          //mám datum svozu
          return $date;
        }
      }
    } else if ($delType === self::DEL_TYPE_ROZVOZ || $delType === self::DEL_TYPE_OSOBNE) {
      while (TRUE) {
        $date = $date->getNextDay();
        //je to 1 nebo 3 a je to pracovní den
        if ($date->weekDayIndex === 2 && $date->isWorkDay) {
          //mám datum startu
          return $date;
        }
      }
    }
  }

  /**
   * vrací datum počátku přepravy (svoz/chystání vlastní přepravy)
   *
   * @param $date DateInfo
   * @param $delType
   * @return DateInfo
   */
  private function getEndDate(DateInfo $date, $delType) {
    return $date->getNextDay();
  }

  private function checkLimits($delTerms, $dateFrom, $dateTo) {
    if (empty($dateFrom) || empty($dateTo)) {
      return $delTerms;
    }

    foreach ($delTerms as $key => $label) {
      $from = substr($key, 0, 4) . "-" . substr($key, 4, 2). "-" . substr($key, 6, 2);
      $key = substr($key, 8);
      $to = substr($key, 0, 4) . "-" . substr($key, 4, 2). "-" . substr($key, 6, 2);
      $dateFromText = $dateFrom->format('Y-m-d');
      $dateToText = $dateTo->format('Y-m-d');
      $delTermInInterval = (bool)dibi::fetchSingle("SELECT DATE('$to') BETWEEN DATE('$dateFromText') AND DATE('$dateToText')");
      if (!$delTermInInterval) {
        unset($delTerms[$key]);
      }
    }
    return $delTerms;
  }

  private function getEnumDelTypes() {
    return array(
      self::DEL_TYPE_ROZVOZ => "Rozvoz",
      self::DEL_TYPE_KURYR => "Kurýr",
      self::DEL_TYPE_OSOBNE => "Osobně",

    );
  }
}

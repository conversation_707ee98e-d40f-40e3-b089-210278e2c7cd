<?php


class DateInfo {
  /** @var integer */
  public $date;

  /** @var string */
  public $dateFormated;

  /** @var string */
  public $dateFormatedSys;

  /** @var integer */
  public $weekDayIndex;

  /** @var integer */
  public $weekIndex;

  /** @var integer */
  public $month;

  /** @var integer */
  public $day;

  /** @var boolean */
  public $isWorkDay;

  /** @var string */
  public $label;

  /** @var string */
  public $data;

  /** @var array */
  private $holidays;

  public function __construct($date, $holidays) {
    $this->date = $date;

    $this->holidays = $holidays;

    //vyplní ostatní informace o datumu
    $this->fillInfo();

  }

  private function fillInfo() {

    $dayName = array(
      1 => "pondělí",
      2 => "úterý",
      3 => "středa",
      4 => "čtvrtek",
      5 => "pátek",
      6 => "sobota",
      0 => "neděle",
    );

    $this->weekDayIndex = (int)date('w', $this->date);
    $this->weekIndex = (int)date('W', $this->date);
    $this->month = (int)date('n', $this->date);
    $this->day = (int)date('j', $this->date);
    $this->dateFormated = (string)date('d.m.Y', $this->date);
    $this->dateFormatedSys = (string)date('Y-m-d', $this->date);
    $this->isWorkDay = $this->isWorkingDay();
    $this->label = $this->dateFormated . " " . $dayName[$this->weekDayIndex];
    $this->data = (string)date('Ymd', $this->date);
  }

  /**
   * vrátí datum + jeden den
   *
   * @return DateInfo
   */
  public function getNextDay() {
    $newDate = $this->date + (24 * 60 * 60);
    return new DateInfo($newDate, $this->holidays);
  }

  /**
   * vrací zda se jedná o pracovní den
   *
   * @return bool
   */
  private function isWorkingDay() {
    //je to sobota nebo neděle?
    if ($this->weekDayIndex === 6 || $this->weekDayIndex === 0) {
      return false;
    }
    //je to svátek?
    $key = $this->month . '-' . $this->day;
    if (isset($this->holidays[$key])) {
      return false;
    }
    return true;
  }
}

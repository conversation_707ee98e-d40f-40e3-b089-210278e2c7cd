<?php

/**
 * <AUTHOR> Data Latvia
 * @version 1.0
 * @package sample
 */


/**
 * Package
 * @package sample
 * @subpackage classes
 */

class fioECom {
  //const DOMAIN = 'https://secureshop-test.firstdata.lv';
  const DOMAIN = 'https://secureshop.firstdata.lv';

  const DB_TABLE_TRANSACTIONS = 'fio_transaction';
  const DB_TABLE_BATCH = 'fio_batch';
  const DB_TABLE_ERROR = 'fio_error';

  var $language = 'cs';
  var $currency = 203;

  /**
   * Variable $url
   * @access private
   * @var string
   */
  var $url;
   /**
   * Variable $keystore
   * @access private
   * @var string
   */
  var $keystore;
  /**
   * Variable $keystorepassword
   * @access private
   * @var string
   */
  var $keystorepassword;

  /**
   * Variable $verbose
   * @access private
   * @var boolean
   */
  var $verbose;

  var $errors = array();

  /**
   * Constructor sets up {$link, $keystore, $keystorepassword, $verbose}
   * @param string $url url to declare
   * @param string $keystore value of the keystore
   * @param string $keystorepassword value of the keystorepassword
   * @param boolean $verbose TRUE to output verbose information. Writes output to STDERR, or the file specified using CURLOPT_STDERR.
  */
  function fioECom($id, $keystorepassword, $verbose = 1, $language = 'cs') {
      $this->url = self::DOMAIN.':8443/ecomm/MerchantHandler';
      $this->keystore = dirname(__FILE__).'/certs/'.$id.'keystore.pem';
      $this->keystorepassword = $keystorepassword;
      $this->verbose = $verbose;
  }


  function isOk() {
    return (count($this->errors) == 0);
  }

  /**
   * Send parameters
   *
   * @param array post parameters
   * @return string result
   */
  function sentPost($params){

	  if(!file_exists($this->keystore)){
		  $result = "file " . $this->keystore . " not exists";
      $this->errors[] = $result;
		  return $result;
	  }

	  $post = "";

  	foreach ($params as $key => $value){
		   $post .= "$key=$value&";
	  }

  	//$post = http_build_query($params);

	  $curl = curl_init();
	  if($this->verbose){
		  curl_setopt($curl, CURLOPT_VERBOSE, true);
		  curl_setopt($curl, CURLOPT_STDERR, fopen(LOG_DIR . '/curl.log', 'wb+'));
	  }
    curl_setopt($curl, CURLOPT_URL, $this->url);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
    //curl_setopt($curl, CURLOPT_SSLVERSION,2);
    curl_setopt($curl, CURLOPT_SSLCERT, $this->keystore);
    curl_setopt($curl, CURLOPT_CAINFO, $this->keystore);
    curl_setopt($curl, CURLOPT_SSLKEYPASSWD, $this->keystorepassword);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $post);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
	  $result =curl_exec ($curl);

    if(curl_error($curl)){
		  $result = curl_error($curl);
		  error_log($result);
	  }
	  curl_close ($curl);
	  return $result;
  }

    /**
   * Registering of transaction
   * @param string $type type transaction
   * @param int $amount transaction amount in minor units, mandatory
   * @param string $client_ip_addr client’s IP address, mandatory
   * @param string $description description of transaction, optional
   * @return string TRANSACTION_ID
  */
  function startTrans($type, $amount, $client_ip_addr, $description, $account) {
    $ret = array();
    $amount = (double)$amount * 100;
    $trans_id = "";
    $url = "";
    switch ($type) {
       case 'sms':
         $resp = $this->startSMSTrans($amount, urlencode($client_ip_addr), urlencode($description), $account);
         break;
       case 'dms':
         $resp = $this->startDMSAuth($amount, urlencode($client_ip_addr), urlencode($description), $account);
         break;
    }
    if (substr($resp,0,14)=="TRANSACTION_ID") {
      $trans_id = substr($resp,16,28);
      $ret["url"] = self::DOMAIN."/ecomm/ClientHandler?trans_id=".urlencode($trans_id);
      $ret["transId"] = $trans_id;
    } else{
      $this->errors[] = "Nepodařilo se zjistit TRANSACTION_ID. ".$resp;
    }

    if (!empty($trans_id)) {
      $result = dibi::query("INSERT INTO ".self::DB_TABLE_TRANSACTIONS." VALUES ('', '$trans_id', '$account', '$amount', '".$this->currency."', '$client_ip_addr', '$description', '".$this->language."', 'NO', '???', 'NO')");

      if (!$result) {
        $this->errors[] = '*** Invalid query: ' . mysql_error();
      }
    }
    return $ret;
  }

  /**
   * Registering of SMS transaction
   * @param int $amount transaction amount in minor units, mandatory
   * @param string $ip client’s IP address, mandatory
   * @param string $desc description of transaction, optional
   * @return string TRANSACTION_ID
  */

  function startSMSTrans($amount, $ip, $desc, $account){

	  $params = array(
		  'command' => 'v',
		  'amount'  => $amount,
		  'currency'=> $this->currency,
		  'client_ip_addr'      => $ip,
		  'description'    => $desc,
      'language'=> $this->language,
		  'account'=> $account
	  );
	  return $this->sentPost($params);
  }

  /**
   * Registering of DMS authorisation
   * @param int $amount transaction amount in minor units, mandatory
   * @param string $ip client’s IP address, mandatory
   * @param string $desc description of transaction, optional
   * @return string TRANSACTION_ID
  */

  function startDMSAuth($amount, $ip, $desc, $account){

	  $params = array(
		  'command' => 'a',
		  'msg_type'=> 'DMS',
		  'amount'  => $amount,
		  'currency'=> $this->currency,
		  'client_ip_addr'      => $ip,
		  'description'    => $desc,
      'account'=> $account
	  );
	  return $this->sentPost($params);
  }

  /**
   * Making of DMS transaction
   * @param int $auth_id id of previously made successeful authorisation
   * @param int $amount transaction amount in minor units, mandatory
   * @param string $ip client’s IP address, mandatory
   * @param string $desc description of transaction, optional
   * @return string RESULT, RESULT_CODE, RRN, APPROVAL_CODE
  */

  function makeDMSTrans($auth_id, $amount, $ip, $desc, $account){

	  $params = array(
		  'command' => 't',
		  'msg_type'=> 'DMS',
		  'trans_id' => $auth_id,
		  'amount'  => $amount,
		  'currency'=> $this->currency,
		  'client_ip_addr' => $ip,
		  'description'    => $desc,
		  'language'=> $this->language,
      'account'=> $account
	  );

	  $str = $this->sentPost($params);
	  return $str;
  }

  /**
   * Transaction result
   * @param int $trans_id transaction identifier, mandatory
   * @param string $ip client’s IP address, mandatory
   * @return string RESULT, RESULT_CODE, 3DSECURE, AAV, RRN, APPROVAL_CODE
  */

  public function getTransResult($trans_id, $ip){
    //$trans_id = urlencode($trans_id);
    //$ip = urlencode($ip);
	  $params = array(
		  'command' => 'c',
		  'trans_id' => $trans_id,
		  'client_ip_addr'      => $ip
	  );

    return $this->sentPost($params);
  }

  /**
   * Transaction reversal
   * @param int $trans_id transaction identifier, mandatory
   * @param int $amount transaction amount in minor units, mandatory
   * @return string RESULT, RESULT_CODE
  */

  function reverse($trans_id, $amount){
    $trans_id = urlencode($trans_id);
	  $params = array(
		  'command'  => 'r',
		  'trans_id' => $trans_id,
		  'amount'   => (double)$amount * 100
	  );

	  $str = $this->sentPost($params);
	  return $str;
  }

  /**
   * Closing of business day
   * @return string RESULT, RESULT_CODE, FLD_075, FLD_076, FLD_087, FLD_088
  */

  function closeDay(){

    $params = array(
		  'command' => 'b',
	  );

	  $str = $this->sentPost($params);
	  return $str;

  }

  function  getResponseMessage($responseCode) {
    $msg = "";
    switch ($responseCode) {
       case '000':
         $msg = "Transakce schválena.";
         break;
       case '400':
         $msg = "Vrácení platby proběhlo úspěšně.";
         break;
       case '129':
         $msg = "Transakce NEBYLA schválena.";
         break;
       default:
         $msg = "Transakce NEBYLA schválena."." (Response Code: $responseCode)";
    }
    return $msg;
  }



}
?>

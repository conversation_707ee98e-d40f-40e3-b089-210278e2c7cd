<?php

class ModelFactory {
  /* @var */
  public $storage;

  private $models = Array();

    /** ID aktualni meny; */
  private $curId = 1;

  /** zaokrouleni pocet des. mist aktualni meny */
  private $curDigits = 0;

  /** meny ktere eshop pouziva */
  private $currencies = array();

  private $prccat = 'a';

  public function __construct($storage) {
    $this->storage = $storage;
  }

  /**
   * nastavi aktualni menu
   * @param array $currencies
   * @param $curId
   */
  public function setCurrency($currencies, $curId) {
    $this->currencies = $currencies;
    $this->curId = (int)$curId;
    $this->curDigits = (int)$this->currencies[$this->curId]["decimals"];
  }

  /**
   * @param string $prccat
   */
  public function setPrcCat($prccat) {
    $this->prccat = $prccat;
  }

  /**
   * @param $name
   * @return mixed
   */
  private function getModel($name) {
    if (!isset($this->models[$name])) {
      $className = "\Model\\" . $name . 'Model';
      $this->models[$name] = new $className($this);
      $this->models[$name]->setCurrency($this->currencies, $this->curId);
      $this->models[$name]->setPrcCat($this->prccat);
    }

    return $this->models[$name];
  }

  /**
   * @param $name string
   * @param bool $onlyActive
   * @return array
   */
  public function getEnum($name, $onlyActive = TRUE) {
    if ($name === 'proaccess') {
      return $this->getModel('Products')->getEnumProAccess();
    }

    if ($name === 'protag1') {
      return $this->getModel('Products')->getEnumProTag1();
    }

    if ($name === 'protag2') {
      return $this->getModel('Products')->getEnumProTag2();
    }

    if ($name === 'protag3') {
      return $this->getModel('Products')->getEnumProTag3();
    }

    if ($name === 'proorigin') {
      return $this->getModel('Products')->getEnumProOrigin();
    }

    if ($name === 'manid') {
      return $this->getModel('Manufacturers')->getEnumManId();
    }

    if ($name === 'newtypid') {
      return $this->getModel('News')->getEnumNewTypId();
    }
    if ($name === 'newgrpid') {
      return $this->getModel('News')->getEnumNewGrpId();
    }

    if ($name ==='manid') {
      return $this->getModel('Manufacturers')->getEnumManId($onlyActive);
    }

    if ($name ==='ordstatus') {
      return $this->getModel('Orders')->getEnumOrdStatus();
    }

  }

  /**
   *
   * @return \Model\AdminsModel
   */
  public function getAdminsModel() {
    return $this->getModel("Admins");
  }

  /**
   *
   * @return \Model\ArticlesModel
   */
  public function getArticlesModel() {
    return $this->getModel("Articles");
  }

  /**
   *
   * @return \Model\AttachmentsModel
   */
  public function getAttachmentsModel() {
    return $this->getModel("Attachments");
  }

  /**
   *
   * @return \Model\BaseModel
   */
  public function getBaseModel() {
    return $this->getModel("Base");
  }

  /**
   *
   * @return \Model\BookmarksModel
   */
  public function getBookmarksModel() {
    return $this->getModel("Bookmarks");
  }


  /**
   *
   * @return \Model\CatalogsModel
   */
  public function getCatalogsModel() {
    return $this->getModel("Catalogs");
  }


  /**
   *
   * @return \Model\CatPlacesModel
   */
  public function getCatPlacesModel() {
    return $this->getModel("CatPlaces");
  }

  /**
   *
   * @return \Model\CommentsModel
   */
  public function getCommentsModel() {
    return $this->getModel("Comments");
  }

  /**
   *
   * @return \Model\ConfigModel
   */
  public function getConfigModel() {
    return $this->getModel("Config");
  }


  /**
   *
   * @return \Model\CouponsModel
   */
  public function getCouponsModel() {
    return $this->getModel("Coupons");
  }

  /**
   *
   * @return \Model\DeliveriesModel
   */
  public function getDeliveriesModel() {
    return $this->getModel("Deliveries");
  }

  /**
   *
   * @return \Model\DeliveryModesModel
   */
  public function getDeliveryModesModel() {
    return $this->getModel("DeliveryModes");
  }

  /**
   *
   * @return \Model\DelPathItemsModel
   */
  public function getDelPathItemsModel() {
    return $this->getModel("DelPathItems");
  }

  /**
   *
   * @return \Model\ConfigModel
   */
  public function getDiscountsModel() {
    return $this->getModel("Discounts");
  }


  /**
   *
   * @return \Model\EnumcatsModel
   */
  public function getEnumcatsModel() {
    return $this->getModel("Enumcats");
  }


  /**
 *
 * @return \Model\FulltextlogsModel
 */
  public function getFulltextlogsModel() {
    return $this->getModel("Fulltextlogs");
  }

    /**
   *
   * @return \Model\HolidaysModel
   */
  public function getHolidaysModel() {
    return $this->getModel("Holidays");
  }

  /**
   *
   * @return \Model\MailingsModel
   */
  public function getMailingsModel() {
    return $this->getModel("Mailings");
  }


  /**
   *
   * @return \Model\MailingStatsModel
   */
  public function getMailingStatsModel() {
    return $this->getModel("MailingStats");
  }


  /**
   *
   * @return \Model\MailsModel
   */
  public function getMailsModel() {
    return $this->getModel("Mails");
  }


  /**
   *
   * @return \Model\ManufacturersModel
   */
  public function getManufacturersModel() {
    return $this->getModel("Manufacturers");
  }


  /**
   *
   * @return \Model\MenuIndexsModel
   */
  public function getMenuIndexsModel() {
    return $this->getModel("MenuIndexs");
  }


  /**
   *
   * @return \Model\MenusModel
   */
  public function getMenusModel() {
    return $this->getModel("Menus");
  }


  /**
   *
   * @return \Model\NewsModel
   */
  public function getNewsModel() {
    return $this->getModel("News");
  }


  /**
   *
   * @return \Model\OrdersModel
   */
  public function getOrdersModel() {
    return $this->getModel("Orders");
  }


  /**
   *
   * @return \Model\OrdItemsModel
   */
  public function getOrdItemsModel() {
    return $this->getModel("OrdItems");
  }


  /**
   *
   * @return \Model\PagesModel
   */
  public function getPagesModel() {
    return $this->getModel("Pages");
  }


  /**
   *
   * @return \Model\PayModesModel
   */
  public function getPayModesModel() {
    return $this->getModel("PayModes");
  }


  /**
   *
   * @return \Model\ProAccessModel
   */
  public function getProAccessModel() {
    return $this->getModel("ProAccess");
  }


  /**
   *
   * @return \Model\ProductPricesModel
   */
  public function getProductPricesModel() {
    return $this->getModel("ProductPrices");
  }


  /**
   *
   * @return \Model\ProductsModel
   */
  public function getProductsModel() {
    return $this->getModel("Products");
  }

  /**
   *
   * @return \Model\ProlinksModel
   */
  public function getProlinksModel() {
    return $this->getModel("Prolinks");
  }


  /**
   *
   * @return \Model\ProparamDefsModel
   */
  public function getProparamDefsModel() {
    return $this->getModel("ProparamDefs");
  }


  /**
   *
   * @return \Model\ProParamsModel
   */
  public function getProParamsModel() {
    return $this->getModel("ProParams");
  }

  /**
   *
   * @return \Model\StoItemsModel
   */
  public function getStoItems() {
    return $this->getModel("StoItems");
  }

  /**
   *
   * @return \Model\UlozenkapointsModel
   */
  public function getUlozenkapointsModel() {
    return $this->getModel("Ulozenkapoints");
  }


  /**
   *
   * @return \Model\UsersModel
   */
  public function getUsersModel() {
    return $this->getModel("Users");
  }

  /**
   *
   * @return \Model\WatchdogsModel
   */
  public function getWatchdogsModel() {
    return $this->getModel("Watchdogs");
  }

  /**
   *
   * @return \Model\ZasilkovnapointsModel
   */
  public function getZasilkovnapointsModel() {
    return $this->getModel("Zasilkovnapoints");
  }
}

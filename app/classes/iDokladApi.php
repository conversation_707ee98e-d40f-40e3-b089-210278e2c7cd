<?php


use malcanek\iDoklad\iDoklad;
use malcanek\iDoklad\request\iDokladFilter;
use malcanek\iDoklad\request\iDokladRequest;
use Model\OrdersModel;

class iDokladApi {

  public $lastErrorMsg = "";

  /**
   * @var ModelFactory
   */
  private $model;

  private $clientId;
  private $secret;


  public function __construct($config, $model) {
    $this->clientId = $config["id"];
    $this->secret = $config["secret"];
    $this->model = $model;
  }

  public function postOrder($id, $curKey, $redirectUrl) {

    $iDoklad = new iDoklad($this->clientId, $this->secret, $redirectUrl);
    $iDoklad->authCCF();

    $ords = $this->model->getOrdersModel();
    $ord = $ords->load($id);
    if (!empty($ord->ordinvcode)) {
      $this->lastErrorMsg = "Tato objenávka již má vygenerovanou fakturu.";
      return false;
    }

    //kontrola povinných polí
    $companyName = (!empty($ord->ordifirname) ? $ord->ordifirname : $ord->ordilname . ' ' . $ord->ordiname);
    $postCode = str_replace(' ', '', $ord->ordipostcode);
    $mandarCheck = TRUE;
    if (empty($companyName)) {
      $this->lastErrorMsg .= "Jméno, příjmení nebo název firmy musí být vyplněný ";
      $mandarCheck = false;
    }

    if (empty($ord->ordicouid)) {
      $this->lastErrorMsg .= "Země musí být vyplněna ";
      $mandarCheck = false;
    }
    if ($mandarCheck === false) {
      return false;
    }

    $PurchaserId = 0;

    $ordItems = dibi::fetchAll("
    SELECT *
    FROM orditems
    LEFT JOIN products ON (oriproid=proid)
    WHERE oriordid=%i", $ord->ordid, " AND oritypid=0 ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");

    //rozpočítám dopravu dle sazeb DPH
    $delivery = dibi::fetch("SELECT * FROM orditems WHERE oriordid=%i", $ord->ordid, " AND oritypid=1");
    $delPrice = $delivery->oriprice;

    $pros = $this->model->getProductsModel();
    $vats = $pros->getEnumProVatId();

    $configVat = dibi::query("select cfgcode, cfgvalue from config where cfgcode like 'VATTYPE_%'")->fetchPairs("cfgcode", "cfgvalue");

    //nastavím proměnné
    $vatSum = 0;
    $vatSumLevel = array();
    foreach ($vats as $key => $row) {
      $vatLev[$key] = (double)$configVat["VATTYPE_".$key]/100;
      $vatSumaryPro[$key] = array();
      $vatSumaryPro[$key]['price'] = 0;
      $vatSumaryPro[$key]['vat'] = 0;
      $vatSumaryPro[$key]['vatLevel'] = $configVat["VATTYPE_".$key];
      $vatSumaryPro[$key]['pricevat'] = 0;
    }


    //načtu položky obj
    //převodník ID dph eshop => id DPH iDoklad
    $VatIdConvert = array(
      0 => 1, //zakladni
      1 => 0, //snizena
      2 => 3, //snizena 2
      3 => 2  //nulová
    );

    //převodník ID zemí eshop => id zemi iDoklad
    if ($curKey === 'CZK') {
      $CouIdConvert = array(
        1 => 2, //ČR
        2 => 1, //Slovensko
      );
    } else {
      $CouIdConvert = array(
        1 => 14, //Austria
        2 => 86, //Germany
        3 => 177, //Poland
        4 => 1, //Slovakia
        5 => 233, //United Kingdom
      );
    }

    foreach ($VatIdConvert as $key => $row) {
      $vatSumLevel[$key] = 0;
    }

    //načtu položky obj.
    $iDokladItems = array();
    $vatSumLevels = array();

    foreach ($ordItems as $key => $row) {
      $iDokladItems[$key] = array(
        'Name' => $row->oriname,
        'Unit' => 'ks',
        'UnitPrice' => $row->oriprice,
        'PriceType' => 0,
        'Amount' => $row->oriqty,
        'VatRateType' => $VatIdConvert[$row->orivatid]
      );

      if ($row->oriweight > 0) {
        if ($ord->ordmode === OrdersModel::ORDER_MODE_WHOLESALE) {
          if ($row->promasid > 0) {
            //načtu název zboží hlavní položky
            $proName = dibi::fetchSingle("SELECT proname FROM products WHERE proid=%i", $row->promasid);
            $iDokladItems[$key]["Name"] = $proName . " " . $this->formatWeight($row->oriweightreal);
          }
          $iDokladItems[$key]["Amount"] = (double)$row->oriweightreal;
          $iDokladItems[$key]["Unit"] = "ks";
          $iDokladItems[$key]["Amount"] = "1";
          $iDokladItems[$key]["UnitPrice"] = (double)$row->oripricereal;
        } else {
          $iDokladItems[$key]["Amount"] = (int)$row->oriqty;
          $iDokladItems[$key]["Unit"] = "ks";
          $iDokladItems[$key]["UnitPrice"] = (double)$row->oriprice;
        }
      }

      //uložím si všechny sazby DPH v objednávce
      $vatSumLevels[$row->orivatid] = $row->orivatid;

      // sumarizace DPH pro dopravu
      $vatSumLevel[$row->orivatid] += (double)$row->oriprice*(double)$row->oriqty;
    }

    foreach ($vatSumLevel as $key => $valSum) {
      $vatSum += $valSum;
    }

    if (count($vatSumLevel) > 0) {
      //rozpočítám dopravu
      foreach ($vatSumLevel as $key => $sum) {
        $delPriceByVat = round($vatSumLevel[$key]/$vatSum*(double)$delPrice, 2);
        if ($delPriceByVat > 0) {
          $iDokladItems[] = array(
            'Name' => $delivery->oriname,
            'Unit' => 'ks',
            'UnitPrice' => $delPriceByVat,
            'PriceType' => 0,
            'Amount' => 1,
            'VatRateType' => $VatIdConvert[$key]
          );
        }
      }
    }

    //pokud je sleva vložím slevu
    $discount = dibi::fetch("SELECT * FROM orditems WHERE oriordid=%i", $ord->ordid, " AND oritypid=3");
    if ($discount) {
      //zjistim sazbu pro slevu
      $discountVatId = 0;
      if (count($vatSumLevels) === 1) {
        $discountVatId = reset($vatSumLevels);
      }

      $iDokladItems[] = array(
        'Name' => $discount->oriname,
        'Unit' => 'ks',
        'UnitPrice' => $discount->oriprice,
        'PriceType' => 0,
        'Amount' => 1,
        'VatRateType' => 0
      );
    }

    //sleva je nad objednávkou
    if ((double)$ord->orddisc > 0) {
      //zjistim sazbu pro slevu
      $discountVatId = 0;
      if (count($vatSumLevels) === 1) {
        $discountVatId = reset($vatSumLevels);
      }

      $iDokladItems[] = array(
        'Name' => "Slevový kupón " . $ord->ordcoucode,
        'Unit' => 'ks',
        'UnitPrice' => (int)$ord->orddisc * (-1),
        'PriceType' => 0,
        'Amount' => 1,
        'VatRateType' => 0
      );
    }

    //načtu kontakt
    $request = new iDokladRequest('Contacts');

    if (!empty($ord->ordic)) {
      $filter = new iDokladFilter('IdentificationNumber', '==', $ord->ordic);
      $request->addFilter($filter);
    } else {
      $filter = new iDokladFilter('CompanyName', '==', $companyName);
      $request->addFilter($filter);
      if (!empty($ord->ordistreet)) {
        $filter = new iDokladFilter('Street', '==', trim($ord->ordistreet . ' ' . $ord->ordistreetno));
        $request->addFilter($filter);
      }
      if (!empty($ord->ordicity)) {
        $filter = new iDokladFilter('City', '==', $ord->ordicity);
        $request->addFilter($filter);
      }
      if (!empty($postCode)) {
        $filter = new iDokladFilter('PostalCode', '==', $postCode);
        $request->addFilter($filter);
      }
      $request->setFilterType('and');
    }

    /*
    //pokud je sleva v %
    $discountPer = 0;
    if (!empty($ord->ordcoucode)) {
      $coupon = dibi::fetch("SELECT * FROM coupons WHERE coucode=%s", $ord->ordcoucode);
      if ($coupon && $coupon->couvalue > 0 && $coupon->couvalueunit === '%') {
        $discountPer = $coupon->couvalue;
      }
    }
    */

    $request->addMethodType('GET');
    $response = $iDoklad->sendRequest($request);
    $data = $response->getData();
    $cnt = (int)$response->getTotalItems();
    if ($cnt === 1) {
      $PurchaserId = $data[0]["Id"];
    } else {
      //zalozim novy kontakt
      $requestNewContact = new iDokladRequest('Contacts');
      $requestNewContact->addMethodType('POST');
      $data = array(
        'CompanyName' => $companyName,
        'Firstname' => $ord->ordiname,
        'Surname' => $ord->ordilname,
        'Street' => trim($ord->ordistreet.' '.$ord->ordistreetno),
        'City' => $ord->ordicity,
        'CountryId' => $CouIdConvert[$ord->ordicouid],
        'PostalCode' => $postCode,
        'Mobile' => $ord->ordtel,
        'Email' => $ord->ordmail,
        'IdentificationNumber' => $ord->ordic,
        'VatIdentificationNumber' => $ord->orddic
      );
      $requestNewContact->addPostParameters($data);
      $response = $iDoklad->sendRequest($requestNewContact);
      if ($response->getCode() === 200) {
        $data = $response->getData();
        $PurchaserId = (int)$data["Id"];
      } else {
        $this->lastErrorMsg = "Založení nového konmtaktu do idokladu se nezdařilo: ".$response->getCodeText();
        return FALSE;
      }
    }


    //převod číselník pro způsob úhrady
    $dels = $this->model->getDeliveryModesModel();
    $payMode = $dels->load($ord->orddelid);
    $paymentType = array(
      "paybefore" => 1, //převodem
      "creditcard" => 2, //kartou
      "cash" => 3, //hotově
      "dobirka" => 4, //dobírka
    );

    $payId = 3;
    if (isset($paymentType[$payMode->delcode])) {
      $payId = $paymentType[$payMode->delcode];
    }

    // odešlu fakturu
    if ($PurchaserId > 0) {
      $request = new iDokladRequest('IssuedInvoices');
      $request->addMethodType('POST');
      $data = array(
          'PurchaserId' => $PurchaserId,
          'VariableSymbol' => $ord->ordcode,
          'CurrencyId' => $curKey === 'CZK' ? 1 : 2,
          'OrderNumber' => $ord->ordcode,
          'PaymentOptionId' => $payId,
          'Description' => 'ESHOP',
          'IssuedInvoiceItems' => $iDokladItems
      );

      /*
      if ($discountPer > 0) {
        $data["DiscountPercentage"] = $discountPer;
      }
      */

      $request->addPostParameters($data);
      try {
        $response = $iDoklad->sendRequest($request);
      } catch (\malcanek\iDoklad\iDokladException $e) {
        $this->lastErrorMsg = $e->getMessage();
      }

      if ($response->getCode() === 200) {
        $data = $response->getData();
        $invCode = $data["DocumentNumber"];
        $invId = (int)$data["Id"];
        $ords->update($id, array(
          'ordinvcode' => $invCode,
          'ordcode2' => $invId,
          'ordinvdate' => new \DateTime(),
        ));

        //odmailuji
        $sendRequest = new iDokladRequest('IssuedInvoices');
        $sendRequest->addMethodType('POST');
        $data = array(
          'DocumentId' => $invId,
          'Method' => 1,
          'SendToAccountant' => FALSE,
          'SendToPartner' => TRUE,
          'SendToSelf' => FALSE,
        );

        $sendRequest->addPostParameters($data);

        try {
          $response = $iDoklad->sendRequest($sendRequest);
        } catch (\malcanek\iDoklad\iDokladException $e) {
          $this->lastErrorMsg = $e->getMessage();
        }

        return $invId;
      }

      $this->lastErrorMsg = $response->getCodeText();
      return FALSE;
    }
  }

  public function mailOrder($id, $redirectUrl) {
    $iDoklad = new iDoklad($this->clientId, $this->secret, $redirectUrl);
    $iDoklad->authCCF();

    //odmailuji
    $sendRequest = new iDokladRequest('Mails/IssuedInvoice/Send');
    $sendRequest->addMethodType('POST');
    $data = array(
      'DocumentId' => $id,
      'Method' => 1,
      'OtherRecipients' => ['<EMAIL>'],
      'SendToAccountant' => FALSE,
      'SendToPartner' => TRUE,
      'SendToSelf' => FALSE,
    );

    $sendRequest->addPostParameters($data);

    try {
      $response = $iDoklad->sendRequest($sendRequest);
    } catch (\malcanek\iDoklad\iDokladException $e) {
      $this->lastErrorMsg = $e->getMessage();
      return FALSE;
    }
    return true;
  }


  /**
   * vraci naformatovanou hmotnost
   *
   * @param $weight
   * @param mixed $decimals
   * @return string
   */
    private function formatWeight($weight, $decimals=3) {
      //if ((double)$weight == 0) return "na dotaz";
      $weight = (double)$weight;
      $formated = str_replace(" ", "\xc2\xa0", number_format($weight, $decimals, ",", " "));
      if ($decimals === 1) {
        $formated .= '0';
      }
      return $formated." Kg";
    }
}

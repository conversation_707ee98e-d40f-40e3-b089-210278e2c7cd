<?php
namespace Model;
use dibi;
use Nette;

class ModelException extends \Exception { }

abstract class BaseModel {

  use Nette\SmartObject;

  //identifikatory vlastnosti datoveho pole
  /** @var string nazev tabulky */
  protected $tableName;

  /** @var string nastaveni */
  protected $config;

  /** @var string nazev tabulky s prefixem */
  protected $table;

  /**
  * @var Nette\Caching\IStorage
  */
  public $storage;

  /**
   * @var \ModelFactory
   */
  public $model;

  protected $cache;

  /** nastaveni z neonu */
  public $neonParameters = array();

  /** ID aktualni meny; */
  protected $curId = 1;

  /** zaokrouleni pocet des. mist aktualni meny */
  protected $curDigits = 2;

  /** meny ktere eshop pouziva */
  protected $currencies = array();

  protected $prccat = 'a';

  const ORDER_MODE_RETAIL = "r";
  const ORDER_MODE_WHOLESALE = "w";

  const ITEM_STORETYPE_WEIGHT = 'w';
  const ITEM_STORETYPE_QTY = 'q';

  public function __construct($model) {
    $this->table = $this->tableName;
    $this->model = $model;
    $this->storage = $model->storage;
    $this->cache = new Nette\Caching\Cache($this->storage, $this->tableName);
  }

  /**
   * nastavi aktualni menu
   * @param $currencies
   * @param $curId
   */
  public function setCurrency($currencies, $curId) {
    $this->currencies = $currencies;
    $this->curId = (int)$curId;
    $this->curDigits = (int)$this->currencies[$this->curId]["decimals"];
  }

  /**
   * nastavi aktualni menu
   * @param $prccat
   */
  public function setPrcCat($prccat) {
    $this->prccat = $prccat;
  }


  /**
  * vraci cache modelu
  *
  */
  public function getCache() {
    return $this->cache;
  }


  public function getDataSource($sql="") {
    if ($sql == "") $sql = $this->getSql();
    return dibi::dataSource($sql);
  }

  public function getSql () {
    return "SELECT * FROM $this->table";
  }

  /**
   * vraci jeden zaznam
   *
   * @param integer $id hodnota id ve smyslu jednoznacneho identifikatoru, nemusi byt primarni klic
   * @param string $col nazev sloupce bez prefixu
   * @return \Dibi\Row
   * @throws \Dibi\Exception
   */
  public function load($id, $col='id') {
    $colName = $this->fieldPrefix.$col;
    if ($col == 'id') {
      $f = 'i';
    } else {
      $f = 's';
    }
    return dibi::fetch($this->getSql()." WHERE ".$colName.'=%'.$f, $id, " LIMIT 1");
  }

  /**
   * vrací typ skladové položky váhová|kusová
   *
   * @param $pro array záznam z tabulky products
   * @return string self::ITEM_STORETYPE_WEIGHT|self::ITEM_STORETYPE_QTY
   */
  public function getItemStoreType($pro) {
    $ean = $pro->procode2;
    if (strpos($ean, '29') === 0) {
      return self::ITEM_STORETYPE_WEIGHT;
    }
    return self::ITEM_STORETYPE_QTY;
  }

  public function update($id, $data,$setDateU = True) {
    $this->cacheClean();
    if ($setDateU) $data[$this->fieldPrefix.'dateu'] = new \DateTime;
    return dibi::update($this->table, $data)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }

  public function insert($data) {
    $this->cacheClean();
    if (!isset($data[$this->fieldPrefix.'datec'])) {
      $data[$this->fieldPrefix.'datec'] = new \DateTime;
    }
    return dibi::insert($this->table, $data)
      ->execute(dibi::IDENTIFIER);
  }

  public function save(&$id, $data, $setDateU = True) {
    if ($id > 0) {
      return $this->update($id, $data, $setDateU);
    } else {
      $id = $this->insert($data);
      return($id > 0);
    }
  }

  public function delete($id) {
    $this->cacheClean();
    return dibi::delete($this->table)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }

  public function fetchAll($sql) {
    $result = dibi::dataSource($sql)
      ->getResult();
    return $result->fetchAll();
  }

  /**
  * vraci obsah cache
  *
  * @param string $key - identifikator promenne
  */
  public function cacheGet($key) {
    $value = $this->cache->load($key);
    if ($value === NULL) {
      return false;
    } else {
      return $value;
    }
  }

  /**
   * ulozi cache
   *
   * @param string $key - identifikator promenne
   * @param mixed $data
   * @return mixed
   * @throws \Throwable
   */
  public function cacheSave($key, $data) {
    return $this->cache->save($key, $data, array(Nette\Caching\Cache::TAGS => array($this->tableName)));
  }

  /**
  * vymaze cache vazanou k prislusne tabulce
  *
  */
  public function cacheClean() {
    $this->cache->clean(array(Nette\Caching\Cache::TAGS => array($this->tableName)));
  }

  public function getConfig() {
    if (empty($this->config)) {
      $cfgs = $this->model->getConfigModel();
      $this->config = $cfgs->getConfig();
    }
    return $this->config;
  }

  public function getEnumOrdMode() {
    return array(
      self::ORDER_MODE_RETAIL => "Maloobchod",
      self::ORDER_MODE_WHOLESALE => "Velkoobchod"
    );
  }

  public function getEnumMonths() {
    return [
      1  => 'leden',
      2  => 'únor',
      3  => 'březen',
      4  => 'duben',
      5  => 'květen',
      6  => 'červen',
      7  => 'červenec',
      8  => 'srpen',
      9  => 'září',
      10 => 'říjen',
      11 => 'listopad',
      12 => 'prosinec',
    ];
  }

}

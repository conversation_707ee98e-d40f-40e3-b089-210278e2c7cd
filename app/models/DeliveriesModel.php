<?php
namespace Model;
use <PERSON>te, dibi;

class DeliveriesModel extends BaseModel {
  
  protected $tableName = "deliveries";
  protected $fieldPrefix = "del";

  public function delete($delid) {
    $stis = $this->Model->getStoItemsModel();

    dibi::begin();
    $del = dibi::fetch("SELECT * FROM deliveries WHERE delid=%i", $delid);
    if ($del === FALSE) throw new ModelException("Dodávka ID:$delid nenalezena", '500');

    $rows = dibi::fetchAll("SELECT * FROM stoitems WHERE stidelid=%i", $delid);
    try {
      foreach ($rows as $row) {
        //zjistim jestli je blokace na objednavce
        $ordItemsBlocked = dibi::fetchAll("
          SELECT oriordid
          FROM stoitems 
          INNER JOIN orditems ON (stioriid=oriid)
          WHERE stidelid=%i", $del->delid, " AND stioriid > 0
        ");
        if (count($ordItemsBlocked) > 0) throw new ModelException("Nelze mazat dodavku, ktera je blokovana na obj.");
        
        
        $stiid = dibi::fetchSingle("SELECT stiid FROM stoitems WHERE stidelid=%i", $del->delid);
        $stis->delete($stiid);
      }
      parent::delete($delid);
    } catch (\RuntimeException $e) {
      dibi::rollback();
      throw new ModelException("Dodávku ID:$delid se nepodařilo vymazat.".$e->getMessage(), '500');
    }
    dibi::commit();
    return true;
  }

  public function logStatus($id, $status, $admid)  {
    //zapisu do logu
    return dibi::insert('deliveries_log', array(
      'deldelid'=>$id,
      'delstatus'=>$status,
      'deladmid'=>$admid,
      'deldatec'=>new \DateTime(),
    ))->execute(dibi::IDENTIFIER);
  }
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik delstatus
  * @return array
  */
  public function getEnumDelStatus() {
    return array(
      0 => 'Vytvořený',
      //2 => 'Na cestě',
      1 => 'Naskladněný',
    );
  }
}

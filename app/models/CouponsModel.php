<?php
namespace Model;
use dibi;

class CouponsModel extends BaseModel {

  protected $tableName = "coupons";
  protected $fieldPrefix = "cou";

  /**
   * vraci jeden zaznam
   *
   * @param integer $id hodnota id ve smyslu jednoznacneho identifikatoru, nemusi byt primarni klic
   * @param string $col nazev sloupce bez prefixu
   * @return dibiRow
   */
  public function load($id, $col='id') {
    $coupon = parent::load($id, $col);
    //dotáhnu slevu na produkty
    if ($coupon) {
      $coupon->products = array();
      if (!empty($coupon->couproducts)) {
        $products = explode("|", $coupon->couproducts);
        foreach ($products as $product) {
          $arr = explode(";", $product);
          $coupon->products[$arr[0]] = array(
            "proid" => $arr[0],
            "proname" => NULL,
            "proprice" => NULL,
            "prodiscount" => $arr[1],
            "used" => FALSE,
          );
        }
      }

    }
    return $coupon;
  }

  public function insert($data) {
    $couid = parent::insert($data);
    //naplnim kod kuponu
    if ($couid > 0 && empty($data["coucode"])) {
      $cou = $this->load($couid);
      $code = $couid."-".substr(md5($couid.$couid.$cou->coudatec.$cou->couvalue), 0, 3)."-".substr(md5($couid.$couid.$cou->coudatec.$cou->couvalue), 3, 3);
      if ($this->update($couid, array('coucode'=>$code))) {
        return $couid;
      }
    }
    return $couid;
  }

  /**
   * vraci SQL SELECT
   *
   * @return string
   */
  public function getSql() {
    $sql = "SELECT *, (CURDATE() > couvalidto) AS couexpired FROM ".$this->table;
    return($sql);
  }

  public function getBasketCoupon($basket) {
    $cou = dibi::fetch("SELECT * FROM ".TABLENAME_COUPONS." WHERE coubasid=%i", $basket->basid);
    if ($cou === FALSE) {
      //kupon neexistuje, vygeneruji ho
      $rows = dibi::fetchAll("SELECT * FROM " . TABLENAME_BASKETITEMS . " WHERE baibasid=%i", $basket->basid);
      $validto = dibi::fetchSingle("SELECT Now() + INTERVAL + 10 DAY");
      $vals = array(
        'coubasid' => $basket->basid,
        'couusrid' => 0,
        'coumail' => $basket->basmail,
        'coupayfree' => $basket->baspayfree,
        'coudelfree' => $basket->basdelfree,
        'couvalidto' => $validto
      );
      $couproducts = "";
      foreach ($rows as $row) {
        $couproducts .= $row->baiproid . ";" . $row->baidiscount . "|";
      }
      $vals["couproducts"] = trim($couproducts, "|");
      $id = $this->insert($vals);
      $cou = $this->load($id);
    }
    return $cou;
  }

  public function getMailingCoupon($mailing, $usr) {
    //zjistim jestli kupon uz neexistuje
    $cou = dibi::fetch("SELECT * FROM ".TABLENAME_COUPONS." WHERE coumamid=%i", $mailing->mamid, " AND couusrid=%i", $usr->usrid);
    if ($cou === FALSE) {
      //kupon neexistuje, vygeneruji ho
      $validto = dibi::fetchSingle("SELECT mamdatestart + INTERVAL + 30 DAY FROM mailings WHERE mamid=%i", $mailing->mamid);
      $vals = array(
        'coumamid' => $mailing->mamid,
        'couusrid' => $usr->usrid,
        'coumail' => $usr->usrmail,
        'couvalue' => $mailing->mamcouponvalue,
        'couvalidto' => $validto,
      );

      $id = $this->insert($vals);
      $cou = $this->load($id);
    }
    return $cou;
  }

  public function getOrderCoupon($order, $couponValue) {
    //zjistim jestli kupon uz neexistuje
    $cou = dibi::fetch("SELECT * FROM ".TABLENAME_COUPONS." WHERE couordid=%i", $order->ordid);
    if ($cou === FALSE) {
      //kupon neexistuje, vygeneruji ho
      $validto = dibi::fetchSingle("SELECT CURDATE() + INTERVAL + 30 DAY");
      $vals = array(
        'couordid' => $order->ordid,
        'couusrid' => NULL,
        'coumail' => $order->ordmail,
        'couvalue' => $couponValue,
        'couvalidto' => $validto,
      );

      $id = $this->insert($vals);
      $cou = $this->load($id);
    }
    return $cou;
  }

  /**
   * ověří platnost kupónu
   *
   * @param string $couCode
   * @param int $orderValue
   * @param string $userEmail
   * @param bool $isNew
   * @return array
   */
  public function validateCoupon($couCode, $orderValue=0, $userEmail='', $isNew=FALSE) {
    $ret = array();
    $cou = $this->load($couCode, "code");
    if ($cou) {
      $ret["data"] = $cou;
      if ($cou->couexpired && $isNew) {
        $ret["status"] = "expired";
        $ret["text"] = "Vypršela platnost slevového kuónu ".$cou->coucode.".";
      } else if ($cou->coustatus == 1 && $isNew) {
        $ret["status"] = "spent";
        $ret["text"] = "Slevový kupón ".$cou->coucode." už není platný.";
      } else {
        //kupón je platný, pokud je vyplněna couvalue kontroluji oproti ceně v košíku a emailu
        if ($cou->couvaluelimit > 0 && $cou->couvaluelimit > $orderValue) {
          $ret["status"] = "limit";
          $ret["text"] = "Minimální hodnota objednávky pro tento kupón je ".number_format($cou->couvaluelimit, 0, ',', ' ') . " Kč.";
        } else if ((bool)$cou->couonlyreg && empty($userEmail)) {
          $ret["status"] = "limit";
          $ret["text"] = "Tento kupón je určený jen pro registrované zákazníky. Prosím přihlašte se nebo zaregistrujte.";
        } else if (!empty($cou->coumail) && trim($cou->coumail) !== $userEmail) {
          $ret["status"] = "limit";
          $ret["text"] = "Tento kupón je vázaný na konkretní zákaznický účet. Prosím přihlašte se nebo zaregistrujte pod emailem, ke kterému je vázán tento kupón.";
        } else {
          $ret["status"] = "ok";
          $ret["text"] = "";
        }
      }
    } else {
      $ret["data"] = array();
      $ret["status"] = "notfound";
      $ret["text"] = "Slevový kupón ".$couCode." nenalezen";
    }
    return $ret;
  }

  /**
   * použije slevový kupón
   *
   * @param string $couCode
   * @param int $orderValue
   * @param string $userEmail
   * @return array
   */
  public function useCoupon($couCode, $orderValue=0, $userEmail='', $validate=TRUE) {
    //nejprve ověřím zda je platný
    $ret = array();
    if ($validate) {
      $ret = $this->validateCoupon($couCode, $orderValue, $userEmail);
    } else {
      $ret["status"] = "ok";
      $ret["data"] = $this->load($couCode, "code");
    }

    if ($ret["status"] == "ok" || $validate===FALSE) {
      $cou = $ret["data"];
      $vals = array();
      $vals["coucounter"] = $cou->coucounter + 1;
      if ($vals["coucounter"] > $cou->couqty && $cou->couqty > 0) {
        $ret["status"] = "spent";
        $ret["text"] = "Slevový kupón je už vyčerpaný.";
      } else {
        if ($vals["coucounter"] == $cou->couqty && $cou->couqty > 0) {
          $vals["coustatus"] = 1;
        }
        $this->update($cou->couid, $vals);
      }
    }
    return $ret;
  }

  /**
   * uvolní  slevový kupón
   *
   * @param string $couCode
   * @return array
   */
  public function unsetCoupon($couCode) {
    $cou = $this->load($couCode, "code");
    $vals = array();
    $vals["coucounter"] = $cou->coucounter - 1;
    $vals["coucounter"] = MAX($vals["coucounter"], 0);
    if ($cou->coucounter == $cou->couqty &&  $cou->coustatus == 1 && $cou->couqty > 0) $vals["coustatus"] = 0;
    $this->update($cou->couid, $vals);
  }

  /********************* ciselniky *********************/
  /**
  * ciselnik coustatus
  * @return array
  */
  public function getEnumCouStatus() {
    return array(
      0 => 'Platný',
      1 => 'Vyčerpaný',
    );
  }

}

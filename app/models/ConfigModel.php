<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class ConfigModel extends BaseModel {

  protected $tableName = "config";
  protected $fieldPrefix = "cfg";

  public function getConfig() {
    $cache = $this->cacheGet('configDb');
    if ($cache === FALSE) {
      //naplnim uzivatelske nastaveni do cache
      $result = dibi::query('SELECT * FROM config');
      $cache = $result->fetchPairs('cfgcode', 'cfgvalue');

      if (isset($cache["PRICE2RATE"])) $cache["PRICE2RATE"] = (double)str_replace(',', '.', $cache["PRICE2RATE"]);
      $this->cacheSave('configDb', $cache);
    }
    $this->config = $cache;
    return $cache;
  }

}

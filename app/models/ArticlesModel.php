<?php
namespace Model;
use Nette, dibi;

class ArticlesModel extends BaseModel {

  protected $tableName = "articles";
  protected $fieldPrefix = "art";

  public function runCounter($id) {
    return dibi::query("UPDATE articles SET artcnt=artcnt+1 WHERE artid=%i", $id);
  }

  /********************* ciselniky *********************/
  /**
  * ciselnik typy <PERSON>ů
  * @return array
  */
  public function getEnumArtTypId() {
    return array(
      1 => 'Stránka',
      2 => 'Blog',
      4 => 'Recepty',
      3 => 'Novinky',

    );
  }

  public function getEnumArtTop() {
    return array(
      0 => 'žádné',
      1 => 'Odborné <PERSON>',
      2 => 'Proč nakupovat u nás',
    );
  }

  public function getEnumArtStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  public function genEnumTags($typId=1) {

    $key = 'articleTags_' . $typId;

    $tags = $this->cacheGet($key);
    if ($tags === FALSE) {
      $rows = dibi::fetchAll("SELECT arttags FROM articles GROUP BY arttags");
      $tags = array();
      foreach ($rows as $row) {
        $arr = explode('|', trim($row->arttags, '|'));
        foreach ($arr as $tag) {
          $tag = Nette\Utils\Strings::firstUpper($tag);
          $tags[$tag] = $tag;
        }
      }
      $this->cacheSave($key, $tags);
    }
    return $tags;
  }
}

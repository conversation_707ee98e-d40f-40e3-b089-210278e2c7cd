<?php
namespace Model;
use Net<PERSON>, dibi;

class OrdItemsModel extends BaseModel {

  protected $tableName = "orditems";
  protected $fieldPrefix = "ori";

  public function insert($data) {

    if (empty($data["oripriceoriginal"]) && !empty($data["oriprice"])) {
      $data["oripriceoriginal"] = $data["oriprice"];
    }

    if (!empty($data["oriproid"]) && empty($data["orifill"])) {
      $data["orifill"] = $data["oriqty"];
    }

    if (!empty($data["oriproid"]) && empty($data["oriweight"])) {
      $data["oriweight"] = (double)dibi::fetchSingle("SELECT proweight FROM products WHERE proid=%i", $data["oriproid"]);
    }

    return parent::insert($data);
  }

  public function delete($id) {
    $oi = $this->load($id);
    if ($oi->oritypid === 0) {
      //vymazu svazane polozky
      $rows = dibi::fetchAll("SELECT oriid FROM ".$this->table." WHERE orioriid=%i", $id);
      foreach ($rows as $row) {
        $this->delete($row->oriid);
      }
    }
    return parent::delete($id);
  }

  /**
   * přepočítá hmotnost, cenu, vykrytí položky objednávky
   *
   * @param $oriid
   * @return bool
   * @throws \Dibi\Exception
   */
  public function recalcItem($oriid) {
    //načtu položku
    $ori = $this->load($oriid);
    if (empty($ori->oriproid)) {
      return true;
    }

    $vals = array();

    //načtu příslušný produkt
    $pros = $this->model->getProductsModel();
    $pro = $pros->load($ori->oriproid);

    //zjistim mode obj. a cenovou kategorii
    $ord = dibi::fetch("
      SELECT ordmode, coalesce(usrprccat, 'a') as usrprccat
      FROM orders
          LEFT JOIN users ON ordusrid=usrid
      WHERE ordid=%i", $ori->oriordid);

    $prcCat = $ord->usrprccat;

    $priceByCat = "proprice1" . $prcCat;
    $vals["oriprice"] = $pro->$priceByCat;
    $vals["oriweight"] = $pro->proweight;

    $oriPriceReal = NULL;
    $vals["oripricereal"] = NULL;
    $vals["oriweightreal"] = NULL;
    If ($ori->orifill===0 && (double)$pro->proweight > 0) {
      //načtu hmotnost jaká byla vyskladněná
      $vals["oriweightreal"] = (double)dibi::fetchSingle("SELECT SUM(stiweight) FROM stoitems WHERE stioriid=%i", $ori->oriid);
      //načtu skutečnou cenu podle vyskladněné ceny
      $vals["oripricereal"] = round($vals["oriweightreal"] * $pro->$priceByCat / $pro->proweight, 2);
    }
    $this->update($ori->oriid, $vals);
    $pros->recalcProduct($pro->proid);
    $ords = $this->model->getOrdersModel();
    $ords->recalcOrder($ori->oriordid);
  }
}

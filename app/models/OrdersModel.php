<?php
namespace Model;
use dibi;

class OrdersModel extends BaseModel {

  protected $tableName = "orders";
  protected $fieldPrefix = "ord";

  public function insert($data) {
    $cous = $this->model->getCouponsModel();

    //naplnim kod objednavky - jedna spolecna ciselna rada pro vsechny objednavky pro aktualni rok
    $today = dibi::fetch("SELECT YEAR(CURDATE()) AS today_year, MONTH(CURDATE()) AS today_month");
    $month = substr('0'.$today->today_month, -2);
    $year = substr($today->today_year, 2, 2);
    $lastOrdCode = (string)dibi::fetchSingle("SELECT ordcode FROM orders ORDER BY ordid DESC LIMIT 1");
    $ordcodeFirst = $year.$month.'00001';

    if (!empty($lastOrdCode)) {
      $lastOrdCode = $year . $month . substr($lastOrdCode, 4);
      $data["ordcode"] = (int)$lastOrdCode + 1;
    } else {
      $data["ordcode"] = $ordcodeFirst;
    }
    if (empty($data["ordcurid"])) {
      $data["ordcurid"] = $this->curId;
    }

    if (empty($data["ordicouid"])) {
      $data["ordicouid"] = 1;
    }
    if (empty($data["ordstcouid"])) {
      $data["ordstcouid"] = 1;
    }

    if (!empty($data["ordcoucode"])) {
      $cous->useCoupon($data["ordcoucode"], 0, $data["ordmail"], FALSE);
    }

    //doplním IP adresu
    if (isset($_SERVER['REMOTE_ADDR'])) {
      $data["ordip"] = $_SERVER['REMOTE_ADDR'];
    }

    //doplním typ objednávky
    if (empty($data["ordmode"])) {
      $data["ordmode"] = self::ORDER_MODE_RETAIL;
      //zjistim cenovou kategorii obj.
      if(!empty($data["ordusrid"])) {
        $usrId = (int)$data["ordusrid"];
        if ($usrId > 0) {
          $prcCat = (string)dibi::fetchSingle("SELECT usrprccat FROM users WHERE usrid=%i", $usrId);
          if ($prcCat !== '' && $prcCat !== 'a') {
            $data["ordmode"] = self::ORDER_MODE_WHOLESALE;
          }
        }
      }
    }

    $ordid = parent::insert($data);
    if ($ordid > 0) {
      $this->logStatus($ordid, 0, (!empty($data["ordadmid"]) ? $data["ordadmid"] : NULL));
      return $ordid;
    }
    return false;
  }

  public function update($id, $data, $setDateU = Null) {
    //pokud prijde kod faktury tak nastavim i datum fakturace
    $ord = $this->load($id);
    if (!empty($data["ordinvcode"])) {
      //overim zda je nastaveny
      if (empty($ord->ordinvcode) && empty($ord->ordinvdate)) {
        $data["ordinvdate"] = new \DateTime();
      } else if (!empty($ord->ordinvcode)) {
        unset($data["ordinvcode"]);
        unset($data["ordinvdate"]);
      }
    } else if (isset($data["ordinvcode"]) && empty($data["ordinvcode"])) {
      $data["ordinvcode"] = NULL;
      $data["ordinvdate"] = NULL;
    }

    //pokud je vyplněn kód kupónu
    if (isset($data["ordcoucode"])) {
      if (!empty($data["ordcoucode"]) && empty($ord->ordcoucode)) {
        $cous = $this->model->getCouponsModel();
        $ret = $cous->useCoupon($data["ordcoucode"], $ord->ordpricevat, $ord->ordmail);
        if ($ret["status"] != "ok") {
          throw new \Exception($ret["text"]);
        }

      } else if (empty($data["ordcoucode"]) && !empty($ord->ordcoucode)) {
        $cous = $this->model->getCouponsModel();
        $cous->unsetCoupon($ord->ordcoucode);

      }
    }
    return parent::update($id, $data, $setDateU);
  }

  public function delete($id) {
    if (parent::delete($id)) {
      //vymazi vsechny polozky
      return dibi::query("DELETE FROM orditems WHERE oriordid=%i", $id);
    }
    return false;
  }

  public function getProducts($ordId, $where = "") {
    return dibi::fetchAll("
      SELECT proid, proname, procode, proweight, proprice1a, proprice1b, proprice1c, proprice1d,
          oriid, oriname, oriprice, oripricereal, oriqty, orifill, oriweight, oriweightreal
      FROM orditems
      INNER JOIN products ON (proid=oriproid)
      WHERE oriordid=%i", $ordId, (!empty($where) ? " AND " . $where : ""));
  }

  public function getList($where, $orderBy) {
    return "
    SELECT orders.*, coalesce(d.delnames, d.delname) AS delname, coalesce(dm.delnames, dm.delname) AS delnamemas, dm.delcode AS delcodemas, d.delcode AS paycode, admname,
    (SELECT orldatec FROM orders_log WHERE orlordid=ordid AND orlstatus=3 ORDER BY orldatec DESC LIMIT 1) AS datesend
    FROM orders
    LEFT JOIN deliverymodes AS d ON (orddelid=d.delid)
    LEFT JOIN deliverymodes AS dm ON (d.delmasid=dm.delid)
    LEFT JOIN admins ON (ordadmid=admid)
    $where
    GROUP BY ordid
    $orderBy
    ";
  }

  public function logStatus($ordid, $status, $admid, $note="")  {
    //zapisu do logu
    return dibi::insert('orders_log', array(
      'orlordid'=>$ordid,
      'orlstatus'=>$status,
      'orladmid'=>$admid,
      'orlnote'=>$note,
      'orldatec'=>new \DateTime,
    ))->execute(dibi::IDENTIFIER);
  }

  /**
   * @param $ordid
   * @param $data
   * @param string $note
   * @return \Dibi\Result|int
   * @throws \Dibi\Exception
   */
  public function logEet($ordid, $data, $note="") {
    //zapisu do logu
    $vals = array(
      "logordid" => $ordid,
      "logprovozid" => $data->data->premiseId,
      "logpoklid" => $data->data->cashRegisterId,
      "logprice" => (double)$data->data->totalPrice,
      "loguuid" => $data->uuid,
      "logbkp" => $data->bkp,
      "logfik" => $data->fik,
      "lognote" => $note,
      "logdatec" => $data->receiptTime,
    );
    return \dibi::insert("eet_log", $vals)->execute(dibi::IDENTIFIER);
  }

  //prepocita objednavku
  public function recalcOrder($id) {
    $config = $this->getConfig();
    $order = $this->load($id);
    if (!$order) return;
    $ordItems = $this->model->getOrdItemsModel();

    //suma objednane zbozi pro vypocet slevy
    $priceSumDisc = (double)dibi::fetchSingle("
      SELECT SUM(oriprice*oriqty)
      FROM orditems
      INNER JOIN products ON (oriproid=proid)
      WHERE
      oriordid=%i AND
      pronotdisc=0 AND
      oritypid=0", $order->ordid);

    //jestli je doprava zdarma
    $delFree = (bool)dibi::fetchSingle("
      SELECT COUNT(proid) > 0
      FROM orditems
      INNER JOIN products ON (oriproid=proid)
      WHERE
      oriordid=%i AND
      prodelfree=1 AND
      oritypid IN (0)", $order->ordid);

    //zjistim jestli ma narok na slevu
    $discount = 0;
    $discountName = "";
    $discountType = "";
    $payFree = FALSE;
    $userDiscPer = 0;
    $userPrcCat = 'a';
    //pokud je nastaven user na obj. zjistim uzivatelskou slevu
    //nactu zakaznika
    $usr = false;
    if ($order->ordusrid > 0) $usr = dibi::fetch("SELECT * FROM users WHERE usrid=%i", $order->ordusrid);
    if ($usr) {
      $userDiscPer = (int)$usr->usrdiscount;
      $userPrcCat = $usr->usrprccat;
    }

    //načtu kupon
    if (!empty($order->ordcoucode)) {
      $cous = $this->model->getCouponsModel();
      $cou = $cous->validateCoupon($order->ordcoucode, $priceSumDisc, $order->ordmail, FALSE);
      if ($cou["status"] != 'ok') {
        $cous->unsetCoupon($order->ordcoucode);
        $order->ordcoucode = NULL;
        $this->update($id, array("ordcoucode"=>NULL));
      }

      $cou = $cous->load($order->ordcoucode, "code");
      if (!empty($cou->coudelfree)) {
        $delFree = TRUE;
      }
      if (!empty($cou->coupayfree)) {
        $payFree = TRUE;
      }
    }

    $discountValue = 0;

    if (!empty($order->ordcoucode) && isset($cou) && !empty($cou->couvalue)) {
      $discount = (double)$cou->couvalue;
      $discountName = "Slevový kupón ".$order->ordcoucode;

      if ($cou["couvalueunit"] == 'Kč') {
        $discountType = 'value';
        $discountPer = 0;
        $discountValue = round((double)$cou["couvalue"], 0);
      }  else if ($cou["couvalueunit"] == '%') {
        $discountType = 'percent';
        $discountPer = $cou["couvalue"];
        $discountValue = round($priceSumDisc * ($cou["couvalue"] / 100), 0);
      }

    } else if (!empty($order->ordcoucode) && isset($cou) && empty($cou->couvalue)) {
      //sleva na produkty jednotlivé - musím přepočítat položky
      $discountType = 'products';
    } else if ($order->orddiscpercent == 0) {
      //je tam slevova polozka ale neni nastavena sleva na objednavce - zjistim jestli ma narok na slevu
      if ($priceSumDisc > 0) {
        //zjistim jestli je mnozstevni sleva
        $disc = (int)dibi::fetchSingle("SELECT dispercent FROM discounts WHERE distypid='volume' AND disprccat=%s", $userPrcCat, " AND discurid=%i", $this->curId, " AND $priceSumDisc BETWEEN disfrom AND disto AND disstatus=0");
        //vyssi slevu pouziju
        if ($disc < $userDiscPer) {
          $discount = $userDiscPer;
          $discountName = "Zákaznická sleva";
          $discountType = "percent";
        } else {
          $discount = $disc;
          $discountName = "Množstevní sleva";
          $discountType = "percent";
        }
      }
    } else {
      $discount = $order->orddiscpercent;
      $discountName = "Sleva";
      $discountType = "percent";
    }

if ((int)$config["DISCOUNT_DISSOLVE"] == 1 && $discountType != 'products') {
      if ($discount == 0) {
        //vymazu slevu
        dibi::query("UPDATE orditems SET oridisc=0 WHERE oriordid=%i", $id);
      } else if ($discount > 0) {
        //u vsech polozek vypocitam slevu
        $rows = dibi::fetchAll("
        SELECT *
        FROM orditems
        INNER JOIN products ON (oriproid=proid)
        WHERE
        oriordid=%i AND
        pronotdisc=0 AND
        oritypid=0", $order->ordid);
        foreach ($rows as $key => $row) {
          $disc = round((double)$row->oriprice * (double)$row->oriqty * (double)$discount / 100);
          dibi::query("UPDATE orditems SET oridisc=$disc WHERE oriid=%i", $row->oriid);
        }
      }
    } else {
      //sleva u položek
      $rows = dibi::fetchAll("
      SELECT *
      FROM orditems
      INNER JOIN products ON (oriproid=proid)
      WHERE
      oriordid=%i AND
      pronotdisc=0 AND
      oritypid=0", $order->ordid);
      foreach ($rows as $key => $row) {
        $d = 0;
        if (isset($cou->products[$row->oriproid])) {
          $product = $cou->products[$row->oriproid];
          $d = $product["prodiscount"];
        }
        dibi::query("UPDATE orditems SET oridisc=0, oridiscount=$d, oriprice=(oripriceoriginal-$d) WHERE oriid=%i", $row->oriid);
      }

      //zjistim ID polozky slevy
      $oriid = (int)dibi::fetchSingle("SELECT oriid FROM orditems WHERE oriordid=%i", $id , " AND oritypid=3");

      //vypočítámn slevu
      $oriPrice = 0;
      $unit = "";
      if ($discountType === 'value') {
        $oriPrice = -1 * $discountValue;
        $unit = "Kč";
      } else if ($discountType === 'percent') {
        $oriPrice = round(-1 * $priceSumDisc * $discount / 100, 0);
        $unit = "%";
      }

      if ($oriid > 0 && $oriPrice == 0) {
        //vymazu slevu
        $ordItems->delete($oriid);
      } else if ($oriid == 0 && $oriPrice != 0) {
        //zalozim polozku slevy
        $values = array(
          'oriordid' => $id,
          'oritypid' => 3,
          'oriqty' => 1,
          'orivatid' => 1,
          'oriname' => $discountName,
          'oriprice' => $oriPrice,
        );
        $ordItems->insert($values);
      } else if ($oriid > 0 && $oriPrice != 0) {
        $values = array(
          'oritypid' => 3,
          'oriname' => $discountName,
          'oriprice' => $oriPrice,
        );
        $ordItems->update($oriid, $values);
      }
    }

    //doprava, aktualizuju polozku
    //zjistim cenu objednaneho zbozi vcetne slevy pro vypocet dopravy zdarma
    $priceSum = (double)dibi::fetchSingle("SELECT SUM((oriprice*oriqty)-oridisc) FROM orditems WHERE oriordid=%i AND oritypid IN (0, 3)", $id);
    //nactu si zpusob dopravy
    $paymode = dibi::fetch("
      SELECT delid, delmasid, delname,
      IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSum." OR (".(($payFree) ? "TRUE" : 'FALSE')." AND delnodelfree=0), 0, delprice".$this->curId.$userPrcCat.") AS delprice
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $userPrcCat, " AND discurid=%i", $this->curId, ")
      WHERE delid=%i", $order->orddelid
    );
    $delmode = dibi::fetch("
      SELECT delid, delmasid, delcode, delname,
      IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSum." OR (".($delFree ? "TRUE" : 'FALSE')." AND delnodelfree=0), 0, delprice".$this->curId.$userPrcCat.") AS delprice,
      IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSum." OR (".($delFree ? "TRUE" : 'FALSE')." AND delnodelfree=0), 1, 0) AS isdelpricefree
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $userPrcCat, " AND discurid=%i", $this->curId, ")
      WHERE delid=%i", $paymode->delmasid
    );

    //pokud vlastní_doprava spočítám cenu dopravy podle PSČ
    if ($delmode->delcode === 'VLASTNI_PREPRAVA') {
      $postCode = (empty($order->ordstpostcode) ? $order->ordipostcode : $order->ordstpostcode);
      $delPrice = (double)dibi::fetchSingle("SELECT dpiprice FROM delpathitems WHERE dpidelid=%i", $delmode->delid, " AND dpipostcode=%s", $postCode);
      if ($delPrice > 0) {
        if ($delmode->isdelpricefree === 1) {
          $delmode->delprice = 0;
        } else {
          $delmode->delprice = $delPrice;
        }
      }
    }

    //zjistim ID polozky dopravy
    $delRow = dibi::fetch("SELECT oriid, oripricemaster FROM orditems WHERE oriordid=%i", $order->ordid , " AND oritypid=1");
    $delPrice = (double)$delRow->oripricemaster > 0 ? $delRow->oripricemaster : ((double)$delmode->delprice + (double)$paymode->delprice);
    $values = array(
      'oriprice' => $delPrice,
    );
    $orditems = $this->model->getOrdItemsModel();
    $orditems->update($delRow->oriid, $values);

    //zjistim cenu objednavky
    $priceSum = dibi::fetchSingle("SELECT SUM((oriprice*oriqty)-oridisc) FROM orditems WHERE oriordid=%i", $id);
    $discSum = dibi::fetchSingle("SELECT SUM(oridisc) FROM orditems WHERE oriordid=%i", $id);


    $vat = (int)$config['VATTYPE_0'];
    $vatLow = (int)$config['VATTYPE_1'];
    $vatType = (string)$config['PRICEVAT'];
    if ($vatType == 'inclvat') {
      $priceSumVat = $priceSum;
      $priceSum = dibi::fetchSingle("SELECT SUM(((oriprice*oriqty)-oridisc)/(1+(IF(COALESCE(orivatid,0)=0,$vat,$vatLow)/100))) FROM orditems WHERE oriordid=%i", $id);
    } else {
      $priceSumVat = dibi::fetchSingle("SELECT SUM(((oriprice*oriqty)-oridisc)*(1+(IF(COALESCE(orivatid,0)=0,$vat,$vatLow)/100))) FROM orditems WHERE oriordid=%i", $id);
    }
    $priceSumVat = round($priceSumVat, 2);

    //zjistim celkovou hmotnost
    $weightSum = (double)dibi::fetchSingle("SELECT SUM(coalesce(oriweightreal, oriweight, 0) *oriqty) FROM orditems INNER JOIN products ON (proid=oriproid) WHERE oriordid=%i", $id);

    $vals = array(
      'ordprice'=>round($priceSum, $this->curDigits),
      'ordpricevat'=>round($priceSumVat, $this->curDigits),
      'orddisc'=>round($discSum, $this->curDigits),
      'ordweight'=>$weightSum,
    );

    //existuje zák. účet se stejným emailem
    if ((int)$order->ordusrid === 0 && !empty($order->ordmail)) {
      //zjistím uživatele podle emailu
      $usrId = (int)dibi::fetchSingle("SELECT usrid from users WHERE usrmail=%s", $order->ordmail);
      if ($usrId > 0) {
        $vals["ordusrid"] = $usrId;
      }
    }

    $this->update($id, $vals);

  }

  public function recalcLoyaltyDiscount($usrId) {
    $this->getConfig();
    $usrs = $this->model->getUsersModel();
    $usr = $usrs->load($usrId);
    if ($usr) {
      $sum = (double)dibi::fetchSingle("
        SELECT SUM(oriprice*oriqty) FROM orders
        INNER JOIN orditems ON oriordid=ordid
        WHERE ordusrid=%i", $usrId, " AND
        ordstatus IN (3,4)");
      if ((int)$usr->usrdiscount === 0 && $sum >= (double)$this->config["LOYALTYDISCOUNT_LIMIT"]) {
        //má nárok na slevu
        $usrs->update($usrId, array('usrdiscount'=>(double)$this->config["LOYALTYDISCOUNT_VALUE"]));
      }
    }
  }

  /**
   * put your comment there...
   *
   * @param $order
   * @return array
   * @throws \Dibi\Exception
   */
  public function blAnalyse($order) {
    $orders = array();
    //$order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
    if ($order->ordstatus != 7) {
      //vyberu vsechny objednavky ktere maji priznak spam a maji stejne udaje o ojednavateli
      $sql = "SELECT ordid, ordcode FROM orders WHERE ordstatus=7 AND ordid!=$order->ordid AND (
      ordilname='$order->ordilname' OR
      ".(empty($order->ordstlname) ? "" : " ordstlname='$order->ordstlname' OR ")."
      (ordistreet='$order->ordistreet' AND ordistreetno='$order->ordistreetno' AND ordicity='$order->ordicity' AND ordipostcode='$order->ordipostcode') OR
      ".(empty($order->ordststreet) ? "" : "(ordststreet='$order->ordststreet' AND ordststreetno='$order->ordststreetno' AND ordstcity='$order->ordstcity' AND ordstpostcode='$order->ordstpostcode') OR ")."
      ordmail='$order->ordmail'
      ".(empty($order->ordtel) ? "" : " OR ordtel='$order->ordtel'").")";
      $rows = dibi::fetchAll($sql);
      foreach ($rows as $row) {
        $orders[$row->ordid] = $row->ordid;
      }
    }
    return($orders);

  }

  public function makeInvoice($id) {
    $orders = $this->model->getOrdersModel();
    $order = $orders->load($id);
    if (!$order) {
      throw new ModelException('Příslušná objednávka nenalezena.');
    } else {
      if (!empty($order->ordinvcode)) {
        throw new ModelException('Faktura je už vystavená.');
      } else {
        if (!$orders->update($id, array('ordinvcode'=>$order->ordcode, 'ordinvdate'=>new \DateTime))) {
          throw new ModelException('Fakturu se nepodařilo vystavit.');
        }
      }
    }
  }

  public function cancelPayment($ordid, $admid)  {
    //najdu posledni nastaveni stavu uhrazeno
    $row = dibi::fetch("SELECT * FROM orders_log WHERE orlordid=%i", $ordid, " AND orlstatus=6 ORDER BY orlid DESC LIMIT 1");
    //upravim poznamku ze tato platba byla stornovana
    dibi::query("UPDATE orders_log SET orlnote = CONCAT(orlnote, ' storno platby (', Now(), ')') WHERE orlid=%i", $row->orlid);
    //najdu predchozi status platby
    $rowPrev = dibi::fetch("SELECT * FROM orders_log WHERE orlordid=%i", $ordid, " AND orlid<%i", $row->orlid, " ORDER BY orlid DESC LIMIT 1");
    $vals = array(
      'ordpaystatus' => 0,
      'orddatepayed' => null,
    );
    if ($rowPrev) {
      $vals["ordstatus"] = $rowPrev->orlstatus;
      $this->logStatus($ordid, $vals["ordstatus"], $admid);
    }
    return $this->update($ordid, $vals);
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik usrstatus
  * @return array
  */
  public function getEnumOrdStatus() {
    return array(
      0 => 'Čeká na zpracování',
      1 => 'Vyřizuje se',
      2 => 'Čeká na platbu',
      6 => 'Zaplaceno',
      3 => 'Odeslána',
      //8 => 'Připraveno k odběru/expedici',
      9 => 'Částečně uzavřená',
      4 => 'Uzavřená',
      5 => 'Stornovaná',
      7 => 'Černá listina',
    );
  }

  public function getEnumOrdDelId($onlyActive = TRUE) {
    //naplnim zpusoby dopravy
    $rows = dibi::fetchAll("SELECT delid, delname FROM deliverymodes WHERE delmasid=0 ".($onlyActive ? "AND delstatus=0" : "")." ORDER BY delorder");
    $deliveryModeRows  = array();
    foreach ($rows  as $row) {
      $deliveryModeRows[$row->delname] = dibi::query("SELECT delid, CONCAT('".$row->delname." / ', delname) AS delname FROM deliverymodes WHERE delmasid=%i ".($onlyActive ? "AND delstatus=0" : "")." ORDER BY delorder", $row->delid)->fetchPairs('delid', 'delname');
    }
    return $deliveryModeRows;
  }

  public function getEnumOrdDelIdSimple() {
    //naplnim zpusoby dopravy
     return dibi::query("SELECT delid, delname FROM deliverymodes WHERE delmasid> 0 AND delstatus=0 ORDER BY delorder")->fetchPairs('delid', 'delname');
  }

  public function getEnumFirms() {
    //naplnim zpusoby dopravy
    return array(
      1 => 'Dodavatel - NEplátce DPH',
      2 => 'Dodavatel - plátce DPH',
    );
  }
  public function getEnumCoolBalikRegId(): array {
    //naplnim zpusoby dopravy
    return [
      //čtvrtek
      6 => 'Ústecký kraj',
      2 => 'Středočeský Kraj - Zóna SEVER',
      7 => 'Liberecký kraj',
      8 => 'Královéhradecký kraj',
      9 => 'Pardubický kraj',
      12 => 'Olomoucký kraj',
      13 => 'Moravskoslezský kraj',
      //středa
      14 => 'Zlínský kraj',
      1 => 'Hlavní město Praha',
      16 => 'Středočeský kraj - Zóna JIH',
      3 => 'Jihočeský kraj',
      4 => 'Plzeňský kraj',
      5 => 'Karlovarský kraj',
      10 => 'Kraj Vysočina',
      18 => 'Jihomoravský kraj',
      19 => 'Bratislavský kraj'
    ];
  }

  public function getEnumCoolBalikRegIdWeekDays(): array {
    //naplnim zpusoby dopravy
    return [
      //čtvrtek
      6 =>  [4],
      2 =>  [4],
      7 =>  [4],
      8 =>  [4],
      9 =>  [4],
      12 =>  [4],
      13 =>  [4],
      //středa
      14 =>  [3],
      1 => [3],
      16 => [3],
      3 => [3],
      4 => [3],
      5 => [3],
      10 => [3],
      18 => [3],
      19 => [3]
    ];
  }



}

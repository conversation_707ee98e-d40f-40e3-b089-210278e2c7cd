<?php
namespace Model;
use dibi;
use <PERSON>\Debugger;

class DeliveryModesModel extends BaseModel {

  protected $tableName = "deliverymodes";
  protected $fieldPrefix = "del";

  public function getDataSource($sql="") {
    if ($sql == "") $sql = $this->getSql();
    return dibi::dataSource($sql);
  }

  public function getSql () {
    return "SELECT *,
      delprice".$this->curId."a AS delprice
      FROM $this->tableName";
  }

  public function getDelFreeLimit($usrPrcCat) {
    $cacheKey = 'delFreeLimit_'.$this->curId.$usrPrcCat;
    $delFreeLimit = $this->cacheGet($cacheKey);
    if ($delFreeLimit === FALSE) {
      //cache neni musim naplnit
      $delFreeLimit = dibi::fetchSingle("
      SELECT MIN(disfrom)
      FROM deliverymodes
      INNER JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $usrPrcCat, " AND discurid=%i", $this->curId, ")
      WHERE (delcode<>'OSOBNE' OR delcode IS NULL) AND delmasid=0 AND delstatus=0
      ORDER BY disfrom");
      $this->cacheSave($cacheKey, $delFreeLimit);
    }
    return $delFreeLimit;
  }

  /**
   * vrati dopravu podle delid
   *
   * @param $delid
   * @param $basket
   * @return mixed
   * @throws \Dibi\Exception
   */
  public function getDelMode($delid, $basket) {
    $dels = $this->getDelModes($basket, "delid=" . $delid);
    return current($dels);
  }

  /**
   * vrati platbu podle delid
   *
   * @param $delid
   * @param $basket
   * @return mixed
   * @throws \Dibi\Exception
   */
  public function getPayMode($delid, $basket) {
    $dels = $this->getPayModes(NULL, $basket, "delid=" . $delid);
    return current($dels);
  }

  /**
   * vrati seznam možných doprav
   *
   * @param $basket
   * @param string $where
   * @return array
   * @throws \Dibi\Exception
   */
  public function getDelModes($basket, $where="") {
    $basketPrice = $basket->priceSumVat;
    $delFree = $basket->delFree;
    $postCode = $basket->postCode;
    $postName = $basket->postName;

    $deldurid = $basket->durId;

    $delIds = array();
    $delPrices = array();

    if (isset($basket->delCodes["VLASTNI_PREPRAVA"])) {
      //zjistím jestli je možno použít rozvoz dle PSČ a jeho cenu
      $rows = $this->getDelPaths($postCode, $postName);

      if ($rows && count($rows) > 0) {
        foreach ($rows as $row) {
          $delIds[$row->delid] = $row->delid;
          $delPrices[$row->delid] = $row->dpiprice;
        }
      }
    }

    $sql[] = "
      SELECT delid, delname, delcode,
      IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$basketPrice." OR (".($delFree ? "TRUE" : 'FALSE')." AND delnodelfree=0), 0, delprice".$this->curId.$this->prccat.") AS delprice,
      IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$basketPrice." OR (".($delFree ? "TRUE" : 'FALSE')." AND delnodelfree=0), 1, 0) AS isdelpricefree,
      delurlparcel, deldesc, deltext".$this->curId." AS deltext, deldates
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s";
    $sql[] = $this->prccat;
    $sql[] = " AND discurid=%i";
    $sql[] = $this->curId;
    $sql[] = ")";

    $whereArr[] = " WHERE delstatus=0 AND delmasid=0 AND (delcurid=0 OR delcurid=%i";
    $whereArr[] = $this->curId;
    $whereArr[] = ")";

    If (count($basket->delIds) > 0) {
      $whereArr[] = " AND ";
      $whereArr[] = " delid in (%i) ";
      $whereArr[] = $basket->delIds;
    }

    if (!empty($where)) {
      $whereArr[] = " AND ";
      $whereArr[] = $where;
    }

    $sql = array_merge($sql, $whereArr);

    $sql[] = "ORDER BY delorder";

    $rows = dibi::query($sql)->fetchAssoc('delid');

    foreach ($rows as $delId => $del) {
      if ($del->isdelpricefree) {
        $rows[$delId]->delprice = 0;
      } else {
        if (isset($delPrices[$delId])) {
          $rows[$delId]->delprice = $delPrices[$delId];
        }
      }
      //pokud rozvoz ale není mezi trasami dle psč, tak dopravu vyřadím
      if ($del->delcode === 'VLASTNI_PREPRAVA' && !array_key_exists($del->delid, $delIds)) {
        unset($rows[$del->delid]);
      }

      //zobrazitcool balík? Podle kraje
      $ords = $this->model->getOrdersModel();
      $WeekDays = $ords->getEnumCoolBalikRegIdWeekDays();
      if ($del->delcode === 'COOL_BALIK') {
        if(!empty($basket->contact["ordregid"]) && (!isset($WeekDays[$basket->contact["ordregid"]]) || (isset($WeekDays[$basket->contact["ordregid"]]) && count($WeekDays[$basket->contact["ordregid"]]) === 0))) {
          unset($rows[$del->delid]);
        }
      }
    }
    reset($rows);
    return $rows;
  }

  /**
   *
   *
   * @param $postCode
   * @param null $delId
   * @return \Dibi\Row[]|boolean
   * @throws \Dibi\Exception
   */
  public function getDelPaths($postCode, $postName, $delId=NULL) {

    $postCode = str_replace(" ", "", trim($postCode));

    if (empty($postCode)) {
      return false;
    }

    return dibi::fetchAll("
      SELECT delid, deldayid, delname, dpiprice
      FROM delpathitems
      INNER JOIN deliverymodes ON delid=dpidelid
      WHERE
        delstatus=0 AND
        dpistatus=0 AND
        " . ($delId !== NULL ? "dpidelid=".$delId . " AND " : "") . "
        dpipostcode=%s", $postCode, " AND dpiplacename=%s", $postName);
  }

  /**
   * vrati seznam možných plateb
   *
   * @param $delMasId
   * @param $basket
   * @param string $where
   * @param string $fetchAssoc
   * @return array
   * @throws \Dibi\Exception
   */
  public function getPayModes($delMasId, $basket, $where="", $fetchAssoc="delid") {

    $basketPrice = $basket->priceSumVat;
    $payFree = $basket->payFree;

    return dibi::query("
      SELECT delid, delmasid, delname, delcode,
      IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$basketPrice." OR (".($payFree ? "TRUE" : 'FALSE')." AND delnodelfree=0), 0, delprice".$this->curId.$this->prccat.") AS delprice,
      delurlparcel, deldesc, deltext".$this->curId." AS deltext
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->prccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND (delcurid=0 OR delcurid=%i", $this->curId, ") ".
      (!empty($delMasId) ? " AND delmasid=".$delMasId : " AND delmasid > 0 ").
      (!empty($where) ? " AND $where " : "")."
      ORDER BY delorder")
      ->fetchAssoc($fetchAssoc);
  }

  /***
   *
   * @param $delType [VLASTNI_PREPRAVA|GEIS|OSOBNE]
   */
  public function getDelDates($delType) {
    if ($delType === 'geis') {
      //svoz 0 - pondělí 2 - středa
      //objedná dva dny předem
      //nejbližší datum pro svoz - musí být 0/2 a a min 2 dny od dneška.

    }

    if ($delType === 'VLASTNI_PREPRAVA') {

    }
  }

  /**
   * nastavi cenovou kategorii pro preddefinovane SQL
   *
   */
  public function setPrcCat($val) {
    if ($val == "") $val = "a";
    $this->prccat = $val;
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik delstatus
  * @return array
  */
  public function getEnumDelStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  public function getEnumPayTypes() {
    return array(
      'paybefore' => 'Platba předem',
      'cetelem' => 'Na splátky Cetelem',
      'dobirka' => 'Dobírka',
      'cash' => 'Platba v hotovosti',
      'creditcard' => 'Platba kartou',
    );
  }

public function getEnumDelTypes() {
    return array(
      'CESKA_POSTA' => 'Česká pošta - Balík do ruky',
      'CESKA_POSTA_NA_POSTU' => 'Česká pošta - Balík Na poštu',
      'CSAD_LOGISTIK_OSTRAVA' => 'ČSAD Logistik Ostrava',
      'DPD' => 'DPD',
      'DHL' => 'DHL',
      'EMS' => 'EMS',
      'FOFR' => 'FOFR',
      'GEBRUDER_WEISS' => 'Gebrüder Weiss',
      'GEIS' => 'Geis',
      'GENERAL_PARCEL' => 'General Parcel',
      'GLS' => 'GLS',
      'HDS' => 'HDS',
      'ULOZENKA' => 'Uloženka',
      'INTIME' => 'InTime',
      'PPL' => 'PPL',
      'RADIALKA' => 'Radiálka',
      'SEEGMULLER' => 'Seegmuller',
      'TNT' => 'TNT',
      'TOPTRANS' => 'TOPTRANS',
      'ZASILKOVNA' => 'Zásilkovna',
      'UPS' => 'UPS',
      'OSOBNE' => 'Osobně',
      'VLASTNI_PREPRAVA' => 'Vlastní přeprava',
      'SPECIAL' => 'Speciální doprava',
      'COOL_BALIK' => 'Cool balík'
    );
  }

  /**
   * ciselnik zpusoby dodani
   *
   * @return array
   * @throws \Dibi\Exception
   */
  public function getEnumDelModes() {
    $items = dibi::query("SELECT * FROM deliverymodes WHERE delmasid=0 ORDER BY delorder")
      ->fetchPairs('delid', 'delname');
    return($items);
  }

  Public function getEnumUlozenkaPlacesXml() {
    $xmlFname = TEMP_DIR.'/cache/ulozenka_pobocky.xml';
    $str = "";
    if (!file_exists($xmlFname)) {
      //stahnu XML pro ulozenku
      $str = @file_get_contents('https://www.ulozenka.cz/download/pobocky.xml');
      if (!empty($str)) {
        @file_put_contents(TEMP_DIR.'/cache/ulozenka_pobocky.xml', $str);
      }
    }

    $ulozenkaPobocky = @simplexml_load_file($xmlFname);
    $items = array();
    if (!empty($ulozenkaPobocky)) {
      foreach ($ulozenkaPobocky as $pobocka) {
        $items[(string)$pobocka->zkratka] = $pobocka->nazev.' - '.$pobocka->ulice.', '.$pobocka->psc;
      }
    }
    return $items;
  }

  Public function getEnumUlozenkaPlaces() {
    return dibi::query("SELECT uloshortcut, uloname FROM ulozenkapoints WHERE ulostatus=0 ORDER BY uloname")->fetchPairs("uloshortcut", "uloname");
  }

  Public function getEnumZasilkovnaPlaces() {
    return dibi::query("SELECT zasid, zasname FROM zasilkovnapoints WHERE zasstatus=0 ORDER BY zasname")->fetchPairs("zasid", "zasname");
  }

  Public function getEnumPathDelId() {
    return dibi::query("SELECT delid, delname FROM deliverymodes WHERE delcode='VLASTNI_PREPRAVA' ORDER BY deldayid")->fetchPairs("delid", "delname");
  }

  Public function getEnumGeisStatuses() {
    return array(
      '10' => 'Převzetí zásilky',
      '11' => 'Storno',
      '20' => 'Příjem na depo',
      '30' => 'Naložení na HB',
      '40' => 'Průchod překladištěm',
      '50' => 'Naložení na rozvoz',
      '51' => 'Závlek',
      '69' => 'Zpět odesílateli',
      '70' => 'Doručení',
      '71' => 'Nedoručení',
      '76' => 'Potvrzení výběru dobírky',
      '93' => 'Doručení do 12 hodin',
      '99' => 'Zásilka k proclení',
      '120' => 'Externí číslo',
      '121' => 'Zákaznická reference',
      '135' => 'Garantované doručení',
      '183' => 'Doručení na výdejní místo',
      '194' => 'Zpět z výdejny',
      '270' => 'Doručeno odesílateli',
      '307' => 'Závlek - lokální sklad'
    );
  }


}

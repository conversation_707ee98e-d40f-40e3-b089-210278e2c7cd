<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class CommentsModel extends BaseModel {

  protected $tableName = "comments";
  protected $fieldPrefix = "cmt";

  /********************* ciselniky *********************/
  public function getEnumCmtStatus() {
    return array(
      0 => "Aktivní",
      1 => "Blokovaný",
    );
  }

  public function getEnumCmtRate() {
    return array(
      1 => 1,
      2 => 2,
      3 => 3,
      4 => 4,
      5 => 5,
    );
  }

  public function getEnumCmtCatId() {
    return array(
      0 =>'Nezařazeno',
      276 =>'spalovače tuků',
      275 =>'jak a kdy dávkovat?',
      274 =>'speciální doplňky a anabolizéry',
      273 =>'rukavice, opasky a další',
      272 =>'kreatin a transportní systémy',
      271 =>'Síla a objem',
      270 =>'ostatní',
      269 =>'aminokyseliny',
      267 =>'sacharidy - gainery',
      266 =>'dieta a hubnutí',
      265 =>'rýsování a dieta',
      290 =>'doporučení suplementů',
      287 =>'klouby, zdraví a nemoci',
      284 =>'problémy s rBGH mlékem',
      261 =>'vše o Nitrixu a N.O. produktech',
      320 =>'technika',
      262 =>'nabírání čisté svalové hmoty',
      263 =>'objem a objemová fáze',
      264 =>'zvýšení tělesné hmotnosti',
      268 =>'proteiny - bílkoviny',
    );
  }
}

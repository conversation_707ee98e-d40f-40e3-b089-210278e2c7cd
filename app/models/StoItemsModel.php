<?php
namespace Model;
use Dibi\DateTime;
use Nette, dibi;

class StoItemsModel extends BaseModel {
  
  protected $tableName = "stoitems";
  protected $fieldPrefix = "sti";

  public function insert($data, $recalcProduct=true) {
    $ret = parent::insert($data);
    //pregeneruju stavy skladu 
    $proid = $data["stiproid"];
    if ($recalcProduct) {
      //$this->recalcProduct($proid);
    }
    return $ret;
  }

  /**
   * naskladní všechny položky dodávky
   *
   * @param $delId
   * @param $delDate
   * @return bool
   * @throws \Dibi\Exception
   */
  public function putOnStore($delId, $delDate) {
    $pros = $this->model->getProductsModel();

    $rows = dibi::fetchAll("SELECT stiid, stiproid, stiqty FROM stoitems WHERE stidelid=%i", $delId);
    foreach ($rows as $row) {
      $this->update($row->stiid, array(
        'stidatein' => new DateTime(),
        'stiqtyfree' => $row->stiqty
      ));
      $pros->recalcProduct($row->stiproid);
    }
    return true;
  }

  /**
   * vyskladní položku objednávky
   *
   * @param $stoItem
   * @param $ordItem
   * @param $prcCat
   * @param string $mode [retail|wholesale] maloobchod, velkoobchod
   * @throws \Dibi\Exception
   */
  public function dispatch($stoItem, $ordItem, $prcCat, $mode="retail") {
    $oris = $this->model->getOrdItemsModel();
    $pros = $this->model->getProductsModel();

    if ((int)$stoItem->stiqtyfree > 0) {
      //načtu příslušný produkt
      $pro = $pros->load($stoItem->stiproid);
      //zjistim zda je položka hmotnostní nebo kusová
      $itemType = $pros->getItemStoreType($pro);

      if ($itemType === $pros::ITEM_STORETYPE_WEIGHT) {
        $stiweightfree = $pro->proweight;
        if ($ordItem->orifill >= $stiweightfree) {
          $orifill = $ordItem->orifill - $stiweightfree;
          $filled = 0;
        } else {
          $orifill = 0;
          $filled = 0;
        }
      } else {
        $stiqtyfree = $stoItem->stiqtyfree;
        if ($ordItem->orifill >= $stiqtyfree) {
          $orifill = $ordItem->orifill - $stiqtyfree;
          $filled = $stiqtyfree;
        } else {
          $orifill = 0;
          $filled = $ordItem->orifill;
        }
      }



      //aktualizuji položku objednávky
      $vals = array();
      $vals['orifill'] = $orifill;

      $oris->update($ordItem->oriid, $vals);

      //příslušné množství odečtu ze skladu
      $vals = array(
        "stioriid" => $ordItem->oriid,
        "stidelid" => $stoItem->stidelid,
        "stiproid" => $stoItem->stiproid,
        "stistiid" => $stoItem->stiid,
        "stiean" => $stoItem->stiean,
        "stiqty" => (-1) * $filled,
        "stiqtyfree" => 0,
        "stiweight" => $stoItem->stiweight,
      );
      $this->insert($vals);
      //vložím zápornou položku
      $vals = array(
        "stiqtyfree" => $stiqtyfree - $filled,
      );
      $this->update($stoItem->stiid, $vals);
    }
    $oris->recalcItem($ordItem->oriid);
  }

}

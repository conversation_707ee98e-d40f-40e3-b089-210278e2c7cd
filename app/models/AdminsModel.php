<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class AdminsModel extends BaseModel {

  protected $tableName = "admins";
  protected $fieldPrefix = "adm";

  public function checkDuplicityEMail($admid, $admmail) {
    $cnt = dibi::fetchSingle("SELECT COUNT(*) AS cnt FROM admins WHERE admmail='$admmail'".($admid>0 ? " AND admid!=$admid" : ""));
    if ($cnt > 0) {
      throw New \RuntimeException("Tento email již existuje.");
    }
  }

  /********************* ciselniky *********************/
  /**
  * ciselnik admstatus
  * @return array
  */
  public function getEnumAdmStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  public function getEnumAdmins() {
    //naplnim zpusoby dopravy
     return dibi::query("SELECT admid, admname FROM admins WHERE admstatus=0 ORDER BY admname")->fetchPairs('admid', 'admname');
  }

}

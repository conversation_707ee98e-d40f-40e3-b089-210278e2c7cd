# Makefile pro správu Docker kontejnerů
# Použití: make [příkaz]

.PHONY: help up down restart build logs shell db-shell clean status ps

# V<PERSON><PERSON>z<PERSON> příkaz
.DEFAULT_GOAL := help

# Barvy pro výstup
GREEN=\033[0;32m
YELLOW=\033[1;33m
RED=\033[0;31m
NC=\033[0m # No Color

help: ## Zobrazí nápovědu
	@echo "$(GREEN)Dostupné příkazy:$(NC)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}'

up: ## Spustí všechny kontejnery
	@echo "$(GREEN)Spouštím Docker kontejnery...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)Kontejnery jsou spuštěné!$(NC)"
	@echo "Web aplikace: http://localhost"
	@echo "Databáze: localhost:3308"

down: ## Zastaví všechny kontejnery
	@echo "$(YELLOW)Zastavuji Docker kontejnery...$(NC)"
	docker-compose down

stop: ## Zastaví kontejnery (alias pro down)
	@make down

restart: ## Restartuje všechny kontejnery
	@echo "$(YELLOW)Restartuji Docker kontejnery...$(NC)"
	docker-compose restart
	@echo "$(GREEN)Kontejnery restartovány!$(NC)"

build: ## Sestaví Docker images
	@echo "$(GREEN)Sestavuji Docker images...$(NC)"
	docker-compose build --no-cache

rebuild: ## Znovu sestaví a spustí kontejnery
	@echo "$(GREEN)Znovu sestavuji a spouštím kontejnery...$(NC)"
	docker-compose down
	docker-compose build --no-cache
	docker-compose up -d
	@echo "$(GREEN)Hotovo!$(NC)"

logs: ## Zobrazí logy všech kontejnerů
	docker-compose logs -f

logs-web: ## Zobrazí logy web kontejneru
	docker-compose logs -f web

logs-db: ## Zobrazí logy databázového kontejneru
	docker-compose logs -f database

shell: ## Otevře shell v web kontejneru
	docker-compose exec web bash

shell-root: ## Otevře root shell v web kontejneru
	docker-compose exec --user root web bash

db-shell: ## Otevře MySQL shell
	docker-compose exec database mysql -u pstrosivejcecz -ppstrosivejcecz pstrosivejcecz2

db-root: ## Otevře MySQL shell jako root
	docker-compose exec database mysql -u root -proot

status: ## Zobrazí stav kontejnerů
	docker-compose ps

ps: ## Alias pro status
	@make status

clean: ## Vyčistí nepoužívané Docker objekty
	@echo "$(YELLOW)Čistím nepoužívané Docker objekty...$(NC)"
	docker system prune -f
	docker volume prune -f
	@echo "$(GREEN)Vyčištěno!$(NC)"

clean-all: ## Vyčistí všechny Docker objekty (POZOR: smaže i data!)
	@echo "$(RED)POZOR: Toto smaže všechny Docker objekty včetně dat!$(NC)"
	@read -p "Opravdu chcete pokračovat? (y/N): " confirm && [ "$$confirm" = "y" ]
	docker-compose down -v
	docker system prune -a -f
	docker volume prune -f

composer-install: ## Spustí composer install v kontejneru
	docker-compose exec web composer install

composer-update: ## Spustí composer update v kontejneru
	docker-compose exec web composer update

npm-install: ## Spustí npm install v kontejneru
	docker-compose exec web npm install

npm-build: ## Spustí npm build v kontejneru
	docker-compose exec web npm run build

backup-db: ## Vytvoří zálohu databáze
	@echo "$(GREEN)Vytvářím zálohu databáze...$(NC)"
	docker-compose exec database mysqldump -u pstrosivejcecz -ppstrosivejcecz pstrosivejcecz2 > backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)Záloha vytvořena!$(NC)"

dev: ## Spustí vývojové prostředí (up + logs)
	@make up
	@make logs

prod: ## Spustí produkční prostředí
	@echo "$(GREEN)Spouštím produkční prostředí...$(NC)"
	docker-compose -f docker-compose.yml up -d --build

info: ## Zobrazí informace o prostředí
	@echo "$(GREEN)=== Informace o prostředí ===$(NC)"
	@echo "Web aplikace: http://localhost"
	@echo "Databáze: localhost:3308"
	@echo "  - Uživatel: pstrosivejcecz"
	@echo "  - Heslo: pstrosivejcecz"
	@echo "  - Databáze: pstrosivejcecz2"
	@echo "  - Root heslo: root"
	@echo ""
	@echo "$(YELLOW)Užitečné příkazy:$(NC)"
	@echo "  make up      - Spustí kontejnery"
	@echo "  make logs    - Zobrazí logy"
	@echo "  make shell   - Otevře shell"
	@echo "  make db-shell - Otevře MySQL shell"

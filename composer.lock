{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "5224ebb557bc142eaf7d788df298f510", "packages": [{"name": "contributte/recaptcha", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/contributte/reCAPTCHA.git", "reference": "823c29d4fdba9ed81e2ddc2f5e2bd1c34a40e248"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/reCAPTCHA/zipball/823c29d4fdba9ed81e2ddc2f5e2bd1c34a40e248", "reference": "823c29d4fdba9ed81e2ddc2f5e2bd1c34a40e248", "shasum": ""}, "require": {"nette/di": "~2.4.11", "nette/forms": "~2.4.7", "nette/utils": "~2.5.1", "php": ">= 5.6"}, "conflict": {"nette/http": "<2.4.0"}, "require-dev": {"ninjify/nunjuck": "^0.2.0", "ninjify/qa": "^0.4.0"}, "suggest": {"ext-openssl": "To make requests via https"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "3.1.x-dev"}}, "autoload": {"psr-4": {"Contributte\\ReCaptcha\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Google reCAPTCHA for Nette - Forms", "homepage": "https://github.com/contributte/reCAPTCHA", "keywords": ["Forms", "<PERSON><PERSON>a", "google", "nette", "recaptcha"], "support": {"issues": "https://github.com/contributte/reCAPTCHA/issues", "source": "https://github.com/contributte/reCAPTCHA/tree/master"}, "time": "2018-08-22T13:31:56+00:00"}, {"name": "dibi/dibi", "version": "v3.0.7", "source": {"type": "git", "url": "https://github.com/dg/dibi.git", "reference": "22e6ea4e404504bb23a7075ed54c64e5d9bfb0e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/dibi/zipball/22e6ea4e404504bb23a7075ed54c64e5d9bfb0e2", "reference": "22e6ea4e404504bb23a7075ed54c64e5d9bfb0e2", "shasum": ""}, "require": {"php": ">=5.4.4"}, "replace": {"dg/dibi": "*"}, "require-dev": {"nette/tester": "~1.7", "tracy/tracy": "~2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/loader.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Dibi is Database Abstraction Library for PHP", "homepage": "https://dibiphp.com", "keywords": ["access", "database", "dbal", "mssql", "mysql", "odbc", "oracle", "pdo", "postgresql", "sqlite", "sqlsrv"], "support": {"issues": "https://github.com/dg/dibi/issues", "source": "https://github.com/dg/dibi/tree/v3.0.7"}, "time": "2017-01-04T14:18:17+00:00"}, {"name": "dotblue/nette-calendarpicker", "version": "dev-nette-2.4", "source": {"type": "git", "url": "https://github.com/dotblue/nette-calendarpicker.git", "reference": "b60e19f32c603b7a21dca8219ad44f95880cee34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dotblue/nette-calendarpicker/zipball/b60e19f32c603b7a21dca8219ad44f95880cee34", "reference": "b60e19f32c603b7a21dca8219ad44f95880cee34", "shasum": ""}, "require": {"nette/di": "~2.4", "nette/forms": "~2.4", "nette/utils": "~2.4", "php": ">=5.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "dotBlue", "homepage": "http://dotblue.net"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "http://github.com/vojtech-dobes"}], "description": "Flexible date (and time) picker for Nette Framework", "keywords": ["Forms", "date", "datetime", "nette", "time"], "support": {"issues": "https://github.com/dotblue/nette-calendarpicker/issues", "source": "https://github.com/dotblue/nette-calendarpicker"}, "time": "2016-05-14T14:21:00+00:00"}, {"name": "ecomailcz/ecomail", "version": "v1.2.7", "source": {"type": "git", "url": "https://github.com/Ecomailcz/ecomail-php.git", "reference": "6f24ff2efce5d1e56f531eb7677ccca55db132a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ecomailcz/ecomail-php/zipball/6f24ff2efce5d1e56f531eb7677ccca55db132a9", "reference": "6f24ff2efce5d1e56f531eb7677ccca55db132a9", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=5.3.2"}, "require-dev": {"phpstan/phpstan": "^0.12.9"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GNU GPL 3"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://filipsedivy.cz", "role": "Developer"}], "description": "Ecomail.cz API Wrapper", "keywords": ["Ecomail", "api", "email", "marketing", "newsletter", "wrapper"], "support": {"issues": "https://github.com/Ecomailcz/ecomail-php/issues", "source": "https://github.com/Ecomailcz/ecomail-php/tree/v1.2.7"}, "time": "2023-08-29T08:18:50+00:00"}, {"name": "guzzlehttp/guzzle", "version": "5.3.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "f9acb4761844317e626a32259205bec1f1bc60d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/f9acb4761844317e626a32259205bec1f1bc60d2", "reference": "f9acb4761844317e626a32259205bec1f1bc60d2", "shasum": ""}, "require": {"guzzlehttp/ringphp": "^1.1", "php": ">=5.4.0", "react/promise": "^2.2"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0"}, "type": "library", "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library and framework for building RESTful web service clients", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/5.3.2"}, "time": "2018-01-15T07:18:01+00:00"}, {"name": "guzzlehttp/ringphp", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/guzzle/RingPHP.git", "reference": "5e2a174052995663dd68e6b5ad838afd47dd615b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/RingPHP/zipball/5e2a174052995663dd68e6b5ad838afd47dd615b", "reference": "5e2a174052995663dd68e6b5ad838afd47dd615b", "shasum": ""}, "require": {"guzzlehttp/streams": "~3.0", "php": ">=5.4.0", "react/promise": "~2.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple API and specification that abstracts away the details of HTTP into a single PHP function.", "support": {"issues": "https://github.com/guzzle/RingPHP/issues", "source": "https://github.com/guzzle/RingPHP/tree/1.1.1"}, "abandoned": true, "time": "2018-07-31T13:22:33+00:00"}, {"name": "guzzlehttp/streams", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/guzzle/streams.git", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/streams/zipball/47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple abstraction over streams of data", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"issues": "https://github.com/guzzle/streams/issues", "source": "https://github.com/guzzle/streams/tree/master"}, "abandoned": true, "time": "2014-10-12T19:18:40+00:00"}, {"name": "ipub/visual-paginator", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/ipublikuj-ui/visual-paginator.git", "reference": "26ba98199a7d6d6a61072fc8323176e6cec39dbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ipublikuj-ui/visual-paginator/zipball/26ba98199a7d6d6a61072fc8323176e6cec39dbe", "reference": "26ba98199a7d6d6a61072fc8323176e6cec39dbe", "shasum": ""}, "require": {"latte/latte": "~2.2", "nette/application": "~2.2", "nette/di": "~2.2", "nette/utils": "~2.2", "php": ">=5.4.0"}, "require-dev": {"janmarek/mockista": "@dev", "nette/bootstrap": "~2.2", "nette/forms": "~2.2", "nette/mail": "~2.2", "nette/robot-loader": "~2.2", "nette/safe-stream": "~2.2", "nette/tester": "@dev", "tracy/tracy": "@dev"}, "type": "library", "autoload": {"psr-0": {"IPub\\VisualPaginator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "iPublikuj:cms", "email": "<EMAIL>", "homepage": "http://www.ipublikuj.eu/"}], "description": "Visual paginator for Nette Framework", "homepage": "https://github.com/iPublikuj/visual-paginator", "keywords": ["ipub", "ipublikuj", "nette", "paginator", "paging", "tools"], "support": {"email": "<EMAIL>", "issues": "https://github.com/iPublikuj/visual-paginator/issues", "source": "https://github.com/ipublikuj-ui/visual-paginator/tree/v1.0.7"}, "abandoned": true, "time": "2018-07-02T18:04:23+00:00"}, {"name": "latte/latte", "version": "v2.11.7", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "0ac0843a459790d471821f6a82f5d13db831a0d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/0ac0843a459790d471821f6a82f5d13db831a0d3", "reference": "0ac0843a459790d471821f6a82f5d13db831a0d3", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": "7.1 - 8.3"}, "conflict": {"nette/application": "<2.4.1"}, "require-dev": {"nette/php-generator": "^3.3.4", "nette/tester": "^2.0", "nette/utils": "^3.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.3"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-iconv": "to use filters |reverse, |substring", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/php-generator": "to use tag {templatePrint}", "nette/utils": "to use filter |webalize"}, "bin": ["bin/latte-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.11-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "support": {"issues": "https://github.com/nette/latte/issues", "source": "https://github.com/nette/latte/tree/v2.11.7"}, "time": "2023-10-18T17:16:11+00:00"}, {"name": "malcanek/idoklad-v2", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/malcanek/iDoklad-v2.git", "reference": "cef063a05485fd0f8c81ea93193f2ec37dc7a134"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/malcanek/iDoklad-v2/zipball/cef063a05485fd0f8c81ea93193f2ec37dc7a134", "reference": "cef063a05485fd0f8c81ea93193f2ec37dc7a134", "shasum": ""}, "require": {"php": ">=5.5.0"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"malcanek\\iDoklad\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.malcanek.cz", "role": "Author"}], "description": "PHP třída pro ulehčení požadavků na iDoklad api v2.", "homepage": "https://github.com/malcanek/iDoklad-v2", "keywords": ["fakturace", "idoklad"], "support": {"issues": "https://github.com/malcanek/iDoklad-v2/issues", "source": "https://github.com/malcanek/iDoklad-v2/tree/master"}, "time": "2024-11-25T11:32:45+00:00"}, {"name": "markette/gopay-inline", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/contributte/gopay-inline.git", "reference": "ce828ece7c98ecfa1eb79d7fb5c8a4a58af34223"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/gopay-inline/zipball/ce828ece7c98ecfa1eb79d7fb5c8a4a58af34223", "reference": "ce828ece7c98ecfa1eb79d7fb5c8a4a58af34223", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=5.6"}, "require-dev": {"mockery/mockery": "^0.9.6", "nette/di": "^2.4.4|^3.0", "nette/tester": "^1.7.0", "ninjify/nunjuck": "^0.1.4", "ninjify/qa": "^0.3.3", "phpstan/phpstan": "^0.12", "phpstan/phpstan-strict-rules": "^0.12"}, "suggest": {"nette/di": "For GopayExtension"}, "type": "library", "extra": {"ninjify": {"qa": {"linter": {"folders": ["src", "tests"]}, "codefixer": {"folders": ["src", "tests"], "ruleset": "nette"}, "codesniffer": {"folders": ["src", "tests"], "ruleset": "nette"}}}, "branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Contributte\\GopayInline\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Milan <PERSON>", "email": "<EMAIL>"}], "description": "GoPay Inline Payment Gateway", "homepage": "https://github.com/Contributte/GopayInline", "keywords": ["api", "eshop", "gopay", "inline", "payment"], "support": {"issues": "https://github.com/contributte/gopay-inline/issues", "source": "https://github.com/contributte/gopay-inline/tree/v1.3.1"}, "time": "2020-10-16T18:45:02+00:00"}, {"name": "mpdf/mpdf", "version": "v8.2.6", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "dd30e3b01061cf8dfe65e7041ab4cc46d8ebdd44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/dd30e3b01061cf8dfe65e7041ab4cc46d8ebdd44", "reference": "dd30e3b01061cf8dfe65e7041ab4cc46d8ebdd44", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "mpdf/psr-http-message-shim": "^1.0 || ^2.0", "mpdf/psr-log-aware-trait": "^2.0 || ^3.0", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0 || ~8.5.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "~2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "https://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2025-08-18T08:51:51+00:00"}, {"name": "mpdf/psr-http-message-shim", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/mpdf/psr-http-message-shim.git", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-http-message-shim/zipball/f25a0153d645e234f9db42e5433b16d9b113920f", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f", "shasum": ""}, "require": {"psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrHttpMessageShim\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Shim to allow support of different psr/message versions.", "support": {"issues": "https://github.com/mpdf/psr-http-message-shim/issues", "source": "https://github.com/mpdf/psr-http-message-shim/tree/v2.0.1"}, "time": "2023-10-02T14:34:03+00:00"}, {"name": "mpdf/psr-log-aware-trait", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-log-aware-trait.git", "reference": "7a077416e8f39eb626dee4246e0af99dd9ace275"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-log-aware-trait/zipball/7a077416e8f39eb626dee4246e0af99dd9ace275", "reference": "7a077416e8f39eb626dee4246e0af99dd9ace275", "shasum": ""}, "require": {"psr/log": "^1.0 || ^2.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrLogAwareTrait\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Trait to allow support of different psr/log versions.", "support": {"issues": "https://github.com/mpdf/psr-log-aware-trait/issues", "source": "https://github.com/mpdf/psr-log-aware-trait/tree/v2.0.0"}, "time": "2023-05-03T06:18:28+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.4", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "07d290f0c47959fd5eed98c95ee5602db07e0b6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/07d290f0c47959fd5eed98c95ee5602db07e0b6a", "reference": "07d290f0c47959fd5eed98c95ee5602db07e0b6a", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.4"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-08-01T08:46:24+00:00"}, {"name": "nette/application", "version": "v2.4.17", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "c2d4def8fce2602d74f03ff29204da0ee708c9dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/c2d4def8fce2602d74f03ff29204da0ee708c9dc", "reference": "c2d4def8fce2602d74f03ff29204da0ee708c9dc", "shasum": ""}, "require": {"nette/component-model": "^2.3", "nette/http": "^2.2", "nette/reflection": "^2.2", "nette/utils": "^2.4.3", "php": ">=5.6 <8.1"}, "conflict": {"nette/di": "<2.4", "nette/forms": "<2.4", "nette/latte": "<2.4 >=3.0", "nette/nette": "<2.2"}, "require-dev": {"latte/latte": "^2.4.3 <2.8", "mockery/mockery": "^1.0", "nette/di": "^2.4", "nette/forms": "^2.4", "nette/robot-loader": "^2.4.2 || ^3.0", "nette/security": "^2.4", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🏆 Nette Application: a full-stack component-based MVC kernel for PHP that helps you write powerful and modern web applications. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "component-based", "control", "framework", "mvc", "mvp", "nette", "presenter", "routing", "seo"], "support": {"issues": "https://github.com/nette/application/issues", "source": "https://github.com/nette/application/tree/v2.4.17"}, "time": "2020-11-24T19:05:35+00:00"}, {"name": "nette/bootstrap", "version": "v2.4.6", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "268816e3f1bb7426c3a4ceec2bd38a036b532543"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/268816e3f1bb7426c3a4ceec2bd38a036b532543", "reference": "268816e3f1bb7426c3a4ceec2bd38a036b532543", "shasum": ""}, "require": {"nette/di": "~2.4.7", "nette/utils": "~2.4", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "~2.2", "nette/application": "~2.3", "nette/caching": "~2.3", "nette/database": "~2.3", "nette/forms": "~2.3", "nette/http": "~2.4.0", "nette/mail": "~2.3", "nette/robot-loader": "^2.4.2 || ^3.0", "nette/safe-stream": "~2.2", "nette/security": "~2.3", "nette/tester": "~2.0", "tracy/tracy": "^2.4.1"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "support": {"issues": "https://github.com/nette/bootstrap/issues", "source": "https://github.com/nette/bootstrap/tree/v2.4"}, "time": "2018-05-17T12:52:20+00:00"}, {"name": "nette/caching", "version": "v2.5.9", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "d93ef446836a5a0ff7ef78d5ffebb7fe043f9953"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/d93ef446836a5a0ff7ef78d5ffebb7fe043f9953", "reference": "d93ef446836a5a0ff7ef78d5ffebb7fe043f9953", "shasum": ""}, "require": {"nette/finder": "^2.2 || ~3.0.0", "nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "^2.4", "nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "support": {"issues": "https://github.com/nette/caching/issues", "source": "https://github.com/nette/caching/tree/v2.5.9"}, "time": "2019-11-19T18:38:13+00:00"}, {"name": "nette/component-model", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "6e7980f5ddec31f68a39e767799b1b0be9dd1014"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/6e7980f5ddec31f68a39e767799b1b0be9dd1014", "reference": "6e7980f5ddec31f68a39e767799b1b0be9dd1014", "shasum": ""}, "require": {"nette/utils": "^2.5 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/application": "<2.4", "nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⚛ Nette Component Model", "homepage": "https://nette.org", "keywords": ["components", "nette"], "support": {"issues": "https://github.com/nette/component-model/issues", "source": "https://github.com/nette/component-model/tree/v2.4.0"}, "time": "2018-03-20T16:32:50+00:00"}, {"name": "nette/database", "version": "v2.4.12", "source": {"type": "git", "url": "https://github.com/nette/database.git", "reference": "c7a1431c9a82b866b7bd464ec9ad8758654f655a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/database/zipball/c7a1431c9a82b866b7bd464ec9ad8758654f655a", "reference": "c7a1431c9a82b866b7bd464ec9ad8758654f655a", "shasum": ""}, "require": {"ext-pdo": "*", "nette/caching": "^2.2", "nette/utils": "^2.4", "php": ">=5.6 <8.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"mockery/mockery": "^1.0.0", "nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💾 Nette Database: layer with a familiar PDO-like API but much more powerful. Building queries, advanced joins, drivers for MySQL, PostgreSQL, SQLite, MS SQL Server and Oracle.", "homepage": "https://nette.org", "keywords": ["database", "mssql", "mysql", "nette", "notorm", "oracle", "pdo", "postgresql", "queries", "sqlite"], "support": {"issues": "https://github.com/nette/database/issues", "source": "https://github.com/nette/database/tree/v2.4.12"}, "time": "2022-11-18T03:14:03+00:00"}, {"name": "nette/di", "version": "v2.4.17", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "aa25fa3dfcb0fc40af8da1f2495af75b63b5db1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/aa25fa3dfcb0fc40af8da1f2495af75b63b5db1d", "reference": "aa25fa3dfcb0fc40af8da1f2495af75b63b5db1d", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/neon": "^2.3.3 || ~3.0.0", "nette/php-generator": "^2.6.1 || ^3.0.0", "nette/utils": "^2.5.0 || ~3.0.0", "php": ">=5.6 <8.1"}, "conflict": {"nette/bootstrap": "<2.4", "nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💎 Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP 7.1 features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "support": {"issues": "https://github.com/nette/di/issues", "source": "https://github.com/nette/di/tree/v2.4.17"}, "time": "2020-11-06T00:28:18+00:00"}, {"name": "nette/finder", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "991aefb42860abeab8e003970c3809a9d83cb932"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/991aefb42860abeab8e003970c3809a9d83cb932", "reference": "991aefb42860abeab8e003970c3809a9d83cb932", "shasum": ""}, "require": {"nette/utils": "^2.4 || ^3.0", "php": ">=7.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔍 Nette Finder: find files and directories with an intuitive API.", "homepage": "https://nette.org", "keywords": ["filesystem", "glob", "iterator", "nette"], "support": {"issues": "https://github.com/nette/finder/issues", "source": "https://github.com/nette/finder/tree/v2.6.0"}, "time": "2022-10-13T01:31:15+00:00"}, {"name": "nette/forms", "version": "v2.4.11", "source": {"type": "git", "url": "https://github.com/nette/forms.git", "reference": "f29624795d77b155d5de82f186d0f63c8ebe03fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/forms/zipball/f29624795d77b155d5de82f186d0f63c8ebe03fe", "reference": "f29624795d77b155d5de82f186d0f63c8ebe03fe", "shasum": ""}, "require": {"nette/component-model": "~2.3", "nette/http": "^2.3.8", "nette/utils": "^2.4.6", "php": ">=5.6 <8.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "~2.4", "nette/di": "~2.4", "nette/tester": "~2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "?? Nette Forms: generating, validating and processing secure forms in PHP. Handy API, fully customizable, server & client side validation and mature design.", "homepage": "https://nette.org", "keywords": ["Forms", "bootstrap", "csrf", "javascript", "nette", "validation"], "support": {"issues": "https://github.com/nette/forms/issues", "source": "https://github.com/nette/forms/tree/v2.4.11"}, "time": "2020-11-06T00:25:25+00:00"}, {"name": "nette/http", "version": "v2.4.12", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "a5adfb1746f1d21d75e8b77f84a3d7f84977dcb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/a5adfb1746f1d21d75e8b77f84a3d7f84977dcb6", "reference": "a5adfb1746f1d21d75e8b77f84a3d7f84977dcb6", "shasum": ""}, "require": {"nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6 <8.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4.8 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of uploaded files", "nette/security": "allows use Nette\\Http\\UserStorage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "support": {"issues": "https://github.com/nette/http/issues", "source": "https://github.com/nette/http/tree/v2.4.12"}, "time": "2020-11-06T00:17:53+00:00"}, {"name": "nette/mail", "version": "v2.4.6", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "431f1774034cc14ee6a795b6514fe6343f75a68e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/431f1774034cc14ee6a795b6514fe6343f75a68e", "reference": "431f1774034cc14ee6a795b6514fe6343f75a68e", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of attached files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Mail: handy email creation and transfer library for PHP with both text and MIME-compliant support.", "homepage": "https://nette.org", "keywords": ["mail", "mailer", "mime", "nette", "smtp"], "support": {"issues": "https://github.com/nette/mail/issues", "source": "https://github.com/nette/mail/tree/v2.4"}, "time": "2018-11-21T22:35:13+00:00"}, {"name": "nette/neon", "version": "v2.4.3", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "5e72b1dd3e2d34f0863c5561139a19df6a1ef398"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/5e72b1dd3e2d34f0863c5561139a19df6a1ef398", "reference": "5e72b1dd3e2d34f0863c5561139a19df6a1ef398", "shasum": ""}, "require": {"ext-iconv": "*", "ext-json": "*", "php": ">=5.6.0"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette NEON: encodes and decodes NEON file format.", "homepage": "http://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/v2.4"}, "time": "2018-03-21T12:12:21+00:00"}, {"name": "nette/nette", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/nette/nette.git", "reference": "c74833a3581a7066a18c66b4f177b6a0bcbbf818"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/nette/zipball/c74833a3581a7066a18c66b4f177b6a0bcbbf818", "reference": "c74833a3581a7066a18c66b4f177b6a0bcbbf818", "shasum": ""}, "require": {"latte/latte": "^2.4", "nette/application": "^2.4", "nette/bootstrap": "^2.4", "nette/caching": "^2.5", "nette/component-model": "^2.4", "nette/database": "^2.4", "nette/di": "^2.4", "nette/finder": "^2.4", "nette/forms": "^2.4", "nette/http": "^2.4", "nette/mail": "^2.4", "nette/neon": "^2.4", "nette/php-generator": "^2.4 || ^3.0", "nette/robot-loader": "^2.4 || ^3.0", "nette/safe-stream": "^2.4", "nette/security": "^2.4", "nette/tokenizer": "^2.2", "nette/utils": "^2.5", "tracy/tracy": "^2.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["Nette/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Framework - innovative framework for fast and easy development of secured web applications in PHP (metapackage)", "homepage": "https://nette.org", "keywords": ["framework", "metapackage", "mvc"], "support": {"issues": "https://github.com/nette/nette/issues", "source": "https://github.com/nette/nette/tree/v2.5.0"}, "time": "2019-02-26T14:38:05+00:00"}, {"name": "nette/php-generator", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "7051954c534cebafd650efe8b145ac75b223cb66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/7051954c534cebafd650efe8b145ac75b223cb66", "reference": "7051954c534cebafd650efe8b145ac75b223cb66", "shasum": ""}, "require": {"nette/utils": "^2.4.2 || ^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "nikic/php-parser": "^4.4", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "suggest": {"nikic/php-parser": "to use ClassType::withBodiesFrom() & GlobalFunction::withBodyFrom()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 7.4 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/master"}, "time": "2020-06-19T14:31:47+00:00"}, {"name": "nette/reflection", "version": "v2.4.2", "source": {"type": "git", "url": "https://github.com/nette/reflection.git", "reference": "b12327e98ead74e87a1315e0d48182a702adf901"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/reflection/zipball/b12327e98ead74e87a1315e0d48182a702adf901", "reference": "b12327e98ead74e87a1315e0d48182a702adf901", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/caching": "^2.2 || ^3.0", "nette/utils": "^2.4 || ^3.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4 || ^3.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Reflection: docblock annotations parser and common reflection classes", "homepage": "https://nette.org", "keywords": ["annotation", "nette", "reflection"], "support": {"issues": "https://github.com/nette/reflection/issues", "source": "https://github.com/nette/reflection/tree/master"}, "abandoned": true, "time": "2017-07-11T19:28:57+00:00"}, {"name": "nette/robot-loader", "version": "v3.1.4", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "bde1d0ad576ff2b95e99a0b0f7d4fbe00c1ec0d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/bde1d0ad576ff2b95e99a0b0f7d4fbe00c1ec0d4", "reference": "bde1d0ad576ff2b95e99a0b0f7d4fbe00c1ec0d4", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/finder": "^2.3 || ^3.0", "nette/utils": "^2.4 || ^3.0", "php": ">=5.6 <8.2"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍀 Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "support": {"issues": "https://github.com/nette/robot-loader/issues", "source": "https://github.com/nette/robot-loader/tree/v3.1.4"}, "time": "2022-02-01T13:39:08+00:00"}, {"name": "nette/safe-stream", "version": "v2.5.1", "source": {"type": "git", "url": "https://github.com/nette/safe-stream.git", "reference": "96c57055927d0f2b4d0fe545896a7a0335adbeb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/safe-stream/zipball/96c57055927d0f2b4d0fe545896a7a0335adbeb5", "reference": "96c57055927d0f2b4d0fe545896a7a0335adbeb5", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"files": ["src/loader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette SafeStream: provides isolation for thread safe manipulation with files via native PHP functions.", "homepage": "https://nette.org", "keywords": ["atomic", "filesystem", "isolation", "nette", "safe", "thread safe"], "support": {"issues": "https://github.com/nette/safe-stream/issues", "source": "https://github.com/nette/safe-stream/tree/v2.5.1"}, "time": "2022-12-12T17:17:33+00:00"}, {"name": "nette/security", "version": "v2.4.4", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "7b8ac90c9ec405bb3b4dab9214bf122d3620fc65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/7b8ac90c9ec405bb3b4dab9214bf122d3620fc65", "reference": "7b8ac90c9ec405bb3b4dab9214bf122d3620fc65", "shasum": ""}, "require": {"nette/utils": "~2.4", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "~2.4", "nette/http": "~2.4", "nette/tester": "~2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "homepage": "https://nette.org", "keywords": ["Authentication", "acl", "authorization", "nette"], "support": {"issues": "https://github.com/nette/security/issues", "source": "https://github.com/nette/security/tree/v2.4"}, "time": "2018-10-17T15:50:54+00:00"}, {"name": "nette/tokenizer", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/nette/tokenizer.git", "reference": "88373e9f79007245af0ccd8132fde117421723b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tokenizer/zipball/88373e9f79007245af0ccd8132fde117421723b2", "reference": "88373e9f79007245af0ccd8132fde117421723b2", "shasum": ""}, "require": {"php": ">=5.4"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.7", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "<PERSON><PERSON>", "homepage": "https://nette.org", "support": {"issues": "https://github.com/nette/tokenizer/issues", "source": "https://github.com/nette/tokenizer/tree/v2.3.0"}, "abandoned": true, "time": "2017-09-08T14:16:06+00:00"}, {"name": "nette/utils", "version": "v2.5.7", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "d272f87cd6491377231702b1ccd920b6e981b713"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/d272f87cd6491377231702b1ccd920b6e981b713", "reference": "d272f87cd6491377231702b1ccd920b6e981b713", "shasum": ""}, "require": {"php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize() and toAscii()", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"files": ["src/loader.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠 Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.5.7"}, "time": "2020-12-13T14:12:17+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "radekdostal/nette-datetimepicker", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/radekdostal/Nette-DateTimePicker.git", "reference": "7ed93f5cea420b98ddd3b907e31d79d7dd9b7225"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/radekdostal/Nette-DateTimePicker/zipball/7ed93f5cea420b98ddd3b907e31d79d7dd9b7225", "reference": "7ed93f5cea420b98ddd3b907e31d79d7dd9b7225", "shasum": ""}, "require": {"nette/di": "^2.2.0", "nette/forms": "^2.2.0", "nette/utils": "^2.2.0", "php": ">=5.3.0"}, "require-dev": {"tracy/tracy": "^2.2.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1", "LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "DatePicker and DateTimePicker input controls for Nette Framework", "support": {"issues": "https://github.com/radekdostal/Nette-DateTimePicker/issues", "source": "https://github.com/radekdostal/Nette-DateTimePicker/tree/v2.7.0"}, "time": "2018-12-12T09:19:15+00:00"}, {"name": "react/promise", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "setasign/fpdi", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "4b53852fde2734ec6a07e458a085db627c60eada"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/4b53852fde2734ec6a07e458a085db627c60eada", "reference": "4b53852fde2734ec6a07e458a085db627c60eada", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^7.1 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "^7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.8"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.4"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2025-08-05T09:57:14+00:00"}, {"name": "tracy/tracy", "version": "v2.6.8", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "08710e2708a67e360b82e243c09489b2af2e3f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/08710e2708a67e360b82e243c09489b2af2e3f54", "reference": "08710e2708a67e360b82e243c09489b2af2e3f54", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": ">=7.1"}, "require-dev": {"nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.2", "nette/utils": "^2.4 || ^3.0", "psr/log": "^1.0"}, "suggest": {"https://nette.org/donate": "Please support <PERSON> via a donation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"files": ["src/Tracy/shortcuts.php"], "classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎 Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "support": {"issues": "https://github.com/nette/tracy/issues", "source": "https://github.com/nette/tracy/tree/v2.6.8"}, "time": "2022-02-15T16:14:57+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"dotblue/nette-calendarpicker": 20, "malcanek/idoklad-v2": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.4", "ext-iconv": "*", "ext-curl": "*", "ext-json": "*", "ext-soap": "*"}, "platform-dev": {}, "platform-overrides": {"php": "7.4"}, "plugin-api-version": "2.6.0"}
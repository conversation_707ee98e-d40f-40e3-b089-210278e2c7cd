{"extends": "stylelint-config-standard-scss", "rules": {"declaration-empty-line-before": null, "media-feature-name-no-vendor-prefix": true, "property-no-vendor-prefix": true, "block-no-empty": null, "selector-max-specificity": "0,4,0", "selector-pseudo-element-colon-notation": null, "no-descending-specificity": null, "selector-class-pattern": null, "shorthand-property-no-redundant-values": null, "scss/no-global-function-names": null, "number-max-precision": null, "function-url-quotes": null, "at-rule-no-vendor-prefix": null, "media-feature-range-notation": null, "selector-attribute-quotes": null, "color-function-notation": null, "alpha-value-notation": null, "declaration-block-no-redundant-longhand-properties": null, "at-rule-no-unknown": [true, {"ignoreAtRules": ["function", "if", "each", "include", "mixin"]}]}}
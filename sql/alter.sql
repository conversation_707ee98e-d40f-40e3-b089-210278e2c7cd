
-- nov<PERSON> tabulka stoitems
-- nová tabulka deliveries

alter table orditems change oriqtyfill orifill double null;

alter table comments
	add cmtrate double null after cmtsendreply;

alter table comments add cmtstatus tinyint(1) null after cmtdatec;
alter table comments modify cmtdatec datetime null after cmtstatus;

alter table comments add cmtordid int null after cmtreid;

-- tabulka fio_transaction

alter table orders add ordpaysesid varchar(100) null after ordstatus;

alter table articles add artbody3 text null after artbody2;
alter table articles add artbody4 text null after artbody3;

alter table attachments add ataurl varchar(255) null after atasize;

DROP TABLE IF EXISTS `coupons`;
CREATE TABLE `coupons` (
  `couid` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `couusrid` int(10) unsigned NOT NULL COMMENT 'id zakaznika',
  `couordid` int(10) unsigned DEFAULT NULL COMMENT 'id objednavky na ktere byl kupon pouzit',
  `coumamid` int(10) unsigned DEFAULT NULL COMMENT 'id mailingu',
  `coubasid` int(11) DEFAULT NULL COMMENT 'id košíku',
  `coumail` varchar(100) COLLATE utf8_unicode_ci NOT NULL COMMENT 'email',
  `couonlyreg` tinyint(4) NOT NULL DEFAULT '0',
  `coucode` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'kod kuponu',
  `couproducts` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'výšet ID produktů a jelich slevy vázaných ke kupónu',
  `couvalue` double unsigned NOT NULL DEFAULT '0' COMMENT 'hodnota',
  `couvalueunit` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `couvaluelimit` double DEFAULT NULL,
  `coupayfree` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'platba zdarma',
  `coudelfree` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'doprava zdarma',
  `couvalidto` date DEFAULT NULL COMMENT 'platnost kuponu',
  `coumailcnt` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'pocitadlo odmailovani kuponu',
  `couqty` int(11) NOT NULL DEFAULT '1' COMMENT 'počet kupónů',
  `coucounter` int(11) DEFAULT NULL COMMENT 'počítadlo kolik už bylo použito',
  `coustatus` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'status (0 - platny, 1 - vycerpany)',
  `coudatec` datetime DEFAULT NULL,
  `coudateu` datetime DEFAULT NULL,
  PRIMARY KEY (`couid`),
  KEY `i_usrid_mamid` (`couusrid`,`coumamid`),
  KEY `i_status_code_mail` (`coumail`,`coucode`,`coustatus`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

alter table deliverymodes add delnames varchar(255) null after delname;

alter table orders add orddelmonth tinyint null after orddeldatedel;

alter table coupons add coucodegroup varchar(100) null after coucode;

INSERT INTO config (cfgcode, cfgtypid, cfgcontroltype, cfgvalues, cfgvalue, cfgorder, cfgnote, cfgdatec, cfgdateu) VALUES ('EGG_MONTHS', 42, 'text', null, '4,5,6,7,8,9', 255, 'Čísla měsíců pro dodávku vajec. Čísla oddělte čárkou.', '2019-11-05 21:08:58', '2020-10-07 14:29:33')

INSERT INTO config (cfgcode, cfgtypid, cfgcontroltype, cfgvalues, cfgvalue, cfgorder, cfgnote, cfgdatec, cfgdateu) VALUES ('EGG_SWITCH', 42, 'combo', '0,1;Ne,Ano', '1', 0, 'Prodej vajec ', '2022-03-09 08:19:04', null)

alter table orders add ordregid tinyint(1) null comment 'kraj';



-- PROD


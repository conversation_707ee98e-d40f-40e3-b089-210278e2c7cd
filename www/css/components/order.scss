// ob<PERSON><PERSON><PERSON><PERSON><PERSON> proces
.order {
  // základ<PERSON><PERSON> pod<PERSON> nad<PERSON>ů
  h1,
  h2,
  h3,
  h4 {
    color: $color_main_light;
    font-weight: 300;
    text-transform: uppercase;
  }

  h1 {
    margin: 20px 0;

    font-size: 22px;

    @media (min-width: $mqsm) {
      margin: 40px 0 30px 0;

      font-size: 30px;
    }
  }

  h2 {
    font-size: 20px;
  }

  h3 {
    font-size: 18px;

    @media (min-width: $mqsm) {
      font-size: 25px;
    }
  }

  h4 {
    font-size: 16px;

    @media (min-width: $mqsm) {
      font-size: 18px;
    }
  }

  p {
    @media (max-width: $mqsm) {
      margin: 5px 0;
    }
  }

  a {
    color: $color_main_light;

    &:hover,
    &:focus {
      color: $color_main_dark;
    }
  }

  // ošetření barev odkazů
  a.btn,
  a.control .icon {
    color: $color_white;
  }
}

// tabulka v objednávce
.order__table {
  width: 100%;

  border: 0;

  th,
  td {
    padding: 5px; // více prostoru v tabulce

    border: 0;

    @media (min-width: $mqsm) {
      padding: 10px 20px; // více prostoru v tabulce
    }
  }

  // ošetření velikosti inputu
  input {
    width: 65px;
  }

  // rozlámání tabulky na malých rozlišeních
  @media only screen and (max-width: $mqxs) {
    display: block;

    th,
    td,
    tr,
    thead,
    tbody {
      display: block;
    }

    th + th {
      border-top: 1px solid $color_main_dark;
    }

    tr td:last-child {
      border-bottom: 2px solid $color_main_light;
    }
  }
}

// produkt + obrázek
.order__product {
  // zmenšení a pozice obrázku
  img {
    float: left;

    width: 70px;
    height: auto;
    margin-right: 8px;
  }

  // odkazy
  a {
    color: $color_main_light;
    font-size: 17px;
    text-decoration: none;
    text-transform: uppercase;

    img {
      &:hover {
        opacity: 0.8;
      }
    }

    &:hover,
    &:focus {
      color: $color_main_dark;
    }
  }
}

// počet kusů
.order__count {
  white-space: nowrap;
}

// cena
.order__price {
  max-width: 65px;

  font-weight: 700;
  text-align: right;
  white-space: nowrap;

  @media only screen and (max-width: $mqxs) {
    width: 100%;
    max-width: 100%;

    font-size: 18px;
    text-align: right;
  }
}

// cena za produkty celkem
.order__price-sum {
  td {
    color: $color_main;

    @media only screen and (max-width: $mqxs) {
      display: none;
    }
  }
}

// sleva
.order__discount {
  td {
    color: $color_success;
  }
}

// celková cena vč slevy, atd.
.order__price-final {
  margin-top: 20px;
  padding: 20px;

  color: $color_white;
  font-size: 16px;
  font-weight: 300;

  background-color: $color_main_light;

  strong {
    float: right;

    font-weight: 700;
    white-space: nowrap;
  }

  @media (min-width: $mqsm) {
    font-size: 25px;

    border-radius: $radius;
  }
}

// cena za dopravu
.order__price-delivery {
  margin-top: 10px;
  padding-top: 20px;

  strong {
    float: right;

    font-weight: 700;
    white-space: nowrap;
  }
}

// prázdný košík
.order__empty {
}

// zadání PSČ
.order__place {
  position: relative;

  margin: 8px 0;
  padding: 15px 20px 15px 110px;

  color: $color_white;
  font-size: 20px;
  line-height: 1.5;

  background-color: $color_label_action;

  input {
    border: none;
  }

  em {
    font-size: 15px;
    font-style: normal;
  }

  .icon {
    position: absolute;
    top: 20px;
    left: 25px;

    width: 60px;
  }
}

// slevový kupón
.order__coupon {
  margin-top: 16px;

  @media (max-width: $mqxxs) {
    font-size: 14px;
  }
}

// platební možnosti
.order__payment,
.order__delivery {
  position: relative;

  margin-bottom: 25px;
  padding-left: 30px;

  font-size: 16px;

  input {
    position: absolute;

    margin-left: -20px;
  }

  a {
    color: $color_main_light;

    &:hover,
    &:focus {
      color: $color_main_dark;
    }
  }
}

// nápověda
.order__help {
  font-size: 14px;
}

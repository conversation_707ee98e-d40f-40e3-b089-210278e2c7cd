// hlavička nad obsahem
.content-header {
  position: relative;

  padding: 40px 15px;

  color: $color_white;
  font-size: 22px;
  text-align: center;

  background-color: $color_main;

  @media (min-width: $mqsm) {
    padding: 100px 0;
  }

  h1,
  p {
    position: relative;
    z-index: $index_page + 2;

    margin: 0;
  }

  h1 {
    position: relative;

    margin-bottom: 20px;
    padding-bottom: 20px;

    font-size: 40px;
    text-transform: uppercase;

    &:before {
      content: '';

      position: absolute;
      bottom: 0;
      left: 50%;

      display: block;

      width: 100px;
      height: 2px;
      margin-left: -50px;

      background-color: $color_secondary;
    }
  }

  &:after {
    content: '';

    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: $index_page + 1;

    display: block;

    background: url(../img/content-header-back.png);
  }
}

// pozadí v hlavičce
.content-header__image {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $index_page;

  display: block;

  background-image: url(../img/content-header-back.jpg);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;

  opacity: 0.5;
}

// detail produktu
.product-detail {
  padding: 30px 0;

  @media (min-width: $mqsm) {
    padding: 60px 0;
  }

  .table {
    input {
      width: 65px; // ošetření velikosti inputu v tabulce
    }
  }
}

// základní styly
.product-detail__content {
  a {
    color: $color_main;

    &:hover,
    &:focus {
      color: $color_main_dark;
    }
  }
}

// nadpis produktu
.product-detail__header {
  margin-top: 0;

  color: $color_main;
  font-size: 24px;

  @media (min-width: $mqsm) {
    font-size: 30px;
  }
}

// popis produktu
.product-detail__description {
  margin-bottom: 20px;
}

// základní informace
.product-detail__info {
  margin-top: 16px;
  margin-bottom: 16px;

  line-height: 1.5;

  p {
    margin: 0 0 20px 0;
  }

  a {
    color: $color_main;
  }
}

// cena produktu
.product-detail__price {
  margin-top: 16px;

  line-height: 1.3;

  strong {
    color: $color_price;
    font-size: 24px;
  }
}

// hlavní obrázek produktu
.product-detail__image {
  margin-right: 20px;
  padding: 5px;

  border: 3px solid $color_main;

  img {
    display: block;

    width: 100%;
  }
}

// další obr<PERSON>zky
.product-detail__gallery {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  margin: 5px -10px;

  font-size: 0;

  a {
    display: block;
    flex: 0 1 25%;

    width: 25%;
    margin: 5px;
    padding: 5px;

    border: 3px solid $color_main;

    transition: opacity 0.3s;

    &:hover,
    &:focus {
      opacity: 0.8;
    }

    span {
      display: block;
      overflow: hidden;

      height: 250px;
    }
  }

  img {
    display: block;
  }
}

// pomocné tlačítka
.product-detail__helpers {
  margin: 16px 0;
}

// hlídací pes
.product-detail__watchdog {
  p {
    margin: 8px 0;
  }

  input[type='email'] {
    max-width: 150px;
    padding: 3px 5px;

    font-size: 14px;
  }

  strong {
    display: inline-block;

    padding-bottom: 5px;

    color: $color_main;
  }

  .icon {
    width: 18px;

    vertical-align: bottom;
  }
}

// výpis chatu
.product-detail__comments {
  a {
    color: $color_main;

    &:hover,
    &:focus {
      color: $color_main_dark;
    }
  }
}

// varianty produktu
.product-detail__variants {
  margin-top: 20px;

  h2 {
    color: $color_main;
  }

  p {
    text-align: center;

    @media (min-width: $mqsm) {
      text-align: right;
    }
  }
}

// hodnocení
.product-detail__rating {
  float: right;

  width: 300px;
  margin: 0 0 20px 20px;
  padding: 20px;

  font-size: 14px;

  background-color: lighten($color_secondary, 25%);

  h3 {
    margin: 0 0 10px 0;

    font-size: 15px;
  }

  p {
    margin: 0;
    padding: 10px 0 5px 0;
  }
}

// koupit
.product-detail__buy {
  margin-top: 15px;
}

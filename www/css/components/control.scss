// ovl<PERSON>dací prvky
.control {
  display: inline-block;

  width: 16px;
  height: 16px;
  margin: 3px 0;

  color: $color_white;
  font-size: 16px;
  font-weight: bold;
  line-height: 16px;
  text-align: center;
  text-decoration: none;

  border-radius: 8px;
  background-color: $color_info;

  cursor: pointer;
  user-select: none; // zabráněn<PERSON>

  // větší verze pro malé rozlišení
  @media (max-width: $mqxs) {
    width: 24px;
    height: 24px;

    font-size: 24px;
    line-height: 24px;

    border-radius: 12px;
  }

  // tisk<PERSON> verze
  @media print {
    display: none;
  }

  // hover efekty
  &:hover,
  &:focus {
    background-color: darken($color_info, 10%);
  }

  // úprava pokud je uvnitř ikona
  .icon {
    width: 12px;
    margin-top: 2px;

    vertical-align: top;

    // větš<PERSON> verze pro malé rozlišení
    @media (max-width: $mqxs) {
      width: 18px;
      margin-top: 3px;
    }
  }
}

a.control {
  color: $color_white !important; // přerazíme pokud by do<PERSON><PERSON> k <PERSON>ř<PERSON> barvy odkazu
}

// velké tlačítko
.control--big {
  width: 32px;
  height: 32px;

  font-size: 32px;
  line-height: 32px;

  border-radius: 16px;

  // úprava pokud je uvnitř ikona
  .icon {
    width: 24px;
    margin-top: 4px;

    vertical-align: top;
  }
}

// tlačítko potvrzení
.control--success {
  background-color: $color_success;

  &:hover,
  &:focus {
    background-color: darken($color_success, 10%);
  }
}

// tlačítko odstranit/zavřít
.control--remove {
  background-color: $color_danger;

  &:hover,
  &:focus {
    background-color: darken($color_danger, 10%);
  }
}

// input s počtem kusů
.control--count {
  white-space: nowrap; // nechceme zalamovat ovládací prvky

  .icon {
    vertical-align: top;
  }

  // omezíme velikost inputu
  input {
    max-width: 75px;
  }
}

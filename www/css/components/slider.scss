// úvodní slider
.slider {
  display: none; // na ma<PERSON><PERSON>ch roz<PERSON> s<PERSON>

  @media (min-width: $mqxs) {
    display: block;
  }

  p {
    margin: 30px 0;
  }

  .content-header {
    overflow: hidden;

    height: 300px;
    padding: 70px 0 0 0;
  }

  // přeražení stylů Slick slideru
  .slick-dots {
    position: absolute;
    right: 10px;
    bottom: 10px;
    z-index: 1;

    height: 26px;
    margin: 0;
    padding: 0;

    text-align: center;

    li {
      display: inline-block;

      margin: 5px;

      &.slick-active button {
        background-color: $color_white;
      }
    }

    button {
      display: block;
      overflow: hidden;

      width: 14px;
      height: 14px;
      padding: 0;

      line-height: 900px;

      border: none;
      border-radius: 14px;
      background-color: $color_secondary;

      // zrušení focusu
      &:focus {
        outline: none;
      }
    }
  }
}

// obs<PERSON><PERSON>, z<PERSON><PERSON><PERSON><PERSON> vzhled
.article {
  padding: 20px 0;

  font-size: 18px;
  line-height: 1.2; // pro v<PERSON><PERSON><PERSON><PERSON>lnost

  @media (min-width: $mqsm) {
    padding: 60px 0;
  }

  // základní odkaz
  // zaměříme všechny odkazy krom tlačítka (btn)
  a {
    &:not(.btn) {
      color: $color_main;

      &:hover,
      &:focus {
        color: $color_main_dark;
      }
    }
  }

  // základní odstavec
  p {
    line-height: 1.6;
  }

  // základní číslovaný seznam
  ol {
    padding-left: 35px;
  }

  // základní citace
  blockquote {
    margin: 16px 0;
    padding: 1px 25px;

    border-left: 2px solid $color_main;
    background-color: lighten($color_main, 60%);
  }

  // základní podoba nadpisů
  h1,
  h2,
  h3,
  h4 {
    color: $color_main;
  }

  h1 {
    font-size: 22px;

    @media (min-width: $mqsm) {
      font-size: 30px;
    }
  }

  // odr<PERSON><PERSON><PERSON>
  ul {
    margin: 0 0 10px 0;
    padding: 0;

    list-style-type: none;

    li {
      position: relative;

      padding: 3px 3px 3px 18px;

      &:before {
        content: '';

        position: absolute;
        top: 11px;
        left: 4px;

        display: block;

        width: 6px;
        height: 6px;

        border-radius: 3px;
        background-color: $color_main;
      }
    }
  }
}

// horní část
.article__perex {
  max-width: 900px;

  text-align: center;

  @media (min-width: $mqsm) {
    margin: 0 auto 40px auto;

    font-size: 20px;
  }
}

// vložený obrázek
.article__image {
  margin-left: 15px;
  padding: 5px;

  border: 3px solid $color_main;

  img {
    display: block;

    width: 100%;
  }
}

// vložené obrázky
.article__images {
  display: flex;
  flex-wrap: wrap;

  margin: 30px -10px;

  font-size: 0;

  a {
    display: block;
    flex: 1 1 1px;

    width: 30%;
    margin: 5px;
    padding: 5px;

    border: 3px solid $color_main;

    transition: opacity 0.3s;

    &:hover,
    &:focus {
      opacity: 0.8;
    }

    span {
      display: block;
      overflow: hidden;

      height: 250px;
    }
  }

  img {
    display: block;
  }
}

// vložené přílohy
.article__attachements {
  ul {
    padding-left: 2px;

    list-style-type: none;
  }

  li {
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .icon {
    width: 22px;
    margin-top: -3px;
    margin-right: 5px;
    margin-bottom: 3px;

    text-decoration: none;
  }
}

// související zboží a příslušenství
.article__related {
  h2 {
    color: $color_main_light;
    font-size: 25px;
    font-weight: 400;
  }
}

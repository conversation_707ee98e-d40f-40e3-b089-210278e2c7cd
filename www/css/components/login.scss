// p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>le
.login {
  display: inline-flex;

  margin-right: 20px;

  @media (max-width: $mqxs) {
    margin-bottom: 15px;
  }

  a {
    padding: 0 1px;

    color: $color_main;

    &:hover,
    &:focus {
      color: $color_main_dark;
    }
  }

  .icon {
    width: 30px;

    color: $color_main;
  }
}

// mobilní verze boxu (reset <PERSON><PERSON><PERSON>)
.login__wrapper {
  @media (max-width: $mq_menu - 1px) {
    position: absolute; // pozicujeme nahoru k ikoně
    top: 48px;
    z-index: 1;

    width: 100%;
    padding: 0;
  }

  @media (max-width: $mqmd) {
    clear: both;
  }
}

// nadpis boxu
.login__header {
  margin: 0 10px 0;

  // klikací ikona na mobilu
  @media (max-width: $mq_menu - 1px) {
    position: absolute;
    top: -1px;
    right: 38px;

    overflow: hidden;

    width: 40px;
    height: 40px;
    padding: 12px 0 0 6px;

    text-indent: -9999px;

    border-radius: 50%;
    background: $color_main_light url(../img/man-mobile.svg) center center no-repeat;
    background-size: 20px 20px;
  }
}

// obsah boxu
.login__content {
  margin: 0;
  padding: 5px 10px 8px 5px;

  line-height: 1.4;

  @media (max-width: $mq_menu - 1px) {
    position: absolute;
    top: 50px;
    right: 10px;
    z-index: $index_menu;

    border: 2px solid $color_main_light;
    background-color: $color_white;
  }
}

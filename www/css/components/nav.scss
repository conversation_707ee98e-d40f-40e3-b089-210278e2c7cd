// navigace
.nav {
  color: $color_white;

  background-color: $color_main;

  // na mobilní verzi bude místo loga pouze ikona
  @media (max-width: $mq_menu) {
    position: absolute;
    top: 9px;
    right: 5px;

    overflow: hidden;

    width: 40px;
    height: 40px;

    text-indent: -9999px;

    border-radius: 50%;
    background: $color_main_light url('../img/menu-icon.svg') center center no-repeat;
    background-size: 26px auto;
  }

  ul {
    margin: 0;
    padding: 0;

    list-style-type: none;

    font-size: 0; // inline-block fix
    text-align: center;
  }

  li {
    display: inline-block;
  }

  a {
    display: block;

    padding: 15px 10px;

    color: $color_white;
    font-size: 20px;
    font-weight: 700;
    text-decoration: none;
    text-transform: uppercase;

    @media (min-width: $mqmd) {
      padding: 25px 30px;
    }

    &:hover,
    &:focus,
    &.is-active {
      background-color: $color_main_dark;
    }
  }

  .icon {
    width: 50px;
    margin-right: 10px;
  }
}

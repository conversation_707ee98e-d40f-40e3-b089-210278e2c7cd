// chybo<PERSON><PERSON> h<PERSON>
.alert {
  margin: 16px 0;
  padding: 1px 16px;

  color: $color_white;

  border-radius: $radius;
  background-color: $color_main;

  @media (min-width: $mqsm) {
    padding: 4px 25px;
  }
}

// hláška s možností zavření
.alert--close {
  position: relative; // pro křížek na zavření

  padding: 1px 30px 1px 16px; // místo na křížek, bude-li třeba

  @media (min-width: $mqsm) {
    padding: 4px 30px 4px 25px; // místo na křížek, bude-li třeba
  }

  .icon--close {
    position: absolute;
    top: 19px;
    right: 17px;

    width: 18px;

    cursor: pointer;

    &:hover,
    &:focus {
      opacity: 0.8;
    }
  }
}

// potvrzující hláška
.alert--success {
  background-color: $color_success;
}

// informační hl<PERSON>š<PERSON>
.alert--info {
  background-color: $color_info;
}

// výstražná hláška
.alert--danger {
  background-color: $color_danger;
}

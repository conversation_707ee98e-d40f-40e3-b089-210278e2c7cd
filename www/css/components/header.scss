// hlavička
.header {
  color: $color_main_dark;

  background-color: $color_white;

  @media (max-width: $mq_menu - 1px) {
    min-height: 115px;
  }

  a {
    color: $color_white;
  }

  .container-fluid {
    position: relative;
  }
}

// logo pro mobil
.header__title {
  position: absolute;
  top: 5px;
  left: 5px;

  @media (min-width: $mq_menu) {
    display: none;
  }

  img {
    width: 46px;
  }
}

// logo
.header__logo {
  @media (min-width: $mq_menu) {
    position: relative;
    z-index: $index_menu + 1;

    margin: -50px 15px -25px 15px;
    padding: 0 !important;
  }

  img {
    display: block;

    width: 50px;

    @media (min-width: $mq_menu) {
      width: auto;
    }
  }
}

// část s logem
.header__center {
  display: none;

  @media (min-width: $mq_menu) {
    display: block;

    margin-top: -30px;
    margin-bottom: 10px;
  }
}

// hlavní menu
.header__nav {
  display: none;

  @media (min-width: $mq_menu) {
    display: block;
  }

  @media (max-width: $mq_menu - 1px) {
    position: absolute;
    top: 50px;
    left: 0;
    z-index: $index_menu;

    width: 100%;
    padding: 15px 0;

    background: $color_white;
  }

  ul {
    margin: 0;
    padding: 0;

    list-style-type: none;

    font-size: 0; // inline-block fix
    text-align: center;
  }

  li {
    @media (min-width: $mq_menu) {
      display: inline-block;

      padding-top: 45px;

      vertical-align: middle;
    }
  }

  a {
    display: block;

    padding: 15px 10px;

    color: $color_main;
    font-size: 20px;
    font-weight: 700;
    text-decoration: none;
    text-transform: uppercase;

    @media (min-width: $mqmd) {
      padding: 15px 15px;
    }

    &:hover,
    &:focus,
    &.is-active {
      color: $color_main_dark;
    }
  }
}

// košík, přihlášení
.header__controls {
  position: absolute;
  top: 10px;
  right: 50px;

  @media (min-width: $mq_menu) {
    right: auto;
    left: 57%;
  }
}

// kontaktní box
.header__contact {
  display: none;

  margin-bottom: 10px;
  padding: 10px 0 12px 0;

  color: $color_white;

  background-color: $color_main_dark;

  @media (min-width: $mq_menu) {
    display: block;
  }

  p {
    margin: 0;
  }

  a {
    display: inline-block;

    padding: 0 10px;

    text-decoration: none;

    &:hover,
    &:focus {
      color: $color_secondary;
    }
  }

  .icon {
    width: 20px;
    margin-top: -2px;
    margin-right: 3px;

    vertical-align: top;
  }
}

// socsítě
.header__social {
  display: none;

  @media (min-width: $mq_menu) {
    display: block;
    float: right;
  }

  a {
    padding: 0 0;
  }
}

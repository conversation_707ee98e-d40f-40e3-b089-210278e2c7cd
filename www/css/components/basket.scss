// odkaz na košík
.basket {
  display: inline-block;

  // klikací celý blok
  a {
    display: flex;

    color: $color_main;
    text-decoration: none;
  }

  .icon {
    width: 30px;

    color: $color_main;
  }
}

// mobilní verze boxu (reset <PERSON><PERSON><PERSON><PERSON><PERSON>)
.basket__wrapper {
  @media (max-width: $mq_menu - 1px) {
    position: absolute; // pozicujeme nahoru k ikoně
    top: 48px;
    z-index: 1;

    width: 100%;
    padding: 0;
  }
}

// nadpis boxu
.basket__header {
  position: relative;

  margin: 0;

  // klikací ikona na mobilu
  @media (max-width: $mq_menu - 1px) {
    position: absolute;
    top: -1px;
    right: 2px;

    overflow: hidden;

    width: 40px;
    height: 40px;
    padding: 12px 0 0 6px;

    text-indent: -9999px;

    border-radius: 50%;
    background: $color_main_light url(../img/basket.svg) center center no-repeat;
    background-size: 20px 20px;
  }
}

// obsah boxu
.basket__content {
  margin: 0;
  padding: 5px 10px 8px 10px;

  line-height: 1.4;
}

// počet kusů v košíku
.basket__count {
  position: absolute;
  top: 20px;
  right: -3px;

  display: none;

  width: 18px;
  height: 18px;
  padding: 5px 0;

  color: $color_main_dark;
  font-size: 11px;
  font-weight: 700;
  text-align: center;

  border-radius: 50%;
  background-color: $color_secondary;

  @media (min-width: $mqsm) {
    display: block;
  }
}

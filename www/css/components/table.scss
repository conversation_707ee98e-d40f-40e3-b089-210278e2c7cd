// tabulka
.table {
  width: 100%;

  border: none;

  tr {
    // zvýraznění řádku po najetí <PERSON>
    &:hover td {
      background-color: lighten($color_main, 60%);
    }
  }

  th,
  td {
    padding: 5px; // více prostoru v tabulce

    text-align: left;

    border: none;

    @media (min-width: $mqsm) {
      padding: 10px 20px; // více prostoru v tabulce
    }
  }

  // rozlámání tabulky na malých rozlišeních
  @media only screen and (max-width: $mqxs) {
    display: block;

    th,
    td,
    tr,
    thead,
    tbody {
      display: block;
    }

    th + th {
      border-top: 1px solid $color_main_light;
    }

    tr td:last-child {
      border-bottom: 2px solid $color_main;
    }
  }

  // hlavička tabulky
  th {
    color: $color_white;

    background-color: $color_main_light;
  }

  a {
    color: $color_main;
  }
}

// vertikální tabulka
.table--vertical {
  th {
    text-align: left;
  }
}

// tabulka provonání
.table--compare {
  td {
    text-align: center;
    vertical-align: top;
  }

  img {
    display: inline-block;
  }

  // nebudeme zdvojovat poslední linku
  tr td:last-child {
    border-bottom: 1px solid $color_main;
  }

  // zrušení rozlámání na malých rozlišeních
  @media only screen and (max-width: $mqxs) {
    display: table;

    th,
    td {
      display: table-cell;
    }

    tr,
    thead,
    tbody {
      display: table-row;
    }
  }

  // zvětšení boxů od většího rozlišení
  @media (min-width: $mqsm) {
    th {
      width: 5%;
    }

    td {
      width: 20%;
    }
  }
}

// produkt v tabulce
.table__product {
  img {
    display: inline-block;

    width: 45px;
    margin-right: 10px;

    vertical-align: middle;
  }

  a {
    font-weight: 700;
    text-decoration: none;
  }
}

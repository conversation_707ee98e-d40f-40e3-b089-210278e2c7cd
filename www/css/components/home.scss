// úvodní strana
.home {
}

// hlavní nadpis webu
.home__title {
  margin: 25px 0;

  text-align: center;
  text-transform: uppercase;

  @media (min-width: $mq_menu) {
    margin: 70px 0;
  }

  @media (max-width: $mqxs - 1px) {
    font-size: 20px;
    line-height: 1.3;
  }

  span {
    display: inline-block;

    padding: 16px 25px 14px 25px;

    color: $color_white;

    border-radius: 3px;
    background: $color_secondary_dark;
  }

  &:before {
    @media (min-width: $mq_menu) {
      content: '';

      position: absolute;
      z-index: -1;

      display: block;

      width: 100%;
      height: 2px;
      margin-top: 26px;

      background: $color_secondary_dark;
    }
  }
}

// výpis produktů
.home__products {
  .category__header {
    text-align: center;
  }
}

// textový blok
.home__block {
  padding: 20px 20px 20px 0;

  font-size: 18px;
  line-height: 1.2; // pro v<PERSON><PERSON><PERSON><PERSON> čitelnost

  // z<PERSON>lad<PERSON><PERSON> podoba nadpisů
  h1,
  h2,
  h3,
  h4 {
    color: $color_main;
  }

  // základní odstavec
  p {
    line-height: 1.6;
  }
}

// výpis fotek
.home__photos {
  padding: 20px 0;

  img {
    width: 100%;
  }
}

// hodnoty
.home__why {
  h3 {
    position: relative;

    padding-bottom: 20px;

    &:before {
      content: '';

      position: absolute;
      bottom: 0;
      left: 50%;

      display: block;

      width: 80px;
      height: 2px;
      margin-left: -40px;

      background: $color_secondary_dark;
    }
  }

  p {
    max-width: 200px;
    margin: 10px auto;

    line-height: 1.3;
  }
}

// ikona (v hodnotách)
.home__icon {
  width: 100px;
  height: 100px;
  margin: 0 auto;
  padding: 0;

  border-radius: 50px;
  background-color: $color_secondary_dark;

  .icon {
    width: 50px;
    margin-top: 25px;

    color: $color_white;
  }
}

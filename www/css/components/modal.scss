// modal okno
// modal se spouští kliknutím na blok, který má class="modal--show"
// ID okna musí být uvedeno v atributu REL rel="modal-name"
// konkrétní okno musí mít ID shodné s REL atributem
// pokud půjde o odkaz, je doporučeno dát ID do hrefu jako kotvu href="#modal-name"
.modal {
  position: absolute;
  top: 0.5%;
  left: 1%;
  z-index: 1000; // přeražení z-indexu

  display: none; // v základním stavu skryté

  width: 100%;

  // pozice přes celou obrazovku
  @media (min-width: $mqxs) {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    overflow: hidden; // zabránění přetečení

    background-color: rgba(0, 0, 0, 0.75); // pozadí - zešednut<PERSON>
  }
}

// reset modalu při nízké výš<PERSON> okna
// přid<PERSON><PERSON><PERSON> pouze <PERSON>Scriptem, vše přeraženo
.modal--reset {
  position: absolute !important;

  overflow: visible !important;

  background-color: transparent !important;

  // reset pozice a centrování
  .modal__body {
    top: 0 !important;

    transform: none !important;
  }
}

// tělo
.modal__body {
  position: absolute; // absolutní pozicování

  width: 98%; // šířka modal okna

  background: $color_white;
  box-shadow: 0 0 13px 2px rgba(0, 0, 0, 0.5); // stín boxu pro lepší přehnednost na nižších rozlišeních

  // doprostřed obrazovky - pouze na vyšším rozlišení
  @media (min-width: $mqxs) {
    top: 50%;
    left: 50%;

    transform: translate(-50%, -50%);
  }

  @media (min-width: $mq_modal) {
    width: 680px;
  }
}

// hlavička
.modal__head {
  padding: 8px;

  color: $color_white;

  background-color: $color_main;

  @media (min-width: $mq_modal) {
    padding: 16px 20px;
  }
}

// zavírací tlačítko
.modal__close {
  display: inline-block;
  float: right;

  font-size: 22px;

  cursor: pointer;

  &:hover,
  &:focus {
    opacity: 0.8;
  }
}

// nadpis
.modal__header {
  margin: 0;

  font-size: 20px;
}

// obsah
.modal__content {
  padding: 8px;

  @media (min-width: $mq_modal) {
    padding: 16px 20px;
  }
}

// patička
.modal__footer {
  padding: 8px;

  @media (min-width: $mq_modal) {
    padding: 16px 20px;
  }
}

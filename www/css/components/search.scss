// hledání v hlavičce
.search {
  position: absolute;
  top: 65px;

  width: 95%;

  @media (min-width: $mq_menu) {
    top: 8px;
    left: 160px;

    width: auto;
  }
}

.search__submit {
  position: absolute;
  top: 0;
  right: 0;

  overflow: hidden;

  width: 38px;
  height: 38px;

  text-indent: -9999px;

  background: url(../img/search.svg) center center no-repeat;
  background-color: transparent;

  opacity: 0.4;

  &:hover,
  &:focus {
    background-color: transparent;

    opacity: 1;
  }
}

.search__input {
  width: 100%;

  font-size: 15px;

  border-color: $color_gray_light;
  border-radius: 5px;

  @media (min-width: $mq_menu) {
    width: 250px;
  }
}

.search__sort {
  a {
    color: $color_main;

    &:hover,
    &:focus {
      color: $color_main_dark;
    }
  }
}

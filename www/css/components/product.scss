// výpis produktů
.product {
  margin: 5px 0;
  padding: 10px;

  text-align: center;

  border: 3px solid $color_main;
  background-color: #fff;

  @media (min-width: $mq_menu) {
    margin: 10px 0;
  }

  &:hover,
  &:focus {
    border-color: $color_secondary;
  }

  // štítky u produktu
  // přetěžujeme pozici - chceme formátovat pouze na výpisu
  .labels {
    position: absolute;
    top: 0;
    left: 0;

    width: 1px; // pro zarovnání štítků pod sebe a zachování inline bloku štítků

    text-align: left;

    // men<PERSON><PERSON> mezery mezi štítky
    .label {
      margin-bottom: 2px;
    }
  }
}

// hlavička produktu
.product__header {
  display: flex;
  align-items: center;
  justify-content: center;

  min-height: 70px;
  margin: 0;

  color: $color_white;
  font-size: 18px;
  line-height: 1.2;

  background-color: $color_main;

  @media (min-width: $mqsm) {
    font-size: 20px;
  }

  a {
    display: block;

    width: 100%;
    padding: 10px;

    color: $color_white;
    text-decoration: none;
  }
}

// obrázek produktu
.product__image {
  position: relative; // pro absolutní pozicování štítků

  img {
    width: 100%;
  }
}

// zkrácený text produktu
.product__info {
  margin: 4px 0 12px 0;

  font-size: 14px;
  line-height: 1.3;
}

// cena
.product__price {
  position: absolute;
  bottom: 2px;
  left: 0;

  padding: 7px 10px;

  color: $color_white;
  font-size: 13px;

  background: $color_main;

  // částka ceny
  strong {
    font-size: 20px;
  }
}

// ovládací prvky (tlačítka, inputy)
.product__controls {
  position: absolute;
  right: 0;
  bottom: 2px;

  .btn {
    margin: 0;

    font-weight: bold;

    border-radius: 0;
  }

  .icon {
    width: 25px;
  }
}

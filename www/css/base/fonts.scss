/* lora-regular - latin-ext_latin */
@font-face {
  font-family: 'Lora';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('../fonts/lora-v16-latin-ext_latin-regular.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('../fonts/lora-v16-latin-ext_latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../fonts/lora-v16-latin-ext_latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
       url('../fonts/lora-v16-latin-ext_latin-regular.woff') format('woff'), /* Modern Browsers */
       url('../fonts/lora-v16-latin-ext_latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
       url('../fonts/lora-v16-latin-ext_latin-regular.svg#Lora') format('svg'); /* Legacy iOS */
}
/* lora-700 - latin-ext_latin */
@font-face {
  font-family: 'Lora';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('../fonts/lora-v16-latin-ext_latin-700.eot'); /* IE9 Compat Modes */
  src: local(''),
       url('../fonts/lora-v16-latin-ext_latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../fonts/lora-v16-latin-ext_latin-700.woff2') format('woff2'), /* Super Modern Browsers */
       url('../fonts/lora-v16-latin-ext_latin-700.woff') format('woff'), /* Modern Browsers */
       url('../fonts/lora-v16-latin-ext_latin-700.ttf') format('truetype'), /* Safari, Android, iOS */
       url('../fonts/lora-v16-latin-ext_latin-700.svg#Lora') format('svg'); /* Legacy iOS */
}

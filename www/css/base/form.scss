// základní nastavení formul<PERSON><PERSON>

// sk<PERSON><PERSON><PERSON> formul<PERSON>ř<PERSON> a prvků v tiskové verzi
form,
button,
input[type='submit'] {
  @media print {
    display: none;
  }
}

// základní vzhled input polí
input,
textarea,
select {
  // ošetření na nižš<PERSON>, k<PERSON><PERSON> je zadán size
  width: 100%;
  max-width: 100%;
  margin-bottom: 5px;
  padding: 9px 10px;

  font-size: 16px;

  border: 1px solid $color_main;
  border-radius: $radius;

  &:focus {
    border: 1px solid $color_main_dark;
  }

  // pole v pořádku
  &.is--success {
    color: $color_success;

    border: 1px solid $color_success;
  }

  // chybně vyplněné pole
  &.is--danger {
    color: $color_danger;

    border: 1px solid $color_danger;
  }
}

// specifické definice pro input
.input--75 {
  width: 74%;
}

.input--50 {
  width: 49%;
}

.input--25 {
  width: 24%;
  max-width: 100px;
}

.input--postcode {
  max-width: 250px;
  margin-left: 10px;
}

// specifické definice pro textarea
.textarea--75 {
  @media (min-width: $mqxxs) {
    width: 74%;
  }
}

.textarea--50 {
  @media (min-width: $mqxxs) {
    width: 49%;
  }
}

// popisek pole
label {
  display: inline-block;

  padding-bottom: 5px;
}

// ošetření submit inputu, pokud je použit
button,
input[type='submit'] {
  width: auto;

  border: none;
  border-radius: $radius_button;

  cursor: pointer;
}

// ošetření chekboxu a radio buttonu
input[type='checkbox'],
input[type='radio'] {
  width: auto;
  margin-right: 5px;
}

// formuláře jsou uzavřeny ve fieldsetu
fieldset {
  margin: 16px 0;
  padding: 0 16px;

  border: 1px solid $color_main;
  border-radius: $radius;

  legend {
    padding: 6px 10px 7px 10px;

    color: $color_white;

    border-radius: $radius;
    background-color: $color_main;
  }

  // ošetření pro nižší rozlišení
  @media (max-width: $mqsm) {
    max-width: 100%;

    input,
    textarea {
      max-width: 100%;
    }
  }
}

// poloviční velikost fieldsetu
.fieldset--half {
  @media (min-width: $mqmd) {
    max-width: 50%;
  }
}

// hvězdičkové hodnocení
.star-rating {
  > fieldset {
    display: inline-block;

    margin: 0;
    padding: 0;

    border: none;

    &:not(:checked) {
      > input {
        position: absolute;
        right: 0;

        opacity: 0;
      }

      > label {
        float: right;
        overflow: hidden;

        width: 30px;
        height: 30px;

        color: $color_gray_light;
        font-size: 30px;
        line-height: 30px;
        white-space: nowrap;

        cursor: pointer;

        &:before {
          content: '★   ';
        }

        &:hover,
        &:hover ~ label {
          color: $color_secondary;

          &:before {
            content: '★   ';
          }
        }
      }
    }

    > input:checked {
      & ~ label {
        color: $color_secondary;

        &:before {
          content: '★   ';
        }
      }
    }

    > label:active {
      position: relative;
      top: 2px;
    }
  }
}

// ošetření google reCaptcha
.g-recaptcha {
  @media (max-width: $mqxxs) {
    width: 1px;

    transform: scale(0.74);
  }
}

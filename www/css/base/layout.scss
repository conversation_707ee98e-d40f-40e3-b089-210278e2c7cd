// maxim<PERSON><PERSON><PERSON> str<PERSON>
.container-fluid {
  max-width: $mqlg;
}

// cleafix na řádek
.row:before,
.row:after {
  content: ' ';

  display: table;
}

.row:after {
  clear: both;
}

.row {
  *zoom: 1;
}

// zarovnání v boxu
.col-center {
  text-align: center;
}

.col-left {
  text-align: left;
}

.col-right {
  text-align: right;
}

// XS sloupec bude na nižších rozliš<PERSON>ích v<PERSON>dy 100%
.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
  @media (max-width: $mqxxs) {
    width: 100%;
  }
}

// stejn<PERSON> vysoké boxy
.row--flex {
  display: flex;
  flex-wrap: wrap;

  // fix pro safari
  &:before,
  &:after {
    content: normal;
  }
}

.row--flex > [class*='col-'] {
  display: flex;
  flex-direction: column;
}

// vnořený div, nechceme mínusové marginy
.row--inner {
  margin: 0;
}

// okraj okolo celých boxů, pouze od vyššího rozlišení
.row--border {
  @media (min-width: $mqxxs) {
    border-top: 1px dotted lighten($color_main, 50%);
    border-left: 1px dotted lighten($color_main, 50%);

    & > div {
      border-right: 1px dotted lighten($color_main, 50%);
      border-bottom: 1px dotted lighten($color_main, 50%);

      // hover efekt vypočítáme z hlavní barvy
      &:hover,
      &:focus {
        z-index: 100;

        box-shadow: 0 0 10px fade($color_main, 40%);
      }
    }
  }
}

// autozarovnávač boxů
.row--autoclear {
  @media (min-width: 1200px) {
    .col-lg-1:nth-child(12n+1) {
      clear: left;
    }

    .col-lg-2:nth-child(6n+1) {
      clear: left;
    }

    .col-lg-3:nth-child(4n+1) {
      clear: left;
    }

    .col-lg-4:nth-child(3n+1) {
      clear: left;
    }

    .col-lg-6:nth-child(odd) {
      clear: left;
    }
  }

  @media (min-width: 992px) and (max-width: 1199px) {
    .col-md-1:nth-child(12n+1) {
      clear: left;
    }

    .col-md-2:nth-child(6n+1) {
      clear: left;
    }

    .col-md-3:nth-child(4n+1) {
      clear: left;
    }

    .col-md-4:nth-child(3n+1) {
      clear: left;
    }

    .col-md-6:nth-child(odd) {
      clear: left;
    }
  }

  @media (min-width: 768px) and (max-width: 991px) {
    .col-sm-1:nth-child(12n+1) {
      clear: left;
    }

    .col-sm-2:nth-child(6n+1) {
      clear: left;
    }

    .col-sm-3:nth-child(4n+1) {
      clear: left;
    }

    .col-sm-4:nth-child(3n+1) {
      clear: left;
    }

    .col-sm-6:nth-child(odd) {
      clear: left;
    }
  }

  @media (max-width: 767px) {
    .col-xs-1:nth-child(12n+1) {
      clear: left;
    }

    .col-xs-2:nth-child(6n+1) {
      clear: left;
    }

    .col-xs-3:nth-child(4n+1) {
      clear: left;
    }

    .col-xs-4:nth-child(3n+1) {
      clear: left;
    }

    .col-xs-6:nth-child(odd) {
      clear: left;
    }
  }
}

// fix gridu pro IE8
.lt-ie9 {
  .container,
  .container-fluid {
    display: table;

    width: 100%;
  }

  .row {
    display: table-row;

    height: 100%;
  }

  [class^=col-] {
    display: table-cell;
  }
}

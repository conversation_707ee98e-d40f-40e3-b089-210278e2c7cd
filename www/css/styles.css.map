{"version": 3, "sources": ["base/form.scss", "styles.css", "libs/normalize.scss", "libs/bootstrap-grid.scss", "libs/magnific-popup.scss", "styles.scss", "../js/libs/slick/slick.scss", "components/autocomplete.scss", "base/variables.scss", "base/base.scss", "base/layout.scss", "base/fonts.scss", "icons/icon.scss", "icons/icons.scss", "components/header.scss", "components/nav.scss", "components/nav-product.scss", "components/article.scss", "components/footer.scss", "components/alert.scss", "components/basket.scss", "components/btn.scss", "components/breadcrumb.scss", "components/category.scss", "components/comments.scss", "components/content-header.scss", "components/control.scss", "components/filter.scss", "components/home.scss", "components/login.scss", "components/label.scss", "components/modal.scss", "components/news.scss", "components/pagination.scss", "components/search.scss", "components/share.scss", "components/slider.scss", "components/stock.scss", "components/user.scss", "components/where.scss", "components/table.scss", "components/tabs.scss", "components/product.scss", "components/product-detail.scss", "components/order.scss", "components/order-progress.scss", "components/order-content.scss", "components/order-info.scss", "components/order-controls.scss", "components/order-delivery.scss", "base/helpers.scss", "base/print.scss"], "names": [], "mappings": "AAyKQ,gBC+wDR;ACx7DA,2EAAA,CAUA,KACE,gBAAA,CACA,6BDDF,CCoBA,GACE,aAAA,CACA,cDJF,CCeA,GACE,8BAAA,CAAA,sBAAA,CACA,QAAA,CACA,gBDNF,CCcA,IACE,+BAAA,CACA,aDPF,CCiBA,EACE,4BDTF,CCiBA,YACE,kBAAA,CACA,yBAAA,CACA,wCAAA,CAAA,gCDVF,CCiBA,SAEE,kBDXF,CCmBA,cAGE,+BAAA,CACA,aDZF,CCmBA,MACE,aDbF,CCqBA,QAEE,aAAA,CACA,aAAA,CACA,iBAAA,CACA,uBDdF,CCiBA,IACE,aDdF,CCiBA,IACE,SDdF,CCwBA,IACE,iBDhBF,CC2BA,sCAKE,mBAAA,CACA,cAAA,CACA,gBAAA,CACA,QDlBF,CC0BA,aAEE,gBDnBF,CC2BA,cAEE,mBDpBF,CC2BA,gDAIE,yBDrBF,CC4BA,wHAIE,iBAAA,CACA,SDtBF,CC6BA,4GAIE,6BDvBF,CC8BA,SACE,0BDxBF,CCkCA,OACE,6BAAA,CAAA,qBAAA,CACA,aAAA,CACA,aAAA,CACA,cAAA,CACA,SAAA,CACA,kBDzBF,CCgCA,SACE,uBD1BF,CCiCA,SACE,aD3BF,CCmCA,6BAEE,6BAAA,CAAA,qBAAA,CACA,SD5BF,CCmCA,kFAEE,WD7BF,CCqCA,cACE,4BAAA,CACA,mBD9BF,CCqCA,yCACE,uBD/BF,CCuCA,6BACE,yBAAA,CACA,YDhCF,CC0CA,QACE,aDlCF,CCyCA,QACE,iBDnCF,CCqDA,kBACE,YDtCF,CE7SA,WAEE,gBAAA,CADA,iBAAA,CAEA,iBAAA,CACA,kBFgTF,CE9SA,yBACE,WACE,WFiTF,CACF,CE/SA,yBACE,WACE,WFiTF,CACF,CE/SA,0BACE,WACE,YFiTF,CACF,CE/SA,iBAEE,gBAAA,CADA,iBAAA,CAEA,iBAAA,CACA,kBFiTF,CE/SA,KACE,iBAAA,CACA,kBFkTF,CEhTA,4eAEE,cAAA,CACA,iBAAA,CACA,kBAAA,CAHA,iBFsTF,CEjTA,2HACE,UFoTF,CElTA,WACE,UFqTF,CEnTA,WACE,kBFsTF,CEpTA,WACE,kBFuTF,CErTA,UACE,SFwTF,CEtTA,UACE,kBFyTF,CEvTA,UACE,kBF0TF,CExTA,UACE,SF2TF,CEzTA,UACE,kBF4TF,CE1TA,UACE,kBF6TF,CE3TA,UACE,SF8TF,CE5TA,UACE,kBF+TF,CE7TA,UACE,iBFgUF,CE9TA,gBACE,UFiUF,CE/TA,gBACE,kBFkUF,CEhUA,gBACE,kBFmUF,CEjUA,eACE,SFoUF,CElUA,eACE,kBFqUF,CEnUA,eACE,kBFsUF,CEpUA,eACE,SFuUF,CErUA,eACE,kBFwUF,CEtUA,eACE,kBFyUF,CEvUA,eACE,SF0UF,CExUA,eACE,kBF2UF,CEzUA,eACE,iBF4UF,CE1UA,eACE,UF6UF,CE3UA,gBACE,SF8UF,CE5UA,gBACE,iBF+UF,CE7UA,gBACE,iBFgVF,CE9UA,eACE,QFiVF,CE/UA,eACE,iBFkVF,CEhVA,eACE,iBFmVF,CEjVA,eACE,QFoVF,CElVA,eACE,iBFqVF,CEnVA,eACE,iBFsVF,CEpVA,eACE,QFuVF,CErVA,eACE,iBFwVF,CEtVA,eACE,gBFyVF,CEvVA,eACE,SF0VF,CExVA,kBACE,gBF2VF,CEzVA,kBACE,wBF4VF,CE1VA,kBACE,wBF6VF,CE3VA,iBACE,eF8VF,CE5VA,iBACE,wBF+VF,CE7VA,iBACE,wBFgWF,CE9VA,iBACE,eFiWF,CE/VA,iBACE,wBFkWF,CEhWA,iBACE,wBFmWF,CEjWA,iBACE,eFoWF,CElWA,iBACE,wBFqWF,CEnWA,iBACE,uBFsWF,CEpWA,iBACE,aFuWF,CErWA,yBACE,2HACE,UFwWF,CEtWA,WACE,UFwWF,CEtWA,WACE,kBFwWF,CEtWA,WACE,kBFwWF,CEtWA,UACE,SFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,SFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,SFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,iBFwWF,CEtWA,gBACE,UFwWF,CEtWA,gBACE,kBFwWF,CEtWA,gBACE,kBFwWF,CEtWA,eACE,SFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,SFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,SFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,UFwWF,CEtWA,gBACE,SFwWF,CEtWA,gBACE,iBFwWF,CEtWA,gBACE,iBFwWF,CEtWA,eACE,QFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,QFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,QFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,gBFwWF,CEtWA,eACE,SFwWF,CEtWA,kBACE,gBFwWF,CEtWA,kBACE,wBFwWF,CEtWA,kBACE,wBFwWF,CEtWA,iBACE,eFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,eFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,eFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,uBFwWF,CEtWA,iBACE,aFwWF,CACF,CEtWA,yBACE,2HACE,UFwWF,CEtWA,WACE,UFwWF,CEtWA,WACE,kBFwWF,CEtWA,WACE,kBFwWF,CEtWA,UACE,SFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,SFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,SFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,iBFwWF,CEtWA,gBACE,UFwWF,CEtWA,gBACE,kBFwWF,CEtWA,gBACE,kBFwWF,CEtWA,eACE,SFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,SFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,SFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,UFwWF,CEtWA,gBACE,SFwWF,CEtWA,gBACE,iBFwWF,CEtWA,gBACE,iBFwWF,CEtWA,eACE,QFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,QFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,QFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,gBFwWF,CEtWA,eACE,SFwWF,CEtWA,kBACE,gBFwWF,CEtWA,kBACE,wBFwWF,CEtWA,kBACE,wBFwWF,CEtWA,iBACE,eFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,eFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,eFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,uBFwWF,CEtWA,iBACE,aFwWF,CACF,CEtWA,0BACE,2HACE,UFwWF,CEtWA,WACE,UFwWF,CEtWA,WACE,kBFwWF,CEtWA,WACE,kBFwWF,CEtWA,UACE,SFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,SFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,SFwWF,CEtWA,UACE,kBFwWF,CEtWA,UACE,iBFwWF,CEtWA,gBACE,UFwWF,CEtWA,gBACE,kBFwWF,CEtWA,gBACE,kBFwWF,CEtWA,eACE,SFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,SFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,SFwWF,CEtWA,eACE,kBFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,UFwWF,CEtWA,gBACE,SFwWF,CEtWA,gBACE,iBFwWF,CEtWA,gBACE,iBFwWF,CEtWA,eACE,QFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,QFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,QFwWF,CEtWA,eACE,iBFwWF,CEtWA,eACE,gBFwWF,CEtWA,eACE,SFwWF,CEtWA,kBACE,gBFwWF,CEtWA,kBACE,wBFwWF,CEtWA,kBACE,wBFwWF,CEtWA,iBACE,eFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,eFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,eFwWF,CEtWA,iBACE,wBFwWF,CEtWA,iBACE,uBFwWF,CEtWA,iBACE,aFwWF,CACF,CGhgCA,QAQE,kBAAA,CACA,UAAA,CAHA,eAAA,CADA,YHugCF,CGjgCA,kBAPE,WAAA,CAFA,MAAA,CAKA,cAAA,CANA,KAAA,CAEA,UHohCF,CG5gCA,UAQE,kCAAA,CADA,sBAAA,CAFA,YHugCF,CGlgCA,eAQE,6BAAA,CAAA,qBAAA,CAJA,WAAA,CACA,MAAA,CAEA,aAAA,CALA,iBAAA,CADA,iBAAA,CAKA,KAAA,CAHA,UH0gCF,CGngCA,sBACE,UAAA,CACA,oBAAA,CACA,WAAA,CACA,qBHsgCF,CGpgCA,qCACE,YHugCF,CGrgCA,aAEE,oBAAA,CAEA,aAAA,CAHA,iBAAA,CAIA,eAAA,CAFA,qBAAA,CAGA,YHwgCF,CGtgCA,8DAGE,WAAA,CADA,UH0gCF,CGvgCA,cACE,eH0gCF,CGxgCA,iEAEE,uBAAA,CACA,eH2gCF,CGzgCA,UACE,cAAA,CACA,sBAAA,CAEA,cH4gCF,CG1gCA,8BACE,WH6gCF,CG3gCA,kDAIE,wBAAA,CACA,qBAAA,CACA,oBAAA,CAAA,gBH8gCF,CG5gCA,wBACE,YH+gCF,CG7gCA,UACE,sBHghCF,CG9gCA,eACE,UAAA,CAMA,QAAA,CADA,gBAAA,CAJA,iBAAA,CAMA,SAAA,CAHA,iBAAA,CAFA,OAAA,CACA,UAAA,CAKA,YHihCF,CGhhCE,iBACE,UHmhCJ,CGlhCI,uBACE,UHqhCN,CGhhCA,sDACE,YHuhCF,CGrhCA,kCAME,uBAAA,CAFA,sBAAA,CACA,QAAA,CAMA,uBAAA,CAAA,eAAA,CARA,cAAA,CAIA,aAAA,CACA,YAAA,CANA,gBAAA,CAOA,SAAA,CAGA,6BAAA,CAAA,yBAAA,CAFA,YH0hCF,CGthCA,yBAEE,QAAA,CADA,SH0hCF,CGvhCA,WAWE,UAAA,CAGA,uCAAA,CADA,iBAAA,CADA,iBAAA,CAVA,WAAA,CACA,mBAAA,CAMA,WAAA,CACA,qBAAA,CANA,iBAAA,CACA,OAAA,CAGA,iBAAA,CADA,oBAAA,CADA,KAAA,CALA,UHuiCF,CGzhCE,kCAEE,SH4hCJ,CG3hCE,kBACE,OH8hCJ,CG5hCA,6BACE,UH+hCF,CG7hCA,2DAEE,UAAA,CAGA,iBAAA,CAFA,UAAA,CACA,gBAAA,CAEA,UHgiCF,CG9hCA,aAIE,UAAA,CACA,gBAAA,CACA,oBAAA,CALA,iBAAA,CAEA,OAAA,CADA,KAAA,CAKA,kBHiiCF,CG/hCA,WAQE,YAAA,CAHA,gBAAA,CAHA,WAAA,CAIA,SAAA,CALA,iBAAA,CAGA,OAAA,CAGA,UAAA,CAEA,uCHkiCF,CGjiCE,kBACE,gBHoiCJ,CGniCE,kCAEE,SHsiCJ,CGriCE,mCAWE,wBAAA,CATA,UAAA,CACA,aAAA,CAEA,QAAA,CAEA,MAAA,CAGA,gBAAA,CADA,eAAA,CAHA,iBAAA,CAEA,KAAA,CAJA,OH+iCJ,CGviCE,iBAEE,wBAAA,CADA,qBAAA,CAEA,OH0iCJ,CGziCE,kBAEE,wBAAA,CADA,qBAAA,CAEA,UH4iCJ,CG1iCA,gBACE,MH6iCF,CG5iCE,sBACE,4BAAA,CACA,gBH+iCJ,CG9iCE,uBAEE,+BAAA,CADA,gBHkjCJ,CG/iCA,iBACE,OHkjCF,CGjjCE,uBACE,2BAAA,CACA,gBHojCJ,CGnjCE,wBACE,8BHsjCJ,CGpjCA,mBAEE,mBAAA,CADA,gBHwjCF,CGtjCE,gCACE,aAAA,CAEA,eAAA,CADA,UH0jCJ,CGxjCE,8BACE,SH2jCJ,CGzjCA,mBAEE,QAAA,CACA,eAAA,CACA,kBAAA,CAHA,UH+jCF,CG3jCE,0BAQE,eAAA,CADA,yCAAA,CAAA,iCAAA,CALA,aAAA,CAIA,WAAA,CAFA,MAAA,CAHA,iBAAA,CAEA,KAAA,CAEA,UHikCJ,CG3jCA,YAME,6BAAA,CAAA,qBAAA,CAFA,aAAA,CADA,WAAA,CAKA,aAAA,CANA,cAAA,CAKA,cAAA,CANA,UHskCF,CG5jCA,wBANE,aHukCF,CG/jCE,kBAYE,eAAA,CAPA,WAAA,CAMA,yCAAA,CAAA,iCAAA,CAVA,UAAA,CAKA,aAAA,CAGA,WAAA,CANA,MAAA,CADA,iBAAA,CAKA,OAAA,CAHA,QAAA,CAIA,UAAA,CAEA,UHokCJ,CGjkCE,kBACE,aAAA,CACA,aAAA,CACA,gBAAA,CACA,mBHokCJ,CGnkCE,mBACE,QHskCJ,CGpkCA,gBAME,WAAA,CAFA,MAAA,CAHA,gBAAA,CACA,iBAAA,CACA,QAAA,CAEA,UHwkCF,CGrkCA,WAGE,aAAA,CADA,oBAAA,CADA,eAAA,CAGA,oBAAA,CACA,kBHwkCF,CGtkCA,+BACE,cHykCF,CGvkCA,2CACE,cH0kCF,CGxkCA,8FAIE,kCACE,cAAA,CACA,eH2kCF,CG1kCA,4BACE,SH4kCF,CG3kCA,kCAEE,QAAA,CADA,KH8kCF,CG5kCA,kCACE,cAAA,CACA,eH8kCF,CG7kCA,gCACE,yBAAA,CACA,QAAA,CAKA,6BAAA,CAAA,qBAAA,CAJA,QAAA,CAEA,eAAA,CACA,cAAA,CAFA,QHklCF,CG9kCE,sCACE,SHglCJ,CG/kCA,6BACE,SAAA,CACA,OHilCF,CGhlCA,2BAME,yBAAA,CAFA,WAAA,CACA,qBAAA,CAIA,SAAA,CAFA,cAAA,CALA,OAAA,CAMA,iBAAA,CAPA,KAAA,CAEA,UHwlCF,CACF,CGjlCA,yBACE,WACE,4BAAA,CACA,wBAAA,CAAA,oBHmlCF,CGllCA,gBACE,0BAAA,CACA,sBAAA,CAAA,kBHolCF,CGnlCA,iBACE,6BAAA,CACA,yBAAA,CAAA,qBHqlCF,CGplCA,eACE,gBAAA,CACA,iBHslCF,CACF,CI36CE,iBAAmB,SAAA,CAAY,oCAAA,CAAA,4BJ+6CjC,CI96CE,2BAA6B,UJk7C/B,CIj7CE,8BAAgC,SJq7ClC,CIp7CE,gCAAkC,SAAA,CAAY,oCAAA,CAAA,4BJy7ChD,CIx7CE,0CAA4C,SJ47C9C,CI37CE,6CAA+C,SJ+7CjD,CK58CA,cAGI,6BAAA,CAAA,qBAAA,CACA,0BAAA,CAMA,sBAAA,CACA,kBAAA,CANA,wBAAA,CAEA,qBAAA,CACA,oBAAA,CACA,gBAAA,CAGA,uCLg9CJ,CK98CA,0BAZI,aAAA,CADA,iBLm+CJ,CKt9CA,YAII,QAAA,CAFA,eAAA,CAGA,SLi9CJ,CK/8CI,kBACI,YLi9CR,CK98CI,qBACI,cAAA,CACA,WLg9CR,CK78CA,qDAEI,+BAAA,CAEA,2BAAA,CAEA,uBLg9CJ,CK78CA,aAII,aAAA,CAFA,MAAA,CADA,iBAAA,CAEA,KLi9CJ,CK98CI,uCAEI,UAAA,CACA,aL+8CR,CK58CI,mBACI,UL88CR,CK38CI,4BACI,iBL68CR,CK18CA,aAcI,YAAA,CAbA,UAAA,CACA,WAAA,CACA,cL88CJ,CK78CI,uBACI,WL+8CR,CK78CI,iBACI,aL+8CR,CK78CI,+BACI,YL+8CR,CK18CI,0BACI,mBL48CR,CKz8CI,gCACI,aL28CR,CKx8CI,4BACI,iBL08CR,CKv8CI,6BAGI,4BAAA,CAFA,aAAA,CACA,WL08CR,CKt8CA,0BACI,YLy8CJ,CMxiDA,kBAKE,aCMa,CDVb,oBAAA,CAKA,eAAA,CAHA,gBN4iDF,CMtiDA,0BAIE,eCXc,CDUd,wBAAA,CAFA,aN2iDF,CMriDA,yBACE,eAAA,CAEA,eAAA,CAEA,kBNsiDF,CMniDA,uBACE,kBNsiDF,CMniDA,iCACE,aCbkB,CDclB,eNsiDF,CMniDA,oBACE,eNsiDF,CMniDA,2BAGE,+BAAA,CAFA,aNuiDF,CQ7kDA,KACE,6BAAA,CAAA,qBRglDF,CQ7kDA,iBAGE,0BAAA,CAAA,kBRglDF,CQ7kDA,IACE,8BAAA,CAAA,sBRglDF,CQ5kDA,MACE,cR+kDF,CQ3kDA,KAUE,qBD5Bc,CCuBd,aDtBc,CCuBd,sBAAA,CACA,cAAA,CACA,aAAA,CALA,QAAA,CAFA,iBRolDF,CQvkDA,cACE,kBR0kDF,CQvkDA,QAOE,WAAA,CALA,oBAAA,CAGA,WRykDF,CQpkDA,eANE,cR8kDF,CSxnDA,iBACE,gBT2nDF,CSvnDA,uBAEE,WAAA,CAEA,aTynDF,CStnDA,WACE,UTynDF,CStnDA,MACE,MTynDF,CSrnDA,YACE,iBTwnDF,CSrnDA,UACE,eTwnDF,CSrnDA,WACE,gBTwnDF,CSxmDE,yBAZF,2HAaI,UTunDF,CACF,CSnnDA,WACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cTsnDF,CSnnDE,mCAEE,cTonDJ,CShnDA,yBACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,6BAAA,CAAA,yBAAA,CAAA,qBTmnDF,CS/mDA,YACE,QTknDF,CS7mDE,yBADF,aAGI,8BAAA,CADA,6BTknDF,CS/mDE,iBAEE,gCAAA,CADA,+BTknDJ,CS9mDI,8CAIE,6CAAA,CAAA,qCAAA,CAFA,WTgnDN,CACF,CSvmDE,0BACE,2CACE,UT0mDJ,CSvmDE,0CACE,UTymDJ,CStmDE,0CACE,UTwmDJ,CSrmDE,0CACE,UTumDJ,CSpmDE,yCACE,UTsmDJ,CACF,CSnmDE,gDACE,2CACE,UTqmDJ,CSlmDE,0CACE,UTomDJ,CSjmDE,0CACE,UTmmDJ,CShmDE,0CACE,UTkmDJ,CS/lDE,yCACE,UTimDJ,CACF,CS9lDE,+CACE,2CACE,UTgmDJ,CS7lDE,0CACE,UT+lDJ,CS5lDE,0CACE,UT8lDJ,CS3lDE,0CACE,UT6lDJ,CS1lDE,yCACE,UT4lDJ,CACF,CSzlDE,yBACE,2CACE,UT2lDJ,CSxlDE,0CACE,UT0lDJ,CSvlDE,0CACE,UTylDJ,CStlDE,0CACE,UTwlDJ,CSrlDE,yCACE,UTulDJ,CACF,CSjlDE,4CAEE,aAAA,CAEA,UTmlDJ,CShlDE,aACE,iBAAA,CAEA,WTilDJ,CS9kDE,sBACE,kBTglDJ,CUzxDA,WAIE,iBAAA,CAHA,gBAAA,CACA,iBAAA,CACA,eAAA,CAEA,sDAAA,CACA,uXV6xDF,CUrxDA,WAIE,iBAAA,CAHA,gBAAA,CACA,iBAAA,CACA,eAAA,CAEA,kDAAA,CACA,mWVwxDF,CDvyDE,aAHF,+BAII,YC4yDF,CACF,CDxyDA,sBAWE,wBAAA,CACA,eQaO,CRhBP,cAAA,CAHA,iBAAA,CADA,cAAA,CAEA,gBAAA,CAHA,UCgzDF,CDtyDE,wCACE,wBC0yDJ,CDtyDE,0DAGE,wBAAA,CAFA,aC2yDJ,CDryDE,uDAGE,wBAAA,CAFA,aC0yDJ,CDnyDA,WACE,SCsyDF,CDnyDA,WACE,SCsyDF,CDnyDA,WAEE,eAAA,CADA,SCuyDF,CDnyDA,iBAEE,gBAAA,CADA,eCuyDF,CDjyDE,yBADF,cAEI,SCqyDF,CDjyDF,cAEI,SCqyDF,CALF,CD3xDA,MACE,oBAAA,CAEA,kBCmyDF,CD/xDA,0BAIE,WAAA,CACA,iBQpDc,CRsDd,cAAA,CALA,UCqyDF,CD5xDA,uCAGE,gBAAA,CADA,UCgyDF,CD3xDA,SAIE,wBAAA,CACA,eQvEO,CRmEP,aAAA,CACA,cCgyDF,CD3xDE,gBAME,wBQxGW,CRuGX,eQ9EK,CR4EL,UQ9GY,CR4GZ,oBCgyDJ,CDvxDE,yBAGE,0CAEE,cC0xDJ,CACF,CDpxDE,yBADF,gBAEI,aCwxDF,CACF,CDnxDE,sBAME,WAAA,CALA,oBAAA,CAEA,QAAA,CACA,SCsxDJ,CDjxDM,0CAIE,SAAA,CAHA,iBAAA,CACA,OCoxDR,CD/wDM,0CAOE,UQzJa,CR8Jb,cAAA,CAXA,WAAA,CAOA,kBAAA,CAHA,WAAA,CAIA,oBAAA,CAPA,eAAA,CAQA,kBAAA,CANA,UCsxDR,CD5wDQ,iDACE,cC8wDV,CD3wDQ,sGAEE,aC4wDV,CD1wDU,oHACE,cC4wDZ,CDrwDM,0CACE,aCuwDR,CDrwDQ,iDACE,cCuwDV,CDlwDI,mCACE,iBAAA,CACA,OCowDN,CD7vDE,yBADF,aAII,4BAAA,CAAA,wBAAA,CAAA,oBAAA,CAFA,SCkwDF,CACF,CW98DA,MACE,oBAAA,CAEA,iBAAA,CADA,qBXk9DF,CW/8DE,aACE,UAAA,CACA,aXi9DJ,CW58DA,WAKE,WAAA,CAFA,MAAA,CAFA,iBAAA,CACA,KAAA,CAEA,UAAA,CAEA,iBAAA,CACA,mBAAA,CACA,+BAAA,CAAA,uBX+8DF,CYn+DA,kBACE,UZs+DF,CYp+DE,yBACE,gBZs+DJ,CYl+DA,kBACE,UZq+DF,CYn+DE,yBACE,gBZq+DJ,CYj+DA,mBACE,UZo+DF,CYl+DE,0BACE,gBZo+DJ,CYh+DA,gBACE,UZm+DF,CYj+DE,uBACE,gBZm+DJ,CY/9DA,cACE,UZk+DF,CYh+DE,qBACE,gBZk+DJ,CY99DA,YACE,UZi+DF,CY/9DE,mBACE,gBZi+DJ,CY79DA,YACE,UZg+DF,CY99DE,mBACE,gBZg+DJ,CY59DA,aACE,UZ+9DF,CY79DE,oBACE,gBZ+9DJ,CY39DA,WACE,UZ89DF,CY59DE,kBACE,gBZ89DJ,CY19DA,gBACE,UZ69DF,CY39DE,uBACE,gBZ69DJ,CYz9DA,aACE,UZ49DF,CY19DE,oBACE,gBZ49DJ,CYx9DA,gBACE,UZ29DF,CYz9DE,uBACE,gBZ29DJ,CYv9DA,kBACE,UZ09DF,CYx9DE,yBACE,gBZ09DJ,CYt9DA,YACE,UZy9DF,CYv9DE,mBACE,gBZy9DJ,CYr9DA,iBACE,UZw9DF,CYt9DE,wBACE,gBZw9DJ,CYp9DA,UACE,UZu9DF,CYr9DE,iBACE,gBZu9DJ,CYn9DA,gBACE,UZs9DF,CYp9DE,uBACE,gBZs9DJ,CYl9DA,aACE,UZq9DF,CYn9DE,oBACE,gBZq9DJ,CYj9DA,WACE,UZo9DF,CYl9DE,kBACE,gBZo9DJ,CYh9DA,aACE,UZm9DF,CYj9DE,oBACE,gBZm9DJ,CY/8DA,UACE,UZk9DF,CYh9DE,iBACE,gBZk9DJ,CY98DA,aACE,UZi9DF,CY/8DE,oBACE,gBZi9DJ,CY78DA,YACE,UZg9DF,CY98DE,mBACE,gBZg9DJ,CY58DA,aACE,UZ+8DF,CY78DE,oBACE,gBZ+8DJ,CY38DA,iBACE,UZ88DF,CY58DE,wBACE,gBZ88DJ,CY18DA,iBACE,UZ68DF,CY38DE,wBACE,gBZ68DJ,CYz8DA,iBACE,UZ48DF,CY18DE,wBACE,gBZ48DJ,CYx8DA,iBACE,UZ28DF,CYz8DE,wBACE,gBZ28DJ,CYv8DA,cACE,UZ08DF,CYx8DE,qBACE,gBZ08DJ,CYt8DA,YACE,UZy8DF,CYv8DE,mBACE,gBZy8DJ,CYr8DA,eACE,UZw8DF,CYt8DE,sBACE,gBZw8DJ,CYp8DA,YACE,UZu8DF,CYr8DE,mBACE,gBZu8DJ,CYn8DA,aACE,UZs8DF,CYp8DE,oBACE,gBZs8DJ,CYl8DA,aACE,UZq8DF,CYn8DE,oBACE,gBZq8DJ,CYj8DA,aACE,UZo8DF,CYl8DE,oBACE,gBZo8DJ,CYh8DA,aACE,UZm8DF,CYj8DE,oBACE,gBZm8DJ,Ca/tEA,QAGE,qBNDc,CMDd,abmuEF,Ca/tEE,yBALF,QAMI,gBbkuEF,CACF,CahuEE,UACE,UbkuEJ,Ca/tEE,yBACE,iBbiuEJ,Ca5tEA,eAGE,QAAA,CAFA,iBAAA,CACA,ObguEF,Ca7tEE,yBALF,eAMI,YbguEF,CACF,Ca9tEE,mBACE,UbguEJ,Ca1tEE,yBADF,cAKI,uBAAA,CACA,mBAAA,CAJA,iBAAA,CACA,WbguEF,CACF,Ca3tEE,kBACE,aAAA,CAEA,Ub4tEJ,Ca1tEI,yBALF,kBAMI,Ub6tEJ,CACF,CaxtEA,gBACE,Yb2tEF,CaztEE,yBAHF,gBAII,aAAA,CAGA,kBAAA,CADA,gBb4tEF,CACF,CavtEA,aACE,Yb0tEF,CaxtEE,yBAHF,aAII,ab2tEF,CACF,CaztEE,yBAPF,aAgBI,eNjFY,CM2EZ,MAAA,CAIA,cAAA,CANA,iBAAA,CACA,QAAA,CAIA,UAAA,CAFA,Wb+tEF,CACF,CaxtEE,gBAME,WAAA,CAFA,oBAAA,CAHA,QAAA,CACA,SAAA,CAKA,iBbwtEJ,CaptEI,yBADF,gBAEI,oBAAA,CAEA,gBAAA,CAEA,qBbqtEJ,CACF,CaltEE,eAKE,aNpGW,CMgGX,aAAA,CAKA,iBAAA,CACA,eAAA,CAJA,iBAAA,CAKA,oBAAA,CACA,wBbktEJ,CahtEI,yBAXF,eAYI,YbmtEJ,CACF,CajtEI,mEAGE,abitEN,Ca3sEA,kBACE,iBAAA,CAEA,UAAA,CADA,Qb+sEF,Ca5sEE,yBALF,kBAOI,QAAA,CADA,UbgtEF,CACF,Ca3sEA,iBAQE,wBN1IkB,CMwIlB,UNlJc,CM6Id,YAAA,CAEA,kBAAA,CACA,mBb+sEF,CazsEE,yBAVF,iBAWI,ab4sEF,CACF,Ca1sEE,mBACE,Qb4sEJ,CazsEE,mBACE,oBAAA,CAEA,cAAA,CAEA,oBbysEJ,CavsEI,kDAEE,abwsEN,CapsEE,uBAGE,gBAAA,CADA,eAAA,CAGA,kBAAA,CAJA,UbysEJ,CahsEA,gBACE,YbmsEF,CajsEE,yBAHF,gBAII,aAAA,CACA,WbosEF,CACF,CalsEE,kBACE,SbosEJ,Ccp4EA,KAGE,wBPQa,COVb,Udw4EF,Ccn4EE,yBANF,KAmBI,0DAAA,CACA,yBAAA,CAFA,iBAAA,CAJA,WAAA,CAHA,eAAA,CAJA,iBAAA,CAEA,SAAA,CAOA,mBAAA,CARA,OAAA,CAKA,Udy4EF,CACF,Cch4EE,QAME,WAAA,CAFA,oBAAA,CAHA,QAAA,CACA,SAAA,CAKA,iBdg4EJ,Cc73EE,QACE,oBd+3EJ,Cc53EE,OAKE,UPxCY,COoCZ,aAAA,CAKA,iBAAA,CACA,eAAA,CAJA,iBAAA,CAKA,oBAAA,CACA,wBd43EJ,Cc13EI,yBAXF,OAYI,iBd63EJ,CACF,Cc33EI,2CAGE,wBd23EN,Ccv3EE,WAEE,iBAAA,CADA,Ud03EJ,Cet7EA,aAGE,eAAA,CAFA,iBf07EF,Cet7EE,gBAQE,wBAAA,CAFA,URTY,CQOZ,oBAAA,CAHA,QAAA,CACA,Sf27EJ,Cej7EE,qBAGE,aAAA,CAFA,yBfo7EJ,Ceh7EI,4BACE,WAAA,CAIA,aAAA,CAIA,iBAAA,CAFA,oBAAA,CAJA,iBfo7EN,Cez6EE,0BAGE,wBR5BW,CQ0BX,cf46EJ,Cex6EI,4BACE,wBf06EN,Cex6EM,oEAEE,wBfy6ER,Cep6EE,eAQE,gCAAA,CAHA,URtDY,CQkDZ,aAAA,CAEA,yBAAA,CAGA,oBfq6EJ,Cej6EI,0CAEE,wBfk6EN,Ce35EE,yBADF,sBAOI,SAAA,CALA,iBAAA,CACA,QAAA,CAGA,UAAA,CAFA,Sfi6EF,CACF,Ce15EA,qBACE,Yf65EF,Cev5EI,yBADF,wCAEI,YAAA,CAEA,gBf05EJ,CACF,Cep5EI,oDACE,Sfs5EN,Ce54EE,4FACE,afi5EJ,Ce/4EI,yBAHF,0CASI,wBRlFG,CQmFH,yBRnFG,CQsFH,cAAA,CADA,UAAA,CARA,iBAAA,CACA,KAAA,CAEA,Sfq5EJ,Ce74EI,gGAEE,Sf84EN,CACF,Cex4EA,qBASE,wBRjImB,CQ4HnB,URvIc,CQwId,kBAAA,CACA,eAAA,CALA,QAAA,CACA,qBAAA,CAKA,iBf24EF,Cev4EE,yBAXF,qBAYI,wBR7GK,CQ8GL,yBf04EF,CACF,Cet4EA,4BACE,Ofy4EF,Cep4EE,yBADF,oBAEI,gBfw4EF,CACF,Cet4EE,yCACE,qBfw4EJ,Cer4EE,sBAEE,qBR/JiB,CQ8JjB,6Bfw4EJ,Cer4EI,wDAEE,qBfs4EN,CgBjjFA,SAGE,kBAAA,CACA,eAAA,CAHA,chBsjFF,CgBjjFE,yBANF,SAOI,chBojFF,CACF,CgB/iFI,qBACE,ahBijFN,CgB/iFM,sDAEE,ahBgjFR,CgB1iFE,WACE,ehB4iFJ,CgBxiFE,YACE,iBhB0iFJ,CgBtiFE,oBAKE,wBAAA,CADA,6BAAA,CAHA,aAAA,CACA,gBhB0iFJ,CgBniFE,gDAIE,ahBqiFJ,CgBliFE,YACE,kBhBoiFJ,CgBliFI,yBAHF,YAII,kBhBqiFJ,CACF,CgBjiFE,YAIE,oBAAA,CAHA,eAAA,CACA,ShBoiFJ,CgBhiFI,eAGE,wBAAA,CAFA,iBhBmiFN,CgB/hFM,sBAaE,wBTxEO,CSuEP,iBAAA,CAXA,UAAA,CAMA,aAAA,CAGA,UAAA,CALA,QAAA,CAFA,iBAAA,CACA,QAAA,CAKA,ShBiiFR,CgBthFA,gBACE,eAAA,CAEA,iBhBwhFF,CgBthFE,yBALF,gBAQI,iBAAA,CAFA,kBhB0hFF,CACF,CgBphFA,gBAIE,wBAAA,CAHA,gBAAA,CACA,WhBwhFF,CgBphFE,oBACE,aAAA,CAEA,UhBqhFJ,CgBhhFA,iBACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAIA,WAAA,CAFA,iBhBmhFF,CgB/gFE,mBACE,aAAA,CACA,kBAAA,CAMA,wBAAA,CANA,oBAAA,CAAA,gBAAA,CAAA,YAAA,CAGA,UAAA,CACA,WAAA,CAIA,8BAAA,CAAA,sBAAA,CANA,ShBohFJ,CgB5gFI,kDAEE,UhB6gFN,CgB1gFI,wBACE,aAAA,CAGA,YAAA,CAFA,ehB6gFN,CgBvgFE,qBACE,ahBygFJ,CgBngFE,0BAGE,oBAAA,CAFA,gBhBugFJ,CgBlgFE,0BAEE,iBAAA,CADA,chBqgFJ,CgBjgFE,6BAIE,iBAAA,CADA,gBAAA,CADA,eAAA,CAIA,oBAAA,CALA,UhBugFJ,CgB5/EE,qBACE,aTxKiB,CSyKjB,mBAAA,CACA,ehB+/EJ,CiBtrFA,QAOE,wBVKkB,CUTlB,UVDc,CUEd,kBAAA,CACA,eAAA,CAJA,mBjB6rFF,CiBrrFE,yBATF,QAUI,gBjBwrFF,CACF,CiBtrFE,WACE,kBAAA,CAEA,wBjBurFJ,CiBprFE,UACE,ajBsrFJ,CiBprFI,gCAEE,UjBqrFN,CiB/qFA,aACE,kBjBkrFF,CiB9qFA,cACE,ajBirFF,CiB7qFA,kBAOE,WAAA,CACA,eAAA,CAHA,oBAAA,CAHA,eAAA,CACA,SjBmrFF,CiB5qFE,yBAVF,kBAWI,iBjB+qFF,CACF,CiB7qFE,qBACE,oBAAA,CAEA,kBjB8qFJ,CiB3qFE,oBAOE,UVjEY,CU2DZ,aAAA,CAGA,oBAAA,CADA,eAAA,CAEA,iBAAA,CAGA,oBAAA,CACA,wBjB2qFJ,CiBxqFI,oDAEE,ajByqFN,CiBrqFE,wBAEE,gBAAA,CADA,UjBwqFJ,CiBlqFA,aAKE,kBAAA,CAFA,oBAAA,CAFA,SjBuqFF,CiBjqFE,yBAPF,aAQI,ejBoqFF,CACF,CiBlqFE,gBACE,oBjBoqFJ,CiBjqFE,eAKE,UVvGY,CUmGZ,aAAA,CAEA,ejBmqFJ,CiBtpFE,iEACE,ajB4pFJ,CiBvpFA,mBAKE,wBVlHmB,CU8GnB,eAAA,CAEA,iBjB0pFF,CkBvxFA,OAOE,wBXIa,CWLb,eX8BO,CWhCP,UXFc,CWDd,aAAA,CACA,gBlB6xFF,CkBtxFE,yBATF,OAUI,gBlByxFF,CACF,CkBrxFA,cAGE,yBAAA,CAFA,iBlByxFF,CkBrxFE,yBALF,cAMI,yBlBwxFF,CACF,CkBtxFE,2BAOE,cAAA,CANA,iBAAA,CAEA,UAAA,CADA,QAAA,CAGA,UlBwxFJ,CkBpxFI,kEAEE,UlBqxFN,CkB/wFA,gBACE,wBlBkxFF,CkB9wFA,aACE,wBlBixFF,CkB7wFA,eACE,wBlBgxFF,CmBp0FA,QACE,oBnBu0FF,CmBp0FE,UAGE,aZIW,CYNX,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAGA,oBnBq0FJ,CmBl0FE,cAGE,aZHW,CYCX,UnBq0FJ,CmB7zFE,yBADF,iBAOI,SAAA,CALA,iBAAA,CACA,QAAA,CAGA,UAAA,CAFA,SnBm0FF,CACF,CmB5zFA,gBAGE,QAAA,CAFA,iBnBg0FF,CmB3zFE,yBANF,gBAoBI,uDAAA,CACA,yBAAA,CAFA,iBAAA,CALA,WAAA,CAHA,eAAA,CAIA,oBAAA,CARA,iBAAA,CAEA,SAAA,CAQA,mBAAA,CATA,QAAA,CAKA,UnBk0FF,CACF,CmBtzFA,iBAIE,eAAA,CAHA,QAAA,CACA,oBnB0zFF,CmBpzFA,eAiBE,wBZlEkB,CYiElB,iBAAA,CALA,aZhEkB,CY0DlB,YAAA,CAOA,kBAAA,CACA,eAAA,CALA,WAAA,CACA,aAAA,CARA,iBAAA,CAEA,UAAA,CAWA,iBAAA,CAZA,QAAA,CAKA,UnB6zFF,CmBjzFE,yBAnBF,eAoBI,anBozFF,CACF,CoB14FA,KAYE,wBbDa,CaDb,4BAAA,CACA,iBb0Bc,CajCd,UbFc,CaYd,cAAA,CATA,YAAA,CACA,cAAA,CALA,iBAAA,CACA,iBAAA,CAKA,oBAAA,CACA,wBpBg5FF,CoBx4FE,sBAEE,wBpBy4FJ,CoBr4FE,WAEE,YAAA,CAEA,qBAAA,CAHA,UpBy4FJ,CoBj4FA,MACE,oBpBo4FF,CoBh4FA,YAGE,iBAAA,CAFA,epBo4FF,CoB/3FE,kBACE,UpBi4FJ,CoB53FA,UAGE,iBAAA,CAFA,iBpBg4FF,CoB33FE,gBACE,UpB63FJ,CoBz3FE,yBAXF,UAcI,iBAAA,CAFA,gBpB63FF,CoBz3FE,gBACE,UpB23FJ,CACF,CoBt3FA,cACE,wBpBy3FF,CoBv3FE,wCAEE,wBpBw3FJ,CoBn3FA,WACE,wBpBs3FF,CoBp3FE,kCAEE,wBpBq3FJ,CoBh3FA,aACE,wBpBm3FF,CoBj3FE,sCAEE,wBpBk3FJ,CoB72FA,UAGE,wBbvFuB,CaqFvB,UpBi3FF,CoB72FE,gCAEE,wBpB82FJ,CqB19FA,YAOE,wBAAA,CAJA,UdDc,CcEd,eAAA,CAHA,cAAA,CAIA,iBrB69FF,CqBz9FE,cACE,UrB29FJ,CqBz9FI,0BAGE,0CAAA,CAFA,iBrB49FN,CqBt9FE,iBAUE,mDAAA,CATA,oBAAA,CAIA,YAAA,CAHA,eAAA,CAMA,mBAAA,CADA,qBAAA,CAHA,UrB29FJ,CsBl/FA,UACE,etBq/FF,CsBj/FA,kBACE,atBo/FF,CsBl/FE,yBAHF,kBAII,mBtBq/FF,CACF,CsBj/FA,uBACE,atBo/FF,CsBl/FE,yBAHF,uBAMI,kBAAA,CAFA,atBs/FF,CACF,CsBl/FE,yBACE,atBo/FJ,CsBl/FI,8DAEE,atBm/FN,CsB5+FE,kBAUE,wBflCW,CeiCX,efRK,CeKL,UfvCY,CekCZ,aAAA,CAEA,kBAAA,CACA,iBAAA,CAGA,oBtB++FJ,CsB1+FI,gDAEE,wBtB2+FN,CsBt+FE,sBAGE,gBAAA,CADA,gBAAA,CAGA,qBAAA,CAJA,UtB2+FJ,CsBl+FA,mBACE,etBq+FF,CsBl+FE,+BACE,QtBo+FJ,CsBl+FI,yBAHF,+BAII,WtBq+FJ,CACF,CsBj+FE,0BACE,ctBm+FJ,CuB7iGA,gBAGE,kBAAA,CAFA,WvBijGF,CuB7iGE,kBAEE,eAAA,CADA,cvBgjGJ,CuB1iGA,uBAGE,yBAAA,CAFA,evB8iGF,CuBxiGA,kBAGE,mBAAA,CAFA,QvB4iGF,CuBtiGA,gBACE,UhBvBmB,CgBwBnB,kBvByiGF,CwBzkGA,gBASE,wBjBEa,CiBNb,UjBHc,CiBId,kBAAA,CAHA,iBAAA,CAFA,iBAAA,CAMA,iBxB2kGF,CwBvkGE,yBAXF,gBAYI,exB0kGF,CACF,CwBxkGE,qCAKE,QAAA,CAHA,iBAAA,CACA,UxB2kGJ,CwBtkGE,mBAME,gBAAA,CAHA,kBAAA,CACA,mBAAA,CAHA,iBAAA,CAMA,wBxBskGJ,CwBpkGI,0BAaE,wBjB7Bc,CiBoBd,QAAA,CAHA,UAAA,CAMA,aAAA,CAGA,UAAA,CALA,QAAA,CAMA,iBAAA,CARA,iBAAA,CAMA,WxBskGN,CwB9jGE,sBAYE,8CAAA,CANA,QAAA,CALA,UAAA,CASA,aAAA,CAHA,MAAA,CAJA,iBAAA,CAEA,OAAA,CADA,KAAA,CAIA,UxBikGJ,CwBxjGA,uBAUE,oDAAA,CAEA,uBAAA,CADA,2BAAA,CAEA,qBAAA,CATA,QAAA,CAIA,aAAA,CAHA,MAAA,CAUA,UAAA,CAdA,iBAAA,CAEA,OAAA,CADA,KAAA,CAIA,UxBikGF,CyBzoGA,SAeE,wBlBFmB,CkBCnB,iBAAA,CAPA,UlBLc,CkBed,cAAA,CAhBA,oBAAA,CAOA,cAAA,CACA,eAAA,CALA,WAAA,CAMA,gBAAA,CALA,YAAA,CAMA,iBAAA,CACA,oBAAA,CAMA,wBAAA,CAAA,qBAAA,CAAA,oBAAA,CAAA,gBAAA,CAfA,UzBupGF,CyBroGE,yBArBF,SA4BI,kBAAA,CAHA,gBAAA,CAFA,WAAA,CAGA,kBAAA,CAJA,UzB4oGF,CACF,CyBnoGE,aAhCF,SAiCI,YzBsoGF,CACF,CyBnoGE,8BAEE,wBzBooGJ,CyBhoGE,eAEE,cAAA,CAEA,kBAAA,CAHA,UzBooGJ,CyB9nGI,yBAPF,eASI,cAAA,CADA,UzBkoGJ,CACF,CyB7nGA,UACE,oBzBgoGF,CyB5nGA,cAOE,kBAAA,CAHA,cAAA,CAFA,WAAA,CAGA,gBAAA,CAJA,UzBmoGF,CyB1nGE,oBAEE,cAAA,CAEA,kBAAA,CAHA,UzB8nGJ,CyBtnGA,kBACE,wBzBynGF,CyBvnGE,gDAEE,wBzBwnGJ,CyBnnGA,iBACE,wBzBsnGF,CyBpnGE,8CAEE,wBzBqnGJ,CyBhnGA,gBACE,kBzBmnGF,CyBjnGE,sBACE,kBzBmnGJ,CyB/mGE,sBACE,czBinGJ,C0B/tGA,QACE,e1BkuGF,C0B9tGA,qBAIE,iBAAA,CAHA,iBAAA,CAKA,kBAAA,CAHA,U1BkuGF,C0B7tGE,yBARF,qBASI,S1BguGF,CACF,C0B9tGE,yBAZF,qBAaI,S1BiuGF,CACF,C0B/tGE,0BAhBF,qBAiBI,S1BkuGF,CACF,C0BhuGE,2BAIE,iBAAA,CADA,cAAA,CAFA,iB1BouGJ,C0B5tGA,iBAQE,wBnB/Ba,CmB8Bb,2BnBLO,CmBIP,4BnBJO,CmBEP,UnBpCc,CmBiCd,YAAA,CACA,sB1BmuGF,C0B1tGE,wBACE,gB1B4tGJ,C0BttGE,yBADF,aAGI,kBAAA,CADA,e1B2tGF,CACF,C2B7wGA,aACE,aAAA,CAEA,iBAAA,CACA,wB3B+wGF,C2B7wGE,yBANF,aAOI,a3BgxGF,CACF,C2B9wGE,yBAVF,aAWI,iBAAA,CACA,e3BixGF,CACF,C2B/wGE,kBAQE,kBpBVqB,CoBSrB,iBAAA,CAFA,UpBtBY,CoBkBZ,oBAAA,CAEA,sB3BmxGJ,C2B1wGI,yBADF,oBAaI,kBpB1BmB,CoBenB,UAAA,CAKA,aAAA,CAGA,UAAA,CACA,eAAA,CAPA,iBAAA,CAKA,UAAA,CAJA,U3BixGJ,CACF,C2BnwGE,kCACE,iB3BswGJ,C2BjwGA,aAGE,kBAAA,CACA,eAAA,CAHA,wB3BswGF,C2BhwGE,gEAIE,a3BkwGJ,C2B9vGE,eACE,e3BgwGJ,C2B3vGA,cACE,c3B8vGF,C2B5vGE,kBACE,U3B8vGJ,C2BxvGE,cAGE,mBAAA,CAFA,iB3B4vGJ,C2BxvGI,qBAaE,kBpBxFmB,CoB+EnB,QAAA,CAHA,UAAA,CAMA,aAAA,CAGA,UAAA,CALA,QAAA,CAMA,iBAAA,CARA,iBAAA,CAMA,U3B0vGN,C2BlvGE,aAIE,eAAA,CAFA,gBAAA,CADA,e3BsvGJ,C2B9uGA,YAOE,wBpB5GuB,CoB2GvB,kBAAA,CAJA,YAAA,CACA,aAAA,CACA,SAAA,CAHA,W3BsvGF,C2B9uGE,kBAIE,UpBjIY,CoB+HZ,eAAA,CADA,U3BkvGJ,C4Bl3GA,OACE,0BAAA,CAAA,2BAAA,CAAA,0BAAA,CAAA,mBAAA,CAEA,iB5Bo3GF,C4Bl3GE,yBALF,OAMI,kB5Bq3GF,CACF,C4Bn3GE,SAGE,arBDW,CqBDX,a5Bs3GJ,C4Bl3GI,8BAEE,a5Bm3GN,C4B/2GE,aAGE,arBZW,CqBUX,U5Bk3GJ,C4B12GE,yBADF,gBAOI,SAAA,CALA,iBAAA,CACA,QAAA,CAGA,UAAA,CAFA,S5Bg3GF,CACF,C4B32GE,yBAVF,gBAWI,U5B82GF,CACF,C4B12GA,eACE,a5B62GF,C4B12GE,yBAJF,eAkBI,2DAAA,CACA,yBAAA,CAFA,iBAAA,CALA,WAAA,CAHA,eAAA,CAIA,oBAAA,CARA,iBAAA,CAEA,UAAA,CAQA,mBAAA,CATA,QAAA,CAKA,U5Bi3GF,CACF,C4Br2GA,gBAIE,eAAA,CAHA,QAAA,CACA,wB5By2GF,C4Br2GE,yBANF,gBAaI,qBrB/EY,CqB8EZ,wBAAA,CALA,iBAAA,CAEA,UAAA,CADA,QAAA,CAEA,W5B02GF,CACF,C6Bx7GA,OAYE,wBtBFa,CsBCb,etBwBO,CsB7BP,UtBLc,CsBAd,oBAAA,CAMA,kBAAA,CACA,oBAAA,CALA,iBAAA,CACA,eAAA,CAKA,wB7B27GF,C6Br7GE,aAGE,kBAAA,CAFA,U7Bw7GJ,C6Bl7GE,aACE,eAAA,CAEA,qB7Bm7GJ,C6B76GE,8BAEE,U7B+6GJ,C6B16GA,eACE,qB7B66GF,C6B15GA,0DACE,wB7By6GF,C6Br6GA,aACE,wB7Bw6GF,C6Br6GE,mBACE,e7Bu6GJ,C6Bl6GA,eAKE,qBtB7Ec,CsB2Ed,sBAAA,CACA,etB1CO,CsBuCP,a7Bw6GF,C6Bh6GA,eACE,wB7Bm6GF,C6Bh6GE,iBACE,UtBtFY,CsBuFZ,oB7Bk6GJ,C6B/5GE,qBAGE,cAAA,CAFA,e7Bk6GJ,C6B95GI,sDAEE,U7B+5GN,C6Bz5GA,kBAGE,qBtB1Gc,CsBwGd,a7B65GF,C8BngHA,OAME,YAAA,CAHA,OAAA,CAFA,iBAAA,CACA,OAAA,CAMA,UAAA,CAJA,Y9BwgHF,C8BjgHE,yBAXF,OAoBI,gCAAA,CALA,QAAA,CACA,MAAA,CAEA,eAAA,CANA,cAAA,CAEA,OAAA,CADA,K9BygHF,CACF,C8B7/GA,cAKE,sCAAA,CAFA,0BAAA,CAFA,2B9BkgHF,C8B3/GE,2BACE,eAAA,CAEA,gCAAA,CAAA,4BAAA,CAAA,wB9B4/GJ,C8Bv/GA,aAKE,evBjDc,CuBkDd,8CAAA,CAAA,sCAAA,CALA,iBAAA,CAEA,S9B2/GF,C8Br/GE,yBATF,aAWI,QAAA,CADA,OAAA,CAGA,sCAAA,CAAA,kCAAA,CAAA,8B9Bu/GF,CACF,C8Br/GE,yBAhBF,aAiBI,W9Bw/GF,CACF,C8Bp/GA,aAKE,wBvB9Da,CuB4Db,UvBrEc,CuBmEd,W9By/GF,C8Bn/GE,yBAPF,aAQI,iB9Bs/GF,CACF,C8Bl/GA,cAME,cAAA,CALA,oBAAA,CACA,WAAA,CAEA,kB9Bq/GF,C8Bj/GE,wCAEE,U9Bk/GJ,C8B7+GA,eAGE,iBAAA,CAFA,Q9Bi/GF,C8B3+GA,gBACE,W9B8+GF,C8B5+GE,yBAHF,gBAII,iB9B++GF,CACF,C8B3+GA,eACE,W9B8+GF,C8B5+GE,yBAHF,eAII,iB9B++GF,CACF,C+BhmHA,YACE,kB/BmmHF,C+BjmHE,eAGE,kBAAA,CACA,eAAA,CAHA,iB/BqmHJ,C+B/lHE,cAGE,kBAAA,CAFA,Q/BkmHJ,C+B7lHE,gBACE,UAAA,CAEA,iB/B8lHJ,C+BplHE,yBADF,aAEI,WAAA,CAEA,W/BulHF,CACF,CgC5nHA,YACE,UAAA,CAEA,kBhC8nHF,CgC3nHE,+BAOE,ezBuBK,CyB5BL,oBAAA,CAEA,iBAAA,CACA,gBhC6nHJ,CgCvnHE,cAIE,wBzBRiB,CyBKjB,UzBhBY,CyBiBZ,oBhC0nHJ,CgCtnHI,wCAIE,wBzBhBS,CyBcT,UhCwnHN,CgChnHA,oBAGE,wBAAA,CAFA,ehConHF,CiCtpHA,QACE,iBAAA,CACA,QAAA,CAEA,SjCwpHF,CiCtpHE,yBANF,QAQI,UAAA,CADA,OAAA,CAGA,UjCwpHF,CACF,CiCrpHA,gBAYE,+CAAA,CACA,4BAAA,CALA,WAAA,CAOA,UAAA,CAVA,eAAA,CAJA,iBAAA,CAEA,OAAA,CAOA,mBAAA,CARA,KAAA,CAKA,UjC2pHF,CiCjpHE,4CAEE,4BAAA,CAEA,SjCipHJ,CiC7oHA,eAKE,iB1BpCmB,C0BqCnB,iBAAA,CAHA,kBAAA,CAFA,UjCmpHF,CiC5oHE,yBARF,eASI,WjC+oHF,CACF,CiC3oHE,gBACE,ajC8oHJ,CiC5oHI,4CAEE,ajC6oHN,CkCjsHE,uBAJE,a3BSW,C2BRX,qBlCitHJ,CkC9sHE,SACE,oBAAA,CAGA,kBAAA,CAIA,SAAA,CAFA,oBlCwsHJ,CkCpsHI,8BAEE,UlCqsHN,CkChsHE,aAGE,kBAAA,CAFA,UlCmsHJ,CmC3tHA,QACE,YnC8tHF,CmC5tHE,yBAHF,QAII,anC+tHF,CACF,CmC7tHE,UACE,anC+tHJ,CmC5tHE,wBAGE,YAAA,CAFA,eAAA,CAGA,gBnC6tHJ,CmCztHE,oBAGE,WAAA,CAGA,WAAA,CACA,QAAA,CACA,SAAA,CAPA,iBAAA,CACA,UAAA,CAQA,iBAAA,CANA,SnC+tHJ,CmCvtHI,uBACE,oBAAA,CAEA,UnCwtHN,CmCttHM,2CACE,qBnCwtHR,CmCptHI,2BAYE,wB5BrCc,C4BmCd,WAAA,CACA,kBAAA,CAVA,aAAA,CAIA,WAAA,CAGA,oBAAA,CANA,eAAA,CAIA,SAAA,CAFA,UnC2tHN,CmChtHM,iCACE,YnCktHR,CoC3wHA,OAWE,wBAAA,CADA,e7B0BO,C6B9BP,U7BJc,C6BDd,oBAAA,CAMA,gBAAA,CAJA,cAAA,CACA,eAAA,CAIA,wBpC8wHF,CoCxwHE,aAGE,kBAAA,CAFA,UpC2wHJ,CoCpwHA,kBACE,wBpCuwHF,CoCnwHA,oBACE,qBpCswHF,CqC9xHA,YAKE,qBAAA,CADA,e9B4BO,C8B/BP,aAAA,CACA,qBrCmyHF,CsCxyHE,SACE,iBAAA,CACA,etC2yHJ,CsC9xHE,kCACE,etCqyHJ,CsC/xHE,mBACE,etCkyHJ,CsCxxHE,eAIE,cAAA,CAHA,eAAA,CACA,mBtC4xHJ,CsCvxHE,iBACE,YAAA,CACA,mBtCyxHJ,CsCtxHE,iBACE,etCwxHJ,CsClxHE,kBAME,qBAAA,CADA,wBAAA,CAJA,YAAA,CAEA,iBtCsxHJ,CuC90HA,OAGE,WAAA,CAFA,UvCk1HF,CuC50HI,mBACE,wBvC80HN,CuC10HE,oBAME,WAAA,CAJA,WAAA,CAEA,evC40HJ,CuCx0HI,yBARF,oBASI,iBvC40HJ,CACF,CuCx0HE,yCAGE,+DAKE,avC20HJ,CuCx0HE,aACE,4BvC00HJ,CuCv0HE,wBACE,+BvCy0HJ,CACF,CuCr0HE,UAGE,wBhCrCiB,CgCmCjB,UvCw0HJ,CuCn0HE,SACE,avCq0HJ,CuC/zHE,oBACE,evCk0HJ,CuC5zHE,mBACE,iBAAA,CACA,kBvC+zHJ,CuC5zHE,oBACE,oBvC8zHJ,CuC1zHE,iCACE,+BvC4zHJ,CuCxzHE,yCAhBF,gBAiBI,avC2zHF,CuCzzHE,sCAEE,kBvC2zHJ,CuCxzHE,+DAGE,iBvC0zHJ,CACF,CuCtzHE,yBACE,mBACE,QvCwzHJ,CuCrzHE,mBACE,SvCuzHJ,CACF,CuCjzHE,oBACE,oBAAA,CAGA,iBAAA,CAEA,qBAAA,CAHA,UvCqzHJ,CuC/yHE,kBACE,eAAA,CACA,oBvCizHJ,CwC36HA,MACE,exC86HF,CwC56HE,yBAHF,MAII,+BxC+6HF,CACF,CwC36HA,YASE,wBjCLmB,CiCEnB,UjCbc,CiCQd,aAAA,CAEA,kBAAA,CACA,iBAAA,CAGA,oBxC66HF,CwCz6HE,oCAEE,wBxC06HJ,CwCt6HE,sBAIE,wBjCdgB,CiCWhB,ajChBW,CiCiBX,exCy6HJ,CwCl6HA,YAIE,+BAAA,CACA,6BAAA,CAFA,8BAAA,CAFA,iBxCw6HF,CwCl6HE,yBAPF,YAQI,iBxCq6HF,CACF,CwCj6HA,cAGE,gBAAA,CAFA,YxCq6HF,CyCt9HA,SAOE,qBAAA,CADA,wBAAA,CALA,YAAA,CACA,YAAA,CAEA,iBzC09HF,CyCr9HE,yBATF,SAUI,azCw9HF,CACF,CyCt9HE,8BAEE,oBzCu9HJ,CyCl9HE,iBAGE,MAAA,CAFA,iBAAA,CAMA,eAAA,CALA,KAAA,CAGA,SzCo9HJ,CyC/8HI,wBACE,iBzCi9HN,CyC38HA,iBACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,wBAAA,CAAA,0BAAA,CAAA,qBAAA,CAAA,kBAAA,CACA,uBAAA,CAAA,8BAAA,CAAA,oBAAA,CASA,wBlCtCa,CkCkCb,UlC3Cc,CkC4Cd,kBAAA,CANA,sBAAA,CAOA,eAAA,CAJA,QAAA,CADA,ezCk9HF,CyCz8HE,yBAdF,iBAeI,iBzC48HF,CACF,CyC18HE,mBAME,UlC3DY,CkCsDZ,aAAA,CAGA,YAAA,CAGA,oBAAA,CAJA,UzC88HJ,CyCr8HA,gBACE,iBzCw8HF,CyCt8HE,oBACE,UzCw8HJ,CyCn8HA,eAGE,iBAAA,CACA,eAAA,CAHA,iBzCw8HF,CyCj8HA,gBAUE,kBlCnFa,CkC2Eb,UAAA,CAKA,UlCzFc,CkC0Fd,kBAAA,CALA,MAAA,CAEA,gBAAA,CAJA,iBzC08HF,CyC97HE,uBACE,iBzCg8HJ,CyC37HA,mBAGE,UAAA,CAFA,iBAAA,CACA,OzC+7HF,CyC57HE,wBAKE,eAAA,CAFA,eAAA,CAFA,QzCg8HJ,CyCz7HE,yBACE,UzC27HJ,C0ChjIA,gBACE,c1CmjIF,C0CjjIE,yBAHF,gBAII,c1CojIF,CACF,C0CjjII,6BACE,U1CmjIN,C0C5iIE,2BACE,a1C+iIJ,C0C7iII,kEAEE,a1C8iIN,C0CxiIA,wBAGE,anCnBa,CmCoBb,gBAAA,CAHA,Y1C6iIF,C0CxiIE,yBANF,wBAOI,kB1C2iIF,CACF,C0CviIA,6BACE,kB1C0iIF,C0CtiIA,sBAIE,eAAA,CAFA,kBAAA,CADA,e1C2iIF,C0CtiIE,wBACE,e1CwiIJ,C0CriIE,wBACE,a1CuiIJ,C0CliIA,uBAGE,eAAA,CAFA,e1CsiIF,C0CliIE,8BACE,UnC9CY,CmC+CZ,gB1CoiIJ,C0C/hIA,uBAIE,wBAAA,CAHA,iBAAA,CACA,W1CmiIF,C0C/hIE,2BACE,aAAA,CAEA,U1CgiIJ,C0C3hIA,yBACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,uBAAA,CAAA,8BAAA,CAAA,oBAAA,CAIA,WAAA,CAJA,sBAAA,CAEA,gB1C8hIF,C0C1hIE,2BACE,aAAA,CACA,kBAAA,CAMA,wBAAA,CANA,oBAAA,CAAA,gBAAA,CAAA,YAAA,CAGA,UAAA,CACA,WAAA,CAIA,8BAAA,CAAA,sBAAA,CANA,S1C+hIJ,C0CvhII,kEAEE,U1CwhIN,C0CrhII,gCACE,aAAA,CAGA,YAAA,CAFA,e1CwhIN,C0ClhIE,6BACE,a1CohIJ,C0C/gIA,yBACE,a1CkhIF,C0C7gIE,4BACE,Y1CghIJ,C0C7gIE,4CAIE,iBAAA,CAHA,eAAA,CACA,e1CghIJ,C0C3gIE,iCAKE,anCzIW,CmCqIX,oBAAA,CAEA,kB1C6gIJ,C0CxgIE,gCAGE,qBAAA,CAFA,U1C2gIJ,C0CngIE,4BACE,a1CsgIJ,C0CpgII,oEAEE,a1CqgIN,C0C//HA,0BACE,e1CkgIF,C0ChgIE,6BACE,a1CkgIJ,C0C//HE,4BACE,iB1CigIJ,C0C//HI,yBAHF,4BAII,gB1CkgIJ,CACF,C0C7/HA,wBASE,wBAAA,CARA,WAAA,CAMA,iBAAA,CAHA,oBAAA,CACA,YAAA,CAFA,W1CmgIF,C0C3/HE,2BAGE,kBAAA,CAFA,e1C8/HJ,C0Cz/HE,0BACE,QAAA,CACA,kB1C2/HJ,C0Ct/HA,qBACE,e1Cy/HF,C2C5sIE,wCAIE,apCOiB,CoCNjB,eAAA,CACA,wB3C+sIJ,C2C5sIE,UAGE,kBAAA,CAFA,a3C+sIJ,C2C3sII,yBALF,UAQI,kBAAA,CAFA,kB3C+sIJ,CACF,C2C1sIE,UACE,iB3C4sIJ,C2CzsIE,UACE,kB3C2sIJ,C2CzsII,yBAHF,UAII,mB3C4sIJ,CACF,C2CzsIE,UACE,c3C2sIJ,C2CzsII,yBAHF,UAII,kB3C4sIJ,CACF,C2CxsII,yBADF,SAEI,Y3C2sIJ,CACF,C2CxsIE,SACE,a3C0sIJ,C2CxsII,8BAEE,a3CysIN,C2CpsIE,oCAEE,U3CssIJ,C2CjsIA,cAGE,QAAA,CAFA,U3CqsIF,C2CjsIE,kCAIE,QAAA,CAFA,W3CosIJ,C2ChsII,yBANF,kCAOI,iB3CosIJ,CACF,C2ChsIE,oBACE,U3CksIJ,C2C9rIE,yCAGE,yGAKE,a3CisIJ,C2C9rIE,oBACE,4B3CgsIJ,C2C7rIE,+BACE,+B3C+rIJ,CACF,C2CxrIE,oBACE,UAAA,CAGA,WAAA,CACA,gBAAA,CAFA,U3C4rIJ,C2CtrIE,kBACE,apC7GiB,CoC8GjB,mBAAA,CACA,oBAAA,CACA,wB3CwrIJ,C2CrrIM,4BACE,U3CurIR,C2CnrII,gDAEE,a3CorIN,C2CzqIA,4BAJE,kB3CwrIF,C2CprIA,cAGE,eAAA,CAFA,cAAA,CAGA,gB3CgrIF,C2C7qIE,yCAPF,cAWI,kBAAA,CAFA,cAAA,CAGA,gBAAA,CAJA,U3CmrIF,CACF,C2C1qIE,qBACE,a3C6qIJ,C2C3qII,yCAHF,qBAII,Y3C8qIJ,CACF,C2CxqIE,oBACE,a3C2qIJ,C2CtqIA,oBAQE,wBpChLmB,CoC4KnB,UpCvLc,CoCwLd,cAAA,CACA,eAAA,CALA,eAAA,CACA,Y3C6qIF,C2CrqIE,2BACE,WAAA,CAEA,eAAA,CACA,kB3CsqIJ,C2CnqIE,yBAjBF,oBAoBI,epCrKK,CoCmKL,mB3CuqIF,CACF,C2CjqIA,uBACE,eAAA,CACA,gB3CoqIF,C2ClqIE,8BACE,WAAA,CAEA,eAAA,CACA,kB3CmqIJ,C2C1pIA,cAUE,qBpCjNqB,CoC6MrB,UpCnOc,CoCoOd,iBAAA,CACA,eAAA,CALA,YAAA,CACA,4BAAA,CAHA,iB3CmqIF,C2CxpIE,oBACE,W3C0pIJ,C2CvpIE,iBACE,kBAAA,CACA,iB3CypIJ,C2CtpIE,oBAGE,SAAA,CAFA,iBAAA,CACA,QAAA,CAGA,U3CupIJ,C2ClpIA,eACE,e3CqpIF,C2CnpIE,yBAHF,eAII,iB3CspIF,CACF,C2ClpIA,iCAOE,cAAA,CAHA,kBAAA,CACA,iBAAA,CAHA,iB3CwpIF,C2CjpIE,6CAGE,iBAAA,CAFA,iB3CqpIJ,C2ChpIE,qCACE,a3CmpIJ,C2CjpII,kGAEE,a3CopIN,C2C9oIA,aACE,iB3CipIF,C4C76II,4DAIE,wBrCOmB,CqCTnB,U5Cg7IN,C4Cz6IE,uEAKE,wBrCJgB,CqCChB,arCNW,CqCOX,e5C46IJ,C4Cx6II,yBAPF,uEAQI,4B5C46IJ,CACF,C4Cz6IE,mDAUE,wBrCxBiB,CqCqBjB,UrChCY,CqC2BZ,aAAA,CAEA,kBAAA,CACA,gBAAA,CAGA,oB5C06IJ,C4Ct6II,yBAZF,mDAaI,iB5C06IJ,CACF,C6Cn9IA,eACE,c7Cs9IF,C8Cp9IA,kBAQE,wBAAA,CADA,evC0BO,CuC7BP,UvCLc,CuCMd,eAAA,CAJA,YAAA,CACA,iB9C29IF,C8Cn9IE,oBACE,U9Cq9IJ,C8Cn9II,oDAEE,U9Co9IN,C8C/8IE,yBACE,kB9Ci9IJ,C8C58IA,sBACE,wB9C+8IF,C8C38IA,sBACE,wB9C88IF,C8C18IA,oBACE,wB9C68IF,C8Cz8IA,kBACE,wB9C48IF,C+Cz/IA,gBAEE,kBAAA,CADA,e/C6/IF,C+Cz/IE,yBALF,gBAMI,iB/C4/IF,CACF,C+Cz/IE,yCAEE,Y/C2/IJ,C+Cv/IE,yBACE,+BACE,gB/Cy/IJ,CACF,CgD3gJE,yBADF,gBAEI,mBhD+gJF,CACF,CgD7gJE,oBACE,oBhD+gJJ,CgD1gJA,sBAGE,+BAAA,CAEA,cAAA,CAJA,mBhD+gJF,CgDhgJA,iDAGE,6BAAA,CADA,8BAAA,CADA,4BhD2gJF,CgDtgJA,4BAIE,oBAAA,CAHA,QAAA,CACA,ShD0gJF,CgDrgJA,+BAKE,+BAAA,CAEA,cAAA,CANA,aAAA,CAEA,sBhDygJF,CgDlgJA,6BACE,gBAAA,CAEA,qBhDogJF,CgDhgJA,sBACE,2BhDmgJF,CgDjgJE,0BAEE,gBAAA,CADA,eAAA,CAGA,qBhDkgJJ,CgDr/IA,yBACE,ehDw/IF,CiDtkJA,QACE,iBjDykJF,CiDtkJA,MACE,ejDykJF,CiDtkJA,OACE,gBjDykJF,CiDrkJA,QACE,kBjDwkJF,CiDpkJA,OACE,UjDukJF,CiDpkJA,QACE,WjDukJF,CiDpkJA,KACE,UjDukJF,CiDnkJA,qBAEE,WAAA,CAEA,ajDqkJF,CiDlkJA,UACE,UjDqkJF,CiDlkJA,KACE,MjDqkJF,CkD9mJA,aAEE,EAGE,yBAAA,CAGA,qBAAA,CADA,iCAAA,CAAA,yBAAA,CAGA,qBAAA,CACA,wBAAA,CACA,2BAAA,CANA,0BAAA,CAGA,oBlDmnJF,CkD9mJA,OAVE,oBAAA,CADA,gClDqoJF,CkD1nJA,KASE,yBAAA,CACA,wBAAA,CAJA,6BAAA,CAFA,yBAAA,CAFA,kBAAA,CACA,mBAAA,CAFA,oBAAA,CAIA,4BlDqnJF,CkD9mJA,YAAiB,oBlDinJjB,CkDhnJA,GAAK,wBlDmnJL,CkDlnJA,GAAK,wBlDqnJL,CkDpnJA,GAAK,wBlDunJL,CkDtnJA,GAAK,wBlDynJL,CkDxnJA,IAAM,kClD2nJN,CkD1nJA,MAAS,2BlD6nJT,CkD5nJA,MAAQ,qBlD+nJR,CkD9nJA,4CAAqF,oBAAA,CAAlC,8BlDkoJnD,CkDjoJA,MAAQ,oBAAA,CAAwB,yBlDqoJhC,CkDpoJA,GAAmD,yBlDwoJnD,CkDvoJA,MADK,sClD2oJL,CkDzoJA,MAAS,gClD4oJT,CkD3oJA,GAAK,iClD8oJL,CkD3oJA,0QAG+I,sBlD8oJ/I,CkD3oJA,YAAc,uBlD8oJd,CkD3oJA,kBAA6E,oCAAA,CAAlC,8BAAA,CAA0E,4BAAA,CAAjG,mBlDipJpB,CkDhpJA,kCAAoC,UlDmpJpC,CkDlpJA,aAAe,uBlDqpJf,CkDppJA,iBAAmB,wBlDupJnB,CkDtpJA,SAAW,elDypJX,CkDxpJA,6BAAgC,kBAAA,CAAsB,mBlD4pJtD,CkDzpJA,KAAO,WlD4pJP,CkD3pJA,UAAY,QlD8pJZ,CkD3pJA,QAAU,SlD8pJV,CkD1pJA,gCADgB,kBlDoqJhB,CkDnqJA,kBAAoB,+BAAA,CAAsC,YAAA,CAAqC,gClDmqJ/F,CkDhqJA,YAAc,sBlDmqJd,CkDlqJA,YAAc,mBlDqqJd,CkDlqJA,aAAe,oBlDqqJf,CkDpqJA,6BAAgC,+BlDuqJhC,CkDpqJA,yBAA6B,YlDuqJ7B,CkDtqJA,mBAAqB,UlDyqJrB,CACF", "file": "styles.css", "sourcesContent": ["// základní nastavení formul<PERSON><PERSON>\r\n\r\n// sk<PERSON><PERSON><PERSON> formul<PERSON>ř<PERSON> a prvků v tiskové verzi\r\nform,\nbutton,\ninput[type='submit'] {\n  @media print {\n    display: none;\r\n  }\n}\r\n\r\n// základní vzhled input polí\r\ninput,\ntextarea,\nselect {\n  // ošetření na nižš<PERSON>, k<PERSON><PERSON> je zadán size\r\n  width: 100%;\r\n  max-width: 100%;\r\n  margin-bottom: 5px;\r\n  padding: 9px 10px;\r\n\n  font-size: 16px;\r\n\n  border: 1px solid $color_main;\r\n  border-radius: $radius;\r\n\r\n  &:focus {\n    border: 1px solid $color_main_dark;\r\n  }\r\n\r\n  // pole v pořádku\r\n  &.is--success {\n    color: $color_success;\r\n\n    border: 1px solid $color_success;\r\n  }\r\n\r\n  // chybně vyplněné pole\r\n  &.is--danger {\n    color: $color_danger;\r\n\n    border: 1px solid $color_danger;\r\n  }\n}\r\n\r\n// specifické definice pro input\r\n.input--75 {\n  width: 74%;\r\n}\r\n\r\n.input--50 {\n  width: 49%;\r\n}\r\n\r\n.input--25 {\n  width: 24%;\r\n  max-width: 100px;\r\n}\r\n\r\n.input--postcode {\n  max-width: 250px;\r\n  margin-left: 10px;\r\n}\r\n\r\n// specifické definice pro textarea\r\n.textarea--75 {\n  @media (min-width: $mqxxs) {\n    width: 74%;\r\n  }\n}\r\n\r\n.textarea--50 {\n  @media (min-width: $mqxxs) {\n    width: 49%;\r\n  }\n}\r\n\r\n// popisek pole\r\nlabel {\n  display: inline-block;\r\n\n  padding-bottom: 5px;\r\n}\r\n\r\n// ošetření submit inputu, pokud je použit\r\nbutton,\ninput[type='submit'] {\n  width: auto;\r\n\n  border: none;\r\n  border-radius: $radius_button;\r\n\n  cursor: pointer;\r\n}\r\n\r\n// ošetření chekboxu a radio buttonu\r\ninput[type='checkbox'],\ninput[type='radio'] {\n  width: auto;\r\n  margin-right: 5px;\r\n}\r\n\r\n// formuláře jsou uzavřeny ve fieldsetu\r\nfieldset {\n  margin: 16px 0;\r\n  padding: 0 16px;\r\n\n  border: 1px solid $color_main;\r\n  border-radius: $radius;\r\n\r\n  legend {\n    padding: 6px 10px 7px 10px;\r\n\n    color: $color_white;\r\n\n    border-radius: $radius;\r\n    background-color: $color_main;\r\n  }\r\n\r\n  // ošetření pro nižší rozlišení\r\n  @media (max-width: $mqsm) {\n    max-width: 100%;\r\n\r\n    input,\n    textarea {\n      max-width: 100%;\r\n    }\n  }\n}\r\n\r\n// poloviční velikost fieldsetu\r\n.fieldset--half {\n  @media (min-width: $mqmd) {\n    max-width: 50%;\r\n  }\n}\r\n\r\n// hvězdičkové hodnocení\r\n.star-rating {\n  > fieldset {\n    display: inline-block;\r\n\n    margin: 0;\r\n    padding: 0;\r\n\n    border: none;\r\n\r\n    &:not(:checked) {\n      > input {\n        position: absolute;\r\n        right: 0;\r\n\n        opacity: 0;\r\n      }\r\n\r\n      > label {\n        float: right;\r\n        overflow: hidden;\r\n\n        width: 30px;\r\n        height: 30px;\r\n\n        color: $color_gray_light;\r\n        font-size: 30px;\r\n        line-height: 30px;\r\n        white-space: nowrap;\r\n\n        cursor: pointer;\r\n\r\n        &:before {\n          content: '★   ';\r\n        }\r\n\r\n        &:hover,\n        &:hover ~ label {\n          color: $color_secondary;\r\n\r\n          &:before {\n            content: '★   ';\r\n          }\n        }\n      }\n    }\r\n\r\n    > input:checked {\n      & ~ label {\n        color: $color_secondary;\r\n\r\n        &:before {\n          content: '★   ';\r\n        }\n      }\n    }\r\n\r\n    > label:active {\n      position: relative;\r\n      top: 2px;\r\n    }\n  }\n}\r\n\r\n// ošetření google reCaptcha\r\n.g-recaptcha {\n  @media (max-width: $mqxxs) {\n    width: 1px;\r\n\n    transform: scale(0.74);\r\n  }\n}\r\n", "@charset \"UTF-8\";\n/*! normalize.css v8.0.0 | MIT License | github.com/necolas/normalize.css */\n/* Document\n   ========================================================================== */\n/**\n * 1. Correct the line height in all browsers.\n * 2. Prevent adjustments of font size after orientation changes in iOS.\n */\nhtml {\n  line-height: 1.15; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n}\n\n/* Sections\n   ========================================================================== */\n/**\n * Remove the margin in all browsers.\n */\nbody {\n  margin: 0;\n}\n\n/**\n * Correct the font size and margin on `h1` elements within `section` and\n * `article` contexts in Chrome, Firefox, and Safari.\n */\nh1 {\n  font-size: 2em;\n  margin: 0.67em 0;\n}\n\n/* Grouping content\n   ========================================================================== */\n/**\n * 1. Add the correct box sizing in Firefox.\n * 2. Show the overflow in Edge and IE.\n */\nhr {\n  box-sizing: content-box; /* 1 */\n  height: 0; /* 1 */\n  overflow: visible; /* 2 */\n}\n\n/**\n * 1. Correct the inheritance and scaling of font size in all browsers.\n * 2. Correct the odd `em` font sizing in all browsers.\n */\npre {\n  font-family: monospace, monospace; /* 1 */\n  font-size: 1em; /* 2 */\n}\n\n/* Text-level semantics\n   ========================================================================== */\n/**\n * Remove the gray background on active links in IE 10.\n */\na {\n  background-color: transparent;\n}\n\n/**\n * 1. Remove the bottom border in Chrome 57-\n * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\n */\nabbr[title] {\n  border-bottom: none; /* 1 */\n  text-decoration: underline; /* 2 */\n  text-decoration: underline dotted; /* 2 */\n}\n\n/**\n * Add the correct font weight in Chrome, Edge, and Safari.\n */\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/**\n * 1. Correct the inheritance and scaling of font size in all browsers.\n * 2. Correct the odd `em` font sizing in all browsers.\n */\ncode,\nkbd,\nsamp {\n  font-family: monospace, monospace; /* 1 */\n  font-size: 1em; /* 2 */\n}\n\n/**\n * Add the correct font size in all browsers.\n */\nsmall {\n  font-size: 80%;\n}\n\n/**\n * Prevent `sub` and `sup` elements from affecting the line height in\n * all browsers.\n */\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/* Embedded content\n   ========================================================================== */\n/**\n * Remove the border on images inside links in IE 10.\n */\nimg {\n  border-style: none;\n}\n\n/* Forms\n   ========================================================================== */\n/**\n * 1. Change the font styles in all browsers.\n * 2. Remove the margin in Firefox and Safari.\n */\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  line-height: 1.15; /* 1 */\n  margin: 0; /* 2 */\n}\n\n/**\n * Show the overflow in IE.\n * 1. Show the overflow in Edge.\n */\nbutton,\ninput { /* 1 */\n  overflow: visible;\n}\n\n/**\n * Remove the inheritance of text transform in Edge, Firefox, and IE.\n * 1. Remove the inheritance of text transform in Firefox.\n */\nbutton,\nselect { /* 1 */\n  text-transform: none;\n}\n\n/**\n * Correct the inability to style clickable types in iOS and Safari.\n */\nbutton,\n[type=button],\n[type=reset],\n[type=submit] {\n  -webkit-appearance: button;\n}\n\n/**\n * Remove the inner border and padding in Firefox.\n */\nbutton::-moz-focus-inner,\n[type=button]::-moz-focus-inner,\n[type=reset]::-moz-focus-inner,\n[type=submit]::-moz-focus-inner {\n  border-style: none;\n  padding: 0;\n}\n\n/**\n * Restore the focus styles unset by the previous rule.\n */\nbutton:-moz-focusring,\n[type=button]:-moz-focusring,\n[type=reset]:-moz-focusring,\n[type=submit]:-moz-focusring {\n  outline: 1px dotted ButtonText;\n}\n\n/**\n * Correct the padding in Firefox.\n */\nfieldset {\n  padding: 0.35em 0.75em 0.625em;\n}\n\n/**\n * 1. Correct the text wrapping in Edge and IE.\n * 2. Correct the color inheritance from `fieldset` elements in IE.\n * 3. Remove the padding so developers are not caught out when they zero out\n *    `fieldset` elements in all browsers.\n */\nlegend {\n  box-sizing: border-box; /* 1 */\n  color: inherit; /* 2 */\n  display: table; /* 1 */\n  max-width: 100%; /* 1 */\n  padding: 0; /* 3 */\n  white-space: normal; /* 1 */\n}\n\n/**\n * Add the correct vertical alignment in Chrome, Firefox, and Opera.\n */\nprogress {\n  vertical-align: baseline;\n}\n\n/**\n * Remove the default vertical scrollbar in IE 10+.\n */\ntextarea {\n  overflow: auto;\n}\n\n/**\n * 1. Add the correct box sizing in IE 10.\n * 2. Remove the padding in IE 10.\n */\n[type=checkbox],\n[type=radio] {\n  box-sizing: border-box; /* 1 */\n  padding: 0; /* 2 */\n}\n\n/**\n * Correct the cursor style of increment and decrement buttons in Chrome.\n */\n[type=number]::-webkit-inner-spin-button,\n[type=number]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/**\n * 1. Correct the odd appearance in Chrome and Safari.\n * 2. Correct the outline style in Safari.\n */\n[type=search] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/**\n * Remove the inner padding in Chrome and Safari on macOS.\n */\n[type=search]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/**\n * 1. Correct the inability to style clickable types in iOS and Safari.\n * 2. Change font properties to `inherit` in Safari.\n */\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/* Interactive\n   ========================================================================== */\n/*\n * Add the correct display in Edge, IE 10+, and Firefox.\n */\ndetails {\n  display: block;\n}\n\n/*\n * Add the correct display in all browsers.\n */\nsummary {\n  display: list-item;\n}\n\n/* Misc\n   ========================================================================== */\n/**\n * Add the correct display in IE 10+.\n */\ntemplate {\n  display: none;\n}\n\n/**\n * Add the correct display in IE 10.\n */\n[hidden] {\n  display: none;\n}\n\n.container {\n  margin-right: auto;\n  margin-left: auto;\n  padding-left: 10px;\n  padding-right: 10px;\n}\n\n@media (min-width: 768px) {\n  .container {\n    width: 740px;\n  }\n}\n@media (min-width: 992px) {\n  .container {\n    width: 960px;\n  }\n}\n@media (min-width: 1200px) {\n  .container {\n    width: 1160px;\n  }\n}\n.container-fluid {\n  margin-right: auto;\n  margin-left: auto;\n  padding-left: 10px;\n  padding-right: 10px;\n}\n\n.row {\n  margin-left: -10px;\n  margin-right: -10px;\n}\n\n.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {\n  position: relative;\n  min-height: 1px;\n  padding-left: 10px;\n  padding-right: 10px;\n}\n\n.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {\n  float: left;\n}\n\n.col-xs-12 {\n  width: 100%;\n}\n\n.col-xs-11 {\n  width: 91.66666667%;\n}\n\n.col-xs-10 {\n  width: 83.33333333%;\n}\n\n.col-xs-9 {\n  width: 75%;\n}\n\n.col-xs-8 {\n  width: 66.66666667%;\n}\n\n.col-xs-7 {\n  width: 58.33333333%;\n}\n\n.col-xs-6 {\n  width: 50%;\n}\n\n.col-xs-5 {\n  width: 41.66666667%;\n}\n\n.col-xs-4 {\n  width: 33.33333333%;\n}\n\n.col-xs-3 {\n  width: 25%;\n}\n\n.col-xs-2 {\n  width: 16.66666667%;\n}\n\n.col-xs-1 {\n  width: 8.33333333%;\n}\n\n.col-xs-pull-12 {\n  right: 100%;\n}\n\n.col-xs-pull-11 {\n  right: 91.66666667%;\n}\n\n.col-xs-pull-10 {\n  right: 83.33333333%;\n}\n\n.col-xs-pull-9 {\n  right: 75%;\n}\n\n.col-xs-pull-8 {\n  right: 66.66666667%;\n}\n\n.col-xs-pull-7 {\n  right: 58.33333333%;\n}\n\n.col-xs-pull-6 {\n  right: 50%;\n}\n\n.col-xs-pull-5 {\n  right: 41.66666667%;\n}\n\n.col-xs-pull-4 {\n  right: 33.33333333%;\n}\n\n.col-xs-pull-3 {\n  right: 25%;\n}\n\n.col-xs-pull-2 {\n  right: 16.66666667%;\n}\n\n.col-xs-pull-1 {\n  right: 8.33333333%;\n}\n\n.col-xs-pull-0 {\n  right: auto;\n}\n\n.col-xs-push-12 {\n  left: 100%;\n}\n\n.col-xs-push-11 {\n  left: 91.66666667%;\n}\n\n.col-xs-push-10 {\n  left: 83.33333333%;\n}\n\n.col-xs-push-9 {\n  left: 75%;\n}\n\n.col-xs-push-8 {\n  left: 66.66666667%;\n}\n\n.col-xs-push-7 {\n  left: 58.33333333%;\n}\n\n.col-xs-push-6 {\n  left: 50%;\n}\n\n.col-xs-push-5 {\n  left: 41.66666667%;\n}\n\n.col-xs-push-4 {\n  left: 33.33333333%;\n}\n\n.col-xs-push-3 {\n  left: 25%;\n}\n\n.col-xs-push-2 {\n  left: 16.66666667%;\n}\n\n.col-xs-push-1 {\n  left: 8.33333333%;\n}\n\n.col-xs-push-0 {\n  left: auto;\n}\n\n.col-xs-offset-12 {\n  margin-left: 100%;\n}\n\n.col-xs-offset-11 {\n  margin-left: 91.66666667%;\n}\n\n.col-xs-offset-10 {\n  margin-left: 83.33333333%;\n}\n\n.col-xs-offset-9 {\n  margin-left: 75%;\n}\n\n.col-xs-offset-8 {\n  margin-left: 66.66666667%;\n}\n\n.col-xs-offset-7 {\n  margin-left: 58.33333333%;\n}\n\n.col-xs-offset-6 {\n  margin-left: 50%;\n}\n\n.col-xs-offset-5 {\n  margin-left: 41.66666667%;\n}\n\n.col-xs-offset-4 {\n  margin-left: 33.33333333%;\n}\n\n.col-xs-offset-3 {\n  margin-left: 25%;\n}\n\n.col-xs-offset-2 {\n  margin-left: 16.66666667%;\n}\n\n.col-xs-offset-1 {\n  margin-left: 8.33333333%;\n}\n\n.col-xs-offset-0 {\n  margin-left: 0%;\n}\n\n@media (min-width: 768px) {\n  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {\n    float: left;\n  }\n  .col-sm-12 {\n    width: 100%;\n  }\n  .col-sm-11 {\n    width: 91.66666667%;\n  }\n  .col-sm-10 {\n    width: 83.33333333%;\n  }\n  .col-sm-9 {\n    width: 75%;\n  }\n  .col-sm-8 {\n    width: 66.66666667%;\n  }\n  .col-sm-7 {\n    width: 58.33333333%;\n  }\n  .col-sm-6 {\n    width: 50%;\n  }\n  .col-sm-5 {\n    width: 41.66666667%;\n  }\n  .col-sm-4 {\n    width: 33.33333333%;\n  }\n  .col-sm-3 {\n    width: 25%;\n  }\n  .col-sm-2 {\n    width: 16.66666667%;\n  }\n  .col-sm-1 {\n    width: 8.33333333%;\n  }\n  .col-sm-pull-12 {\n    right: 100%;\n  }\n  .col-sm-pull-11 {\n    right: 91.66666667%;\n  }\n  .col-sm-pull-10 {\n    right: 83.33333333%;\n  }\n  .col-sm-pull-9 {\n    right: 75%;\n  }\n  .col-sm-pull-8 {\n    right: 66.66666667%;\n  }\n  .col-sm-pull-7 {\n    right: 58.33333333%;\n  }\n  .col-sm-pull-6 {\n    right: 50%;\n  }\n  .col-sm-pull-5 {\n    right: 41.66666667%;\n  }\n  .col-sm-pull-4 {\n    right: 33.33333333%;\n  }\n  .col-sm-pull-3 {\n    right: 25%;\n  }\n  .col-sm-pull-2 {\n    right: 16.66666667%;\n  }\n  .col-sm-pull-1 {\n    right: 8.33333333%;\n  }\n  .col-sm-pull-0 {\n    right: auto;\n  }\n  .col-sm-push-12 {\n    left: 100%;\n  }\n  .col-sm-push-11 {\n    left: 91.66666667%;\n  }\n  .col-sm-push-10 {\n    left: 83.33333333%;\n  }\n  .col-sm-push-9 {\n    left: 75%;\n  }\n  .col-sm-push-8 {\n    left: 66.66666667%;\n  }\n  .col-sm-push-7 {\n    left: 58.33333333%;\n  }\n  .col-sm-push-6 {\n    left: 50%;\n  }\n  .col-sm-push-5 {\n    left: 41.66666667%;\n  }\n  .col-sm-push-4 {\n    left: 33.33333333%;\n  }\n  .col-sm-push-3 {\n    left: 25%;\n  }\n  .col-sm-push-2 {\n    left: 16.66666667%;\n  }\n  .col-sm-push-1 {\n    left: 8.33333333%;\n  }\n  .col-sm-push-0 {\n    left: auto;\n  }\n  .col-sm-offset-12 {\n    margin-left: 100%;\n  }\n  .col-sm-offset-11 {\n    margin-left: 91.66666667%;\n  }\n  .col-sm-offset-10 {\n    margin-left: 83.33333333%;\n  }\n  .col-sm-offset-9 {\n    margin-left: 75%;\n  }\n  .col-sm-offset-8 {\n    margin-left: 66.66666667%;\n  }\n  .col-sm-offset-7 {\n    margin-left: 58.33333333%;\n  }\n  .col-sm-offset-6 {\n    margin-left: 50%;\n  }\n  .col-sm-offset-5 {\n    margin-left: 41.66666667%;\n  }\n  .col-sm-offset-4 {\n    margin-left: 33.33333333%;\n  }\n  .col-sm-offset-3 {\n    margin-left: 25%;\n  }\n  .col-sm-offset-2 {\n    margin-left: 16.66666667%;\n  }\n  .col-sm-offset-1 {\n    margin-left: 8.33333333%;\n  }\n  .col-sm-offset-0 {\n    margin-left: 0%;\n  }\n}\n@media (min-width: 992px) {\n  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {\n    float: left;\n  }\n  .col-md-12 {\n    width: 100%;\n  }\n  .col-md-11 {\n    width: 91.66666667%;\n  }\n  .col-md-10 {\n    width: 83.33333333%;\n  }\n  .col-md-9 {\n    width: 75%;\n  }\n  .col-md-8 {\n    width: 66.66666667%;\n  }\n  .col-md-7 {\n    width: 58.33333333%;\n  }\n  .col-md-6 {\n    width: 50%;\n  }\n  .col-md-5 {\n    width: 41.66666667%;\n  }\n  .col-md-4 {\n    width: 33.33333333%;\n  }\n  .col-md-3 {\n    width: 25%;\n  }\n  .col-md-2 {\n    width: 16.66666667%;\n  }\n  .col-md-1 {\n    width: 8.33333333%;\n  }\n  .col-md-pull-12 {\n    right: 100%;\n  }\n  .col-md-pull-11 {\n    right: 91.66666667%;\n  }\n  .col-md-pull-10 {\n    right: 83.33333333%;\n  }\n  .col-md-pull-9 {\n    right: 75%;\n  }\n  .col-md-pull-8 {\n    right: 66.66666667%;\n  }\n  .col-md-pull-7 {\n    right: 58.33333333%;\n  }\n  .col-md-pull-6 {\n    right: 50%;\n  }\n  .col-md-pull-5 {\n    right: 41.66666667%;\n  }\n  .col-md-pull-4 {\n    right: 33.33333333%;\n  }\n  .col-md-pull-3 {\n    right: 25%;\n  }\n  .col-md-pull-2 {\n    right: 16.66666667%;\n  }\n  .col-md-pull-1 {\n    right: 8.33333333%;\n  }\n  .col-md-pull-0 {\n    right: auto;\n  }\n  .col-md-push-12 {\n    left: 100%;\n  }\n  .col-md-push-11 {\n    left: 91.66666667%;\n  }\n  .col-md-push-10 {\n    left: 83.33333333%;\n  }\n  .col-md-push-9 {\n    left: 75%;\n  }\n  .col-md-push-8 {\n    left: 66.66666667%;\n  }\n  .col-md-push-7 {\n    left: 58.33333333%;\n  }\n  .col-md-push-6 {\n    left: 50%;\n  }\n  .col-md-push-5 {\n    left: 41.66666667%;\n  }\n  .col-md-push-4 {\n    left: 33.33333333%;\n  }\n  .col-md-push-3 {\n    left: 25%;\n  }\n  .col-md-push-2 {\n    left: 16.66666667%;\n  }\n  .col-md-push-1 {\n    left: 8.33333333%;\n  }\n  .col-md-push-0 {\n    left: auto;\n  }\n  .col-md-offset-12 {\n    margin-left: 100%;\n  }\n  .col-md-offset-11 {\n    margin-left: 91.66666667%;\n  }\n  .col-md-offset-10 {\n    margin-left: 83.33333333%;\n  }\n  .col-md-offset-9 {\n    margin-left: 75%;\n  }\n  .col-md-offset-8 {\n    margin-left: 66.66666667%;\n  }\n  .col-md-offset-7 {\n    margin-left: 58.33333333%;\n  }\n  .col-md-offset-6 {\n    margin-left: 50%;\n  }\n  .col-md-offset-5 {\n    margin-left: 41.66666667%;\n  }\n  .col-md-offset-4 {\n    margin-left: 33.33333333%;\n  }\n  .col-md-offset-3 {\n    margin-left: 25%;\n  }\n  .col-md-offset-2 {\n    margin-left: 16.66666667%;\n  }\n  .col-md-offset-1 {\n    margin-left: 8.33333333%;\n  }\n  .col-md-offset-0 {\n    margin-left: 0%;\n  }\n}\n@media (min-width: 1200px) {\n  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {\n    float: left;\n  }\n  .col-lg-12 {\n    width: 100%;\n  }\n  .col-lg-11 {\n    width: 91.66666667%;\n  }\n  .col-lg-10 {\n    width: 83.33333333%;\n  }\n  .col-lg-9 {\n    width: 75%;\n  }\n  .col-lg-8 {\n    width: 66.66666667%;\n  }\n  .col-lg-7 {\n    width: 58.33333333%;\n  }\n  .col-lg-6 {\n    width: 50%;\n  }\n  .col-lg-5 {\n    width: 41.66666667%;\n  }\n  .col-lg-4 {\n    width: 33.33333333%;\n  }\n  .col-lg-3 {\n    width: 25%;\n  }\n  .col-lg-2 {\n    width: 16.66666667%;\n  }\n  .col-lg-1 {\n    width: 8.33333333%;\n  }\n  .col-lg-pull-12 {\n    right: 100%;\n  }\n  .col-lg-pull-11 {\n    right: 91.66666667%;\n  }\n  .col-lg-pull-10 {\n    right: 83.33333333%;\n  }\n  .col-lg-pull-9 {\n    right: 75%;\n  }\n  .col-lg-pull-8 {\n    right: 66.66666667%;\n  }\n  .col-lg-pull-7 {\n    right: 58.33333333%;\n  }\n  .col-lg-pull-6 {\n    right: 50%;\n  }\n  .col-lg-pull-5 {\n    right: 41.66666667%;\n  }\n  .col-lg-pull-4 {\n    right: 33.33333333%;\n  }\n  .col-lg-pull-3 {\n    right: 25%;\n  }\n  .col-lg-pull-2 {\n    right: 16.66666667%;\n  }\n  .col-lg-pull-1 {\n    right: 8.33333333%;\n  }\n  .col-lg-pull-0 {\n    right: auto;\n  }\n  .col-lg-push-12 {\n    left: 100%;\n  }\n  .col-lg-push-11 {\n    left: 91.66666667%;\n  }\n  .col-lg-push-10 {\n    left: 83.33333333%;\n  }\n  .col-lg-push-9 {\n    left: 75%;\n  }\n  .col-lg-push-8 {\n    left: 66.66666667%;\n  }\n  .col-lg-push-7 {\n    left: 58.33333333%;\n  }\n  .col-lg-push-6 {\n    left: 50%;\n  }\n  .col-lg-push-5 {\n    left: 41.66666667%;\n  }\n  .col-lg-push-4 {\n    left: 33.33333333%;\n  }\n  .col-lg-push-3 {\n    left: 25%;\n  }\n  .col-lg-push-2 {\n    left: 16.66666667%;\n  }\n  .col-lg-push-1 {\n    left: 8.33333333%;\n  }\n  .col-lg-push-0 {\n    left: auto;\n  }\n  .col-lg-offset-12 {\n    margin-left: 100%;\n  }\n  .col-lg-offset-11 {\n    margin-left: 91.66666667%;\n  }\n  .col-lg-offset-10 {\n    margin-left: 83.33333333%;\n  }\n  .col-lg-offset-9 {\n    margin-left: 75%;\n  }\n  .col-lg-offset-8 {\n    margin-left: 66.66666667%;\n  }\n  .col-lg-offset-7 {\n    margin-left: 58.33333333%;\n  }\n  .col-lg-offset-6 {\n    margin-left: 50%;\n  }\n  .col-lg-offset-5 {\n    margin-left: 41.66666667%;\n  }\n  .col-lg-offset-4 {\n    margin-left: 33.33333333%;\n  }\n  .col-lg-offset-3 {\n    margin-left: 25%;\n  }\n  .col-lg-offset-2 {\n    margin-left: 16.66666667%;\n  }\n  .col-lg-offset-1 {\n    margin-left: 8.33333333%;\n  }\n  .col-lg-offset-0 {\n    margin-left: 0%;\n  }\n}\n/* Magnific Popup CSS */\n.mfp-bg {\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1042;\n  overflow: hidden;\n  position: fixed;\n  background: #0b0b0b;\n  opacity: 0.8;\n}\n\n.mfp-wrap {\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1043;\n  position: fixed;\n  outline: none !important;\n  -webkit-backface-visibility: hidden;\n}\n\n.mfp-container {\n  text-align: center;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0;\n  top: 0;\n  padding: 0 8px;\n  box-sizing: border-box;\n}\n\n.mfp-container:before {\n  content: \"\";\n  display: inline-block;\n  height: 100%;\n  vertical-align: middle;\n}\n\n.mfp-align-top .mfp-container:before {\n  display: none;\n}\n\n.mfp-content {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  margin: 0 auto;\n  text-align: left;\n  z-index: 1045;\n}\n\n.mfp-inline-holder .mfp-content,\n.mfp-ajax-holder .mfp-content {\n  width: 100%;\n  cursor: auto;\n}\n\n.mfp-ajax-cur {\n  cursor: progress;\n}\n\n.mfp-zoom-out-cur, .mfp-zoom-out-cur .mfp-image-holder .mfp-close {\n  cursor: -moz-zoom-out;\n  cursor: -webkit-zoom-out;\n  cursor: zoom-out;\n}\n\n.mfp-zoom {\n  cursor: pointer;\n  cursor: -webkit-zoom-in;\n  cursor: -moz-zoom-in;\n  cursor: zoom-in;\n}\n\n.mfp-auto-cursor .mfp-content {\n  cursor: auto;\n}\n\n.mfp-close,\n.mfp-arrow,\n.mfp-preloader,\n.mfp-counter {\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  user-select: none;\n}\n\n.mfp-loading.mfp-figure {\n  display: none;\n}\n\n.mfp-hide {\n  display: none !important;\n}\n\n.mfp-preloader {\n  color: #CCC;\n  position: absolute;\n  top: 50%;\n  width: auto;\n  text-align: center;\n  margin-top: -0.8em;\n  left: 8px;\n  right: 8px;\n  z-index: 1044;\n}\n\n.mfp-preloader a {\n  color: #CCC;\n}\n\n.mfp-preloader a:hover {\n  color: #FFF;\n}\n\n.mfp-s-ready .mfp-preloader {\n  display: none;\n}\n\n.mfp-s-error .mfp-content {\n  display: none;\n}\n\nbutton.mfp-close,\nbutton.mfp-arrow {\n  overflow: visible;\n  cursor: pointer;\n  background: transparent;\n  border: 0;\n  -webkit-appearance: none;\n  display: block;\n  outline: none;\n  padding: 0;\n  z-index: 1046;\n  box-shadow: none;\n  touch-action: manipulation;\n}\n\nbutton::-moz-focus-inner {\n  padding: 0;\n  border: 0;\n}\n\n.mfp-close {\n  width: 44px;\n  height: 44px;\n  line-height: 44px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  text-decoration: none;\n  text-align: center;\n  opacity: 0.65;\n  padding: 0 0 18px 10px;\n  color: #FFF;\n  font-style: normal;\n  font-size: 28px;\n  font-family: Arial, Baskerville, monospace;\n}\n\n.mfp-close:hover,\n.mfp-close:focus {\n  opacity: 1;\n}\n\n.mfp-close:active {\n  top: 1px;\n}\n\n.mfp-close-btn-in .mfp-close {\n  color: #333;\n}\n\n.mfp-image-holder .mfp-close,\n.mfp-iframe-holder .mfp-close {\n  color: #FFF;\n  right: -6px;\n  text-align: right;\n  padding-right: 6px;\n  width: 100%;\n}\n\n.mfp-counter {\n  position: absolute;\n  top: 0;\n  right: 0;\n  color: #CCC;\n  font-size: 12px;\n  line-height: 18px;\n  white-space: nowrap;\n}\n\n.mfp-arrow {\n  position: absolute;\n  opacity: 0.65;\n  margin: 0;\n  top: 50%;\n  margin-top: -55px;\n  padding: 0;\n  width: 90px;\n  height: 110px;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.mfp-arrow:active {\n  margin-top: -54px;\n}\n\n.mfp-arrow:hover,\n.mfp-arrow:focus {\n  opacity: 1;\n}\n\n.mfp-arrow:before,\n.mfp-arrow:after {\n  content: \"\";\n  display: block;\n  width: 0;\n  height: 0;\n  position: absolute;\n  left: 0;\n  top: 0;\n  margin-top: 35px;\n  margin-left: 35px;\n  border: medium inset transparent;\n}\n\n.mfp-arrow:after {\n  border-top-width: 13px;\n  border-bottom-width: 13px;\n  top: 8px;\n}\n\n.mfp-arrow:before {\n  border-top-width: 21px;\n  border-bottom-width: 21px;\n  opacity: 0.7;\n}\n\n.mfp-arrow-left {\n  left: 0;\n}\n\n.mfp-arrow-left:after {\n  border-right: 17px solid #FFF;\n  margin-left: 31px;\n}\n\n.mfp-arrow-left:before {\n  margin-left: 25px;\n  border-right: 27px solid #3F3F3F;\n}\n\n.mfp-arrow-right {\n  right: 0;\n}\n\n.mfp-arrow-right:after {\n  border-left: 17px solid #FFF;\n  margin-left: 39px;\n}\n\n.mfp-arrow-right:before {\n  border-left: 27px solid #3F3F3F;\n}\n\n.mfp-iframe-holder {\n  padding-top: 40px;\n  padding-bottom: 40px;\n}\n\n.mfp-iframe-holder .mfp-content {\n  line-height: 0;\n  width: 100%;\n  max-width: 900px;\n}\n\n.mfp-iframe-holder .mfp-close {\n  top: -40px;\n}\n\n.mfp-iframe-scaler {\n  width: 100%;\n  height: 0;\n  overflow: hidden;\n  padding-top: 56.25%;\n}\n\n.mfp-iframe-scaler iframe {\n  position: absolute;\n  display: block;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);\n  background: #000;\n}\n\n/* Main image in popup */\nimg.mfp-img {\n  width: auto;\n  max-width: 100%;\n  height: auto;\n  display: block;\n  line-height: 0;\n  box-sizing: border-box;\n  padding: 40px 0 40px;\n  margin: 0 auto;\n}\n\n/* The shadow behind the image */\n.mfp-figure {\n  line-height: 0;\n}\n\n.mfp-figure:after {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 40px;\n  bottom: 40px;\n  display: block;\n  right: 0;\n  width: auto;\n  height: auto;\n  z-index: -1;\n  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);\n  background: #444;\n}\n\n.mfp-figure small {\n  color: #BDBDBD;\n  display: block;\n  font-size: 12px;\n  line-height: 14px;\n}\n\n.mfp-figure figure {\n  margin: 0;\n}\n\n.mfp-bottom-bar {\n  margin-top: -36px;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n  cursor: auto;\n}\n\n.mfp-title {\n  text-align: left;\n  line-height: 18px;\n  color: #F3F3F3;\n  word-wrap: break-word;\n  padding-right: 36px;\n}\n\n.mfp-image-holder .mfp-content {\n  max-width: 100%;\n}\n\n.mfp-gallery .mfp-image-holder .mfp-figure {\n  cursor: pointer;\n}\n\n@media screen and (max-width: 800px) and (orientation: landscape), screen and (max-height: 300px) {\n  /**\n       * Remove all paddings around the image on small screen\n       */\n  .mfp-img-mobile .mfp-image-holder {\n    padding-left: 0;\n    padding-right: 0;\n  }\n  .mfp-img-mobile img.mfp-img {\n    padding: 0;\n  }\n  .mfp-img-mobile .mfp-figure:after {\n    top: 0;\n    bottom: 0;\n  }\n  .mfp-img-mobile .mfp-figure small {\n    display: inline;\n    margin-left: 5px;\n  }\n  .mfp-img-mobile .mfp-bottom-bar {\n    background: rgba(0, 0, 0, 0.6);\n    bottom: 0;\n    margin: 0;\n    top: auto;\n    padding: 3px 5px;\n    position: fixed;\n    box-sizing: border-box;\n  }\n  .mfp-img-mobile .mfp-bottom-bar:empty {\n    padding: 0;\n  }\n  .mfp-img-mobile .mfp-counter {\n    right: 5px;\n    top: 3px;\n  }\n  .mfp-img-mobile .mfp-close {\n    top: 0;\n    right: 0;\n    width: 35px;\n    height: 35px;\n    line-height: 35px;\n    background: rgba(0, 0, 0, 0.6);\n    position: fixed;\n    text-align: center;\n    padding: 0;\n  }\n}\n@media all and (max-width: 900px) {\n  .mfp-arrow {\n    -webkit-transform: scale(0.75);\n    transform: scale(0.75);\n  }\n  .mfp-arrow-left {\n    -webkit-transform-origin: 0;\n    transform-origin: 0;\n  }\n  .mfp-arrow-right {\n    -webkit-transform-origin: 100%;\n    transform-origin: 100%;\n  }\n  .mfp-container {\n    padding-left: 6px;\n    padding-right: 6px;\n  }\n}\n.mfp-fade.mfp-bg {\n  opacity: 0;\n  transition: all 0.15s ease-out;\n}\n\n.mfp-fade.mfp-bg.mfp-ready {\n  opacity: 0.8;\n}\n\n.mfp-fade.mfp-bg.mfp-removing {\n  opacity: 0;\n}\n\n.mfp-fade.mfp-wrap .mfp-content {\n  opacity: 0;\n  transition: all 0.15s ease-out;\n}\n\n.mfp-fade.mfp-wrap.mfp-ready .mfp-content {\n  opacity: 1;\n}\n\n.mfp-fade.mfp-wrap.mfp-removing .mfp-content {\n  opacity: 0;\n}\n\n/* Slider */\n.slick-slider {\n  position: relative;\n  display: block;\n  box-sizing: border-box;\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -ms-touch-action: pan-y;\n  touch-action: pan-y;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.slick-list {\n  position: relative;\n  overflow: hidden;\n  display: block;\n  margin: 0;\n  padding: 0;\n}\n.slick-list:focus {\n  outline: none;\n}\n.slick-list.dragging {\n  cursor: pointer;\n  cursor: hand;\n}\n\n.slick-slider .slick-track,\n.slick-slider .slick-list {\n  -webkit-transform: translate3d(0, 0, 0);\n  -moz-transform: translate3d(0, 0, 0);\n  -ms-transform: translate3d(0, 0, 0);\n  -o-transform: translate3d(0, 0, 0);\n  transform: translate3d(0, 0, 0);\n}\n\n.slick-track {\n  position: relative;\n  left: 0;\n  top: 0;\n  display: block;\n}\n.slick-track:before, .slick-track:after {\n  content: \"\";\n  display: table;\n}\n.slick-track:after {\n  clear: both;\n}\n.slick-loading .slick-track {\n  visibility: hidden;\n}\n\n.slick-slide {\n  float: left;\n  height: 100%;\n  min-height: 1px;\n  display: none;\n}\n[dir=rtl] .slick-slide {\n  float: right;\n}\n.slick-slide img {\n  display: block;\n}\n.slick-slide.slick-loading img {\n  display: none;\n}\n.slick-slide.dragging img {\n  pointer-events: none;\n}\n.slick-initialized .slick-slide {\n  display: block;\n}\n.slick-loading .slick-slide {\n  visibility: hidden;\n}\n.slick-vertical .slick-slide {\n  display: block;\n  height: auto;\n  border: 1px solid transparent;\n}\n\n.slick-arrow.slick-hidden {\n  display: none;\n}\n\n.postcode_ac_name {\n  display: inline-block;\n  margin-left: 10px;\n  color: #684f40;\n  font-weight: 700;\n}\n\n.autocomplete-suggestions {\n  overflow: auto;\n  border: 1px solid #684f40;\n  background: #fff;\n}\n\n.autocomplete-suggestion {\n  overflow: hidden;\n  padding: 7px 8px;\n  white-space: nowrap;\n}\n\n.autocomplete-selected {\n  background: #e8ded9;\n}\n\n.autocomplete-suggestions strong {\n  color: #fdb822;\n  font-weight: normal;\n}\n\n.autocomplete-group {\n  padding: 2px 5px;\n}\n\n.autocomplete-group strong {\n  display: block;\n  border-bottom: 1px solid #2e2f32;\n}\n\nhtml {\n  box-sizing: border-box;\n}\n\n*,\n*:before,\n*:after {\n  box-sizing: inherit;\n}\n\nimg {\n  box-sizing: content-box;\n}\n\n:root {\n  font-size: 100%;\n}\n\nbody {\n  position: relative;\n  margin: 0;\n  color: #2e2f32;\n  font-family: Lora, serif;\n  font-size: 16px;\n  line-height: 1;\n  background-color: #fff;\n}\n\n@-ms-viewport {\n  width: device-width;\n}\nimg,\nsvg {\n  display: inline-block;\n  max-width: 100%;\n  height: auto;\n  border: none;\n}\n\niframe {\n  max-width: 100%;\n}\n\n.container-fluid {\n  max-width: 1200px;\n}\n\n.row:before,\n.row:after {\n  content: \" \";\n  display: table;\n}\n\n.row:after {\n  clear: both;\n}\n\n.row {\n  *zoom: 1;\n}\n\n.col-center {\n  text-align: center;\n}\n\n.col-left {\n  text-align: left;\n}\n\n.col-right {\n  text-align: right;\n}\n\n@media (max-width: 419px) {\n  .col-xs-1,\n  .col-xs-2,\n  .col-xs-3,\n  .col-xs-4,\n  .col-xs-5,\n  .col-xs-6,\n  .col-xs-7,\n  .col-xs-8,\n  .col-xs-9,\n  .col-xs-10,\n  .col-xs-11,\n  .col-xs-12 {\n    width: 100%;\n  }\n}\n\n.row--flex {\n  display: flex;\n  flex-wrap: wrap;\n}\n.row--flex:before, .row--flex:after {\n  content: normal;\n}\n\n.row--flex > [class*=col-] {\n  display: flex;\n  flex-direction: column;\n}\n\n.row--inner {\n  margin: 0;\n}\n\n@media (min-width: 419px) {\n  .row--border {\n    border-top: 1px dotted #ded1c9;\n    border-left: 1px dotted #ded1c9;\n  }\n  .row--border > div {\n    border-right: 1px dotted #ded1c9;\n    border-bottom: 1px dotted #ded1c9;\n  }\n  .row--border > div:hover, .row--border > div:focus {\n    z-index: 100;\n    box-shadow: 0 0 10px fade(#684f40, 40%);\n  }\n}\n\n@media (min-width: 1200px) {\n  .row--autoclear .col-lg-1:nth-child(12n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-lg-2:nth-child(6n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-lg-3:nth-child(4n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-lg-4:nth-child(3n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-lg-6:nth-child(odd) {\n    clear: left;\n  }\n}\n@media (min-width: 992px) and (max-width: 1199px) {\n  .row--autoclear .col-md-1:nth-child(12n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-md-2:nth-child(6n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-md-3:nth-child(4n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-md-4:nth-child(3n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-md-6:nth-child(odd) {\n    clear: left;\n  }\n}\n@media (min-width: 768px) and (max-width: 991px) {\n  .row--autoclear .col-sm-1:nth-child(12n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-sm-2:nth-child(6n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-sm-3:nth-child(4n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-sm-4:nth-child(3n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-sm-6:nth-child(odd) {\n    clear: left;\n  }\n}\n@media (max-width: 767px) {\n  .row--autoclear .col-xs-1:nth-child(12n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-xs-2:nth-child(6n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-xs-3:nth-child(4n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-xs-4:nth-child(3n+1) {\n    clear: left;\n  }\n  .row--autoclear .col-xs-6:nth-child(odd) {\n    clear: left;\n  }\n}\n\n.lt-ie9 .container,\n.lt-ie9 .container-fluid {\n  display: table;\n  width: 100%;\n}\n.lt-ie9 .row {\n  display: table-row;\n  height: 100%;\n}\n.lt-ie9 [class^=col-] {\n  display: table-cell;\n}\n\n/* lora-regular - latin-ext_latin */\n@font-face {\n  font-family: \"Lora\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(\"../fonts/lora-v16-latin-ext_latin-regular.eot\"); /* IE9 Compat Modes */\n  src: local(\"\"), url(\"../fonts/lora-v16-latin-ext_latin-regular.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/lora-v16-latin-ext_latin-regular.woff2\") format(\"woff2\"), url(\"../fonts/lora-v16-latin-ext_latin-regular.woff\") format(\"woff\"), url(\"../fonts/lora-v16-latin-ext_latin-regular.ttf\") format(\"truetype\"), url(\"../fonts/lora-v16-latin-ext_latin-regular.svg#Lora\") format(\"svg\"); /* Legacy iOS */\n}\n/* lora-700 - latin-ext_latin */\n@font-face {\n  font-family: \"Lora\";\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(\"../fonts/lora-v16-latin-ext_latin-700.eot\"); /* IE9 Compat Modes */\n  src: local(\"\"), url(\"../fonts/lora-v16-latin-ext_latin-700.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/lora-v16-latin-ext_latin-700.woff2\") format(\"woff2\"), url(\"../fonts/lora-v16-latin-ext_latin-700.woff\") format(\"woff\"), url(\"../fonts/lora-v16-latin-ext_latin-700.ttf\") format(\"truetype\"), url(\"../fonts/lora-v16-latin-ext_latin-700.svg#Lora\") format(\"svg\"); /* Legacy iOS */\n}\n@media print {\n  form,\n  button,\n  input[type=submit] {\n    display: none;\n  }\n}\n\ninput,\ntextarea,\nselect {\n  width: 100%;\n  max-width: 100%;\n  margin-bottom: 5px;\n  padding: 9px 10px;\n  font-size: 16px;\n  border: 1px solid #684f40;\n  border-radius: 0;\n}\ninput:focus,\ntextarea:focus,\nselect:focus {\n  border: 1px solid #392b23;\n}\ninput.is--success,\ntextarea.is--success,\nselect.is--success {\n  color: #5cb85c;\n  border: 1px solid #5cb85c;\n}\ninput.is--danger,\ntextarea.is--danger,\nselect.is--danger {\n  color: #d9534f;\n  border: 1px solid #d9534f;\n}\n\n.input--75 {\n  width: 74%;\n}\n\n.input--50 {\n  width: 49%;\n}\n\n.input--25 {\n  width: 24%;\n  max-width: 100px;\n}\n\n.input--postcode {\n  max-width: 250px;\n  margin-left: 10px;\n}\n\n@media (min-width: 419px) {\n  .textarea--75 {\n    width: 74%;\n  }\n}\n\n@media (min-width: 419px) {\n  .textarea--50 {\n    width: 49%;\n  }\n}\n\nlabel {\n  display: inline-block;\n  padding-bottom: 5px;\n}\n\nbutton,\ninput[type=submit] {\n  width: auto;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\ninput[type=checkbox],\ninput[type=radio] {\n  width: auto;\n  margin-right: 5px;\n}\n\nfieldset {\n  margin: 16px 0;\n  padding: 0 16px;\n  border: 1px solid #684f40;\n  border-radius: 0;\n}\nfieldset legend {\n  padding: 6px 10px 7px 10px;\n  color: #fff;\n  border-radius: 0;\n  background-color: #684f40;\n}\n@media (max-width: 992px) {\n  fieldset {\n    max-width: 100%;\n  }\n  fieldset input,\n  fieldset textarea {\n    max-width: 100%;\n  }\n}\n\n@media (min-width: 992px) {\n  .fieldset--half {\n    max-width: 50%;\n  }\n}\n\n.star-rating > fieldset {\n  display: inline-block;\n  margin: 0;\n  padding: 0;\n  border: none;\n}\n.star-rating > fieldset:not(:checked) > input {\n  position: absolute;\n  right: 0;\n  opacity: 0;\n}\n.star-rating > fieldset:not(:checked) > label {\n  float: right;\n  overflow: hidden;\n  width: 30px;\n  height: 30px;\n  color: #999;\n  font-size: 30px;\n  line-height: 30px;\n  white-space: nowrap;\n  cursor: pointer;\n}\n.star-rating > fieldset:not(:checked) > label:before {\n  content: \"★   \";\n}\n.star-rating > fieldset:not(:checked) > label:hover, .star-rating > fieldset:not(:checked) > label:hover ~ label {\n  color: #fdb822;\n}\n.star-rating > fieldset:not(:checked) > label:hover:before, .star-rating > fieldset:not(:checked) > label:hover ~ label:before {\n  content: \"★   \";\n}\n.star-rating > fieldset > input:checked ~ label {\n  color: #fdb822;\n}\n.star-rating > fieldset > input:checked ~ label:before {\n  content: \"★   \";\n}\n.star-rating > fieldset > label:active {\n  position: relative;\n  top: 2px;\n}\n\n@media (max-width: 419px) {\n  .g-recaptcha {\n    width: 1px;\n    transform: scale(0.74);\n  }\n}\n\n.icon {\n  display: inline-block;\n  vertical-align: middle;\n  position: relative;\n}\n.icon:before {\n  content: \"\";\n  display: block;\n}\n\n.icon__svg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  fill: currentColor;\n  pointer-events: none;\n  transform: translateZ(0);\n}\n\n.icon--arrow-down {\n  width: 16px;\n}\n.icon--arrow-down:before {\n  padding-top: 100%;\n}\n\n.icon--arrow-left {\n  width: 16px;\n}\n.icon--arrow-left:before {\n  padding-top: 100%;\n}\n\n.icon--arrow-right {\n  width: 16px;\n}\n.icon--arrow-right:before {\n  padding-top: 100%;\n}\n\n.icon--arrow-up {\n  width: 16px;\n}\n.icon--arrow-up:before {\n  padding-top: 100%;\n}\n\n.icon--basket {\n  width: 16px;\n}\n.icon--basket:before {\n  padding-top: 100%;\n}\n\n.icon--calc {\n  width: 16px;\n}\n.icon--calc:before {\n  padding-top: 100%;\n}\n\n.icon--chat {\n  width: 16px;\n}\n.icon--chat:before {\n  padding-top: 100%;\n}\n\n.icon--close {\n  width: 16px;\n}\n.icon--close:before {\n  padding-top: 100%;\n}\n\n.icon--dog {\n  width: 16px;\n}\n.icon--dog:before {\n  padding-top: 100%;\n}\n\n.icon--download {\n  width: 16px;\n}\n.icon--download:before {\n  padding-top: 100%;\n}\n\n.icon--email {\n  width: 16px;\n}\n.icon--email:before {\n  padding-top: 100%;\n}\n\n.icon--facebook {\n  width: 16px;\n}\n.icon--facebook:before {\n  padding-top: 100%;\n}\n\n.icon--googleplus {\n  width: 16px;\n}\n.icon--googleplus:before {\n  padding-top: 100%;\n}\n\n.icon--info {\n  width: 16px;\n}\n.icon--info:before {\n  padding-top: 100%;\n}\n\n.icon--instagram {\n  width: 16px;\n}\n.icon--instagram:before {\n  padding-top: 100%;\n}\n\n.icon--ko {\n  width: 16px;\n}\n.icon--ko:before {\n  padding-top: 100%;\n}\n\n.icon--location {\n  width: 16px;\n}\n.icon--location:before {\n  padding-top: 100%;\n}\n\n.icon--login {\n  width: 16px;\n}\n.icon--login:before {\n  padding-top: 100%;\n}\n\n.icon--man {\n  width: 16px;\n}\n.icon--man:before {\n  padding-top: 100%;\n}\n\n.icon--minus {\n  width: 16px;\n}\n.icon--minus:before {\n  padding-top: 100%;\n}\n\n.icon--ok {\n  width: 16px;\n}\n.icon--ok:before {\n  padding-top: 100%;\n}\n\n.icon--phone {\n  width: 16px;\n}\n.icon--phone:before {\n  padding-top: 100%;\n}\n\n.icon--plus {\n  width: 16px;\n}\n.icon--plus:before {\n  padding-top: 100%;\n}\n\n.icon--print {\n  width: 16px;\n}\n.icon--print:before {\n  padding-top: 100%;\n}\n\n.icon--product-1 {\n  width: 16px;\n}\n.icon--product-1:before {\n  padding-top: 100%;\n}\n\n.icon--product-2 {\n  width: 16px;\n}\n.icon--product-2:before {\n  padding-top: 100%;\n}\n\n.icon--product-3 {\n  width: 16px;\n}\n.icon--product-3:before {\n  padding-top: 100%;\n}\n\n.icon--product-4 {\n  width: 16px;\n}\n.icon--product-4:before {\n  padding-top: 100%;\n}\n\n.icon--search {\n  width: 16px;\n}\n.icon--search:before {\n  padding-top: 100%;\n}\n\n.icon--star {\n  width: 16px;\n}\n.icon--star:before {\n  padding-top: 100%;\n}\n\n.icon--twitter {\n  width: 16px;\n}\n.icon--twitter:before {\n  padding-top: 100%;\n}\n\n.icon--warn {\n  width: 16px;\n}\n.icon--warn:before {\n  padding-top: 100%;\n}\n\n.icon--wheel {\n  width: 16px;\n}\n.icon--wheel:before {\n  padding-top: 100%;\n}\n\n.icon--why-1 {\n  width: 16px;\n}\n.icon--why-1:before {\n  padding-top: 100%;\n}\n\n.icon--why-2 {\n  width: 16px;\n}\n.icon--why-2:before {\n  padding-top: 100%;\n}\n\n.icon--why-3 {\n  width: 16px;\n}\n.icon--why-3:before {\n  padding-top: 100%;\n}\n\n.header {\n  color: #392b23;\n  background-color: #fff;\n}\n@media (max-width: 991px) {\n  .header {\n    min-height: 115px;\n  }\n}\n.header a {\n  color: #fff;\n}\n.header .container-fluid {\n  position: relative;\n}\n\n.header__title {\n  position: absolute;\n  top: 5px;\n  left: 5px;\n}\n@media (min-width: 992px) {\n  .header__title {\n    display: none;\n  }\n}\n.header__title img {\n  width: 46px;\n}\n\n@media (min-width: 992px) {\n  .header__logo {\n    position: relative;\n    z-index: 101;\n    margin: -50px 15px -25px 15px;\n    padding: 0 !important;\n  }\n}\n.header__logo img {\n  display: block;\n  width: 50px;\n}\n@media (min-width: 992px) {\n  .header__logo img {\n    width: auto;\n  }\n}\n\n.header__center {\n  display: none;\n}\n@media (min-width: 992px) {\n  .header__center {\n    display: block;\n    margin-top: -30px;\n    margin-bottom: 10px;\n  }\n}\n\n.header__nav {\n  display: none;\n}\n@media (min-width: 992px) {\n  .header__nav {\n    display: block;\n  }\n}\n@media (max-width: 991px) {\n  .header__nav {\n    position: absolute;\n    top: 50px;\n    left: 0;\n    z-index: 100;\n    width: 100%;\n    padding: 15px 0;\n    background: #fff;\n  }\n}\n.header__nav ul {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n  font-size: 0;\n  text-align: center;\n}\n@media (min-width: 992px) {\n  .header__nav li {\n    display: inline-block;\n    padding-top: 45px;\n    vertical-align: middle;\n  }\n}\n.header__nav a {\n  display: block;\n  padding: 15px 10px;\n  color: #684f40;\n  font-size: 20px;\n  font-weight: 700;\n  text-decoration: none;\n  text-transform: uppercase;\n}\n@media (min-width: 992px) {\n  .header__nav a {\n    padding: 15px 15px;\n  }\n}\n.header__nav a:hover, .header__nav a:focus, .header__nav a.is-active {\n  color: #392b23;\n}\n\n.header__controls {\n  position: absolute;\n  top: 10px;\n  right: 50px;\n}\n@media (min-width: 992px) {\n  .header__controls {\n    right: auto;\n    left: 57%;\n  }\n}\n\n.header__contact {\n  display: none;\n  margin-bottom: 10px;\n  padding: 10px 0 12px 0;\n  color: #fff;\n  background-color: #392b23;\n}\n@media (min-width: 992px) {\n  .header__contact {\n    display: block;\n  }\n}\n.header__contact p {\n  margin: 0;\n}\n.header__contact a {\n  display: inline-block;\n  padding: 0 10px;\n  text-decoration: none;\n}\n.header__contact a:hover, .header__contact a:focus {\n  color: #fdb822;\n}\n.header__contact .icon {\n  width: 20px;\n  margin-top: -2px;\n  margin-right: 3px;\n  vertical-align: top;\n}\n\n.header__social {\n  display: none;\n}\n@media (min-width: 992px) {\n  .header__social {\n    display: block;\n    float: right;\n  }\n}\n.header__social a {\n  padding: 0 0;\n}\n\n.nav {\n  color: #fff;\n  background-color: #684f40;\n}\n@media (max-width: 992px) {\n  .nav {\n    position: absolute;\n    top: 9px;\n    right: 5px;\n    overflow: hidden;\n    width: 40px;\n    height: 40px;\n    text-indent: -9999px;\n    border-radius: 50%;\n    background: #97735d url(\"../img/menu-icon.svg\") center center no-repeat;\n    background-size: 26px auto;\n  }\n}\n.nav ul {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n  font-size: 0;\n  text-align: center;\n}\n.nav li {\n  display: inline-block;\n}\n.nav a {\n  display: block;\n  padding: 15px 10px;\n  color: #fff;\n  font-size: 20px;\n  font-weight: 700;\n  text-decoration: none;\n  text-transform: uppercase;\n}\n@media (min-width: 992px) {\n  .nav a {\n    padding: 25px 30px;\n  }\n}\n.nav a:hover, .nav a:focus, .nav a.is-active {\n  background-color: #392b23;\n}\n.nav .icon {\n  width: 50px;\n  margin-right: 10px;\n}\n\n.nav-product {\n  position: relative;\n  margin-top: 15px;\n}\n.nav-product ul {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n  color: #fff;\n  background-color: #97735d;\n}\n.nav-product li ul a {\n  padding: 7px 10px 7px 35px;\n  font-size: 90%;\n}\n.nav-product li ul a:before {\n  content: \">\";\n  position: absolute;\n  display: block;\n  margin: 2px 0 0 -12px;\n  font-size: 10px;\n}\n.nav-product li.is-active {\n  padding: 10px 0;\n  background-color: #684f40;\n}\n.nav-product li.is-active a {\n  background-color: #684f40;\n}\n.nav-product li.is-active a:hover, .nav-product li.is-active a:focus {\n  background-color: #392b23;\n}\n.nav-product a {\n  display: block;\n  padding: 8px 10px 8px 20px;\n  color: #fff;\n  text-decoration: none;\n  border-bottom: 1px dotted #684f40;\n}\n.nav-product a:hover, .nav-product a:focus {\n  background-color: #392b23;\n}\n\n@media (max-width: 992px) {\n  .nav-product__wrapper {\n    position: absolute;\n    top: 32px;\n    z-index: 1;\n    width: 100%;\n    padding: 0;\n  }\n}\n\n.nav-product--hidden {\n  display: none;\n}\n\n@media (min-width: 992px) {\n  .nav-product--switch .nav-product__menu {\n    display: none;\n    padding-top: 35px;\n  }\n}\n.nav-product--switch.is-active .nav-product__header {\n  opacity: 1;\n}\n.nav-product--switch.is-active .nav-product__menu {\n  display: block;\n}\n.nav-product--switch .nav-product__header {\n  display: block;\n}\n@media (min-width: 992px) {\n  .nav-product--switch .nav-product__header {\n    position: absolute;\n    top: 0;\n    width: 49%;\n    border-top-left-radius: 0;\n    border-top-right-radius: 0;\n    opacity: 0.8;\n    cursor: pointer;\n  }\n  .nav-product--switch .nav-product__header:hover, .nav-product--switch .nav-product__header:focus {\n    opacity: 1;\n  }\n}\n\n.nav-product__header {\n  margin: 0;\n  padding: 10px 15px 7px 15px;\n  color: #fff;\n  font-size: 18px;\n  font-weight: bold;\n  text-align: center;\n  background-color: #97735d;\n}\n@media (min-width: 992px) {\n  .nav-product__header {\n    border-top-left-radius: 0;\n    border-top-right-radius: 0;\n  }\n}\n\n.nav-product__header--right {\n  right: 0;\n}\n\n@media (min-width: 992px) {\n  .nav-product__other {\n    padding-top: 10px;\n  }\n}\n.nav-product__other .nav-product__header {\n  background-color: #666;\n}\n.nav-product__other a {\n  border-bottom: 1px dotted #333;\n  background-color: #999;\n}\n.nav-product__other a:hover, .nav-product__other a:focus {\n  background-color: #333;\n}\n\n.article {\n  padding: 20px 0;\n  font-size: 18px;\n  line-height: 1.2;\n}\n@media (min-width: 992px) {\n  .article {\n    padding: 60px 0;\n  }\n}\n.article a:not(.btn) {\n  color: #684f40;\n}\n.article a:not(.btn):hover, .article a:not(.btn):focus {\n  color: #392b23;\n}\n.article p {\n  line-height: 1.6;\n}\n.article ol {\n  padding-left: 35px;\n}\n.article blockquote {\n  margin: 16px 0;\n  padding: 1px 25px;\n  border-left: 2px solid #684f40;\n  background-color: #f1ece9;\n}\n.article h1,\n.article h2,\n.article h3,\n.article h4 {\n  color: #684f40;\n}\n.article h1 {\n  font-size: 22px;\n}\n@media (min-width: 992px) {\n  .article h1 {\n    font-size: 30px;\n  }\n}\n.article ul {\n  margin: 0 0 10px 0;\n  padding: 0;\n  list-style-type: none;\n}\n.article ul li {\n  position: relative;\n  padding: 3px 3px 3px 18px;\n}\n.article ul li:before {\n  content: \"\";\n  position: absolute;\n  top: 11px;\n  left: 4px;\n  display: block;\n  width: 6px;\n  height: 6px;\n  border-radius: 3px;\n  background-color: #684f40;\n}\n\n.article__perex {\n  max-width: 900px;\n  text-align: center;\n}\n@media (min-width: 992px) {\n  .article__perex {\n    margin: 0 auto 40px auto;\n    font-size: 20px;\n  }\n}\n\n.article__image {\n  margin-left: 15px;\n  padding: 5px;\n  border: 3px solid #684f40;\n}\n.article__image img {\n  display: block;\n  width: 100%;\n}\n\n.article__images {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 30px -10px;\n  font-size: 0;\n}\n.article__images a {\n  display: block;\n  flex: 1 1 1px;\n  width: 30%;\n  margin: 5px;\n  padding: 5px;\n  border: 3px solid #684f40;\n  transition: opacity 0.3s;\n}\n.article__images a:hover, .article__images a:focus {\n  opacity: 0.8;\n}\n.article__images a span {\n  display: block;\n  overflow: hidden;\n  height: 250px;\n}\n.article__images img {\n  display: block;\n}\n\n.article__attachements ul {\n  padding-left: 2px;\n  list-style-type: none;\n}\n.article__attachements li {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.article__attachements .icon {\n  width: 22px;\n  margin-top: -3px;\n  margin-right: 5px;\n  margin-bottom: 3px;\n  text-decoration: none;\n}\n\n.article__related h2 {\n  color: #97735d;\n  font-size: 25px;\n  font-weight: 400;\n}\n\n.footer {\n  padding-bottom: 40px;\n  color: #fff;\n  font-size: 18px;\n  line-height: 1.5;\n  background-color: #392b23;\n}\n@media (min-width: 992px) {\n  .footer {\n    margin-top: 100px;\n  }\n}\n.footer h3 {\n  margin-bottom: 10px;\n  text-transform: uppercase;\n}\n.footer a {\n  color: #fdb822;\n}\n.footer a:hover, .footer a:focus {\n  color: #fff;\n}\n\n.footer__map {\n  margin-bottom: 40px;\n}\n\n.footer__logo {\n  display: block;\n}\n\n.footer__products {\n  margin: 0;\n  margin-bottom: 10px;\n  padding: 0;\n  list-style-type: none;\n  font-size: 0;\n  font-weight: bold;\n}\n@media (max-width: 991px) {\n  .footer__products {\n    text-align: center;\n  }\n}\n.footer__products li {\n  display: inline-block;\n  font-size: 22px;\n}\n.footer__products a {\n  display: block;\n  min-width: 190px;\n  margin: 0 10px 10px 0;\n  padding: 10px 15px;\n  color: #fff;\n  text-decoration: none;\n  text-transform: uppercase;\n}\n.footer__products a:hover, .footer__products a:focus {\n  color: #fdb822;\n}\n.footer__products .icon {\n  width: 45px;\n  margin-right: 6px;\n}\n\n.footer__nav {\n  padding: 0;\n  list-style-type: none;\n  font-size: 15px;\n}\n@media (min-width: 992px) {\n  .footer__nav {\n    margin: 25px 0 0 0;\n  }\n}\n.footer__nav li {\n  display: inline-block;\n}\n.footer__nav a {\n  display: block;\n  padding: 4px 6px;\n  color: #fff;\n}\n.footer__nav a:hover, .footer__nav a:focus {\n  color: #fdb822;\n}\n\n.footer__contact .icon {\n  color: #fdb822;\n}\n\n.footer__copyright {\n  margin-top: 20px;\n  text-align: center;\n  background-color: #97735d;\n}\n\n.alert {\n  margin: 16px 0;\n  padding: 1px 16px;\n  color: #fff;\n  border-radius: 0;\n  background-color: #684f40;\n}\n@media (min-width: 992px) {\n  .alert {\n    padding: 4px 25px;\n  }\n}\n\n.alert--close {\n  position: relative;\n  padding: 1px 30px 1px 16px;\n}\n@media (min-width: 992px) {\n  .alert--close {\n    padding: 4px 30px 4px 25px;\n  }\n}\n.alert--close .icon--close {\n  position: absolute;\n  top: 19px;\n  right: 17px;\n  width: 18px;\n  cursor: pointer;\n}\n.alert--close .icon--close:hover, .alert--close .icon--close:focus {\n  opacity: 0.8;\n}\n\n.alert--success {\n  background-color: #5cb85c;\n}\n\n.alert--info {\n  background-color: #97735d;\n}\n\n.alert--danger {\n  background-color: #d9534f;\n}\n\n.basket {\n  display: inline-block;\n}\n.basket a {\n  display: flex;\n  color: #684f40;\n  text-decoration: none;\n}\n.basket .icon {\n  width: 30px;\n  color: #684f40;\n}\n\n@media (max-width: 991px) {\n  .basket__wrapper {\n    position: absolute;\n    top: 48px;\n    z-index: 1;\n    width: 100%;\n    padding: 0;\n  }\n}\n\n.basket__header {\n  position: relative;\n  margin: 0;\n}\n@media (max-width: 991px) {\n  .basket__header {\n    position: absolute;\n    top: -1px;\n    right: 2px;\n    overflow: hidden;\n    width: 40px;\n    height: 40px;\n    padding: 12px 0 0 6px;\n    text-indent: -9999px;\n    border-radius: 50%;\n    background: #97735d url(../img/basket.svg) center center no-repeat;\n    background-size: 20px 20px;\n  }\n}\n\n.basket__content {\n  margin: 0;\n  padding: 5px 10px 8px 10px;\n  line-height: 1.4;\n}\n\n.basket__count {\n  position: absolute;\n  top: 20px;\n  right: -3px;\n  display: none;\n  width: 18px;\n  height: 18px;\n  padding: 5px 0;\n  color: #392b23;\n  font-size: 11px;\n  font-weight: 700;\n  text-align: center;\n  border-radius: 50%;\n  background-color: #fdb822;\n}\n@media (min-width: 992px) {\n  .basket__count {\n    display: block;\n  }\n}\n\n.btn {\n  margin-bottom: 4px;\n  padding: 10px 12px;\n  color: #fff;\n  font: inherit;\n  font-size: 16px;\n  text-decoration: none;\n  text-transform: uppercase;\n  border: 1px solid transparent;\n  border-radius: 5px;\n  background-color: #684f40;\n  cursor: pointer;\n}\n.btn:hover, .btn:focus {\n  background-color: #291f19;\n}\n.btn .icon {\n  width: 16px;\n  margin: 0 3px;\n  vertical-align: bottom;\n}\n\na.btn {\n  display: inline-block;\n}\n\n.btn--small {\n  padding: 5px 6px;\n  font-size: 14px;\n}\n.btn--small .icon {\n  width: 15px;\n}\n\n.btn--big {\n  padding: 15px 17px;\n  font-size: 20px;\n}\n.btn--big .icon {\n  width: 20px;\n}\n@media (max-width: 419px) {\n  .btn--big {\n    padding: 8px 10px;\n    font-size: 14px;\n  }\n  .btn--big .icon {\n    width: 14px;\n  }\n}\n\n.btn--success {\n  background-color: #5cb85c;\n}\n.btn--success:hover, .btn--success:focus {\n  background-color: #449d44;\n}\n\n.btn--info {\n  background-color: #97735d;\n}\n.btn--info:hover, .btn--info:focus {\n  background-color: #785b4a;\n}\n\n.btn--danger {\n  background-color: #d9534f;\n}\n.btn--danger:hover, .btn--danger:focus {\n  background-color: #c9302c;\n}\n\n.btn--buy {\n  color: #fff;\n  background-color: #e08700;\n}\n.btn--buy:hover, .btn--buy:focus {\n  background-color: #ad6800;\n}\n\n.breadcrumb {\n  padding: 15px 0;\n  color: #fff;\n  line-height: 1.3;\n  text-align: center;\n  background-color: #ad8d7a;\n}\n.breadcrumb a {\n  color: #fff;\n}\n.breadcrumb a:first-child {\n  padding-left: 28px;\n  background: url(../img/man.svg) left center no-repeat;\n}\n.breadcrumb span {\n  display: inline-block;\n  overflow: hidden;\n  width: 20px;\n  margin: 0 7px;\n  vertical-align: middle;\n  text-indent: -9999px;\n  background: url(../img/breadcrumb.svg) center center no-repeat;\n}\n\n.category {\n  margin-top: 15px;\n}\n\n.category__header {\n  color: #684f40;\n}\n@media (min-width: 992px) {\n  .category__header {\n    font-size: 25px;\n  }\n}\n\n.category__description {\n  margin: 20px 0;\n}\n@media (min-width: 992px) {\n  .category__description {\n    margin: 50px 0;\n    font-size: 18px;\n  }\n}\n.category__description a {\n  color: #e08700;\n}\n.category__description a:hover, .category__description a:focus {\n  color: #fdb822;\n}\n\n.category__item a {\n  display: block;\n  margin-bottom: 10px;\n  padding: 10px 15px;\n  color: #fff;\n  text-decoration: none;\n  border-radius: 0;\n  background-color: #684f40;\n}\n.category__item a:hover, .category__item a:focus {\n  background-color: #97735d;\n}\n.category__item .icon {\n  width: 16px;\n  margin-right: 2px;\n  margin-left: -4px;\n  vertical-align: bottom;\n}\n\n.category__filters {\n  margin: 16px 0 0 0;\n}\n.category__filters .pagination {\n  margin: 0;\n}\n@media (min-width: 992px) {\n  .category__filters .pagination {\n    float: right;\n  }\n}\n.category__filters .label {\n  font-size: 16px;\n}\n\n.comments__item {\n  padding: 5px;\n  font-size: 15px;\n}\n.comments__item p {\n  margin-top: 8px;\n  margin-bottom: 0;\n}\n\n.comments__item--reply {\n  margin-top: 25px;\n  border: 1px dotted #684f40;\n}\n\n.comments__header {\n  margin: 0;\n  font-size: 17px;\n}\n\n.comments__date {\n  color: #999;\n  font-size: 13px;\n}\n\n.content-header {\n  position: relative;\n  padding: 40px 15px;\n  color: #fff;\n  font-size: 22px;\n  text-align: center;\n  background-color: #684f40;\n}\n@media (min-width: 992px) {\n  .content-header {\n    padding: 100px 0;\n  }\n}\n.content-header h1,\n.content-header p {\n  position: relative;\n  z-index: 12;\n  margin: 0;\n}\n.content-header h1 {\n  position: relative;\n  margin-bottom: 20px;\n  padding-bottom: 20px;\n  font-size: 40px;\n  text-transform: uppercase;\n}\n.content-header h1:before {\n  content: \"\";\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  display: block;\n  width: 100px;\n  height: 2px;\n  margin-left: -50px;\n  background-color: #fdb822;\n}\n.content-header:after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 11;\n  display: block;\n  background: url(../img/content-header-back.png);\n}\n\n.content-header__image {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 10;\n  display: block;\n  background-image: url(../img/content-header-back.jpg);\n  background-repeat: no-repeat;\n  background-position: center center;\n  background-size: cover;\n  opacity: 0.5;\n}\n\n.control {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  margin: 3px 0;\n  color: #fff;\n  font-size: 16px;\n  font-weight: bold;\n  line-height: 16px;\n  text-align: center;\n  text-decoration: none;\n  border-radius: 8px;\n  background-color: #97735d;\n  cursor: pointer;\n  user-select: none;\n}\n@media (max-width: 576px) {\n  .control {\n    width: 24px;\n    height: 24px;\n    font-size: 24px;\n    line-height: 24px;\n    border-radius: 12px;\n  }\n}\n@media print {\n  .control {\n    display: none;\n  }\n}\n.control:hover, .control:focus {\n  background-color: #785b4a;\n}\n.control .icon {\n  width: 12px;\n  margin-top: 2px;\n  vertical-align: top;\n}\n@media (max-width: 576px) {\n  .control .icon {\n    width: 18px;\n    margin-top: 3px;\n  }\n}\n\na.control {\n  color: #fff !important;\n}\n\n.control--big {\n  width: 32px;\n  height: 32px;\n  font-size: 32px;\n  line-height: 32px;\n  border-radius: 16px;\n}\n.control--big .icon {\n  width: 24px;\n  margin-top: 4px;\n  vertical-align: top;\n}\n\n.control--success {\n  background-color: #5cb85c;\n}\n.control--success:hover, .control--success:focus {\n  background-color: #449d44;\n}\n\n.control--remove {\n  background-color: #d9534f;\n}\n.control--remove:hover, .control--remove:focus {\n  background-color: #c9302c;\n}\n\n.control--count {\n  white-space: nowrap;\n}\n.control--count .icon {\n  vertical-align: top;\n}\n.control--count input {\n  max-width: 75px;\n}\n\n.filter {\n  margin: 16px 0 0 0;\n}\n\n.filter__tiles label {\n  position: relative;\n  width: 100%;\n  padding-left: 23px;\n  vertical-align: top;\n}\n@media (min-width: 419px) {\n  .filter__tiles label {\n    width: 49%;\n  }\n}\n@media (min-width: 576px) {\n  .filter__tiles label {\n    width: 32%;\n  }\n}\n@media (min-width: 1200px) {\n  .filter__tiles label {\n    width: 24%;\n  }\n}\n.filter__tiles label input {\n  position: absolute;\n  margin-top: 1px;\n  margin-left: -17px;\n}\n\n.filter__results {\n  margin-top: 0;\n  padding: 14px 18px 10px 18px;\n  color: #fff;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n  background-color: #684f40;\n}\n.filter__results strong {\n  margin-right: 7px;\n}\n\n@media (min-width: 992px) {\n  .filter__row {\n    margin-top: 40px;\n    margin-bottom: 40px;\n  }\n}\n\n.home__title {\n  margin: 25px 0;\n  text-align: center;\n  text-transform: uppercase;\n}\n@media (min-width: 992px) {\n  .home__title {\n    margin: 70px 0;\n  }\n}\n@media (max-width: 575px) {\n  .home__title {\n    font-size: 20px;\n    line-height: 1.3;\n  }\n}\n.home__title span {\n  display: inline-block;\n  padding: 16px 25px 14px 25px;\n  color: #fff;\n  border-radius: 3px;\n  background: #e08700;\n}\n@media (min-width: 992px) {\n  .home__title:before {\n    content: \"\";\n    position: absolute;\n    z-index: -1;\n    display: block;\n    width: 100%;\n    height: 2px;\n    margin-top: 26px;\n    background: #e08700;\n  }\n}\n\n.home__products .category__header {\n  text-align: center;\n}\n\n.home__block {\n  padding: 20px 20px 20px 0;\n  font-size: 18px;\n  line-height: 1.2;\n}\n.home__block h1,\n.home__block h2,\n.home__block h3,\n.home__block h4 {\n  color: #684f40;\n}\n.home__block p {\n  line-height: 1.6;\n}\n\n.home__photos {\n  padding: 20px 0;\n}\n.home__photos img {\n  width: 100%;\n}\n\n.home__why h3 {\n  position: relative;\n  padding-bottom: 20px;\n}\n.home__why h3:before {\n  content: \"\";\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  display: block;\n  width: 80px;\n  height: 2px;\n  margin-left: -40px;\n  background: #e08700;\n}\n.home__why p {\n  max-width: 200px;\n  margin: 10px auto;\n  line-height: 1.3;\n}\n\n.home__icon {\n  width: 100px;\n  height: 100px;\n  margin: 0 auto;\n  padding: 0;\n  border-radius: 50px;\n  background-color: #e08700;\n}\n.home__icon .icon {\n  width: 50px;\n  margin-top: 25px;\n  color: #fff;\n}\n\n.login {\n  display: inline-flex;\n  margin-right: 20px;\n}\n@media (max-width: 576px) {\n  .login {\n    margin-bottom: 15px;\n  }\n}\n.login a {\n  padding: 0 1px;\n  color: #684f40;\n}\n.login a:hover, .login a:focus {\n  color: #392b23;\n}\n.login .icon {\n  width: 30px;\n  color: #684f40;\n}\n\n@media (max-width: 991px) {\n  .login__wrapper {\n    position: absolute;\n    top: 48px;\n    z-index: 1;\n    width: 100%;\n    padding: 0;\n  }\n}\n@media (max-width: 992px) {\n  .login__wrapper {\n    clear: both;\n  }\n}\n\n.login__header {\n  margin: 0 10px 0;\n}\n@media (max-width: 991px) {\n  .login__header {\n    position: absolute;\n    top: -1px;\n    right: 38px;\n    overflow: hidden;\n    width: 40px;\n    height: 40px;\n    padding: 12px 0 0 6px;\n    text-indent: -9999px;\n    border-radius: 50%;\n    background: #97735d url(../img/man-mobile.svg) center center no-repeat;\n    background-size: 20px 20px;\n  }\n}\n\n.login__content {\n  margin: 0;\n  padding: 5px 10px 8px 5px;\n  line-height: 1.4;\n}\n@media (max-width: 991px) {\n  .login__content {\n    position: absolute;\n    top: 50px;\n    right: 10px;\n    z-index: 100;\n    border: 2px solid #97735d;\n    background-color: #fff;\n  }\n}\n\n.label {\n  display: inline-block;\n  margin-bottom: 4px;\n  padding: 6px 8px;\n  color: #fff;\n  font-size: 13px;\n  line-height: 15px;\n  text-transform: uppercase;\n  border-radius: 0;\n  background-color: #684f40;\n}\n.label .icon {\n  width: 14px;\n  vertical-align: top;\n}\n.label input {\n  margin-bottom: 0;\n  vertical-align: middle;\n}\n\na .label:hover, a .label:focus {\n  opacity: 0.8;\n}\n\n.label--action {\n  background-color: #cc0000;\n}\n\n.label--tip {\n  background-color: #e08700;\n}\n\n.label--discount {\n  background-color: #e08700;\n}\n\n.label--new {\n  background-color: #e08700;\n}\n\n.label--delivery {\n  background-color: #e08700;\n}\n\n.label--sort {\n  background-color: #97735d;\n}\n.label--sort .icon {\n  margin-left: 5px;\n}\n\n.label--filter {\n  color: #2e2f32;\n  border: 1px dotted #666;\n  border-radius: 0;\n  background-color: #fff;\n}\n\n.label--result {\n  background-color: #97735d;\n}\n.label--result a {\n  color: #fff;\n  text-decoration: none;\n}\n.label--result .icon {\n  margin-left: 5px;\n  cursor: pointer;\n}\n.label--result .icon:hover, .label--result .icon:focus {\n  opacity: 0.8;\n}\n\n.label--is-active {\n  color: #684f40;\n  background-color: #fff;\n}\n\n.modal {\n  position: absolute;\n  top: 0.5%;\n  left: 1%;\n  z-index: 1000;\n  display: none;\n  width: 100%;\n}\n@media (min-width: 576px) {\n  .modal {\n    position: fixed;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    overflow: hidden;\n    background-color: rgba(0, 0, 0, 0.75);\n  }\n}\n\n.modal--reset {\n  position: absolute !important;\n  overflow: visible !important;\n  background-color: transparent !important;\n}\n.modal--reset .modal__body {\n  top: 0 !important;\n  transform: none !important;\n}\n\n.modal__body {\n  position: absolute;\n  width: 98%;\n  background: #fff;\n  box-shadow: 0 0 13px 2px rgba(0, 0, 0, 0.5);\n}\n@media (min-width: 576px) {\n  .modal__body {\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n  }\n}\n@media (min-width: 700px) {\n  .modal__body {\n    width: 680px;\n  }\n}\n\n.modal__head {\n  padding: 8px;\n  color: #fff;\n  background-color: #684f40;\n}\n@media (min-width: 700px) {\n  .modal__head {\n    padding: 16px 20px;\n  }\n}\n\n.modal__close {\n  display: inline-block;\n  float: right;\n  font-size: 22px;\n  cursor: pointer;\n}\n.modal__close:hover, .modal__close:focus {\n  opacity: 0.8;\n}\n\n.modal__header {\n  margin: 0;\n  font-size: 20px;\n}\n\n.modal__content {\n  padding: 8px;\n}\n@media (min-width: 700px) {\n  .modal__content {\n    padding: 16px 20px;\n  }\n}\n\n.modal__footer {\n  padding: 8px;\n}\n@media (min-width: 700px) {\n  .modal__footer {\n    padding: 16px 20px;\n  }\n}\n\n.news__item {\n  margin-bottom: 20px;\n}\n.news__item h3 {\n  margin: 5px 0 10px 0;\n  font-size: 18px;\n  font-weight: 700;\n}\n.news__item p {\n  margin: 0;\n  font-size: 15px;\n}\n.news__item img {\n  float: left;\n  margin-right: 20px;\n}\n\n@media (min-width: 992px) {\n  .news__image {\n    float: right;\n    margin: 15px;\n  }\n}\n\n.pagination {\n  clear: both;\n  margin: 30px 0 10px 0;\n}\n.pagination a,\n.pagination span {\n  display: inline-block;\n  margin-bottom: 5px;\n  padding: 8px 10px;\n  border-radius: 0;\n}\n.pagination a {\n  color: #fff;\n  text-decoration: none;\n  background-color: #97735d;\n}\n.pagination a:hover, .pagination a:focus {\n  color: #fff;\n  background-color: #684f40;\n}\n\n.paginator__current {\n  font-weight: 700;\n  background-color: #e6e6e6;\n}\n\n.search {\n  position: absolute;\n  top: 65px;\n  width: 95%;\n}\n@media (min-width: 992px) {\n  .search {\n    top: 8px;\n    left: 160px;\n    width: auto;\n  }\n}\n\n.search__submit {\n  position: absolute;\n  top: 0;\n  right: 0;\n  overflow: hidden;\n  width: 38px;\n  height: 38px;\n  text-indent: -9999px;\n  background: url(../img/search.svg) center center no-repeat;\n  background-color: transparent;\n  opacity: 0.4;\n}\n.search__submit:hover, .search__submit:focus {\n  background-color: transparent;\n  opacity: 1;\n}\n\n.search__input {\n  width: 100%;\n  font-size: 15px;\n  border-color: #999;\n  border-radius: 5px;\n}\n@media (min-width: 992px) {\n  .search__input {\n    width: 250px;\n  }\n}\n\n.search__sort a {\n  color: #684f40;\n}\n.search__sort a:hover, .search__sort a:focus {\n  color: #392b23;\n}\n\n.share strong {\n  color: #684f40;\n  vertical-align: middle;\n}\n.share a {\n  display: inline-block;\n  color: #684f40;\n  font-size: 30px;\n  vertical-align: middle;\n  text-decoration: none;\n  opacity: 1;\n}\n.share a:hover, .share a:focus {\n  opacity: 0.8;\n}\n.share .icon {\n  width: 30px;\n  vertical-align: top;\n}\n\n.slider {\n  display: none;\n}\n@media (min-width: 576px) {\n  .slider {\n    display: block;\n  }\n}\n.slider p {\n  margin: 30px 0;\n}\n.slider .content-header {\n  overflow: hidden;\n  height: 300px;\n  padding: 70px 0 0 0;\n}\n.slider .slick-dots {\n  position: absolute;\n  right: 10px;\n  bottom: 10px;\n  z-index: 1;\n  height: 26px;\n  margin: 0;\n  padding: 0;\n  text-align: center;\n}\n.slider .slick-dots li {\n  display: inline-block;\n  margin: 5px;\n}\n.slider .slick-dots li.slick-active button {\n  background-color: #fff;\n}\n.slider .slick-dots button {\n  display: block;\n  overflow: hidden;\n  width: 14px;\n  height: 14px;\n  padding: 0;\n  line-height: 900px;\n  border: none;\n  border-radius: 14px;\n  background-color: #fdb822;\n}\n.slider .slick-dots button:focus {\n  outline: none;\n}\n\n.stock {\n  display: inline-block;\n  margin-top: 5px;\n  padding: 4px 6px;\n  color: #fff;\n  font-size: 12px;\n  text-transform: uppercase;\n  border-radius: 0;\n  background-color: #684f40;\n}\n.stock .icon {\n  width: 12px;\n  vertical-align: top;\n}\n\n.stock--available {\n  background-color: #e08700;\n}\n\n.stock--unavailable {\n  background-color: #999;\n}\n\n.user__menu {\n  margin: 20px 0;\n  padding: 10px 10px 6px 10px;\n  border-radius: 0;\n  background-color: #cccccc;\n}\n\n.where p {\n  font-size: 20px;\n  line-height: 1.5;\n}\n\n.where__news img {\n  max-width: 420px;\n}\n\n.where__farm img {\n  max-width: 420px;\n}\n\n.where__online img {\n  max-width: 250px;\n}\n\n.where__item p {\n  margin-bottom: 0;\n  padding: 20px 20px 0 20px;\n  font-size: 16px;\n}\n.where__item p + p {\n  margin-top: 0;\n  padding-bottom: 40px;\n}\n.where__item img {\n  max-width: 250px;\n}\n\n.where__photo img {\n  padding: 10px;\n  text-align: center;\n  border: 3px solid #684f40;\n  background-color: #fff;\n}\n\n.table {\n  width: 100%;\n  border: none;\n}\n.table tr:hover td {\n  background-color: #f1ece9;\n}\n.table th,\n.table td {\n  padding: 5px;\n  text-align: left;\n  border: none;\n}\n@media (min-width: 992px) {\n  .table th,\n  .table td {\n    padding: 10px 20px;\n  }\n}\n@media only screen and (max-width: 576px) {\n  .table {\n    display: block;\n  }\n  .table th,\n  .table td,\n  .table tr,\n  .table thead,\n  .table tbody {\n    display: block;\n  }\n  .table th + th {\n    border-top: 1px solid #97735d;\n  }\n  .table tr td:last-child {\n    border-bottom: 2px solid #684f40;\n  }\n}\n.table th {\n  color: #fff;\n  background-color: #97735d;\n}\n.table a {\n  color: #684f40;\n}\n\n.table--vertical th {\n  text-align: left;\n}\n\n.table--compare td {\n  text-align: center;\n  vertical-align: top;\n}\n.table--compare img {\n  display: inline-block;\n}\n.table--compare tr td:last-child {\n  border-bottom: 1px solid #684f40;\n}\n@media only screen and (max-width: 576px) {\n  .table--compare {\n    display: table;\n  }\n  .table--compare th,\n  .table--compare td {\n    display: table-cell;\n  }\n  .table--compare tr,\n  .table--compare thead,\n  .table--compare tbody {\n    display: table-row;\n  }\n}\n@media (min-width: 992px) {\n  .table--compare th {\n    width: 5%;\n  }\n  .table--compare td {\n    width: 20%;\n  }\n}\n\n.table__product img {\n  display: inline-block;\n  width: 45px;\n  margin-right: 10px;\n  vertical-align: middle;\n}\n.table__product a {\n  font-weight: 700;\n  text-decoration: none;\n}\n\n.tabs {\n  margin-top: 20px;\n}\n@media (min-width: 992px) {\n  .tabs {\n    border-bottom: 1px solid #97735d;\n  }\n}\n\n.tabs__name {\n  display: block;\n  margin-bottom: -1px;\n  padding: 15px 20px;\n  color: #fff;\n  text-decoration: none;\n  background-color: #97735d;\n}\n.tabs__name:hover, .tabs__name:focus {\n  background-color: #684f40;\n}\n.tabs__name.is-active {\n  color: #684f40;\n  font-weight: bold;\n  background-color: #fdb822;\n}\n\n.tabs__into {\n  padding: 12px 15px;\n  border-right: 1px solid #97735d;\n  border-bottom: 1px solid #97735d;\n  border-left: 1px solid #97735d;\n}\n@media (min-width: 992px) {\n  .tabs__into {\n    padding: 20px 25px;\n  }\n}\n\n.tabs__header {\n  margin-top: 0;\n  font-size: 24px;\n}\n\n.product {\n  margin: 5px 0;\n  padding: 10px;\n  text-align: center;\n  border: 3px solid #684f40;\n  background-color: #fff;\n}\n@media (min-width: 992px) {\n  .product {\n    margin: 10px 0;\n  }\n}\n.product:hover, .product:focus {\n  border-color: #fdb822;\n}\n.product .labels {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 1px;\n  text-align: left;\n}\n.product .labels .label {\n  margin-bottom: 2px;\n}\n\n.product__header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 70px;\n  margin: 0;\n  color: #fff;\n  font-size: 18px;\n  line-height: 1.2;\n  background-color: #684f40;\n}\n@media (min-width: 992px) {\n  .product__header {\n    font-size: 20px;\n  }\n}\n.product__header a {\n  display: block;\n  width: 100%;\n  padding: 10px;\n  color: #fff;\n  text-decoration: none;\n}\n\n.product__image {\n  position: relative;\n}\n.product__image img {\n  width: 100%;\n}\n\n.product__info {\n  margin: 4px 0 12px 0;\n  font-size: 14px;\n  line-height: 1.3;\n}\n\n.product__price {\n  position: absolute;\n  bottom: 2px;\n  left: 0;\n  padding: 7px 10px;\n  color: #fff;\n  font-size: 13px;\n  background: #684f40;\n}\n.product__price strong {\n  font-size: 20px;\n}\n\n.product__controls {\n  position: absolute;\n  right: 0;\n  bottom: 2px;\n}\n.product__controls .btn {\n  margin: 0;\n  font-weight: bold;\n  border-radius: 0;\n}\n.product__controls .icon {\n  width: 25px;\n}\n\n.product-detail {\n  padding: 30px 0;\n}\n@media (min-width: 992px) {\n  .product-detail {\n    padding: 60px 0;\n  }\n}\n.product-detail .table input {\n  width: 65px;\n}\n\n.product-detail__content a {\n  color: #684f40;\n}\n.product-detail__content a:hover, .product-detail__content a:focus {\n  color: #392b23;\n}\n\n.product-detail__header {\n  margin-top: 0;\n  color: #684f40;\n  font-size: 24px;\n}\n@media (min-width: 992px) {\n  .product-detail__header {\n    font-size: 30px;\n  }\n}\n\n.product-detail__description {\n  margin-bottom: 20px;\n}\n\n.product-detail__info {\n  margin-top: 16px;\n  margin-bottom: 16px;\n  line-height: 1.5;\n}\n.product-detail__info p {\n  margin: 0 0 20px 0;\n}\n.product-detail__info a {\n  color: #684f40;\n}\n\n.product-detail__price {\n  margin-top: 16px;\n  line-height: 1.3;\n}\n.product-detail__price strong {\n  color: #cc0000;\n  font-size: 24px;\n}\n\n.product-detail__image {\n  margin-right: 20px;\n  padding: 5px;\n  border: 3px solid #684f40;\n}\n.product-detail__image img {\n  display: block;\n  width: 100%;\n}\n\n.product-detail__gallery {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  margin: 5px -10px;\n  font-size: 0;\n}\n.product-detail__gallery a {\n  display: block;\n  flex: 0 1 25%;\n  width: 25%;\n  margin: 5px;\n  padding: 5px;\n  border: 3px solid #684f40;\n  transition: opacity 0.3s;\n}\n.product-detail__gallery a:hover, .product-detail__gallery a:focus {\n  opacity: 0.8;\n}\n.product-detail__gallery a span {\n  display: block;\n  overflow: hidden;\n  height: 250px;\n}\n.product-detail__gallery img {\n  display: block;\n}\n\n.product-detail__helpers {\n  margin: 16px 0;\n}\n\n.product-detail__watchdog p {\n  margin: 8px 0;\n}\n.product-detail__watchdog input[type=email] {\n  max-width: 150px;\n  padding: 3px 5px;\n  font-size: 14px;\n}\n.product-detail__watchdog strong {\n  display: inline-block;\n  padding-bottom: 5px;\n  color: #684f40;\n}\n.product-detail__watchdog .icon {\n  width: 18px;\n  vertical-align: bottom;\n}\n\n.product-detail__comments a {\n  color: #684f40;\n}\n.product-detail__comments a:hover, .product-detail__comments a:focus {\n  color: #392b23;\n}\n\n.product-detail__variants {\n  margin-top: 20px;\n}\n.product-detail__variants h2 {\n  color: #684f40;\n}\n.product-detail__variants p {\n  text-align: center;\n}\n@media (min-width: 992px) {\n  .product-detail__variants p {\n    text-align: right;\n  }\n}\n\n.product-detail__rating {\n  float: right;\n  width: 300px;\n  margin: 0 0 20px 20px;\n  padding: 20px;\n  font-size: 14px;\n  background-color: #fee1a0;\n}\n.product-detail__rating h3 {\n  margin: 0 0 10px 0;\n  font-size: 15px;\n}\n.product-detail__rating p {\n  margin: 0;\n  padding: 10px 0 5px 0;\n}\n\n.product-detail__buy {\n  margin-top: 15px;\n}\n\n.order h1,\n.order h2,\n.order h3,\n.order h4 {\n  color: #97735d;\n  font-weight: 300;\n  text-transform: uppercase;\n}\n.order h1 {\n  margin: 20px 0;\n  font-size: 22px;\n}\n@media (min-width: 992px) {\n  .order h1 {\n    margin: 40px 0 30px 0;\n    font-size: 30px;\n  }\n}\n.order h2 {\n  font-size: 20px;\n}\n.order h3 {\n  font-size: 18px;\n}\n@media (min-width: 992px) {\n  .order h3 {\n    font-size: 25px;\n  }\n}\n.order h4 {\n  font-size: 16px;\n}\n@media (min-width: 992px) {\n  .order h4 {\n    font-size: 18px;\n  }\n}\n@media (max-width: 992px) {\n  .order p {\n    margin: 5px 0;\n  }\n}\n.order a {\n  color: #97735d;\n}\n.order a:hover, .order a:focus {\n  color: #392b23;\n}\n.order a.btn,\n.order a.control .icon {\n  color: #fff;\n}\n\n.order__table {\n  width: 100%;\n  border: 0;\n}\n.order__table th,\n.order__table td {\n  padding: 5px;\n  border: 0;\n}\n@media (min-width: 992px) {\n  .order__table th,\n  .order__table td {\n    padding: 10px 20px;\n  }\n}\n.order__table input {\n  width: 65px;\n}\n@media only screen and (max-width: 576px) {\n  .order__table {\n    display: block;\n  }\n  .order__table th,\n  .order__table td,\n  .order__table tr,\n  .order__table thead,\n  .order__table tbody {\n    display: block;\n  }\n  .order__table th + th {\n    border-top: 1px solid #392b23;\n  }\n  .order__table tr td:last-child {\n    border-bottom: 2px solid #97735d;\n  }\n}\n\n.order__product img {\n  float: left;\n  width: 70px;\n  height: auto;\n  margin-right: 8px;\n}\n.order__product a {\n  color: #97735d;\n  font-size: 17px;\n  text-decoration: none;\n  text-transform: uppercase;\n}\n.order__product a img:hover {\n  opacity: 0.8;\n}\n.order__product a:hover, .order__product a:focus {\n  color: #392b23;\n}\n\n.order__count {\n  white-space: nowrap;\n}\n\n.order__price {\n  max-width: 65px;\n  font-weight: 700;\n  text-align: right;\n  white-space: nowrap;\n}\n@media only screen and (max-width: 576px) {\n  .order__price {\n    width: 100%;\n    max-width: 100%;\n    font-size: 18px;\n    text-align: right;\n  }\n}\n\n.order__price-sum td {\n  color: #684f40;\n}\n@media only screen and (max-width: 576px) {\n  .order__price-sum td {\n    display: none;\n  }\n}\n\n.order__discount td {\n  color: #5cb85c;\n}\n\n.order__price-final {\n  margin-top: 20px;\n  padding: 20px;\n  color: #fff;\n  font-size: 16px;\n  font-weight: 300;\n  background-color: #97735d;\n}\n.order__price-final strong {\n  float: right;\n  font-weight: 700;\n  white-space: nowrap;\n}\n@media (min-width: 992px) {\n  .order__price-final {\n    font-size: 25px;\n    border-radius: 0;\n  }\n}\n\n.order__price-delivery {\n  margin-top: 10px;\n  padding-top: 20px;\n}\n.order__price-delivery strong {\n  float: right;\n  font-weight: 700;\n  white-space: nowrap;\n}\n\n.order__place {\n  position: relative;\n  margin: 8px 0;\n  padding: 15px 20px 15px 110px;\n  color: #fff;\n  font-size: 20px;\n  line-height: 1.5;\n  background-color: #cc0000;\n}\n.order__place input {\n  border: none;\n}\n.order__place em {\n  font-size: 15px;\n  font-style: normal;\n}\n.order__place .icon {\n  position: absolute;\n  top: 20px;\n  left: 25px;\n  width: 60px;\n}\n\n.order__coupon {\n  margin-top: 16px;\n}\n@media (max-width: 419px) {\n  .order__coupon {\n    font-size: 14px;\n  }\n}\n\n.order__payment,\n.order__delivery {\n  position: relative;\n  margin-bottom: 25px;\n  padding-left: 30px;\n  font-size: 16px;\n}\n.order__payment input,\n.order__delivery input {\n  position: absolute;\n  margin-left: -20px;\n}\n.order__payment a,\n.order__delivery a {\n  color: #97735d;\n}\n.order__payment a:hover, .order__payment a:focus,\n.order__delivery a:hover,\n.order__delivery a:focus {\n  color: #392b23;\n}\n\n.order__help {\n  font-size: 14px;\n}\n\n.order-progress__item a:hover, .order-progress__item a:focus {\n  color: #fff;\n  background-color: #e08700;\n}\n.order-progress__item a.is-active,\n.order-progress__item span.is-active {\n  color: #684f40;\n  font-weight: bold;\n  background-color: #fdb822;\n}\n@media (min-width: 992px) {\n  .order-progress__item a.is-active,\n  .order-progress__item span.is-active {\n    border-bottom: 1px solid #fff;\n  }\n}\n.order-progress__item a,\n.order-progress__item span {\n  display: block;\n  margin-bottom: -1px;\n  padding: 8px 10px;\n  color: #fff;\n  text-decoration: none;\n  background-color: #97735d;\n}\n@media (min-width: 992px) {\n  .order-progress__item a,\n  .order-progress__item span {\n    padding: 15px 20px;\n  }\n}\n\n.order-content {\n  padding: 30px 0;\n}\n\n.order-info__item {\n  margin: 8px 0;\n  padding: 12px 20px;\n  color: #fff;\n  line-height: 1.5;\n  border-radius: 0;\n  background-color: #684f40;\n}\n.order-info__item a {\n  color: #fff;\n}\n.order-info__item a:hover, .order-info__item a:focus {\n  opacity: 0.8;\n}\n.order-info__item strong {\n  font-size: 18px;\n}\n\n.order-info--discount {\n  background-color: #5cb85c;\n}\n\n.order-info--delivery {\n  background-color: #e08700;\n}\n\n.order-info--weight {\n  background-color: #d9534f;\n}\n\n.order-info--text {\n  background-color: #97735d;\n}\n\n.order-controls {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n@media (max-width: 992px) {\n  .order-controls {\n    text-align: center;\n  }\n}\n.order-controls a,\n.order-controls button {\n  margin: 2px 0;\n}\n@media (min-width: 992px) {\n  .order-controls > div:last-child {\n    text-align: right;\n  }\n}\n\n@media (min-width: 576px) {\n  .order-delivery {\n    padding: 15px 20px 0 20px;\n  }\n}\n.order-delivery img {\n  display: inline-block;\n}\n\n.order-delivery__item {\n  padding: 0 20px 10px 20px;\n  border-bottom: 1px solid #684f40;\n  cursor: pointer;\n}\n\n.order-delivery__types {\n  border-top: 1px solid #684f40;\n  border-right: 1px solid #684f40;\n  border-left: 1px solid #684f40;\n}\n\n.order-delivery__payments {\n  border-top: 1px solid #684f40;\n  border-right: 1px solid #684f40;\n  border-left: 1px solid #684f40;\n}\n\n.order-delivery__payment ul {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n}\n\n.order-delivery__payment label {\n  display: block;\n  padding: 16px 20px 12px 20px;\n  border-bottom: 1px solid #684f40;\n  cursor: pointer;\n}\n\n.order-delivery__payment img {\n  margin-right: 5px;\n  vertical-align: middle;\n}\n\n.order-delivery__name {\n  font-size: 20px !important;\n}\n.order-delivery__name img {\n  margin-top: -3px;\n  margin-right: 3px;\n  vertical-align: middle;\n}\n\n.order-delivery__options {\n  margin-left: 5px;\n}\n\n.center {\n  text-align: center;\n}\n\n.left {\n  text-align: left;\n}\n\n.right {\n  text-align: right;\n}\n\n.nowrap {\n  white-space: nowrap;\n}\n\n.fleft {\n  float: left;\n}\n\n.fright {\n  float: right;\n}\n\n.cls {\n  clear: both;\n}\n\n.cf:before,\n.cf:after {\n  content: \" \";\n  display: table;\n}\n\n.cf:after {\n  clear: both;\n}\n\n.cf {\n  *zoom: 1;\n}\n\n@media print {\n  * {\n    font-family: sans-serif !important;\n    color: #000000 !important;\n    background: #ffffff !important;\n    text-shadow: none !important;\n    box-shadow: none !important;\n    border: none !important;\n    width: auto !important;\n    height: auto !important;\n    max-width: none !important;\n    position: relative !important;\n  }\n  body {\n    width: 100% !important;\n    margin: 0px !important;\n    padding: 0px !important;\n    line-height: 1.4 !important;\n    word-spacing: 1.1pt !important;\n    letter-spacing: 0.2pt !important;\n    font-family: sans-serif !important;\n    color: #000000 !important;\n    background: none !important;\n    font-size: 12pt !important;\n  }\n  h1, h2, h3, h4 {\n    clear: both !important;\n  }\n  h1 {\n    font-size: 19pt !important;\n  }\n  h2 {\n    font-size: 17pt !important;\n  }\n  h3 {\n    font-size: 15pt !important;\n  }\n  h4 {\n    font-size: 12pt !important;\n  }\n  img {\n    margin: 1em 1.5em 1.5em 0em !important;\n  }\n  ul, ol {\n    padding-left: 20px !important;\n  }\n  a img {\n    border: none !important;\n  }\n  a, a:link, a:visited, a:hover, a:active, a:focus {\n    text-decoration: none !important;\n    color: #000000 !important;\n  }\n  table {\n    margin: 1px !important;\n    text-align: left !important;\n  }\n  th {\n    border-bottom: 1px solid #000000 !important;\n    font-weight: bold !important;\n  }\n  td {\n    border-bottom: 1px solid #000000 !important;\n  }\n  th, td {\n    padding: 4px 10px 4px 0px !important;\n  }\n  tr {\n    page-break-inside: avoid !important;\n  }\n  .no, .noprint,\n  object, iframe, form, button,\n  .nav, .btn, .breadcrumb, .nav-product, .filter, .share, .slider, .basket, .login, .search, .pagination,\n  .category__item, .order-progress, .category__filters, .product .labels, .header__nav, .header__contact, .filter__row, .content-header__image {\n    display: none !important;\n  }\n  .order form {\n    display: block !important;\n  }\n  .product__wrapper {\n    width: 32% !important;\n    display: inline-block !important;\n    border-top: 1px dotted gray !important;\n    vertical-align: top !important;\n  }\n  .product__wrapper:nth-child(3n+1) {\n    clear: both;\n  }\n  .product img {\n    margin: 0 auto !important;\n  }\n  .product__header {\n    font-size: 16px !important;\n  }\n  .gallery {\n    text-align: left;\n  }\n  .home__title, .content-header {\n    margin: 0 !important;\n    padding: 0 !important;\n  }\n  .row {\n    margin: 10px;\n  }\n  .row .row {\n    margin: 0;\n  }\n  .header {\n    padding: 0;\n  }\n  .header__logo {\n    margin: 0 !important;\n  }\n  .header__logo img {\n    background-color: #000000 !important;\n    padding: 10px;\n    margin: 0 !important;\n    -webkit-print-color-adjust: exact;\n  }\n  .tabs__name {\n    display: none !important;\n  }\n  .tabs__into {\n    padding: 0 !important;\n  }\n  .order table {\n    width: 100% !important;\n  }\n  .order input, .order textarea {\n    border: 1px solid #000000 !important;\n  }\n  .footer > .container-fluid {\n    display: none;\n  }\n  .footer__copyright {\n    clear: both;\n  }\n}", "/*! normalize.css v8.0.0 | MIT License | github.com/necolas/normalize.css */\r\n\r\n/* Document\r\n   ========================================================================== */\r\n\r\n/**\r\n * 1. Correct the line height in all browsers.\r\n * 2. Prevent adjustments of font size after orientation changes in iOS.\r\n */\r\n\r\nhtml {\r\n  line-height: 1.15; /* 1 */\r\n  -webkit-text-size-adjust: 100%; /* 2 */\r\n}\r\n\r\n/* Sections\r\n   ========================================================================== */\r\n\r\n/**\r\n * Remove the margin in all browsers.\r\n */\r\n\r\nbody {\r\n  margin: 0;\r\n}\r\n\r\n/**\r\n * Correct the font size and margin on `h1` elements within `section` and\r\n * `article` contexts in Chrome, Firefox, and Safari.\r\n */\r\n\r\nh1 {\r\n  font-size: 2em;\r\n  margin: 0.67em 0;\r\n}\r\n\r\n/* Grouping content\r\n   ========================================================================== */\r\n\r\n/**\r\n * 1. Add the correct box sizing in Firefox.\r\n * 2. Show the overflow in Edge and IE.\r\n */\r\n\r\nhr {\r\n  box-sizing: content-box; /* 1 */\r\n  height: 0; /* 1 */\r\n  overflow: visible; /* 2 */\r\n}\r\n\r\n/**\r\n * 1. Correct the inheritance and scaling of font size in all browsers.\r\n * 2. Correct the odd `em` font sizing in all browsers.\r\n */\r\n\r\npre {\r\n  font-family: monospace, monospace; /* 1 */\r\n  font-size: 1em; /* 2 */\r\n}\r\n\r\n/* Text-level semantics\r\n   ========================================================================== */\r\n\r\n/**\r\n * Remove the gray background on active links in IE 10.\r\n */\r\n\r\na {\r\n  background-color: transparent;\r\n}\r\n\r\n/**\r\n * 1. Remove the bottom border in Chrome 57-\r\n * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\r\n */\r\n\r\nabbr[title] {\r\n  border-bottom: none; /* 1 */\r\n  text-decoration: underline; /* 2 */\r\n  text-decoration: underline dotted; /* 2 */\r\n}\r\n\r\n/**\r\n * Add the correct font weight in Chrome, Edge, and Safari.\r\n */\r\n\r\nb,\r\nstrong {\r\n  font-weight: bolder;\r\n}\r\n\r\n/**\r\n * 1. Correct the inheritance and scaling of font size in all browsers.\r\n * 2. Correct the odd `em` font sizing in all browsers.\r\n */\r\n\r\ncode,\r\nkbd,\r\nsamp {\r\n  font-family: monospace, monospace; /* 1 */\r\n  font-size: 1em; /* 2 */\r\n}\r\n\r\n/**\r\n * Add the correct font size in all browsers.\r\n */\r\n\r\nsmall {\r\n  font-size: 80%;\r\n}\r\n\r\n/**\r\n * Prevent `sub` and `sup` elements from affecting the line height in\r\n * all browsers.\r\n */\r\n\r\nsub,\r\nsup {\r\n  font-size: 75%;\r\n  line-height: 0;\r\n  position: relative;\r\n  vertical-align: baseline;\r\n}\r\n\r\nsub {\r\n  bottom: -0.25em;\r\n}\r\n\r\nsup {\r\n  top: -0.5em;\r\n}\r\n\r\n/* Embedded content\r\n   ========================================================================== */\r\n\r\n/**\r\n * Remove the border on images inside links in IE 10.\r\n */\r\n\r\nimg {\r\n  border-style: none;\r\n}\r\n\r\n/* Forms\r\n   ========================================================================== */\r\n\r\n/**\r\n * 1. Change the font styles in all browsers.\r\n * 2. Remove the margin in Firefox and Safari.\r\n */\r\n\r\nbutton,\r\ninput,\r\noptgroup,\r\nselect,\r\ntextarea {\r\n  font-family: inherit; /* 1 */\r\n  font-size: 100%; /* 1 */\r\n  line-height: 1.15; /* 1 */\r\n  margin: 0; /* 2 */\r\n}\r\n\r\n/**\r\n * Show the overflow in IE.\r\n * 1. Show the overflow in Edge.\r\n */\r\n\r\nbutton,\r\ninput { /* 1 */\r\n  overflow: visible;\r\n}\r\n\r\n/**\r\n * Remove the inheritance of text transform in Edge, Firefox, and IE.\r\n * 1. Remove the inheritance of text transform in Firefox.\r\n */\r\n\r\nbutton,\r\nselect { /* 1 */\r\n  text-transform: none;\r\n}\r\n\r\n/**\r\n * Correct the inability to style clickable types in iOS and Safari.\r\n */\r\n\r\nbutton,\r\n[type=\"button\"],\r\n[type=\"reset\"],\r\n[type=\"submit\"] {\r\n  -webkit-appearance: button;\r\n}\r\n\r\n/**\r\n * Remove the inner border and padding in Firefox.\r\n */\r\n\r\nbutton::-moz-focus-inner,\r\n[type=\"button\"]::-moz-focus-inner,\r\n[type=\"reset\"]::-moz-focus-inner,\r\n[type=\"submit\"]::-moz-focus-inner {\r\n  border-style: none;\r\n  padding: 0;\r\n}\r\n\r\n/**\r\n * Restore the focus styles unset by the previous rule.\r\n */\r\n\r\nbutton:-moz-focusring,\r\n[type=\"button\"]:-moz-focusring,\r\n[type=\"reset\"]:-moz-focusring,\r\n[type=\"submit\"]:-moz-focusring {\r\n  outline: 1px dotted ButtonText;\r\n}\r\n\r\n/**\r\n * Correct the padding in Firefox.\r\n */\r\n\r\nfieldset {\r\n  padding: 0.35em 0.75em 0.625em;\r\n}\r\n\r\n/**\r\n * 1. Correct the text wrapping in Edge and IE.\r\n * 2. Correct the color inheritance from `fieldset` elements in IE.\r\n * 3. Remove the padding so developers are not caught out when they zero out\r\n *    `fieldset` elements in all browsers.\r\n */\r\n\r\nlegend {\r\n  box-sizing: border-box; /* 1 */\r\n  color: inherit; /* 2 */\r\n  display: table; /* 1 */\r\n  max-width: 100%; /* 1 */\r\n  padding: 0; /* 3 */\r\n  white-space: normal; /* 1 */\r\n}\r\n\r\n/**\r\n * Add the correct vertical alignment in Chrome, Firefox, and Opera.\r\n */\r\n\r\nprogress {\r\n  vertical-align: baseline;\r\n}\r\n\r\n/**\r\n * Remove the default vertical scrollbar in IE 10+.\r\n */\r\n\r\ntextarea {\r\n  overflow: auto;\r\n}\r\n\r\n/**\r\n * 1. Add the correct box sizing in IE 10.\r\n * 2. Remove the padding in IE 10.\r\n */\r\n\r\n[type=\"checkbox\"],\r\n[type=\"radio\"] {\r\n  box-sizing: border-box; /* 1 */\r\n  padding: 0; /* 2 */\r\n}\r\n\r\n/**\r\n * Correct the cursor style of increment and decrement buttons in Chrome.\r\n */\r\n\r\n[type=\"number\"]::-webkit-inner-spin-button,\r\n[type=\"number\"]::-webkit-outer-spin-button {\r\n  height: auto;\r\n}\r\n\r\n/**\r\n * 1. Correct the odd appearance in Chrome and Safari.\r\n * 2. Correct the outline style in Safari.\r\n */\r\n\r\n[type=\"search\"] {\r\n  -webkit-appearance: textfield; /* 1 */\r\n  outline-offset: -2px; /* 2 */\r\n}\r\n\r\n/**\r\n * Remove the inner padding in Chrome and Safari on macOS.\r\n */\r\n\r\n[type=\"search\"]::-webkit-search-decoration {\r\n  -webkit-appearance: none;\r\n}\r\n\r\n/**\r\n * 1. Correct the inability to style clickable types in iOS and Safari.\r\n * 2. Change font properties to `inherit` in Safari.\r\n */\r\n\r\n::-webkit-file-upload-button {\r\n  -webkit-appearance: button; /* 1 */\r\n  font: inherit; /* 2 */\r\n}\r\n\r\n/* Interactive\r\n   ========================================================================== */\r\n\r\n/*\r\n * Add the correct display in Edge, IE 10+, and Firefox.\r\n */\r\n\r\ndetails {\r\n  display: block;\r\n}\r\n\r\n/*\r\n * Add the correct display in all browsers.\r\n */\r\n\r\nsummary {\r\n  display: list-item;\r\n}\r\n\r\n/* Misc\r\n   ========================================================================== */\r\n\r\n/**\r\n * Add the correct display in IE 10+.\r\n */\r\n\r\ntemplate {\r\n  display: none;\r\n}\r\n\r\n/**\r\n * Add the correct display in IE 10.\r\n */\r\n\r\n[hidden] {\r\n  display: none;\r\n}\r\n", ".container {\r\n  margin-right: auto;\r\n  margin-left: auto;\r\n  padding-left: 10px;\r\n  padding-right: 10px;\r\n}\r\n@media (min-width: 768px) {\r\n  .container {\r\n    width: 740px;\r\n  }\r\n}\r\n@media (min-width: 992px) {\r\n  .container {\r\n    width: 960px;\r\n  }\r\n}\r\n@media (min-width: 1200px) {\r\n  .container {\r\n    width: 1160px;\r\n  }\r\n}\r\n.container-fluid {\r\n  margin-right: auto;\r\n  margin-left: auto;\r\n  padding-left: 10px;\r\n  padding-right: 10px;\r\n}\r\n.row {\r\n  margin-left: -10px;\r\n  margin-right: -10px;\r\n}\r\n.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {\r\n  position: relative;\r\n  min-height: 1px;\r\n  padding-left: 10px;\r\n  padding-right: 10px;\r\n}\r\n.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {\r\n  float: left;\r\n}\r\n.col-xs-12 {\r\n  width: 100%;\r\n}\r\n.col-xs-11 {\r\n  width: 91.66666667%;\r\n}\r\n.col-xs-10 {\r\n  width: 83.33333333%;\r\n}\r\n.col-xs-9 {\r\n  width: 75%;\r\n}\r\n.col-xs-8 {\r\n  width: 66.66666667%;\r\n}\r\n.col-xs-7 {\r\n  width: 58.33333333%;\r\n}\r\n.col-xs-6 {\r\n  width: 50%;\r\n}\r\n.col-xs-5 {\r\n  width: 41.66666667%;\r\n}\r\n.col-xs-4 {\r\n  width: 33.33333333%;\r\n}\r\n.col-xs-3 {\r\n  width: 25%;\r\n}\r\n.col-xs-2 {\r\n  width: 16.66666667%;\r\n}\r\n.col-xs-1 {\r\n  width: 8.33333333%;\r\n}\r\n.col-xs-pull-12 {\r\n  right: 100%;\r\n}\r\n.col-xs-pull-11 {\r\n  right: 91.66666667%;\r\n}\r\n.col-xs-pull-10 {\r\n  right: 83.33333333%;\r\n}\r\n.col-xs-pull-9 {\r\n  right: 75%;\r\n}\r\n.col-xs-pull-8 {\r\n  right: 66.66666667%;\r\n}\r\n.col-xs-pull-7 {\r\n  right: 58.33333333%;\r\n}\r\n.col-xs-pull-6 {\r\n  right: 50%;\r\n}\r\n.col-xs-pull-5 {\r\n  right: 41.66666667%;\r\n}\r\n.col-xs-pull-4 {\r\n  right: 33.33333333%;\r\n}\r\n.col-xs-pull-3 {\r\n  right: 25%;\r\n}\r\n.col-xs-pull-2 {\r\n  right: 16.66666667%;\r\n}\r\n.col-xs-pull-1 {\r\n  right: 8.33333333%;\r\n}\r\n.col-xs-pull-0 {\r\n  right: auto;\r\n}\r\n.col-xs-push-12 {\r\n  left: 100%;\r\n}\r\n.col-xs-push-11 {\r\n  left: 91.66666667%;\r\n}\r\n.col-xs-push-10 {\r\n  left: 83.33333333%;\r\n}\r\n.col-xs-push-9 {\r\n  left: 75%;\r\n}\r\n.col-xs-push-8 {\r\n  left: 66.66666667%;\r\n}\r\n.col-xs-push-7 {\r\n  left: 58.33333333%;\r\n}\r\n.col-xs-push-6 {\r\n  left: 50%;\r\n}\r\n.col-xs-push-5 {\r\n  left: 41.66666667%;\r\n}\r\n.col-xs-push-4 {\r\n  left: 33.33333333%;\r\n}\r\n.col-xs-push-3 {\r\n  left: 25%;\r\n}\r\n.col-xs-push-2 {\r\n  left: 16.66666667%;\r\n}\r\n.col-xs-push-1 {\r\n  left: 8.33333333%;\r\n}\r\n.col-xs-push-0 {\r\n  left: auto;\r\n}\r\n.col-xs-offset-12 {\r\n  margin-left: 100%;\r\n}\r\n.col-xs-offset-11 {\r\n  margin-left: 91.66666667%;\r\n}\r\n.col-xs-offset-10 {\r\n  margin-left: 83.33333333%;\r\n}\r\n.col-xs-offset-9 {\r\n  margin-left: 75%;\r\n}\r\n.col-xs-offset-8 {\r\n  margin-left: 66.66666667%;\r\n}\r\n.col-xs-offset-7 {\r\n  margin-left: 58.33333333%;\r\n}\r\n.col-xs-offset-6 {\r\n  margin-left: 50%;\r\n}\r\n.col-xs-offset-5 {\r\n  margin-left: 41.66666667%;\r\n}\r\n.col-xs-offset-4 {\r\n  margin-left: 33.33333333%;\r\n}\r\n.col-xs-offset-3 {\r\n  margin-left: 25%;\r\n}\r\n.col-xs-offset-2 {\r\n  margin-left: 16.66666667%;\r\n}\r\n.col-xs-offset-1 {\r\n  margin-left: 8.33333333%;\r\n}\r\n.col-xs-offset-0 {\r\n  margin-left: 0%;\r\n}\r\n@media (min-width: 768px) {\r\n  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {\r\n    float: left;\r\n  }\r\n  .col-sm-12 {\r\n    width: 100%;\r\n  }\r\n  .col-sm-11 {\r\n    width: 91.66666667%;\r\n  }\r\n  .col-sm-10 {\r\n    width: 83.33333333%;\r\n  }\r\n  .col-sm-9 {\r\n    width: 75%;\r\n  }\r\n  .col-sm-8 {\r\n    width: 66.66666667%;\r\n  }\r\n  .col-sm-7 {\r\n    width: 58.33333333%;\r\n  }\r\n  .col-sm-6 {\r\n    width: 50%;\r\n  }\r\n  .col-sm-5 {\r\n    width: 41.66666667%;\r\n  }\r\n  .col-sm-4 {\r\n    width: 33.33333333%;\r\n  }\r\n  .col-sm-3 {\r\n    width: 25%;\r\n  }\r\n  .col-sm-2 {\r\n    width: 16.66666667%;\r\n  }\r\n  .col-sm-1 {\r\n    width: 8.33333333%;\r\n  }\r\n  .col-sm-pull-12 {\r\n    right: 100%;\r\n  }\r\n  .col-sm-pull-11 {\r\n    right: 91.66666667%;\r\n  }\r\n  .col-sm-pull-10 {\r\n    right: 83.33333333%;\r\n  }\r\n  .col-sm-pull-9 {\r\n    right: 75%;\r\n  }\r\n  .col-sm-pull-8 {\r\n    right: 66.66666667%;\r\n  }\r\n  .col-sm-pull-7 {\r\n    right: 58.33333333%;\r\n  }\r\n  .col-sm-pull-6 {\r\n    right: 50%;\r\n  }\r\n  .col-sm-pull-5 {\r\n    right: 41.66666667%;\r\n  }\r\n  .col-sm-pull-4 {\r\n    right: 33.33333333%;\r\n  }\r\n  .col-sm-pull-3 {\r\n    right: 25%;\r\n  }\r\n  .col-sm-pull-2 {\r\n    right: 16.66666667%;\r\n  }\r\n  .col-sm-pull-1 {\r\n    right: 8.33333333%;\r\n  }\r\n  .col-sm-pull-0 {\r\n    right: auto;\r\n  }\r\n  .col-sm-push-12 {\r\n    left: 100%;\r\n  }\r\n  .col-sm-push-11 {\r\n    left: 91.66666667%;\r\n  }\r\n  .col-sm-push-10 {\r\n    left: 83.33333333%;\r\n  }\r\n  .col-sm-push-9 {\r\n    left: 75%;\r\n  }\r\n  .col-sm-push-8 {\r\n    left: 66.66666667%;\r\n  }\r\n  .col-sm-push-7 {\r\n    left: 58.33333333%;\r\n  }\r\n  .col-sm-push-6 {\r\n    left: 50%;\r\n  }\r\n  .col-sm-push-5 {\r\n    left: 41.66666667%;\r\n  }\r\n  .col-sm-push-4 {\r\n    left: 33.33333333%;\r\n  }\r\n  .col-sm-push-3 {\r\n    left: 25%;\r\n  }\r\n  .col-sm-push-2 {\r\n    left: 16.66666667%;\r\n  }\r\n  .col-sm-push-1 {\r\n    left: 8.33333333%;\r\n  }\r\n  .col-sm-push-0 {\r\n    left: auto;\r\n  }\r\n  .col-sm-offset-12 {\r\n    margin-left: 100%;\r\n  }\r\n  .col-sm-offset-11 {\r\n    margin-left: 91.66666667%;\r\n  }\r\n  .col-sm-offset-10 {\r\n    margin-left: 83.33333333%;\r\n  }\r\n  .col-sm-offset-9 {\r\n    margin-left: 75%;\r\n  }\r\n  .col-sm-offset-8 {\r\n    margin-left: 66.66666667%;\r\n  }\r\n  .col-sm-offset-7 {\r\n    margin-left: 58.33333333%;\r\n  }\r\n  .col-sm-offset-6 {\r\n    margin-left: 50%;\r\n  }\r\n  .col-sm-offset-5 {\r\n    margin-left: 41.66666667%;\r\n  }\r\n  .col-sm-offset-4 {\r\n    margin-left: 33.33333333%;\r\n  }\r\n  .col-sm-offset-3 {\r\n    margin-left: 25%;\r\n  }\r\n  .col-sm-offset-2 {\r\n    margin-left: 16.66666667%;\r\n  }\r\n  .col-sm-offset-1 {\r\n    margin-left: 8.33333333%;\r\n  }\r\n  .col-sm-offset-0 {\r\n    margin-left: 0%;\r\n  }\r\n}\r\n@media (min-width: 992px) {\r\n  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {\r\n    float: left;\r\n  }\r\n  .col-md-12 {\r\n    width: 100%;\r\n  }\r\n  .col-md-11 {\r\n    width: 91.66666667%;\r\n  }\r\n  .col-md-10 {\r\n    width: 83.33333333%;\r\n  }\r\n  .col-md-9 {\r\n    width: 75%;\r\n  }\r\n  .col-md-8 {\r\n    width: 66.66666667%;\r\n  }\r\n  .col-md-7 {\r\n    width: 58.33333333%;\r\n  }\r\n  .col-md-6 {\r\n    width: 50%;\r\n  }\r\n  .col-md-5 {\r\n    width: 41.66666667%;\r\n  }\r\n  .col-md-4 {\r\n    width: 33.33333333%;\r\n  }\r\n  .col-md-3 {\r\n    width: 25%;\r\n  }\r\n  .col-md-2 {\r\n    width: 16.66666667%;\r\n  }\r\n  .col-md-1 {\r\n    width: 8.33333333%;\r\n  }\r\n  .col-md-pull-12 {\r\n    right: 100%;\r\n  }\r\n  .col-md-pull-11 {\r\n    right: 91.66666667%;\r\n  }\r\n  .col-md-pull-10 {\r\n    right: 83.33333333%;\r\n  }\r\n  .col-md-pull-9 {\r\n    right: 75%;\r\n  }\r\n  .col-md-pull-8 {\r\n    right: 66.66666667%;\r\n  }\r\n  .col-md-pull-7 {\r\n    right: 58.33333333%;\r\n  }\r\n  .col-md-pull-6 {\r\n    right: 50%;\r\n  }\r\n  .col-md-pull-5 {\r\n    right: 41.66666667%;\r\n  }\r\n  .col-md-pull-4 {\r\n    right: 33.33333333%;\r\n  }\r\n  .col-md-pull-3 {\r\n    right: 25%;\r\n  }\r\n  .col-md-pull-2 {\r\n    right: 16.66666667%;\r\n  }\r\n  .col-md-pull-1 {\r\n    right: 8.33333333%;\r\n  }\r\n  .col-md-pull-0 {\r\n    right: auto;\r\n  }\r\n  .col-md-push-12 {\r\n    left: 100%;\r\n  }\r\n  .col-md-push-11 {\r\n    left: 91.66666667%;\r\n  }\r\n  .col-md-push-10 {\r\n    left: 83.33333333%;\r\n  }\r\n  .col-md-push-9 {\r\n    left: 75%;\r\n  }\r\n  .col-md-push-8 {\r\n    left: 66.66666667%;\r\n  }\r\n  .col-md-push-7 {\r\n    left: 58.33333333%;\r\n  }\r\n  .col-md-push-6 {\r\n    left: 50%;\r\n  }\r\n  .col-md-push-5 {\r\n    left: 41.66666667%;\r\n  }\r\n  .col-md-push-4 {\r\n    left: 33.33333333%;\r\n  }\r\n  .col-md-push-3 {\r\n    left: 25%;\r\n  }\r\n  .col-md-push-2 {\r\n    left: 16.66666667%;\r\n  }\r\n  .col-md-push-1 {\r\n    left: 8.33333333%;\r\n  }\r\n  .col-md-push-0 {\r\n    left: auto;\r\n  }\r\n  .col-md-offset-12 {\r\n    margin-left: 100%;\r\n  }\r\n  .col-md-offset-11 {\r\n    margin-left: 91.66666667%;\r\n  }\r\n  .col-md-offset-10 {\r\n    margin-left: 83.33333333%;\r\n  }\r\n  .col-md-offset-9 {\r\n    margin-left: 75%;\r\n  }\r\n  .col-md-offset-8 {\r\n    margin-left: 66.66666667%;\r\n  }\r\n  .col-md-offset-7 {\r\n    margin-left: 58.33333333%;\r\n  }\r\n  .col-md-offset-6 {\r\n    margin-left: 50%;\r\n  }\r\n  .col-md-offset-5 {\r\n    margin-left: 41.66666667%;\r\n  }\r\n  .col-md-offset-4 {\r\n    margin-left: 33.33333333%;\r\n  }\r\n  .col-md-offset-3 {\r\n    margin-left: 25%;\r\n  }\r\n  .col-md-offset-2 {\r\n    margin-left: 16.66666667%;\r\n  }\r\n  .col-md-offset-1 {\r\n    margin-left: 8.33333333%;\r\n  }\r\n  .col-md-offset-0 {\r\n    margin-left: 0%;\r\n  }\r\n}\r\n@media (min-width: 1200px) {\r\n  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {\r\n    float: left;\r\n  }\r\n  .col-lg-12 {\r\n    width: 100%;\r\n  }\r\n  .col-lg-11 {\r\n    width: 91.66666667%;\r\n  }\r\n  .col-lg-10 {\r\n    width: 83.33333333%;\r\n  }\r\n  .col-lg-9 {\r\n    width: 75%;\r\n  }\r\n  .col-lg-8 {\r\n    width: 66.66666667%;\r\n  }\r\n  .col-lg-7 {\r\n    width: 58.33333333%;\r\n  }\r\n  .col-lg-6 {\r\n    width: 50%;\r\n  }\r\n  .col-lg-5 {\r\n    width: 41.66666667%;\r\n  }\r\n  .col-lg-4 {\r\n    width: 33.33333333%;\r\n  }\r\n  .col-lg-3 {\r\n    width: 25%;\r\n  }\r\n  .col-lg-2 {\r\n    width: 16.66666667%;\r\n  }\r\n  .col-lg-1 {\r\n    width: 8.33333333%;\r\n  }\r\n  .col-lg-pull-12 {\r\n    right: 100%;\r\n  }\r\n  .col-lg-pull-11 {\r\n    right: 91.66666667%;\r\n  }\r\n  .col-lg-pull-10 {\r\n    right: 83.33333333%;\r\n  }\r\n  .col-lg-pull-9 {\r\n    right: 75%;\r\n  }\r\n  .col-lg-pull-8 {\r\n    right: 66.66666667%;\r\n  }\r\n  .col-lg-pull-7 {\r\n    right: 58.33333333%;\r\n  }\r\n  .col-lg-pull-6 {\r\n    right: 50%;\r\n  }\r\n  .col-lg-pull-5 {\r\n    right: 41.66666667%;\r\n  }\r\n  .col-lg-pull-4 {\r\n    right: 33.33333333%;\r\n  }\r\n  .col-lg-pull-3 {\r\n    right: 25%;\r\n  }\r\n  .col-lg-pull-2 {\r\n    right: 16.66666667%;\r\n  }\r\n  .col-lg-pull-1 {\r\n    right: 8.33333333%;\r\n  }\r\n  .col-lg-pull-0 {\r\n    right: auto;\r\n  }\r\n  .col-lg-push-12 {\r\n    left: 100%;\r\n  }\r\n  .col-lg-push-11 {\r\n    left: 91.66666667%;\r\n  }\r\n  .col-lg-push-10 {\r\n    left: 83.33333333%;\r\n  }\r\n  .col-lg-push-9 {\r\n    left: 75%;\r\n  }\r\n  .col-lg-push-8 {\r\n    left: 66.66666667%;\r\n  }\r\n  .col-lg-push-7 {\r\n    left: 58.33333333%;\r\n  }\r\n  .col-lg-push-6 {\r\n    left: 50%;\r\n  }\r\n  .col-lg-push-5 {\r\n    left: 41.66666667%;\r\n  }\r\n  .col-lg-push-4 {\r\n    left: 33.33333333%;\r\n  }\r\n  .col-lg-push-3 {\r\n    left: 25%;\r\n  }\r\n  .col-lg-push-2 {\r\n    left: 16.66666667%;\r\n  }\r\n  .col-lg-push-1 {\r\n    left: 8.33333333%;\r\n  }\r\n  .col-lg-push-0 {\r\n    left: auto;\r\n  }\r\n  .col-lg-offset-12 {\r\n    margin-left: 100%;\r\n  }\r\n  .col-lg-offset-11 {\r\n    margin-left: 91.66666667%;\r\n  }\r\n  .col-lg-offset-10 {\r\n    margin-left: 83.33333333%;\r\n  }\r\n  .col-lg-offset-9 {\r\n    margin-left: 75%;\r\n  }\r\n  .col-lg-offset-8 {\r\n    margin-left: 66.66666667%;\r\n  }\r\n  .col-lg-offset-7 {\r\n    margin-left: 58.33333333%;\r\n  }\r\n  .col-lg-offset-6 {\r\n    margin-left: 50%;\r\n  }\r\n  .col-lg-offset-5 {\r\n    margin-left: 41.66666667%;\r\n  }\r\n  .col-lg-offset-4 {\r\n    margin-left: 33.33333333%;\r\n  }\r\n  .col-lg-offset-3 {\r\n    margin-left: 25%;\r\n  }\r\n  .col-lg-offset-2 {\r\n    margin-left: 16.66666667%;\r\n  }\r\n  .col-lg-offset-1 {\r\n    margin-left: 8.33333333%;\r\n  }\r\n  .col-lg-offset-0 {\r\n    margin-left: 0%;\r\n  }\r\n}", "/* Magnific Popup CSS */\r\n.mfp-bg {\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1042;\r\n  overflow: hidden;\r\n  position: fixed;\r\n  background: #0b0b0b;\r\n  opacity: 0.8; }\r\n\r\n.mfp-wrap {\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1043;\r\n  position: fixed;\r\n  outline: none !important;\r\n  -webkit-backface-visibility: hidden; }\r\n\r\n.mfp-container {\r\n  text-align: center;\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  left: 0;\r\n  top: 0;\r\n  padding: 0 8px;\r\n  box-sizing: border-box; }\r\n\r\n.mfp-container:before {\r\n  content: '';\r\n  display: inline-block;\r\n  height: 100%;\r\n  vertical-align: middle; }\r\n\r\n.mfp-align-top .mfp-container:before {\r\n  display: none; }\r\n\r\n.mfp-content {\r\n  position: relative;\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  margin: 0 auto;\r\n  text-align: left;\r\n  z-index: 1045; }\r\n\r\n.mfp-inline-holder .mfp-content,\r\n.mfp-ajax-holder .mfp-content {\r\n  width: 100%;\r\n  cursor: auto; }\r\n\r\n.mfp-ajax-cur {\r\n  cursor: progress; }\r\n\r\n.mfp-zoom-out-cur, .mfp-zoom-out-cur .mfp-image-holder .mfp-close {\r\n  cursor: -moz-zoom-out;\r\n  cursor: -webkit-zoom-out;\r\n  cursor: zoom-out; }\r\n\r\n.mfp-zoom {\r\n  cursor: pointer;\r\n  cursor: -webkit-zoom-in;\r\n  cursor: -moz-zoom-in;\r\n  cursor: zoom-in; }\r\n\r\n.mfp-auto-cursor .mfp-content {\r\n  cursor: auto; }\r\n\r\n.mfp-close,\r\n.mfp-arrow,\r\n.mfp-preloader,\r\n.mfp-counter {\r\n  -webkit-user-select: none;\r\n  -moz-user-select: none;\r\n  user-select: none; }\r\n\r\n.mfp-loading.mfp-figure {\r\n  display: none; }\r\n\r\n.mfp-hide {\r\n  display: none !important; }\r\n\r\n.mfp-preloader {\r\n  color: #CCC;\r\n  position: absolute;\r\n  top: 50%;\r\n  width: auto;\r\n  text-align: center;\r\n  margin-top: -0.8em;\r\n  left: 8px;\r\n  right: 8px;\r\n  z-index: 1044; }\r\n  .mfp-preloader a {\r\n    color: #CCC; }\r\n    .mfp-preloader a:hover {\r\n      color: #FFF; }\r\n\r\n.mfp-s-ready .mfp-preloader {\r\n  display: none; }\r\n\r\n.mfp-s-error .mfp-content {\r\n  display: none; }\r\n\r\nbutton.mfp-close,\r\nbutton.mfp-arrow {\r\n  overflow: visible;\r\n  cursor: pointer;\r\n  background: transparent;\r\n  border: 0;\r\n  -webkit-appearance: none;\r\n  display: block;\r\n  outline: none;\r\n  padding: 0;\r\n  z-index: 1046;\r\n  box-shadow: none;\r\n  touch-action: manipulation; }\r\n\r\nbutton::-moz-focus-inner {\r\n  padding: 0;\r\n  border: 0; }\r\n\r\n.mfp-close {\r\n  width: 44px;\r\n  height: 44px;\r\n  line-height: 44px;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  text-decoration: none;\r\n  text-align: center;\r\n  opacity: 0.65;\r\n  padding: 0 0 18px 10px;\r\n  color: #FFF;\r\n  font-style: normal;\r\n  font-size: 28px;\r\n  font-family: Arial, Baskerville, monospace; }\r\n  .mfp-close:hover,\r\n  .mfp-close:focus {\r\n    opacity: 1; }\r\n  .mfp-close:active {\r\n    top: 1px; }\r\n\r\n.mfp-close-btn-in .mfp-close {\r\n  color: #333; }\r\n\r\n.mfp-image-holder .mfp-close,\r\n.mfp-iframe-holder .mfp-close {\r\n  color: #FFF;\r\n  right: -6px;\r\n  text-align: right;\r\n  padding-right: 6px;\r\n  width: 100%; }\r\n\r\n.mfp-counter {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  color: #CCC;\r\n  font-size: 12px;\r\n  line-height: 18px;\r\n  white-space: nowrap; }\r\n\r\n.mfp-arrow {\r\n  position: absolute;\r\n  opacity: 0.65;\r\n  margin: 0;\r\n  top: 50%;\r\n  margin-top: -55px;\r\n  padding: 0;\r\n  width: 90px;\r\n  height: 110px;\r\n  -webkit-tap-highlight-color: transparent; }\r\n  .mfp-arrow:active {\r\n    margin-top: -54px; }\r\n  .mfp-arrow:hover,\r\n  .mfp-arrow:focus {\r\n    opacity: 1; }\r\n  .mfp-arrow:before,\r\n  .mfp-arrow:after {\r\n    content: '';\r\n    display: block;\r\n    width: 0;\r\n    height: 0;\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0;\r\n    margin-top: 35px;\r\n    margin-left: 35px;\r\n    border: medium inset transparent; }\r\n  .mfp-arrow:after {\r\n    border-top-width: 13px;\r\n    border-bottom-width: 13px;\r\n    top: 8px; }\r\n  .mfp-arrow:before {\r\n    border-top-width: 21px;\r\n    border-bottom-width: 21px;\r\n    opacity: 0.7; }\r\n\r\n.mfp-arrow-left {\r\n  left: 0; }\r\n  .mfp-arrow-left:after {\r\n    border-right: 17px solid #FFF;\r\n    margin-left: 31px; }\r\n  .mfp-arrow-left:before {\r\n    margin-left: 25px;\r\n    border-right: 27px solid #3F3F3F; }\r\n\r\n.mfp-arrow-right {\r\n  right: 0; }\r\n  .mfp-arrow-right:after {\r\n    border-left: 17px solid #FFF;\r\n    margin-left: 39px; }\r\n  .mfp-arrow-right:before {\r\n    border-left: 27px solid #3F3F3F; }\r\n\r\n.mfp-iframe-holder {\r\n  padding-top: 40px;\r\n  padding-bottom: 40px; }\r\n  .mfp-iframe-holder .mfp-content {\r\n    line-height: 0;\r\n    width: 100%;\r\n    max-width: 900px; }\r\n  .mfp-iframe-holder .mfp-close {\r\n    top: -40px; }\r\n\r\n.mfp-iframe-scaler {\r\n  width: 100%;\r\n  height: 0;\r\n  overflow: hidden;\r\n  padding-top: 56.25%; }\r\n  .mfp-iframe-scaler iframe {\r\n    position: absolute;\r\n    display: block;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);\r\n    background: #000; }\r\n\r\n/* Main image in popup */\r\nimg.mfp-img {\r\n  width: auto;\r\n  max-width: 100%;\r\n  height: auto;\r\n  display: block;\r\n  line-height: 0;\r\n  box-sizing: border-box;\r\n  padding: 40px 0 40px;\r\n  margin: 0 auto; }\r\n\r\n/* The shadow behind the image */\r\n.mfp-figure {\r\n  line-height: 0; }\r\n  .mfp-figure:after {\r\n    content: '';\r\n    position: absolute;\r\n    left: 0;\r\n    top: 40px;\r\n    bottom: 40px;\r\n    display: block;\r\n    right: 0;\r\n    width: auto;\r\n    height: auto;\r\n    z-index: -1;\r\n    box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);\r\n    background: #444; }\r\n  .mfp-figure small {\r\n    color: #BDBDBD;\r\n    display: block;\r\n    font-size: 12px;\r\n    line-height: 14px; }\r\n  .mfp-figure figure {\r\n    margin: 0; }\r\n\r\n.mfp-bottom-bar {\r\n  margin-top: -36px;\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  width: 100%;\r\n  cursor: auto; }\r\n\r\n.mfp-title {\r\n  text-align: left;\r\n  line-height: 18px;\r\n  color: #F3F3F3;\r\n  word-wrap: break-word;\r\n  padding-right: 36px; }\r\n\r\n.mfp-image-holder .mfp-content {\r\n  max-width: 100%; }\r\n\r\n.mfp-gallery .mfp-image-holder .mfp-figure {\r\n  cursor: pointer; }\r\n\r\n@media screen and (max-width: 800px) and (orientation: landscape), screen and (max-height: 300px) {\r\n  /**\r\n       * Remove all paddings around the image on small screen\r\n       */\r\n  .mfp-img-mobile .mfp-image-holder {\r\n    padding-left: 0;\r\n    padding-right: 0; }\r\n  .mfp-img-mobile img.mfp-img {\r\n    padding: 0; }\r\n  .mfp-img-mobile .mfp-figure:after {\r\n    top: 0;\r\n    bottom: 0; }\r\n  .mfp-img-mobile .mfp-figure small {\r\n    display: inline;\r\n    margin-left: 5px; }\r\n  .mfp-img-mobile .mfp-bottom-bar {\r\n    background: rgba(0, 0, 0, 0.6);\r\n    bottom: 0;\r\n    margin: 0;\r\n    top: auto;\r\n    padding: 3px 5px;\r\n    position: fixed;\r\n    box-sizing: border-box; }\r\n    .mfp-img-mobile .mfp-bottom-bar:empty {\r\n      padding: 0; }\r\n  .mfp-img-mobile .mfp-counter {\r\n    right: 5px;\r\n    top: 3px; }\r\n  .mfp-img-mobile .mfp-close {\r\n    top: 0;\r\n    right: 0;\r\n    width: 35px;\r\n    height: 35px;\r\n    line-height: 35px;\r\n    background: rgba(0, 0, 0, 0.6);\r\n    position: fixed;\r\n    text-align: center;\r\n    padding: 0; } }\r\n\r\n@media all and (max-width: 900px) {\r\n  .mfp-arrow {\r\n    -webkit-transform: scale(0.75);\r\n    transform: scale(0.75); }\r\n  .mfp-arrow-left {\r\n    -webkit-transform-origin: 0;\r\n    transform-origin: 0; }\r\n  .mfp-arrow-right {\r\n    -webkit-transform-origin: 100%;\r\n    transform-origin: 100%; }\r\n  .mfp-container {\r\n    padding-left: 6px;\r\n    padding-right: 6px; } }\r\n", "// nastavení a proměnné\r\n@import \"base/variables\";\r\n\r\n// externí knihovny\r\n  // normalize ( https://necolas.github.io/normalize.css/ )\r\n  @import \"libs/normalize\";\r\n  // bootstrap grid\r\n  @import \"libs/bootstrap-grid\";\r\n  // lightbox + animace ( https://github.com/dimsemenov/Magnific-Popup )\r\n  @import \"libs/magnific-popup\";\r\n  .mfp-fade.mfp-bg { opacity: 0; transition: all 0.15s ease-out; }\r\n  .mfp-fade.mfp-bg.mfp-ready { opacity: 0.8; }\r\n  .mfp-fade.mfp-bg.mfp-removing { opacity: 0; }\r\n  .mfp-fade.mfp-wrap .mfp-content { opacity: 0; transition: all 0.15s ease-out; }\r\n  .mfp-fade.mfp-wrap.mfp-ready .mfp-content { opacity: 1; }\r\n  .mfp-fade.mfp-wrap.mfp-removing .mfp-content { opacity: 0; }\r\n  // slider Slick ( http://kenwheeler.github.io/slick/ )\r\n  @import \"../js/libs/slick/slick.scss\";\r\n  // našeptávač ( https://github.com/devbridge/jQuery-Autocomplete )\r\n  @import \"components/autocomplete\";\r\n\r\n// základní nastavení a vlastnosti\r\n  // základní nastavení dokumentu\r\n  @import \"base/base\";\r\n  // layout, mřížka\r\n  @import \"base/layout\";\r\n  // webové fonty\r\n  @import \"base/fonts\";\r\n  // základní definice typografie\r\n  @import \"base/form\";\r\n  // SVG ikony\r\n  @import \"icons/icon.scss\";\r\n\r\n// hlavička\r\n@import \"components/header\";\r\n\r\n// navigace\r\n@import \"components/nav\";\r\n  // produktová navigace\r\n  @import \"components/nav-product\";\r\n\r\n// obsah\r\n@import \"components/content\";\r\n  // obsahová část, základní vzhled\r\n  @import \"components/article\";\r\n\r\n// patička\r\n@import \"components/footer\";\r\n\r\n// komponenty\r\n  // chybové hlášky\r\n  @import \"components/alert\";\r\n  // odkaz na košík\r\n  @import \"components/basket\";\r\n  // tlačítka\r\n  @import \"components/btn\";\r\n  // drobečková navigace\r\n  @import \"components/breadcrumb\";\r\n  // kategorie produktů\r\n  @import \"components/category\";\r\n  // komentáře\r\n  @import \"components/comments\";\r\n  // hlavička nad obsahem\r\n  @import \"components/content-header\";\r\n  // ovládací prvky\r\n  @import \"components/control\";\r\n  // filtry zboží\r\n  @import \"components/filter\";\r\n  // úvodní strana\r\n  @import \"components/home\";\r\n  // přihlášení uživatele\r\n  @import \"components/login\";\r\n  // štítky\r\n  @import \"components/label\";\r\n  // modální okna\r\n  @import \"components/modal\";\r\n  // novinky\r\n  @import \"components/news\";\r\n  // stránkování\r\n  @import \"components/pagination\";\r\n  // hledání v hlavičce\r\n  @import \"components/search\";\r\n  // sdílení na sociálních sítích\r\n  @import \"components/share\";\r\n  // úvodní slider\r\n  @import \"components/slider\";\r\n  // skladová dostupnost\r\n  @import \"components/stock\";\r\n  // uživatelské strany\r\n  @import \"components/user\";\r\n  // kde nakoupíte\r\n  @import \"components/where\";\r\n  // tabulka\r\n  @import \"components/table\";\r\n  // záložky\r\n  @import \"components/tabs\";\r\n\r\n// produkty\r\n  // výpis produktů\r\n  @import \"components/product\";\r\n  // detail produktu\r\n  @import \"components/product-detail\";\r\n\r\n// objednávkový proces\r\n@import \"components/order\";\r\n  // postup objednávky\r\n  @import \"components/order-progress\";\r\n  // tělo objednávky\r\n  @import \"components/order-content\";\r\n  // doplňující informace k objednávce\r\n  @import \"components/order-info\";\r\n  // ovládací tlačítka objednávky\r\n  @import \"components/order-controls\";\r\n  // dopravy a platby\r\n  @import \"components/order-delivery\";\r\n\r\n// pomocné třídy\r\n@import \"base/helpers\";\r\n\r\n// tisková verze\r\n@import \"base/print\";\r\n", "/* Slider */\r\n\r\n.slick-slider {\r\n    position: relative;\r\n    display: block;\r\n    box-sizing: border-box;\r\n    -webkit-touch-callout: none;\r\n    -webkit-user-select: none;\r\n    -khtml-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -ms-touch-action: pan-y;\r\n    touch-action: pan-y;\r\n    -webkit-tap-highlight-color: transparent;\r\n}\r\n.slick-list {\r\n    position: relative;\r\n    overflow: hidden;\r\n    display: block;\r\n    margin: 0;\r\n    padding: 0;\r\n\r\n    &:focus {\r\n        outline: none;\r\n    }\r\n\r\n    &.dragging {\r\n        cursor: pointer;\r\n        cursor: hand;\r\n    }\r\n}\r\n.slick-slider .slick-track,\r\n.slick-slider .slick-list {\r\n    -webkit-transform: translate3d(0, 0, 0);\r\n    -moz-transform: translate3d(0, 0, 0);\r\n    -ms-transform: translate3d(0, 0, 0);\r\n    -o-transform: translate3d(0, 0, 0);\r\n    transform: translate3d(0, 0, 0);\r\n}\r\n\r\n.slick-track {\r\n    position: relative;\r\n    left: 0;\r\n    top: 0;\r\n    display: block;\r\n\r\n    &:before,\r\n    &:after {\r\n        content: \"\";\r\n        display: table;\r\n    }\r\n\r\n    &:after {\r\n        clear: both;\r\n    }\r\n\r\n    .slick-loading & {\r\n        visibility: hidden;\r\n    }\r\n}\r\n.slick-slide {\r\n    float: left;\r\n    height: 100%;\r\n    min-height: 1px;\r\n    [dir=\"rtl\"] & {\r\n        float: right;\r\n    }\r\n    img {\r\n        display: block;\r\n    }\r\n    &.slick-loading img {\r\n        display: none;\r\n    }\r\n\r\n    display: none;\r\n\r\n    &.dragging img {\r\n        pointer-events: none;\r\n    }\r\n\r\n    .slick-initialized & {\r\n        display: block;\r\n    }\r\n\r\n    .slick-loading & {\r\n        visibility: hidden;\r\n    }\r\n\r\n    .slick-vertical & {\r\n        display: block;\r\n        height: auto;\r\n        border: 1px solid transparent;\r\n    }\r\n}\r\n.slick-arrow.slick-hidden {\r\n    display: none;\r\n}\r\n", "// naš<PERSON><PERSON><PERSON><PERSON> ( https://github.com/devbridge/jQuery-Autocomplete )\r\n.postcode_ac_name {\n  display: inline-block;\r\n\n  margin-left: 10px;\r\n\n  color: $color_main;\r\n  font-weight: 700;\r\n}\r\n\r\n.autocomplete-suggestions {\n  overflow: auto;\r\n\n  border: 1px solid $color_main;\r\n  background: $color_white;\r\n}\r\n\r\n.autocomplete-suggestion {\n  overflow: hidden;\r\n\n  padding: 7px 8px;\r\n\n  white-space: nowrap;\r\n}\r\n\r\n.autocomplete-selected {\n  background: lighten($color_main_light, 40%);\r\n}\r\n\r\n.autocomplete-suggestions strong {\n  color: $color_secondary;\r\n  font-weight: normal;\r\n}\r\n\r\n.autocomplete-group {\n  padding: 2px 5px;\r\n}\r\n\r\n.autocomplete-group strong {\n  display: block;\r\n\n  border-bottom: 1px solid $color_black;\r\n}\r\n", "// barvy\r\n\r\n  // základní barvy\r\n  $color_white: #fff;\r\n  $color_black: #2e2f32;\r\n\r\n  // šedá barva\r\n  $color_gray: #666;\r\n  $color_gray_dark: #333;\r\n  $color_gray_light: #999;\r\n\r\n  // hlavní barvy projektu\r\n  $color_main: #684f40;\r\n  $color_main_dark: darken( $color_main, 15% );\r\n  $color_main_light: lighten( $color_main, 15% );\r\n\r\n  // sekundrání barvy projektu\r\n  $color_secondary: #fdb822;\r\n  $color_secondary_dark: #e08700;\r\n\r\n  // rozšířené barvy projektu\r\n  $color_price: #cc0000;\r\n  $color_buy: $color_secondary_dark;\r\n\r\n  // barvy pro štítky\r\n  $color_label_action: #cc0000;\r\n  $color_label_tip: #e08700;\r\n  $color_label_discount: #e08700;\r\n  $color_label_new: #e08700;\r\n  $color_label_delivery: #e08700;\r\n\r\n  // barvy pro chybové hl<PERSON>ky\r\n  $color_success: #5cb85c;\r\n  $color_info: $color_main_light;\r\n  $color_danger: #d9534f;\r\n\r\n// zakulacen<PERSON> rohy\r\n$radius: 0;\r\n$radius_button: 5px;\r\n\r\n// breakpointy\r\n\r\n  // základn<PERSON> p<PERSON>ty z Bootstrap Gridu\r\n  $mqxs: 576px;     // small\r\n  $mqsm: 992px;     // medium\r\n  $mqmd: 992px;     // large\r\n  $mqlg: 1200px;    // extra large\r\n  // rozšířené\r\n  $mqxxs: 419px;    // extra small\r\n  $mqxlg: 1380px;   // max large\r\n  $mq_menu: 992px;  // respo menu\r\n  $mq_modal: 700px; // modal okno\r\n\r\n// z-index\r\n$index_base: 1;\r\n$index_page: 10;\r\n$index_menu: 100;\r\n$index_modal: 1000;\r\n", "// box sizing\r\nhtml {\n  box-sizing: border-box;\r\n}\r\n\r\n*,\n*:before,\n*:after {\n  box-sizing: inherit;\r\n}\r\n\r\nimg {\n  box-sizing: content-box;\r\n}\r\n\r\n// nastavení velikosti písma\r\n:root {\n  font-size: 100%;\r\n}\r\n\r\n// základní definice\r\nbody {\n  position: relative;\r\n\n  margin: 0;\r\n\n  color: $color_black;\r\n  font-family: Lora, serif;\r\n  font-size: 16px;\r\n  line-height: 1;\r\n\n  background-color: $color_white;\r\n}\r\n\r\n// základní nastavení respo\r\n@-ms-viewport {\n  width: device-width;\r\n}\r\n\r\nimg,\nsvg {\n  display: inline-block;\r\n\n  max-width: 100%;\r\n  height: auto;\r\n\n  border: none;\r\n}\r\n\r\niframe {\n  max-width: 100%;\r\n}\r\n", "// maxim<PERSON><PERSON><PERSON> str<PERSON>\r\n.container-fluid {\n  max-width: $mqlg;\r\n}\r\n\r\n// cleafix na řádek\r\n.row:before,\n.row:after {\n  content: ' ';\r\n\n  display: table;\r\n}\r\n\r\n.row:after {\n  clear: both;\r\n}\r\n\r\n.row {\n  *zoom: 1;\r\n}\r\n\r\n// zarovnání v boxu\r\n.col-center {\n  text-align: center;\r\n}\r\n\r\n.col-left {\n  text-align: left;\r\n}\r\n\r\n.col-right {\n  text-align: right;\r\n}\r\n\r\n// XS sloupec bude na nižších rozliš<PERSON>ích v<PERSON>dy 100%\r\n.col-xs-1,\n.col-xs-2,\n.col-xs-3,\n.col-xs-4,\n.col-xs-5,\n.col-xs-6,\n.col-xs-7,\n.col-xs-8,\n.col-xs-9,\n.col-xs-10,\n.col-xs-11,\n.col-xs-12 {\n  @media (max-width: $mqxxs) {\n    width: 100%;\r\n  }\n}\r\n\r\n// stejn<PERSON> vysoké boxy\r\n.row--flex {\n  display: flex;\r\n  flex-wrap: wrap;\r\n\r\n  // fix pro safari\r\n  &:before,\n  &:after {\n    content: normal;\r\n  }\n}\r\n\r\n.row--flex > [class*='col-'] {\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n// vnořený div, nechceme mínusové marginy\r\n.row--inner {\n  margin: 0;\r\n}\r\n\r\n// okraj okolo celých boxů, pouze od vyššího rozlišení\r\n.row--border {\n  @media (min-width: $mqxxs) {\n    border-top: 1px dotted lighten($color_main, 50%);\r\n    border-left: 1px dotted lighten($color_main, 50%);\r\n\r\n    & > div {\n      border-right: 1px dotted lighten($color_main, 50%);\r\n      border-bottom: 1px dotted lighten($color_main, 50%);\r\n\r\n      // hover efekt vypočítáme z hlavní barvy\r\n      &:hover,\n      &:focus {\n        z-index: 100;\r\n\n        box-shadow: 0 0 10px fade($color_main, 40%);\r\n      }\n    }\n  }\n}\r\n\r\n// autozarovnávač boxů\r\n.row--autoclear {\n  @media (min-width: 1200px) {\n    .col-lg-1:nth-child(12n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-lg-2:nth-child(6n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-lg-3:nth-child(4n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-lg-4:nth-child(3n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-lg-6:nth-child(odd) {\n      clear: left;\r\n    }\n  }\r\n\r\n  @media (min-width: 992px) and (max-width: 1199px) {\n    .col-md-1:nth-child(12n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-md-2:nth-child(6n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-md-3:nth-child(4n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-md-4:nth-child(3n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-md-6:nth-child(odd) {\n      clear: left;\r\n    }\n  }\r\n\r\n  @media (min-width: 768px) and (max-width: 991px) {\n    .col-sm-1:nth-child(12n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-sm-2:nth-child(6n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-sm-3:nth-child(4n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-sm-4:nth-child(3n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-sm-6:nth-child(odd) {\n      clear: left;\r\n    }\n  }\r\n\r\n  @media (max-width: 767px) {\n    .col-xs-1:nth-child(12n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-xs-2:nth-child(6n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-xs-3:nth-child(4n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-xs-4:nth-child(3n+1) {\n      clear: left;\r\n    }\r\n\r\n    .col-xs-6:nth-child(odd) {\n      clear: left;\r\n    }\n  }\n}\r\n\r\n// fix gridu pro IE8\r\n.lt-ie9 {\n  .container,\n  .container-fluid {\n    display: table;\r\n\n    width: 100%;\r\n  }\r\n\r\n  .row {\n    display: table-row;\r\n\n    height: 100%;\r\n  }\r\n\r\n  [class^=col-] {\n    display: table-cell;\r\n  }\n}\r\n", "/* lora-regular - latin-ext_latin */\r\n@font-face {\r\n  font-family: 'Lora';\r\n  font-style: normal;\r\n  font-weight: 400;\r\n  font-display: swap;\r\n  src: url('../fonts/lora-v16-latin-ext_latin-regular.eot'); /* IE9 Compat Modes */\r\n  src: local(''),\r\n       url('../fonts/lora-v16-latin-ext_latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */\r\n       url('../fonts/lora-v16-latin-ext_latin-regular.woff2') format('woff2'), /* Super Modern Browsers */\r\n       url('../fonts/lora-v16-latin-ext_latin-regular.woff') format('woff'), /* Modern Browsers */\r\n       url('../fonts/lora-v16-latin-ext_latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */\r\n       url('../fonts/lora-v16-latin-ext_latin-regular.svg#Lora') format('svg'); /* Legacy iOS */\r\n}\r\n/* lora-700 - latin-ext_latin */\r\n@font-face {\r\n  font-family: 'Lora';\r\n  font-style: normal;\r\n  font-weight: 700;\r\n  font-display: swap;\r\n  src: url('../fonts/lora-v16-latin-ext_latin-700.eot'); /* IE9 Compat Modes */\r\n  src: local(''),\r\n       url('../fonts/lora-v16-latin-ext_latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */\r\n       url('../fonts/lora-v16-latin-ext_latin-700.woff2') format('woff2'), /* Super Modern Browsers */\r\n       url('../fonts/lora-v16-latin-ext_latin-700.woff') format('woff'), /* Modern Browsers */\r\n       url('../fonts/lora-v16-latin-ext_latin-700.ttf') format('truetype'), /* Safari, Android, iOS */\r\n       url('../fonts/lora-v16-latin-ext_latin-700.svg#Lora') format('svg'); /* Legacy iOS */\r\n}\r\n", "// SVG ikony\r\n.icon {\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  position: relative;\r\n\r\n  &:before {\r\n    content: '';\r\n    display: block;\r\n  }\r\n}\r\n\r\n// vnitřní box s ikonou\r\n.icon__svg {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  fill: currentColor;\r\n  pointer-events: none;\r\n  transform: translateZ(0);\r\n}\r\n\r\n// propojen<PERSON> jednotlivých ikon\r\n@import \"icons/icons.scss\";\r\n", "\n.icon--arrow-down {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--arrow-left {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--arrow-right {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--arrow-up {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--basket {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--calc {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--chat {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--close {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--dog {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--download {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--email {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--facebook {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--googleplus {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--info {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--instagram {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--ko {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--location {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--login {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--man {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--minus {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--ok {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--phone {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--plus {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--print {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--product-1 {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--product-2 {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--product-3 {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--product-4 {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--search {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--star {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--twitter {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--warn {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--wheel {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--why-1 {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--why-2 {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--why-3 {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n", "// hlavička\r\n.header {\n  color: $color_main_dark;\r\n\n  background-color: $color_white;\r\n\r\n  @media (max-width: $mq_menu - 1px) {\n    min-height: 115px;\r\n  }\r\n\r\n  a {\n    color: $color_white;\r\n  }\r\n\r\n  .container-fluid {\n    position: relative;\r\n  }\n}\r\n\r\n// logo pro mobil\r\n.header__title {\n  position: absolute;\r\n  top: 5px;\r\n  left: 5px;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: none;\r\n  }\r\n\r\n  img {\n    width: 46px;\r\n  }\n}\r\n\r\n// logo\r\n.header__logo {\n  @media (min-width: $mq_menu) {\n    position: relative;\r\n    z-index: $index_menu + 1;\r\n\n    margin: -50px 15px -25px 15px;\r\n    padding: 0 !important;\r\n  }\r\n\r\n  img {\n    display: block;\r\n\n    width: 50px;\r\n\r\n    @media (min-width: $mq_menu) {\n      width: auto;\r\n    }\n  }\n}\r\n\r\n// část s logem\r\n.header__center {\n  display: none;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: block;\r\n\n    margin-top: -30px;\r\n    margin-bottom: 10px;\r\n  }\n}\r\n\r\n// hlavní menu\r\n.header__nav {\n  display: none;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: block;\r\n  }\r\n\r\n  @media (max-width: $mq_menu - 1px) {\n    position: absolute;\r\n    top: 50px;\r\n    left: 0;\r\n    z-index: $index_menu;\r\n\n    width: 100%;\r\n    padding: 15px 0;\r\n\n    background: $color_white;\r\n  }\r\n\r\n  ul {\n    margin: 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n\n    font-size: 0; // inline-block fix\r\n    text-align: center;\r\n  }\r\n\r\n  li {\n    @media (min-width: $mq_menu) {\n      display: inline-block;\r\n\n      padding-top: 45px;\r\n\n      vertical-align: middle;\r\n    }\n  }\r\n\r\n  a {\n    display: block;\r\n\n    padding: 15px 10px;\r\n\n    color: $color_main;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    text-decoration: none;\r\n    text-transform: uppercase;\r\n\r\n    @media (min-width: $mqmd) {\n      padding: 15px 15px;\r\n    }\r\n\r\n    &:hover,\n    &:focus,\n    &.is-active {\n      color: $color_main_dark;\r\n    }\n  }\n}\r\n\r\n// košík, přihlášení\r\n.header__controls {\n  position: absolute;\r\n  top: 10px;\r\n  right: 50px;\r\n\r\n  @media (min-width: $mq_menu) {\n    right: auto;\r\n    left: 57%;\r\n  }\n}\r\n\r\n// kontaktní box\r\n.header__contact {\n  display: none;\r\n\n  margin-bottom: 10px;\r\n  padding: 10px 0 12px 0;\r\n\n  color: $color_white;\r\n\n  background-color: $color_main_dark;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: block;\r\n  }\r\n\r\n  p {\n    margin: 0;\r\n  }\r\n\r\n  a {\n    display: inline-block;\r\n\n    padding: 0 10px;\r\n\n    text-decoration: none;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_secondary;\r\n    }\n  }\r\n\r\n  .icon {\n    width: 20px;\r\n    margin-top: -2px;\r\n    margin-right: 3px;\r\n\n    vertical-align: top;\r\n  }\n}\r\n\r\n// socsítě\r\n.header__social {\n  display: none;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: block;\r\n    float: right;\r\n  }\r\n\r\n  a {\n    padding: 0 0;\r\n  }\n}\r\n", "// navigace\r\n.nav {\n  color: $color_white;\r\n\n  background-color: $color_main;\r\n\r\n  // na mobilní verzi bude místo loga pouze ikona\r\n  @media (max-width: $mq_menu) {\n    position: absolute;\r\n    top: 9px;\r\n    right: 5px;\r\n\n    overflow: hidden;\r\n\n    width: 40px;\r\n    height: 40px;\r\n\n    text-indent: -9999px;\r\n\n    border-radius: 50%;\r\n    background: $color_main_light url('../img/menu-icon.svg') center center no-repeat;\r\n    background-size: 26px auto;\r\n  }\r\n\r\n  ul {\n    margin: 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n\n    font-size: 0; // inline-block fix\r\n    text-align: center;\r\n  }\r\n\r\n  li {\n    display: inline-block;\r\n  }\r\n\r\n  a {\n    display: block;\r\n\n    padding: 15px 10px;\r\n\n    color: $color_white;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    text-decoration: none;\r\n    text-transform: uppercase;\r\n\r\n    @media (min-width: $mqmd) {\n      padding: 25px 30px;\r\n    }\r\n\r\n    &:hover,\n    &:focus,\n    &.is-active {\n      background-color: $color_main_dark;\r\n    }\n  }\r\n\r\n  .icon {\n    width: 50px;\r\n    margin-right: 10px;\r\n  }\n}\r\n", "// navigace\r\n.nav-product {\n  position: relative; // pro umístěn<PERSON>\r\n\n  margin-top: 15px;\r\n\r\n  ul {\n    margin: 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n\n    color: $color_white;\r\n\n    background-color: $color_main_light;\r\n  }\r\n\r\n  // podmenu\r\n  li ul a {\n    padding: 7px 10px 7px 35px;\r\n\n    font-size: 90%;\r\n\r\n    &:before {\n      content: '>';\r\n\n      position: absolute;\r\n\n      display: block;\r\n\n      margin: 2px 0 0 -12px;\r\n\n      font-size: 10px;\r\n    }\n  }\r\n\r\n  // aktivní č<PERSON>t\r\n  li.is-active {\n    padding: 10px 0;\r\n\n    background-color: $color_main;\r\n\r\n    a {\n      background-color: $color_main;\r\n\r\n      &:hover,\n      &:focus {\n        background-color: $color_main_dark;\r\n      }\n    }\n  }\r\n\r\n  a {\n    display: block;\r\n\n    padding: 8px 10px 8px 20px;\r\n\n    color: $color_white;\r\n    text-decoration: none;\r\n\n    border-bottom: 1px dotted $color_main;\r\n\r\n    &:hover,\n    &:focus {\n      background-color: $color_main_dark;\r\n    }\n  }\n}\r\n\r\n// mobilní verze menu\r\n.nav-product__wrapper {\n  @media (max-width: $mq_menu) {\n    position: absolute; // pozicujeme nahoru k ikoně\r\n    top: 32px;\r\n    z-index: 1;\r\n\n    width: 100%;\r\n    padding: 0;\r\n  }\n}\r\n\r\n// skryté menu pro full-page stránky\r\n.nav-product--hidden {\n  display: none;\r\n}\r\n\r\n// přepínání produktů/výrobců\r\n.nav-product--switch {\n  .nav-product__menu {\n    @media (min-width: $mqsm) {\n      display: none;  // neaktivní menu skryté\r\n\n      padding-top: 35px;\r\n    }\n  }\r\n\r\n  // aktivní nabídka\r\n  &.is-active {\n    // nadpis\r\n    .nav-product__header {\n      opacity: 1;\r\n    }\r\n\r\n    // menu\r\n    .nav-product__menu {\n      display: block;\r\n    }\n  }\r\n\r\n  // zanoříme, protože chceme stylovat pouze pod switcherem\r\n  .nav-product__header {\n    display: block;\r\n\r\n    @media (min-width: $mqsm) {\n      position: absolute;\r\n      top: 0;\r\n\n      width: 49%;\r\n\n      border-top-left-radius: $radius;\r\n      border-top-right-radius: $radius;\r\n\n      opacity: 0.8;\r\n      cursor: pointer;\r\n\r\n      &:hover,\n      &:focus {\n        opacity: 1;\r\n      }\n    }\n  }\n}\r\n\r\n// nadpis menu\r\n.nav-product__header {\n  margin: 0;\r\n  padding: 10px 15px 7px 15px;\r\n\n  color: $color_white;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-align: center;\r\n\n  background-color: $color_main_light;\r\n\r\n  @media (min-width: $mqsm) {\n    border-top-left-radius: $radius;\r\n    border-top-right-radius: $radius;\r\n  }\n}\r\n\r\n// výrobce bude napravo\r\n.nav-product__header--right {\n  right: 0;\r\n}\r\n\r\n// spodní menu\r\n.nav-product__other {\n  @media (min-width: $mqsm) {\n    padding-top: 10px;\r\n  }\r\n\r\n  .nav-product__header {\n    background-color: $color_gray;\r\n  }\r\n\r\n  a {\n    border-bottom: 1px dotted $color_gray_dark;\r\n    background-color: $color_gray_light;\r\n\r\n    &:hover,\n    &:focus {\n      background-color: $color_gray_dark;\r\n    }\n  }\n}\r\n\r\n// textový blok\r\n.nav-product__textblock {\n}\r\n", "// obs<PERSON><PERSON>, z<PERSON><PERSON><PERSON><PERSON> vzhled\r\n.article {\n  padding: 20px 0;\r\n\n  font-size: 18px;\r\n  line-height: 1.2; // pro v<PERSON><PERSON><PERSON><PERSON>lnost\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 60px 0;\r\n  }\r\n\r\n  // základní odkaz\r\n  // zaměříme všechny odkazy krom tlačítka (btn)\r\n  a {\n    &:not(.btn) {\n      color: $color_main;\r\n\r\n      &:hover,\n      &:focus {\n        color: $color_main_dark;\r\n      }\n    }\n  }\r\n\r\n  // základní odstavec\r\n  p {\n    line-height: 1.6;\r\n  }\r\n\r\n  // základní číslovaný seznam\r\n  ol {\n    padding-left: 35px;\r\n  }\r\n\r\n  // základní citace\r\n  blockquote {\n    margin: 16px 0;\r\n    padding: 1px 25px;\r\n\n    border-left: 2px solid $color_main;\r\n    background-color: lighten($color_main, 60%);\r\n  }\r\n\r\n  // základní podoba nadpisů\r\n  h1,\n  h2,\n  h3,\n  h4 {\n    color: $color_main;\r\n  }\r\n\r\n  h1 {\n    font-size: 22px;\r\n\r\n    @media (min-width: $mqsm) {\n      font-size: 30px;\r\n    }\n  }\r\n\r\n  // odr<PERSON><PERSON><PERSON>\r\n  ul {\n    margin: 0 0 10px 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n\r\n    li {\n      position: relative;\r\n\n      padding: 3px 3px 3px 18px;\r\n\r\n      &:before {\n        content: '';\r\n\n        position: absolute;\r\n        top: 11px;\r\n        left: 4px;\r\n\n        display: block;\r\n\n        width: 6px;\r\n        height: 6px;\r\n\n        border-radius: 3px;\r\n        background-color: $color_main;\r\n      }\n    }\n  }\n}\r\n\r\n// horní část\r\n.article__perex {\n  max-width: 900px;\r\n\n  text-align: center;\r\n\r\n  @media (min-width: $mqsm) {\n    margin: 0 auto 40px auto;\r\n\n    font-size: 20px;\r\n  }\n}\r\n\r\n// vložený obrázek\r\n.article__image {\n  margin-left: 15px;\r\n  padding: 5px;\r\n\n  border: 3px solid $color_main;\r\n\r\n  img {\n    display: block;\r\n\n    width: 100%;\r\n  }\n}\r\n\r\n// vložené obrázky\r\n.article__images {\n  display: flex;\r\n  flex-wrap: wrap;\r\n\n  margin: 30px -10px;\r\n\n  font-size: 0;\r\n\r\n  a {\n    display: block;\r\n    flex: 1 1 1px;\r\n\n    width: 30%;\r\n    margin: 5px;\r\n    padding: 5px;\r\n\n    border: 3px solid $color_main;\r\n\n    transition: opacity 0.3s;\r\n\r\n    &:hover,\n    &:focus {\n      opacity: 0.8;\r\n    }\r\n\r\n    span {\n      display: block;\r\n      overflow: hidden;\r\n\n      height: 250px;\r\n    }\n  }\r\n\r\n  img {\n    display: block;\r\n  }\n}\r\n\r\n// vložené přílohy\r\n.article__attachements {\n  ul {\n    padding-left: 2px;\r\n\n    list-style-type: none;\r\n  }\r\n\r\n  li {\n    margin-top: 5px;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .icon {\n    width: 22px;\r\n    margin-top: -3px;\r\n    margin-right: 5px;\r\n    margin-bottom: 3px;\r\n\n    text-decoration: none;\r\n  }\n}\r\n\r\n// související zboží a příslušenství\r\n.article__related {\n  h2 {\n    color: $color_main_light;\r\n    font-size: 25px;\r\n    font-weight: 400;\r\n  }\n}\r\n", "// patička\r\n.footer {\n  padding-bottom: 40px;\r\n\n  color: $color_white;\r\n  font-size: 18px;\r\n  line-height: 1.5;\r\n\n  background-color: $color_main_dark;\r\n\r\n  @media (min-width: $mqsm) {\n    margin-top: 100px;\r\n  }\r\n\r\n  h3 {\n    margin-bottom: 10px;\r\n\n    text-transform: uppercase;\r\n  }\r\n\r\n  a {\n    color: $color_secondary;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_white;\r\n    }\n  }\n}\r\n\r\n// mapa\r\n.footer__map {\n  margin-bottom: 40px;\r\n}\r\n\r\n// logo\r\n.footer__logo {\n  display: block;\r\n}\r\n\r\n// produkty\r\n.footer__products {\n  margin: 0;\r\n  margin-bottom: 10px;\r\n  padding: 0;\r\n\n  list-style-type: none;\r\n\n  font-size: 0;\r\n  font-weight: bold;\r\n\r\n  @media (max-width: $mq_menu - 1px) {\n    text-align: center;\r\n  }\r\n\r\n  li {\n    display: inline-block;\r\n\n    font-size: 22px;\r\n  }\r\n\r\n  a {\n    display: block;\r\n\n    min-width: 190px;\r\n    margin: 0 10px 10px 0;\r\n    padding: 10px 15px;\r\n\n    color: $color_white;\r\n    text-decoration: none;\r\n    text-transform: uppercase;\r\n\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_secondary;\r\n    }\n  }\r\n\r\n  .icon {\n    width: 45px;\r\n    margin-right: 6px;\r\n  }\n}\r\n\r\n// doplňkov<PERSON> menu\r\n.footer__nav {\n  padding: 0;\r\n\n  list-style-type: none;\r\n\n  font-size: 15px;\r\n\r\n  @media (min-width: $mqsm) {\n    margin: 25px 0 0 0;\r\n  }\r\n\r\n  li {\n    display: inline-block;\r\n  }\r\n\r\n  a {\n    display: block;\r\n\n    padding: 4px 6px;\r\n\n    color: $color_white;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_secondary;\r\n    }\n  }\n}\r\n\r\n// kontakt\r\n.footer__contact {\n  .icon {\n    color: $color_secondary;\r\n  }\n}\r\n\r\n// copyright\r\n.footer__copyright {\n  margin-top: 20px;\r\n\n  text-align: center;\r\n\n  background-color: $color_main_light;\r\n}\r\n", "// chybo<PERSON><PERSON> h<PERSON>\r\n.alert {\n  margin: 16px 0;\r\n  padding: 1px 16px;\r\n\n  color: $color_white;\r\n\n  border-radius: $radius;\r\n  background-color: $color_main;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 4px 25px;\r\n  }\n}\r\n\r\n// hláška s možností zavření\r\n.alert--close {\n  position: relative; // pro křížek na zavření\r\n\n  padding: 1px 30px 1px 16px; // místo na křížek, bude-li třeba\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 4px 30px 4px 25px; // místo na křížek, bude-li třeba\r\n  }\r\n\r\n  .icon--close {\n    position: absolute;\r\n    top: 19px;\r\n    right: 17px;\r\n\n    width: 18px;\r\n\n    cursor: pointer;\r\n\r\n    &:hover,\n    &:focus {\n      opacity: 0.8;\r\n    }\n  }\n}\r\n\r\n// potvrzující hláška\r\n.alert--success {\n  background-color: $color_success;\r\n}\r\n\r\n// informační hl<PERSON>š<PERSON>\r\n.alert--info {\n  background-color: $color_info;\r\n}\r\n\r\n// výstražná hláška\r\n.alert--danger {\n  background-color: $color_danger;\r\n}\r\n", "// odkaz na košík\r\n.basket {\n  display: inline-block;\r\n\r\n  // klikací celý blok\r\n  a {\n    display: flex;\r\n\n    color: $color_main;\r\n    text-decoration: none;\r\n  }\r\n\r\n  .icon {\n    width: 30px;\r\n\n    color: $color_main;\r\n  }\n}\r\n\r\n// mobilní verze boxu (reset <PERSON><PERSON><PERSON><PERSON><PERSON>)\r\n.basket__wrapper {\n  @media (max-width: $mq_menu - 1px) {\n    position: absolute; // pozicujeme nahoru k ikoně\r\n    top: 48px;\r\n    z-index: 1;\r\n\n    width: 100%;\r\n    padding: 0;\r\n  }\n}\r\n\r\n// nadpis boxu\r\n.basket__header {\n  position: relative;\r\n\n  margin: 0;\r\n\r\n  // klikací ikona na mobilu\r\n  @media (max-width: $mq_menu - 1px) {\n    position: absolute;\r\n    top: -1px;\r\n    right: 2px;\r\n\n    overflow: hidden;\r\n\n    width: 40px;\r\n    height: 40px;\r\n    padding: 12px 0 0 6px;\r\n\n    text-indent: -9999px;\r\n\n    border-radius: 50%;\r\n    background: $color_main_light url(../img/basket.svg) center center no-repeat;\r\n    background-size: 20px 20px;\r\n  }\n}\r\n\r\n// obsah boxu\r\n.basket__content {\n  margin: 0;\r\n  padding: 5px 10px 8px 10px;\r\n\n  line-height: 1.4;\r\n}\r\n\r\n// počet kusů v košíku\r\n.basket__count {\n  position: absolute;\r\n  top: 20px;\r\n  right: -3px;\r\n\n  display: none;\r\n\n  width: 18px;\r\n  height: 18px;\r\n  padding: 5px 0;\r\n\n  color: $color_main_dark;\r\n  font-size: 11px;\r\n  font-weight: 700;\r\n  text-align: center;\r\n\n  border-radius: 50%;\r\n  background-color: $color_secondary;\r\n\r\n  @media (min-width: $mqsm) {\n    display: block;\r\n  }\n}\r\n", "// tlačítko\r\n.btn {\n  margin-bottom: 4px;\r\n  padding: 10px 12px;\r\n\n  color: $color_white;\r\n  font: inherit; // pro sladění vzhledu buttonu a odkazu\r\n  font-size: 16px;\r\n  text-decoration: none;\r\n  text-transform: uppercase;\r\n\n  border: 1px solid transparent;\r\n  border-radius: $radius_button;\r\n  background-color: $color_main;\r\n\n  cursor: pointer;\r\n\r\n  &:hover,\n  &:focus {\n    background-color: darken($color_main, 20%);\r\n  }\r\n\r\n  // ikona v tlačítku\r\n  .icon {\n    width: 16px;\r\n    margin: 0 3px;\r\n\n    vertical-align: bottom;\r\n  }\n}\r\n\r\n// úprava odkazu\r\na.btn {\n  display: inline-block;\r\n}\r\n\r\n// kompaktní tlačítko\r\n.btn--small {\n  padding: 5px 6px;\r\n\n  font-size: 14px;\r\n\r\n  // úprava velikosti ikony\r\n  .icon {\n    width: 15px;\r\n  }\n}\r\n\r\n// výrazné tlačítko\r\n.btn--big {\n  padding: 15px 17px;\r\n\n  font-size: 20px;\r\n\r\n  // úprava velikosti ikony\r\n  .icon {\n    width: 20px;\r\n  }\r\n\r\n  // na malém rozlišení bude vždy malý\r\n  @media (max-width: $mqxxs) {\n    padding: 8px 10px;\r\n\n    font-size: 14px;\r\n\r\n    .icon {\n      width: 14px;\r\n    }\n  }\n}\r\n\r\n// potvrzující tlačítko výrazné\r\n.btn--success {\n  background-color: $color_success;\r\n\r\n  &:hover,\n  &:focus {\n    background-color: darken($color_success, 10%);\r\n  }\n}\r\n\r\n// informativní tlačítko\r\n.btn--info {\n  background-color: $color_info;\r\n\r\n  &:hover,\n  &:focus {\n    background-color: darken($color_info, 10%);\r\n  }\n}\r\n\r\n// výstražné tlačítko\r\n.btn--danger {\n  background-color: $color_danger;\r\n\r\n  &:hover,\n  &:focus {\n    background-color: darken($color_danger, 10%);\r\n  }\n}\r\n\r\n// tlačítko koupit\r\n.btn--buy {\n  color: $color_white;\r\n\n  background-color: $color_buy;\r\n\r\n  &:hover,\n  &:focus {\n    background-color: darken($color_buy, 10%);\r\n  }\n}\r\n", "// drobečková navigace\r\n.breadcrumb {\n  padding: 15px 0;\r\n\n  color: $color_white;\r\n  line-height: 1.3;\r\n  text-align: center;\r\n\n  background-color: lighten($color_main_light, 10%);\r\n\r\n  a {\n    color: $color_white;\r\n\r\n    &:first-child {\n      padding-left: 28px;\r\n\n      background: url(../img/man.svg) left center no-repeat;\r\n    }\n  }\r\n\r\n  span {\n    display: inline-block;\r\n    overflow: hidden;\r\n\n    width: 20px;\r\n    margin: 0 7px;\r\n\n    vertical-align: middle;\r\n    text-indent: -9999px;\r\n\n    background: url(../img/breadcrumb.svg) center center no-repeat;\r\n  }\n}\r\n", "// kategorie produktů\r\n.category {\n  margin-top: 15px;\r\n}\r\n\r\n// nadpis kategorie\r\n.category__header {\n  color: $color_main;\r\n\r\n  @media (min-width: $mqsm) {\n    font-size: 25px;\r\n  }\n}\r\n\r\n// popis kategorie\r\n.category__description {\n  margin: 20px 0;\r\n\r\n  @media (min-width: $mqsm) {\n    margin: 50px 0;\r\n\n    font-size: 18px;\r\n  }\r\n\r\n  a {\n    color: $color_secondary_dark;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_secondary;\r\n    }\n  }\n}\r\n\r\n// podkategorie na výpisu\r\n.category__item {\n  a {\n    display: block;\r\n\n    margin-bottom: 10px;\r\n    padding: 10px 15px;\r\n\n    color: $color_white;\r\n    text-decoration: none;\r\n\n    border-radius: $radius;\r\n    background-color: $color_main;\r\n\r\n    &:hover,\n    &:focus {\n      background-color: $color_main_light;\r\n    }\n  }\r\n\r\n  // ikona v tlačítku\r\n  .icon {\n    width: 16px;\r\n    margin-right: 2px;\r\n    margin-left: -4px;\r\n\n    vertical-align: bottom;\r\n  }\n}\r\n\r\n// stránkov<PERSON>í, řazení\r\n.category__filters {\n  margin: 16px 0 0 0;\r\n\r\n  // úprava pozice str<PERSON>\r\n  .pagination {\n    margin: 0;\r\n\r\n    @media (min-width: $mqmd) {\n      float: right;\r\n    }\n  }\r\n\r\n  // úprava velikosti štítků\r\n  .label {\n    font-size: 16px;\r\n  }\n}\r\n", "// komentáře\r\n.comments {\n}\r\n\r\n// komentář\r\n.comments__item {\n  padding: 5px;\r\n\n  font-size: 15px;\r\n\r\n  p {\n    margin-top: 8px;\r\n    margin-bottom: 0;\r\n  }\n}\r\n\r\n// odpověď\r\n.comments__item--reply {\n  margin-top: 25px;\r\n\n  border: 1px dotted $color_main;\r\n}\r\n\r\n// nadpis komentáře\r\n.comments__header {\n  margin: 0;\r\n\n  font-size: 17px;\r\n}\r\n\r\n// datum\r\n.comments__date {\n  color: $color_gray_light;\r\n  font-size: 13px;\r\n}\r\n", "// hlavička nad obsahem\r\n.content-header {\n  position: relative;\r\n\n  padding: 40px 15px;\r\n\n  color: $color_white;\r\n  font-size: 22px;\r\n  text-align: center;\r\n\n  background-color: $color_main;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 100px 0;\r\n  }\r\n\r\n  h1,\n  p {\n    position: relative;\r\n    z-index: $index_page + 2;\r\n\n    margin: 0;\r\n  }\r\n\r\n  h1 {\n    position: relative;\r\n\n    margin-bottom: 20px;\r\n    padding-bottom: 20px;\r\n\n    font-size: 40px;\r\n    text-transform: uppercase;\r\n\r\n    &:before {\n      content: '';\r\n\n      position: absolute;\r\n      bottom: 0;\r\n      left: 50%;\r\n\n      display: block;\r\n\n      width: 100px;\r\n      height: 2px;\r\n      margin-left: -50px;\r\n\n      background-color: $color_secondary;\r\n    }\n  }\r\n\r\n  &:after {\n    content: '';\r\n\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    z-index: $index_page + 1;\r\n\n    display: block;\r\n\n    background: url(../img/content-header-back.png);\r\n  }\n}\r\n\r\n// pozadí v hlavičce\r\n.content-header__image {\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: $index_page;\r\n\n  display: block;\r\n\n  background-image: url(../img/content-header-back.jpg);\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n  background-size: cover;\r\n\n  opacity: 0.5;\r\n}\r\n", "// ovl<PERSON>dací prvky\r\n.control {\n  display: inline-block;\r\n\n  width: 16px;\r\n  height: 16px;\r\n  margin: 3px 0;\r\n\n  color: $color_white;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  line-height: 16px;\r\n  text-align: center;\r\n  text-decoration: none;\r\n\n  border-radius: 8px;\r\n  background-color: $color_info;\r\n\n  cursor: pointer;\r\n  user-select: none; // zabráněn<PERSON>\r\n\r\n  // větší verze pro malé rozlišení\r\n  @media (max-width: $mqxs) {\n    width: 24px;\r\n    height: 24px;\r\n\n    font-size: 24px;\r\n    line-height: 24px;\r\n\n    border-radius: 12px;\r\n  }\r\n\r\n  // tisk<PERSON> verze\r\n  @media print {\n    display: none;\r\n  }\r\n\r\n  // hover efekty\r\n  &:hover,\n  &:focus {\n    background-color: darken($color_info, 10%);\r\n  }\r\n\r\n  // úprava pokud je uvnitř ikona\r\n  .icon {\n    width: 12px;\r\n    margin-top: 2px;\r\n\n    vertical-align: top;\r\n\r\n    // větš<PERSON> verze pro malé rozlišení\r\n    @media (max-width: $mqxs) {\n      width: 18px;\r\n      margin-top: 3px;\r\n    }\n  }\n}\r\n\r\na.control {\n  color: $color_white !important; // přerazíme pokud by do<PERSON><PERSON> k <PERSON>ř<PERSON> barvy odkazu\r\n}\r\n\r\n// velké tlačítko\r\n.control--big {\n  width: 32px;\r\n  height: 32px;\r\n\n  font-size: 32px;\r\n  line-height: 32px;\r\n\n  border-radius: 16px;\r\n\r\n  // úprava pokud je uvnitř ikona\r\n  .icon {\n    width: 24px;\r\n    margin-top: 4px;\r\n\n    vertical-align: top;\r\n  }\n}\r\n\r\n// tlačítko potvrzení\r\n.control--success {\n  background-color: $color_success;\r\n\r\n  &:hover,\n  &:focus {\n    background-color: darken($color_success, 10%);\r\n  }\n}\r\n\r\n// tlačítko odstranit/zavřít\r\n.control--remove {\n  background-color: $color_danger;\r\n\r\n  &:hover,\n  &:focus {\n    background-color: darken($color_danger, 10%);\r\n  }\n}\r\n\r\n// input s počtem kusů\r\n.control--count {\n  white-space: nowrap; // nechceme zalamovat ovládací prvky\r\n\r\n  .icon {\n    vertical-align: top;\r\n  }\r\n\r\n  // omezíme velikost inputu\r\n  input {\n    max-width: 75px;\r\n  }\n}\r\n", "// filtry zboží\r\n.filter {\n  margin: 16px 0 0 0;\r\n}\r\n\r\n// přehledn<PERSON><PERSON><PERSON><PERSON> ve sloupcích\r\n.filter__tiles label {\n  position: relative; // budeme absolutně pozicovat label\r\n\n  width: 100%;\r\n  padding-left: 23px; // místo na input\r\n\n  vertical-align: top;\r\n\r\n  @media (min-width: $mqxxs) {\n    width: 49%;\r\n  }\r\n\r\n  @media (min-width: $mqxs) {\n    width: 32%;\r\n  }\r\n\r\n  @media (min-width: $mqlg) {\n    width: 24%;\r\n  }\r\n\r\n  input {\n    position: absolute;\r\n\n    margin-top: 1px;\r\n    margin-left: -17px;\r\n  }\n}\r\n\r\n// výsledky filtrování\r\n.filter__results {\n  margin-top: -$radius; // napojení na filtry\r\n  padding: 14px 18px 10px 18px;\r\n\n  color: $color_white;\r\n\n  border-bottom-right-radius: $radius;\r\n  border-bottom-left-radius: $radius;\r\n  background-color: $color_main;\r\n\r\n  // nadpis boxu\r\n  strong {\n    margin-right: 7px;\r\n  }\n}\r\n\r\n// zjednodušená filtrace\r\n.filter__row {\n  @media (min-width: $mq_menu) {\n    margin-top: 40px;\r\n    margin-bottom: 40px;\r\n  }\n}\r\n", "// úvodní strana\r\n.home {\n}\r\n\r\n// hlavní nadpis webu\r\n.home__title {\n  margin: 25px 0;\r\n\n  text-align: center;\r\n  text-transform: uppercase;\r\n\r\n  @media (min-width: $mq_menu) {\n    margin: 70px 0;\r\n  }\r\n\r\n  @media (max-width: $mqxs - 1px) {\n    font-size: 20px;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  span {\n    display: inline-block;\r\n\n    padding: 16px 25px 14px 25px;\r\n\n    color: $color_white;\r\n\n    border-radius: 3px;\r\n    background: $color_secondary_dark;\r\n  }\r\n\r\n  &:before {\n    @media (min-width: $mq_menu) {\n      content: '';\r\n\n      position: absolute;\r\n      z-index: -1;\r\n\n      display: block;\r\n\n      width: 100%;\r\n      height: 2px;\r\n      margin-top: 26px;\r\n\n      background: $color_secondary_dark;\r\n    }\n  }\n}\r\n\r\n// výpis produktů\r\n.home__products {\n  .category__header {\n    text-align: center;\r\n  }\n}\r\n\r\n// textový blok\r\n.home__block {\n  padding: 20px 20px 20px 0;\r\n\n  font-size: 18px;\r\n  line-height: 1.2; // pro v<PERSON><PERSON><PERSON><PERSON> čitelnost\r\n\r\n  // z<PERSON>lad<PERSON><PERSON> podoba nadpisů\r\n  h1,\n  h2,\n  h3,\n  h4 {\n    color: $color_main;\r\n  }\r\n\r\n  // základní odstavec\r\n  p {\n    line-height: 1.6;\r\n  }\n}\r\n\r\n// výpis fotek\r\n.home__photos {\n  padding: 20px 0;\r\n\r\n  img {\n    width: 100%;\r\n  }\n}\r\n\r\n// hodnoty\r\n.home__why {\n  h3 {\n    position: relative;\r\n\n    padding-bottom: 20px;\r\n\r\n    &:before {\n      content: '';\r\n\n      position: absolute;\r\n      bottom: 0;\r\n      left: 50%;\r\n\n      display: block;\r\n\n      width: 80px;\r\n      height: 2px;\r\n      margin-left: -40px;\r\n\n      background: $color_secondary_dark;\r\n    }\n  }\r\n\r\n  p {\n    max-width: 200px;\r\n    margin: 10px auto;\r\n\n    line-height: 1.3;\r\n  }\n}\r\n\r\n// ikona (v hodnotách)\r\n.home__icon {\n  width: 100px;\r\n  height: 100px;\r\n  margin: 0 auto;\r\n  padding: 0;\r\n\n  border-radius: 50px;\r\n  background-color: $color_secondary_dark;\r\n\r\n  .icon {\n    width: 50px;\r\n    margin-top: 25px;\r\n\n    color: $color_white;\r\n  }\n}\r\n", "// p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>le\r\n.login {\n  display: inline-flex;\r\n\n  margin-right: 20px;\r\n\r\n  @media (max-width: $mqxs) {\n    margin-bottom: 15px;\r\n  }\r\n\r\n  a {\n    padding: 0 1px;\r\n\n    color: $color_main;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_main_dark;\r\n    }\n  }\r\n\r\n  .icon {\n    width: 30px;\r\n\n    color: $color_main;\r\n  }\n}\r\n\r\n// mobilní verze boxu (reset <PERSON><PERSON><PERSON>)\r\n.login__wrapper {\n  @media (max-width: $mq_menu - 1px) {\n    position: absolute; // pozicujeme nahoru k ikoně\r\n    top: 48px;\r\n    z-index: 1;\r\n\n    width: 100%;\r\n    padding: 0;\r\n  }\r\n\r\n  @media (max-width: $mqmd) {\n    clear: both;\r\n  }\n}\r\n\r\n// nadpis boxu\r\n.login__header {\n  margin: 0 10px 0;\r\n\r\n  // klikací ikona na mobilu\r\n  @media (max-width: $mq_menu - 1px) {\n    position: absolute;\r\n    top: -1px;\r\n    right: 38px;\r\n\n    overflow: hidden;\r\n\n    width: 40px;\r\n    height: 40px;\r\n    padding: 12px 0 0 6px;\r\n\n    text-indent: -9999px;\r\n\n    border-radius: 50%;\r\n    background: $color_main_light url(../img/man-mobile.svg) center center no-repeat;\r\n    background-size: 20px 20px;\r\n  }\n}\r\n\r\n// obsah boxu\r\n.login__content {\n  margin: 0;\r\n  padding: 5px 10px 8px 5px;\r\n\n  line-height: 1.4;\r\n\r\n  @media (max-width: $mq_menu - 1px) {\n    position: absolute;\r\n    top: 50px;\r\n    right: 10px;\r\n    z-index: $index_menu;\r\n\n    border: 2px solid $color_main_light;\r\n    background-color: $color_white;\r\n  }\n}\r\n", "// štítky\r\n// lze p<PERSON>t na cokoliv, názvy jsou zavádějící pro standardní využití\r\n.label {\n  display: inline-block;\r\n\n  margin-bottom: 4px;\r\n  padding: 6px 8px;\r\n\n  color: $color_white;\r\n  font-size: 13px;\r\n  line-height: 15px;\r\n  text-transform: uppercase;\r\n\n  border-radius: $radius;\r\n  background-color: $color_main;\r\n\r\n  // úprava velikosti ikony\r\n  .icon {\n    width: 14px;\r\n\n    vertical-align: top;\r\n  }\r\n\r\n  // pokud je uvnitř formulářový prvek (styly pro radio nebo checkbox)\r\n  input {\n    margin-bottom: 0;\r\n\n    vertical-align: middle;\r\n  }\n}\r\n\r\n// hover efekt po najetí, pokud je štítek v odkazu\r\na .label {\n  &:hover,\n  &:focus {\n    opacity: 0.8;\r\n  }\n}\r\n\r\n// akce\r\n.label--action {\n  background-color: $color_label_action;\r\n}\r\n\r\n// tip\r\n.label--tip {\n  background-color: $color_label_tip;\r\n}\r\n\r\n// sleva\r\n.label--discount {\n  background-color: $color_label_discount;\r\n}\r\n\r\n// novinka\r\n.label--new {\n  background-color: $color_label_new;\r\n}\r\n\r\n// doprava zdarma\r\n.label--delivery {\n  background-color: $color_label_delivery;\r\n}\r\n\r\n// štítek pro řazení\r\n.label--sort {\n  background-color: $color_main_light;\r\n\r\n  // pokud obsahuje ikonu\r\n  .icon {\n    margin-left: 5px;\r\n  }\n}\r\n\r\n// štítek pro filtrování\r\n.label--filter {\n  color: $color_black;\r\n\n  border: 1px dotted $color_gray;\r\n  border-radius: $radius;\r\n  background-color: $color_white;\r\n}\r\n\r\n// štítek pro výsledky filtrování\r\n.label--result {\n  background-color: $color_main_light;\r\n\r\n  // zavírací tlačítko\r\n  a {\n    color: $color_white;\r\n    text-decoration: none;\r\n  }\r\n\r\n  .icon {\n    margin-left: 5px;\r\n\n    cursor: pointer;\r\n\r\n    &:hover,\n    &:focus {\n      opacity: 0.8;\r\n    }\n  }\n}\r\n\r\n// štítek je aktivní (standardně pro řazení)\r\n.label--is-active {\n  color: $color_main;\r\n\n  background-color: $color_white;\r\n}\r\n", "// modal okno\r\n// modal se spouští kliknutím na blok, který má class=\"modal--show\"\r\n// ID okna musí být uvedeno v atributu REL rel=\"modal-name\"\r\n// konkrétní okno musí mít ID shodné s REL atributem\r\n// pokud půjde o odkaz, je doporučeno dát ID do hrefu jako kotvu href=\"#modal-name\"\r\n.modal {\n  position: absolute;\r\n  top: 0.5%;\r\n  left: 1%;\r\n  z-index: 1000; // přeražení z-indexu\r\n\n  display: none; // v základním stavu skryté\r\n\n  width: 100%;\r\n\r\n  // pozice přes celou obrazovku\r\n  @media (min-width: $mqxs) {\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n\n    overflow: hidden; // zabránění přetečení\r\n\n    background-color: rgba(0, 0, 0, 0.75); // pozadí - zešednut<PERSON>\r\n  }\n}\r\n\r\n// reset modalu při nízké výš<PERSON> okna\r\n// přid<PERSON><PERSON><PERSON> pouze <PERSON>Scriptem, vše přeraženo\r\n.modal--reset {\n  position: absolute !important;\r\n\n  overflow: visible !important;\r\n\n  background-color: transparent !important;\r\n\r\n  // reset pozice a centrování\r\n  .modal__body {\n    top: 0 !important;\r\n\n    transform: none !important;\r\n  }\n}\r\n\r\n// tělo\r\n.modal__body {\n  position: absolute; // absolutní pozicování\r\n\n  width: 98%; // šířka modal okna\r\n\n  background: $color_white;\r\n  box-shadow: 0 0 13px 2px rgba(0, 0, 0, 0.5); // stín boxu pro lepší přehnednost na nižších rozlišeních\r\n\r\n  // doprostřed obrazovky - pouze na vyšším rozlišení\r\n  @media (min-width: $mqxs) {\n    top: 50%;\r\n    left: 50%;\r\n\n    transform: translate(-50%, -50%);\r\n  }\r\n\r\n  @media (min-width: $mq_modal) {\n    width: 680px;\r\n  }\n}\r\n\r\n// hlavička\r\n.modal__head {\n  padding: 8px;\r\n\n  color: $color_white;\r\n\n  background-color: $color_main;\r\n\r\n  @media (min-width: $mq_modal) {\n    padding: 16px 20px;\r\n  }\n}\r\n\r\n// zavírací tlačítko\r\n.modal__close {\n  display: inline-block;\r\n  float: right;\r\n\n  font-size: 22px;\r\n\n  cursor: pointer;\r\n\r\n  &:hover,\n  &:focus {\n    opacity: 0.8;\r\n  }\n}\r\n\r\n// nadpis\r\n.modal__header {\n  margin: 0;\r\n\n  font-size: 20px;\r\n}\r\n\r\n// obsah\r\n.modal__content {\n  padding: 8px;\r\n\r\n  @media (min-width: $mq_modal) {\n    padding: 16px 20px;\r\n  }\n}\r\n\r\n// patička\r\n.modal__footer {\n  padding: 8px;\r\n\r\n  @media (min-width: $mq_modal) {\n    padding: 16px 20px;\r\n  }\n}\r\n", "// novinky\r\n.news {\n}\r\n\r\n// výpis novinek\r\n.news__item {\n  margin-bottom: 20px;\r\n\r\n  h3 {\n    margin: 5px 0 10px 0;\r\n\n    font-size: 18px;\r\n    font-weight: 700;\r\n  }\r\n\r\n  p {\n    margin: 0;\r\n\n    font-size: 15px;\r\n  }\r\n\r\n  img {\n    float: left;\r\n\n    margin-right: 20px;\r\n  }\n}\r\n\r\n// nadpis\r\n.news__header {\n}\r\n\r\n// vložený obrázek\r\n.news__image {\n  @media (min-width: $mqsm) {\n    float: right;\r\n\n    margin: 15px;\r\n  }\n}\r\n\r\n// datum a další informace\r\n.news__date {\n}\r\n\r\n// obsah novinky\r\n.news__content {\n}\r\n\r\n", "// stránkování\r\n.pagination {\n  clear: both;\r\n\n  margin: 30px 0 10px 0;\r\n\r\n  // základní definice boxů\r\n  a,\n  span {\n    display: inline-block;\r\n\n    margin-bottom: 5px;\r\n    padding: 8px 10px;\r\n\n    border-radius: $radius;\r\n  }\r\n\r\n\r\n  a {\n    color: $color_white;\r\n    text-decoration: none;\r\n\n    background-color: $color_main_light;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_white;\r\n\n      background-color: $color_main;\r\n    }\n  }\n}\r\n\r\n// aktivní stránka\r\n.paginator__current {\n  font-weight: 700;\r\n\n  background-color: lighten($color_gray_light, 30%);\r\n}\r\n", "// hledání v hlavičce\r\n.search {\n  position: absolute;\r\n  top: 65px;\r\n\n  width: 95%;\r\n\r\n  @media (min-width: $mq_menu) {\n    top: 8px;\r\n    left: 160px;\r\n\n    width: auto;\r\n  }\n}\r\n\r\n.search__submit {\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n\n  overflow: hidden;\r\n\n  width: 38px;\r\n  height: 38px;\r\n\n  text-indent: -9999px;\r\n\n  background: url(../img/search.svg) center center no-repeat;\r\n  background-color: transparent;\r\n\n  opacity: 0.4;\r\n\r\n  &:hover,\n  &:focus {\n    background-color: transparent;\r\n\n    opacity: 1;\r\n  }\n}\r\n\r\n.search__input {\n  width: 100%;\r\n\n  font-size: 15px;\r\n\n  border-color: $color_gray_light;\r\n  border-radius: 5px;\r\n\r\n  @media (min-width: $mq_menu) {\n    width: 250px;\r\n  }\n}\r\n\r\n.search__sort {\n  a {\n    color: $color_main;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_main_dark;\r\n    }\n  }\n}\r\n", "// sdílení na sociálních sítích\r\n.share {\n  strong {\n    color: $color_main;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  a {\n    display: inline-block;\r\n\n    color: $color_main;\r\n    font-size: 30px;\r\n    vertical-align: middle;\r\n    text-decoration: none;\r\n\n    opacity: 1;\r\n\r\n    &:hover,\n    &:focus {\n      opacity: 0.8;\r\n    }\n  }\r\n\r\n  // úprava velikosti ikon\r\n  .icon {\n    width: 30px;\r\n\n    vertical-align: top;\r\n  }\n}\r\n", "// úvodní slider\r\n.slider {\n  display: none; // na ma<PERSON><PERSON>ch roz<PERSON> s<PERSON>\r\n\r\n  @media (min-width: $mqxs) {\n    display: block;\r\n  }\r\n\r\n  p {\n    margin: 30px 0;\r\n  }\r\n\r\n  .content-header {\n    overflow: hidden;\r\n\n    height: 300px;\r\n    padding: 70px 0 0 0;\r\n  }\r\n\r\n  // přeražení stylů Slick slideru\r\n  .slick-dots {\n    position: absolute;\r\n    right: 10px;\r\n    bottom: 10px;\r\n    z-index: 1;\r\n\n    height: 26px;\r\n    margin: 0;\r\n    padding: 0;\r\n\n    text-align: center;\r\n\r\n    li {\n      display: inline-block;\r\n\n      margin: 5px;\r\n\r\n      &.slick-active button {\n        background-color: $color_white;\r\n      }\n    }\r\n\r\n    button {\n      display: block;\r\n      overflow: hidden;\r\n\n      width: 14px;\r\n      height: 14px;\r\n      padding: 0;\r\n\n      line-height: 900px;\r\n\n      border: none;\r\n      border-radius: 14px;\r\n      background-color: $color_secondary;\r\n\r\n      // zrušení focusu\r\n      &:focus {\n        outline: none;\r\n      }\n    }\n  }\n}\r\n", "// skladová dostupnost\r\n.stock {\n  display: inline-block;\r\n\n  margin-top: 5px;\r\n  padding: 4px 6px;\r\n\n  color: $color_white;\r\n  font-size: 12px;\r\n  text-transform: uppercase;\r\n\n  border-radius: $radius;\r\n  background-color: $color_main;\r\n\r\n  // úprava velikosti ikony\r\n  .icon {\n    width: 12px;\r\n\n    vertical-align: top;\r\n  }\n}\r\n\r\n// dostupný\r\n.stock--available {\n  background-color: $color_buy;\r\n}\r\n\r\n// nedostupný\r\n.stock--unavailable {\n  background-color: $color_gray_light;\r\n}\r\n", "// uživatelské strany\r\n.user {\n}\r\n\r\n// vnitřní menu po přihlášení uživatele\r\n.user__menu {\n  margin: 20px 0;\r\n  padding: 10px 10px 6px 10px; // kompenzace spodního paddingu vzhledem k tlačítkům uvnitř\r\n\n  border-radius: $radius;\r\n  background-color: lighten($color_gray_light, 20%);\r\n}\r\n", "// kde nakoupíte\r\n.where {\n  p {\n    font-size: 20px;\r\n    line-height: 1.5;\r\n  }\n}\r\n\r\n// aktuálně\r\n.where__news {\n  img {\n    max-width: 420px;\r\n  }\n}\r\n\r\n// farma\r\n.where__farm {\n  img {\n    max-width: 420px;\r\n  }\n}\r\n\r\n// online\r\n.where__online {\n  img {\n    max-width: 250px;\r\n  }\n}\r\n\r\n// další\r\n.where__other {\n}\r\n\r\n// položka\r\n.where__item {\n  p {\n    margin-bottom: 0;\r\n    padding: 20px 20px 0 20px;\r\n\n    font-size: 16px;\r\n  }\r\n\r\n  p + p {\n    margin-top: 0;\r\n    padding-bottom: 40px;\r\n  }\r\n\r\n  img {\n    max-width: 250px;\r\n  }\n}\r\n\r\n// fotografie\r\n.where__photo {\n  img {\n    padding: 10px;\r\n\n    text-align: center;\r\n\n    border: 3px solid $color_main;\r\n    background-color: #fff;\r\n  }\n}\r\n", "// tabulka\r\n.table {\n  width: 100%;\r\n\n  border: none;\r\n\r\n  tr {\n    // zvýraznění řádku po najetí <PERSON>\r\n    &:hover td {\n      background-color: lighten($color_main, 60%);\r\n    }\n  }\r\n\r\n  th,\n  td {\n    padding: 5px; // více prostoru v tabulce\r\n\n    text-align: left;\r\n\n    border: none;\r\n\r\n    @media (min-width: $mqsm) {\n      padding: 10px 20px; // více prostoru v tabulce\r\n    }\n  }\r\n\r\n  // rozlámání tabulky na malých rozlišeních\r\n  @media only screen and (max-width: $mqxs) {\n    display: block;\r\n\r\n    th,\n    td,\n    tr,\n    thead,\n    tbody {\n      display: block;\r\n    }\r\n\r\n    th + th {\n      border-top: 1px solid $color_main_light;\r\n    }\r\n\r\n    tr td:last-child {\n      border-bottom: 2px solid $color_main;\r\n    }\n  }\r\n\r\n  // hlavička tabulky\r\n  th {\n    color: $color_white;\r\n\n    background-color: $color_main_light;\r\n  }\r\n\r\n  a {\n    color: $color_main;\r\n  }\n}\r\n\r\n// vertikální tabulka\r\n.table--vertical {\n  th {\n    text-align: left;\r\n  }\n}\r\n\r\n// tabulka provonání\r\n.table--compare {\n  td {\n    text-align: center;\r\n    vertical-align: top;\r\n  }\r\n\r\n  img {\n    display: inline-block;\r\n  }\r\n\r\n  // nebudeme zdvojovat poslední linku\r\n  tr td:last-child {\n    border-bottom: 1px solid $color_main;\r\n  }\r\n\r\n  // zrušení rozlámání na malých rozlišeních\r\n  @media only screen and (max-width: $mqxs) {\n    display: table;\r\n\r\n    th,\n    td {\n      display: table-cell;\r\n    }\r\n\r\n    tr,\n    thead,\n    tbody {\n      display: table-row;\r\n    }\n  }\r\n\r\n  // zvětšení boxů od většího rozlišení\r\n  @media (min-width: $mqsm) {\n    th {\n      width: 5%;\r\n    }\r\n\r\n    td {\n      width: 20%;\r\n    }\n  }\n}\r\n\r\n// produkt v tabulce\r\n.table__product {\n  img {\n    display: inline-block;\r\n\n    width: 45px;\r\n    margin-right: 10px;\r\n\n    vertical-align: middle;\r\n  }\r\n\r\n  a {\n    font-weight: 700;\r\n    text-decoration: none;\r\n  }\n}\r\n", "// zálozky\r\n.tabs {\n  margin-top: 20px;\r\n\r\n  @media (min-width: $mqsm) {\n    border-bottom: 1px solid $color_main_light;\r\n  }\n}\r\n\r\n// názvy z<PERSON>ložek\r\n.tabs__name {\n  display: block;\r\n\n  margin-bottom: -1px; // záporný margin pro aktuální záložku\r\n  padding: 15px 20px;\r\n\n  color: $color_white;\r\n  text-decoration: none;\r\n\n  background-color: $color_main_light;\r\n\r\n  &:hover,\n  &:focus {\n    background-color: $color_main;\r\n  }\r\n\r\n  // aktivní z<PERSON>ložka\r\n  &.is-active {\n    color: $color_main;\r\n    font-weight: bold;\r\n\n    background-color: $color_secondary;\r\n  }\n}\r\n\r\n// obsah záložek\r\n.tabs__into {\n  padding: 12px 15px;\r\n\n  border-right: 1px solid $color_main_light;\r\n  border-bottom: 1px solid $color_main_light;\r\n  border-left: 1px solid $color_main_light;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 20px 25px;\r\n  }\n}\r\n\r\n// nadpis záložky\r\n.tabs__header {\n  margin-top: 0;\r\n\n  font-size: 24px;\r\n}\r\n", "// výpis produktů\r\n.product {\n  margin: 5px 0;\r\n  padding: 10px;\r\n\n  text-align: center;\r\n\n  border: 3px solid $color_main;\r\n  background-color: #fff;\r\n\r\n  @media (min-width: $mq_menu) {\n    margin: 10px 0;\r\n  }\r\n\r\n  &:hover,\n  &:focus {\n    border-color: $color_secondary;\r\n  }\r\n\r\n  // štítky u produktu\r\n  // přetěžujeme pozici - chceme formátovat pouze na výpisu\r\n  .labels {\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n\n    width: 1px; // pro zarovnání štítků pod sebe a zachování inline bloku štítků\r\n\n    text-align: left;\r\n\r\n    // men<PERSON><PERSON> mezery mezi štítky\r\n    .label {\n      margin-bottom: 2px;\r\n    }\n  }\n}\r\n\r\n// hlavička produktu\r\n.product__header {\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\n  min-height: 70px;\r\n  margin: 0;\r\n\n  color: $color_white;\r\n  font-size: 18px;\r\n  line-height: 1.2;\r\n\n  background-color: $color_main;\r\n\r\n  @media (min-width: $mqsm) {\n    font-size: 20px;\r\n  }\r\n\r\n  a {\n    display: block;\r\n\n    width: 100%;\r\n    padding: 10px;\r\n\n    color: $color_white;\r\n    text-decoration: none;\r\n  }\n}\r\n\r\n// obrázek produktu\r\n.product__image {\n  position: relative; // pro absolutní pozicování štítků\r\n\r\n  img {\n    width: 100%;\r\n  }\n}\r\n\r\n// zkrácený text produktu\r\n.product__info {\n  margin: 4px 0 12px 0;\r\n\n  font-size: 14px;\r\n  line-height: 1.3;\r\n}\r\n\r\n// cena\r\n.product__price {\n  position: absolute;\r\n  bottom: 2px;\r\n  left: 0;\r\n\n  padding: 7px 10px;\r\n\n  color: $color_white;\r\n  font-size: 13px;\r\n\n  background: $color_main;\r\n\r\n  // částka ceny\r\n  strong {\n    font-size: 20px;\r\n  }\n}\r\n\r\n// ovládací prvky (tlačítka, inputy)\r\n.product__controls {\n  position: absolute;\r\n  right: 0;\r\n  bottom: 2px;\r\n\r\n  .btn {\n    margin: 0;\r\n\n    font-weight: bold;\r\n\n    border-radius: 0;\r\n  }\r\n\r\n  .icon {\n    width: 25px;\r\n  }\n}\r\n", "// detail produktu\r\n.product-detail {\n  padding: 30px 0;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 60px 0;\r\n  }\r\n\r\n  .table {\n    input {\n      width: 65px; // ošetření velikosti inputu v tabulce\r\n    }\n  }\n}\r\n\r\n// základní styly\r\n.product-detail__content {\n  a {\n    color: $color_main;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_main_dark;\r\n    }\n  }\n}\r\n\r\n// nadpis produktu\r\n.product-detail__header {\n  margin-top: 0;\r\n\n  color: $color_main;\r\n  font-size: 24px;\r\n\r\n  @media (min-width: $mqsm) {\n    font-size: 30px;\r\n  }\n}\r\n\r\n// popis produktu\r\n.product-detail__description {\n  margin-bottom: 20px;\r\n}\r\n\r\n// základní informace\r\n.product-detail__info {\n  margin-top: 16px;\r\n  margin-bottom: 16px;\r\n\n  line-height: 1.5;\r\n\r\n  p {\n    margin: 0 0 20px 0;\r\n  }\r\n\r\n  a {\n    color: $color_main;\r\n  }\n}\r\n\r\n// cena produktu\r\n.product-detail__price {\n  margin-top: 16px;\r\n\n  line-height: 1.3;\r\n\r\n  strong {\n    color: $color_price;\r\n    font-size: 24px;\r\n  }\n}\r\n\r\n// hlavní obrázek produktu\r\n.product-detail__image {\n  margin-right: 20px;\r\n  padding: 5px;\r\n\n  border: 3px solid $color_main;\r\n\r\n  img {\n    display: block;\r\n\n    width: 100%;\r\n  }\n}\r\n\r\n// další obr<PERSON>zky\r\n.product-detail__gallery {\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n\n  margin: 5px -10px;\r\n\n  font-size: 0;\r\n\r\n  a {\n    display: block;\r\n    flex: 0 1 25%;\r\n\n    width: 25%;\r\n    margin: 5px;\r\n    padding: 5px;\r\n\n    border: 3px solid $color_main;\r\n\n    transition: opacity 0.3s;\r\n\r\n    &:hover,\n    &:focus {\n      opacity: 0.8;\r\n    }\r\n\r\n    span {\n      display: block;\r\n      overflow: hidden;\r\n\n      height: 250px;\r\n    }\n  }\r\n\r\n  img {\n    display: block;\r\n  }\n}\r\n\r\n// pomocné tlačítka\r\n.product-detail__helpers {\n  margin: 16px 0;\r\n}\r\n\r\n// hlídací pes\r\n.product-detail__watchdog {\n  p {\n    margin: 8px 0;\r\n  }\r\n\r\n  input[type='email'] {\n    max-width: 150px;\r\n    padding: 3px 5px;\r\n\n    font-size: 14px;\r\n  }\r\n\r\n  strong {\n    display: inline-block;\r\n\n    padding-bottom: 5px;\r\n\n    color: $color_main;\r\n  }\r\n\r\n  .icon {\n    width: 18px;\r\n\n    vertical-align: bottom;\r\n  }\n}\r\n\r\n// výpis chatu\r\n.product-detail__comments {\n  a {\n    color: $color_main;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_main_dark;\r\n    }\n  }\n}\r\n\r\n// varianty produktu\r\n.product-detail__variants {\n  margin-top: 20px;\r\n\r\n  h2 {\n    color: $color_main;\r\n  }\r\n\r\n  p {\n    text-align: center;\r\n\r\n    @media (min-width: $mqsm) {\n      text-align: right;\r\n    }\n  }\n}\r\n\r\n// hodnocení\r\n.product-detail__rating {\n  float: right;\r\n\n  width: 300px;\r\n  margin: 0 0 20px 20px;\r\n  padding: 20px;\r\n\n  font-size: 14px;\r\n\n  background-color: lighten($color_secondary, 25%);\r\n\r\n  h3 {\n    margin: 0 0 10px 0;\r\n\n    font-size: 15px;\r\n  }\r\n\r\n  p {\n    margin: 0;\r\n    padding: 10px 0 5px 0;\r\n  }\n}\r\n\r\n// koupit\r\n.product-detail__buy {\n  margin-top: 15px;\r\n}\r\n", "// ob<PERSON><PERSON><PERSON><PERSON><PERSON> proces\r\n.order {\n  // základ<PERSON><PERSON> pod<PERSON> nad<PERSON>ů\r\n  h1,\n  h2,\n  h3,\n  h4 {\n    color: $color_main_light;\r\n    font-weight: 300;\r\n    text-transform: uppercase;\r\n  }\r\n\r\n  h1 {\n    margin: 20px 0;\r\n\n    font-size: 22px;\r\n\r\n    @media (min-width: $mqsm) {\n      margin: 40px 0 30px 0;\r\n\n      font-size: 30px;\r\n    }\n  }\r\n\r\n  h2 {\n    font-size: 20px;\r\n  }\r\n\r\n  h3 {\n    font-size: 18px;\r\n\r\n    @media (min-width: $mqsm) {\n      font-size: 25px;\r\n    }\n  }\r\n\r\n  h4 {\n    font-size: 16px;\r\n\r\n    @media (min-width: $mqsm) {\n      font-size: 18px;\r\n    }\n  }\r\n\r\n  p {\n    @media (max-width: $mqsm) {\n      margin: 5px 0;\r\n    }\n  }\r\n\r\n  a {\n    color: $color_main_light;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_main_dark;\r\n    }\n  }\r\n\r\n  // ošetření barev odkazů\r\n  a.btn,\n  a.control .icon {\n    color: $color_white;\r\n  }\n}\r\n\r\n// tabulka v objednávce\r\n.order__table {\n  width: 100%;\r\n\n  border: 0;\r\n\r\n  th,\n  td {\n    padding: 5px; // více prostoru v tabulce\r\n\n    border: 0;\r\n\r\n    @media (min-width: $mqsm) {\n      padding: 10px 20px; // více prostoru v tabulce\r\n    }\n  }\r\n\r\n  // ošetření velikosti inputu\r\n  input {\n    width: 65px;\r\n  }\r\n\r\n  // rozlámání tabulky na malých rozlišeních\r\n  @media only screen and (max-width: $mqxs) {\n    display: block;\r\n\r\n    th,\n    td,\n    tr,\n    thead,\n    tbody {\n      display: block;\r\n    }\r\n\r\n    th + th {\n      border-top: 1px solid $color_main_dark;\r\n    }\r\n\r\n    tr td:last-child {\n      border-bottom: 2px solid $color_main_light;\r\n    }\n  }\n}\r\n\r\n// produkt + obrázek\r\n.order__product {\n  // zmenšení a pozice obrázku\r\n  img {\n    float: left;\r\n\n    width: 70px;\r\n    height: auto;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  // odkazy\r\n  a {\n    color: $color_main_light;\r\n    font-size: 17px;\r\n    text-decoration: none;\r\n    text-transform: uppercase;\r\n\r\n    img {\n      &:hover {\n        opacity: 0.8;\r\n      }\n    }\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_main_dark;\r\n    }\n  }\n}\r\n\r\n// počet kusů\r\n.order__count {\n  white-space: nowrap;\r\n}\r\n\r\n// cena\r\n.order__price {\n  max-width: 65px;\r\n\n  font-weight: 700;\r\n  text-align: right;\r\n  white-space: nowrap;\r\n\r\n  @media only screen and (max-width: $mqxs) {\n    width: 100%;\r\n    max-width: 100%;\r\n\n    font-size: 18px;\r\n    text-align: right;\r\n  }\n}\r\n\r\n// cena za produkty celkem\r\n.order__price-sum {\n  td {\n    color: $color_main;\r\n\r\n    @media only screen and (max-width: $mqxs) {\n      display: none;\r\n    }\n  }\n}\r\n\r\n// sleva\r\n.order__discount {\n  td {\n    color: $color_success;\r\n  }\n}\r\n\r\n// celková cena vč slevy, atd.\r\n.order__price-final {\n  margin-top: 20px;\r\n  padding: 20px;\r\n\n  color: $color_white;\r\n  font-size: 16px;\r\n  font-weight: 300;\r\n\n  background-color: $color_main_light;\r\n\r\n  strong {\n    float: right;\r\n\n    font-weight: 700;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  @media (min-width: $mqsm) {\n    font-size: 25px;\r\n\n    border-radius: $radius;\r\n  }\n}\r\n\r\n// cena za dopravu\r\n.order__price-delivery {\n  margin-top: 10px;\r\n  padding-top: 20px;\r\n\r\n  strong {\n    float: right;\r\n\n    font-weight: 700;\r\n    white-space: nowrap;\r\n  }\n}\r\n\r\n// prázdný košík\r\n.order__empty {\n}\r\n\r\n// zadání PSČ\r\n.order__place {\n  position: relative;\r\n\n  margin: 8px 0;\r\n  padding: 15px 20px 15px 110px;\r\n\n  color: $color_white;\r\n  font-size: 20px;\r\n  line-height: 1.5;\r\n\n  background-color: $color_label_action;\r\n\r\n  input {\n    border: none;\r\n  }\r\n\r\n  em {\n    font-size: 15px;\r\n    font-style: normal;\r\n  }\r\n\r\n  .icon {\n    position: absolute;\r\n    top: 20px;\r\n    left: 25px;\r\n\n    width: 60px;\r\n  }\n}\r\n\r\n// slevový kupón\r\n.order__coupon {\n  margin-top: 16px;\r\n\r\n  @media (max-width: $mqxxs) {\n    font-size: 14px;\r\n  }\n}\r\n\r\n// platební možnosti\r\n.order__payment,\n.order__delivery {\n  position: relative;\r\n\n  margin-bottom: 25px;\r\n  padding-left: 30px;\r\n\n  font-size: 16px;\r\n\r\n  input {\n    position: absolute;\r\n\n    margin-left: -20px;\r\n  }\r\n\r\n  a {\n    color: $color_main_light;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_main_dark;\r\n    }\n  }\n}\r\n\r\n// nápověda\r\n.order__help {\n  font-size: 14px;\r\n}\r\n", "// postup objednávky\r\n.order-progress {\n}\r\n\r\n.order-progress__item {\n  // hover pouze na odkazy\r\n  a {\n    &:hover,\n    &:focus {\n      color: $color_white;\r\n\n      background-color: $color_secondary_dark;\r\n    }\n  }\r\n\r\n  // aktivní záložka\r\n  a.is-active,\n  span.is-active {\n    color: $color_main;\r\n    font-weight: bold;\r\n\n    background-color: $color_secondary;\r\n\r\n    @media (min-width: $mqsm) {\n      border-bottom: 1px solid $color_white;\r\n    }\n  }\r\n\r\n  a,\n  span {\n    display: block;\r\n\n    margin-bottom: -1px; // záporný margin pro aktuální záložku\r\n    padding: 8px 10px;\r\n\n    color: $color_white;\r\n    text-decoration: none;\r\n\n    background-color: $color_main_light;\r\n\r\n    @media (min-width: $mqsm) {\n      padding: 15px 20px;\r\n    }\n  }\n}\r\n", "// tělo obje<PERSON>\r\n.order-content {\n  padding: 30px 0;\r\n}\r\n", "// doplňující informace k objednávce\r\n.order-info {\n}\r\n\r\n.order-info__item {\n  margin: 8px 0;\r\n  padding: 12px 20px;\r\n\n  color: $color_white;\r\n  line-height: 1.5;\r\n\n  border-radius: $radius;\r\n  background-color: $color_main;\r\n\r\n  a {\n    color: $color_white;\r\n\r\n    &:hover,\n    &:focus {\n      opacity: 0.8;\r\n    }\n  }\r\n\r\n  // zvýraznění tučných částí\r\n  strong {\n    font-size: 18px;\r\n  }\n}\r\n\r\n// sleva při nákupu od určité částky\r\n.order-info--discount {\n  background-color: $color_success;\r\n}\r\n\r\n// doprava zdarma od určité částky\r\n.order-info--delivery {\n  background-color: $color_label_delivery;\r\n}\r\n\r\n// prekročen váhový limit\r\n.order-info--weight {\n  background-color: $color_danger;\r\n}\r\n\r\n// doplňující text\r\n.order-info--text {\n  background-color: $color_main_light;\r\n}\r\n", "// ovládací tlačítka objednávky\r\n.order-controls {\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n\r\n  // vycentrování na malém rozlišení\r\n  @media (max-width: $mqsm) {\n    text-align: center;\r\n  }\r\n\r\n  // mezery mezi tlačítky\r\n  a,\n  button {\n    margin: 2px 0;\r\n  }\r\n\r\n  // pokračující tlačítka vpravo\r\n  @media (min-width: $mqsm) {\n    & > div:last-child {\n      text-align: right;\r\n    }\n  }\n}\r\n", "// dopravy a platby\r\n.order-delivery {\n  @media (min-width: $mqxs) {\n    padding: 15px 20px 0 20px;\r\n  }\r\n\r\n  img {\n    display: inline-block;\r\n  }\n}\r\n\r\n// položky\r\n.order-delivery__item {\n  padding: 0 20px 10px 20px;\r\n\n  border-bottom: 1px solid $color_main;\r\n\n  cursor: pointer;\r\n}\r\n\r\n// typy dopravy\r\n.order-delivery__types {\n  border-top: 1px solid $color_main;\r\n  border-right: 1px solid $color_main;\r\n  border-left: 1px solid $color_main;\r\n}\r\n\r\n// platby\r\n.order-delivery__payments {\n  border-top: 1px solid $color_main;\r\n  border-right: 1px solid $color_main;\r\n  border-left: 1px solid $color_main;\r\n}\r\n\r\n.order-delivery__payment ul {\n  margin: 0;\r\n  padding: 0;\r\n\n  list-style-type: none;\r\n}\r\n\r\n.order-delivery__payment label {\n  display: block;\r\n\n  padding: 16px 20px 12px 20px;\r\n\n  border-bottom: 1px solid $color_main;\r\n\n  cursor: pointer;\r\n}\r\n\r\n.order-delivery__payment img {\n  margin-right: 5px;\r\n\n  vertical-align: middle;\r\n}\r\n\r\n// nadpis dopravy\r\n.order-delivery__name {\n  font-size: 20px !important;\r\n\r\n  img {\n    margin-top: -3px;\r\n    margin-right: 3px;\r\n\n    vertical-align: middle;\r\n  }\n}\r\n\r\n// obsah<PERSON>, platby\r\n.order-delivery__content {\n}\r\n\r\n// popis dopravy\r\n.order-delivery__description {\n}\r\n\r\n// volby plateb\r\n.order-delivery__options {\n  margin-left: 5px;\r\n}\r\n", "// zarovnání textu\r\n.center {\n  text-align: center;\r\n}\r\n\r\n.left {\n  text-align: left;\r\n}\r\n\r\n.right {\n  text-align: right;\r\n}\r\n\r\n// nezalamování\r\n.nowrap {\n  white-space: nowrap;\r\n}\r\n\r\n// floatování\r\n.fleft {\n  float: left;\r\n}\r\n\r\n.fright {\n  float: right;\r\n}\r\n\r\n.cls {\n  clear: both;\r\n}\r\n\r\n// micro clearfix ( http://nicolasgallagher.com/micro-clearfix-hack/ )\r\n.cf:before,\n.cf:after {\n  content: ' ';\r\n\n  display: table;\r\n}\r\n\r\n.cf:after {\n  clear: both;\r\n}\r\n\r\n.cf {\n  *zoom: 1;\r\n}\r\n", "// tiskový styl\r\n// resetujeme základní bloky a objekty, upravujeme podle potřeby\r\n\r\n@media print {\r\n\r\n  * {\r\n    font-family: sans-serif !important;\r\n    color: #000000 !important;\r\n    background: #ffffff !important;\r\n    text-shadow: none !important;\r\n    box-shadow: none !important;\r\n    border: none !important;\r\n    width: auto !important;\r\n    height: auto !important;\r\n    max-width: none !important;\r\n    position: relative !important;\r\n  }\r\n  body {\r\n    width: 100% !important;\r\n    margin: 0px !important;\r\n    padding: 0px !important;\r\n    line-height: 1.4 !important;\r\n    word-spacing: 1.1pt !important;\r\n    letter-spacing: 0.2pt !important;\r\n    font-family: sans-serif !important;\r\n    color: #000000 !important;\r\n    background: none !important;\r\n    font-size: 12pt !important;\r\n  }\r\n  h1, h2, h3, h4 { clear: both !important; }\r\n  h1 { font-size: 19pt !important; }\r\n  h2 { font-size: 17pt !important; }\r\n  h3 { font-size: 15pt !important; }\r\n  h4 { font-size: 12pt !important; }\r\n  img { margin: 1em 1.5em 1.5em 0em !important; }\r\n  ul, ol { padding-left: 20px !important; }\r\n  a img { border: none !important; }\r\n  a, a:link, a:visited, a:hover, a:active, a:focus { text-decoration: none !important; color:#000000 !important; }\r\n  table { margin: 1px !important; text-align:left !important; }\r\n  th { border-bottom: 1px solid #000000 !important;  font-weight: bold !important; }\r\n  td { border-bottom: 1px solid #000000 !important; }\r\n  th, td { padding: 4px 10px 4px 0px !important; }\r\n  tr { page-break-inside: avoid !important; }\r\n\r\n  // skryjeme nepotřebné části\r\n  .no, .noprint,\r\n  object, iframe, form, button,\r\n  .nav, .btn, .breadcrumb, .nav-product, .filter, .share, .slider, .basket, .login, .search, .pagination,\r\n  .category__item, .order-progress, .category__filters, .product .labels, .header__nav, .header__contact, .filter__row, .content-header__image { display: none !important; }\r\n\r\n  // formuláře v objednávce\r\n  .order form { display: block !important; }\r\n\r\n  // specifické úpravy\r\n  .product__wrapper { width: 32% !important; display: inline-block !important; border-top: 1px dotted gray !important; vertical-align: top !important; }\r\n  .product__wrapper:nth-child(3n+1) { clear: both; }\r\n  .product img { margin: 0 auto !important; }\r\n  .product__header { font-size: 16px !important; }\r\n  .gallery { text-align: left; }\r\n  .home__title, .content-header { margin: 0 !important; padding: 0 !important; }\r\n\r\n  // oddělení řádků\r\n  .row { margin: 10px }\r\n  .row .row { margin: 0; }\r\n\r\n  // hlavička\r\n  .header { padding: 0; }\r\n\r\n  // logo\r\n  .header__logo { margin: 0 !important; }\r\n  .header__logo img { background-color: #000000 !important; padding: 10px; margin: 0 !important; -webkit-print-color-adjust: exact; }\r\n\r\n  // skryjeme neaktivní taby\r\n  .tabs__name { display: none !important; }\r\n  .tabs__into { padding: 0 !important; }\r\n\r\n  // objednávka\r\n  .order table { width: 100% !important; }\r\n  .order input, .order textarea { border: 1px solid #000000 !important; }\r\n\r\n  // patička\r\n  .footer > .container-fluid { display: none; }\r\n  .footer__copyright { clear: both; }\r\n\r\n}\r\n"]}
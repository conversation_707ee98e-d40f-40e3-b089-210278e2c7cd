
.icon--arrow-down {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--arrow-left {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--arrow-right {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--arrow-up {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--basket {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--calc {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--chat {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--close {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--dog {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--download {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--email {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--facebook {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--googleplus {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--info {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--instagram {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--ko {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--location {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--login {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--man {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--minus {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--ok {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--phone {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--plus {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--print {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--product-1 {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--product-2 {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--product-3 {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--product-4 {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--search {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--star {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--twitter {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--warn {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--wheel {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--why-1 {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--why-2 {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

.icon--why-3 {
  width: 16px;

  &:before {
    padding-top: calc(16 / 16) * 100%;
  }
}

/* Sticky footer styles
-------------------------------------------------- */
html {
  position: relative;
  min-height: 100%;
}
body {
  /* Margin bottom by footer height */
  margin-bottom: 60px;
}

.autocomplete-suggestions { border: 1px solid #999; background: #FFF; overflow: auto; }
.autocomplete-suggestion { padding: 2px 5px; white-space: nowrap; overflow: hidden; }
.autocomplete-selected { background: #F0F0F0; }
.autocomplete-suggestions strong { font-weight: normal; color: #3399FF; }
.autocomplete-group { padding: 2px 5px; }
.autocomplete-group strong { display: block; border-bottom: 1px solid #000; }


.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  /* Set the fixed height of the footer here */
  height: 45px;
  background-color: #eeeeee;
}


/* Custom page CSS
-------------------------------------------------- */
/* Not required for template or sticky footer method. */

/* maxim<PERSON><PERSON><PERSON> */
.container-fluid {
  max-width: 1200px;
}

/* padding z důvodu menu */
body { padding-top: 25px; }

/* úprava formuláře ve filtrech */
.panel-custom label {
  margin-bottom: 0;
  font-weight: normal;
}
.panel-custom .input-group {
  margin-top: 5px;
  margin-bottom: 5px;
}
.panel-custom .panel-heading {
  padding: 5px 10px;
}
.panel-custom .panel-body {
  padding: 5px 10px;
}

/* styly menu */
.navbar-inverse {
  color: #fff;
  border: none;
}
.navbar-inverse .navbar-nav>li>a {
  color: #fff;
}
.navbar-inverse .navbar-nav>li>a:hover {
  color: #f0f0f0;
}

/* úprava pozice loga */
.navbar-brand {
  padding-top: 11px;
}

/* úprava patičky */
.footer {
  padding-top: 10px;
  text-align: center;
}
.footer a {
  text-decoration: underline;
}
.footer p {
  margin-bottom: 0;
}

/* fix záložek, aby mezi nimi mohl být tag form */
.tab-content form > .tab-pane {
  display: none;
}
.tab-content form > .active {
  display: block;
}

/* změna barev ikonek */
.glyphicon-remove {
  color: #d9534f !important;
}
.glyphicon-pencil {
  color: #5cb85c !important;
}
.glyphicon-ok {
  color: #337ab7 !important;
}

/* úprava tabů */
.nav-tabs {
  margin-bottom: 25px;
}

/* doladění */
.panel label {
  margin-right: 4px;
}
.panel label input[type="submit"], .panel label input[type="checkbox"] {
  margin-right: 4px;
}

/* fix menu */
@media (max-width: 767px){
  .navbar-inverse .navbar-nav .open .dropdown-menu>li>a {
    color: #fff;
  }
}
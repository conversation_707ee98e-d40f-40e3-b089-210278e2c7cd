!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?e(require("jquery")):e(window.jQuery||window.Zepto)}(function(u){function e(){}function c(e,t){m.ev.on(N+e+_,t)}function d(e,t,n,i){var r=document.createElement("div");return r.className="mfp-"+e,n&&(r.innerHTML=n),i?t&&t.appendChild(r):(r=u(r),t&&r.appendTo(t)),r}function f(e,t){m.ev.triggerHandler(N+e,t),m.st.callbacks&&(e=e.charAt(0).toLowerCase()+e.slice(1),m.st.callbacks[e])&&m.st.callbacks[e].apply(m,u.isArray(t)?t:[t])}function p(e){return e===L&&m.currTemplate.closeBtn||(m.currTemplate.closeBtn=u(m.st.closeMarkup.replace("%title%",m.st.tClose)),L=e),m.currTemplate.closeBtn}function a(){u.magnificPopup.instance||((m=new e).init(),u.magnificPopup.instance=m)}function o(){y&&(h.after(y.addClass(s)).detach(),y=null)}function r(){n&&u(document.body).removeClass(n)}function t(){r(),m.req&&m.req.abort()}var m,i,g,l,v,L,s,h,y,n,b="Close",B="BeforeClose",w="MarkupParse",C="Open",M="Change",N="mfp",_="."+N,x="mfp-ready",j="mfp-removing",k="mfp-prevent-close",$=!!window.jQuery,I=u(window),F=(u.magnificPopup={instance:null,proto:e.prototype={constructor:e,init:function(){var e=navigator.appVersion;m.isLowIE=m.isIE8=document.all&&!document.addEventListener,m.isAndroid=/android/gi.test(e),m.isIOS=/iphone|ipad|ipod/gi.test(e),m.supportsTransition=function(){var e=document.createElement("p").style,t=["ms","O","Moz","Webkit"];if(void 0!==e.transition)return!0;for(;t.length;)if(t.pop()+"Transition"in e)return!0;return!1}(),m.probablyMobile=m.isAndroid||m.isIOS||/(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent),g=u(document),m.popupsCache={}},open:function(e){if(!1===e.isObj){m.items=e.items.toArray(),m.index=0;for(var t,n=e.items,i=0;i<n.length;i++)if((t=(t=n[i]).parsed?t.el[0]:t)===e.el[0]){m.index=i;break}}else m.items=u.isArray(e.items)?e.items:[e.items],m.index=e.index||0;if(!m.isOpen){m.types=[],v="",e.mainEl&&e.mainEl.length?m.ev=e.mainEl.eq(0):m.ev=g,e.key?(m.popupsCache[e.key]||(m.popupsCache[e.key]={}),m.currTemplate=m.popupsCache[e.key]):m.currTemplate={},m.st=u.extend(!0,{},u.magnificPopup.defaults,e),m.fixedContentPos="auto"===m.st.fixedContentPos?!m.probablyMobile:m.st.fixedContentPos,m.st.modal&&(m.st.closeOnContentClick=!1,m.st.closeOnBgClick=!1,m.st.showCloseBtn=!1,m.st.enableEscapeKey=!1),m.bgOverlay||(m.bgOverlay=d("bg").on("click"+_,function(){m.close()}),m.wrap=d("wrap").attr("tabindex",-1).on("click"+_,function(e){m._checkIfClose(e.target)&&m.close()}),m.container=d("container",m.wrap)),m.contentContainer=d("content"),m.st.preloader&&(m.preloader=d("preloader",m.container,m.st.tLoading));var r=u.magnificPopup.modules;for(i=0;i<r.length;i++){var a=(a=r[i]).charAt(0).toUpperCase()+a.slice(1);m["init"+a].call(m)}f("BeforeOpen"),m.st.showCloseBtn&&(m.st.closeBtnInside?(c(w,function(e,t,n,i){n.close_replaceWith=p(i.type)}),v+=" mfp-close-btn-in"):m.wrap.append(p())),m.st.alignTop&&(v+=" mfp-align-top"),m.fixedContentPos?m.wrap.css({overflow:m.st.overflowY,overflowX:"hidden",overflowY:m.st.overflowY}):m.wrap.css({top:I.scrollTop(),position:"absolute"}),!1!==m.st.fixedBgPos&&("auto"!==m.st.fixedBgPos||m.fixedContentPos)||m.bgOverlay.css({height:g.height(),position:"absolute"}),m.st.enableEscapeKey&&g.on("keyup"+_,function(e){27===e.keyCode&&m.close()}),I.on("resize"+_,function(){m.updateSize()}),m.st.closeOnContentClick||(v+=" mfp-auto-cursor"),v&&m.wrap.addClass(v);var o=m.wH=I.height(),l={},s=(m.fixedContentPos&&m._hasScrollBar(o)&&(s=m._getScrollbarSize())&&(l.marginRight=s),m.fixedContentPos&&(m.isIE7?u("body, html").css("overflow","hidden"):l.overflow="hidden"),m.st.mainClass);return m.isIE7&&(s+=" mfp-ie7"),s&&m._addClassToMFP(s),m.updateItemHTML(),f("BuildControls"),u("html").css(l),m.bgOverlay.add(m.wrap).prependTo(m.st.prependTo||u(document.body)),m._lastFocusedEl=document.activeElement,setTimeout(function(){m.content?(m._addClassToMFP(x),m._setFocus()):m.bgOverlay.addClass(x),g.on("focusin"+_,m._onFocusIn)},16),m.isOpen=!0,m.updateSize(o),f(C),e}m.updateItemHTML()},close:function(){m.isOpen&&(f(B),m.isOpen=!1,m.st.removalDelay&&!m.isLowIE&&m.supportsTransition?(m._addClassToMFP(j),setTimeout(function(){m._close()},m.st.removalDelay)):m._close())},_close:function(){f(b);var e=j+" "+x+" ";m.bgOverlay.detach(),m.wrap.detach(),m.container.empty(),m.st.mainClass&&(e+=m.st.mainClass+" "),m._removeClassFromMFP(e),m.fixedContentPos&&(e={marginRight:""},m.isIE7?u("body, html").css("overflow",""):e.overflow="",u("html").css(e)),g.off("keyup.mfp focusin"+_),m.ev.off(_),m.wrap.attr("class","mfp-wrap").removeAttr("style"),m.bgOverlay.attr("class","mfp-bg"),m.container.attr("class","mfp-container"),!m.st.showCloseBtn||m.st.closeBtnInside&&!0!==m.currTemplate[m.currItem.type]||m.currTemplate.closeBtn&&m.currTemplate.closeBtn.detach(),m.st.autoFocusLast&&m._lastFocusedEl&&u(m._lastFocusedEl).focus(),m.currItem=null,m.content=null,m.currTemplate=null,m.prevHeight=0,f("AfterClose")},updateSize:function(e){var t;m.isIOS?(t=document.documentElement.clientWidth/window.innerWidth,t=window.innerHeight*t,m.wrap.css("height",t),m.wH=t):m.wH=e||I.height(),m.fixedContentPos||m.wrap.css("height",m.wH),f("Resize")},updateItemHTML:function(){var e=m.items[m.index],t=(m.contentContainer.detach(),m.content&&m.content.detach(),(e=e.parsed?e:m.parseEl(m.index)).type),n=(f("BeforeChange",[m.currItem?m.currItem.type:"",t]),m.currItem=e,m.currTemplate[t]||(n=!!m.st[t]&&m.st[t].markup,f("FirstMarkupParse",n),m.currTemplate[t]=!n||u(n)),l&&l!==e.type&&m.container.removeClass("mfp-"+l+"-holder"),m["get"+t.charAt(0).toUpperCase()+t.slice(1)](e,m.currTemplate[t]));m.appendContent(n,t),e.preloaded=!0,f(M,e),l=e.type,m.container.prepend(m.contentContainer),f("AfterChange")},appendContent:function(e,t){(m.content=e)?m.st.showCloseBtn&&m.st.closeBtnInside&&!0===m.currTemplate[t]?m.content.find(".mfp-close").length||m.content.append(p()):m.content=e:m.content="",f("BeforeAppend"),m.container.addClass("mfp-"+t+"-holder"),m.contentContainer.append(m.content)},parseEl:function(e){var t,n=m.items[e];if((n=n.tagName?{el:u(n)}:(t=n.type,{data:n,src:n.src})).el){for(var i=m.types,r=0;r<i.length;r++)if(n.el.hasClass("mfp-"+i[r])){t=i[r];break}n.src=n.el.attr("data-mfp-src"),n.src||(n.src=n.el.attr("href"))}return n.type=t||m.st.type||"inline",n.index=e,n.parsed=!0,m.items[e]=n,f("ElementParse",n),m.items[e]},addGroup:function(t,n){function e(e){e.mfpEl=this,m._openClick(e,t,n)}var i="click.magnificPopup";(n=n||{}).mainEl=t,n.items?(n.isObj=!0,t.off(i).on(i,e)):(n.isObj=!1,n.delegate?t.off(i).on(i,n.delegate,e):(n.items=t).off(i).on(i,e))},_openClick:function(e,t,n){var i=(void 0!==n.midClick?n:u.magnificPopup.defaults).midClick;if(i||!(2===e.which||e.ctrlKey||e.metaKey||e.altKey||e.shiftKey)){i=(void 0!==n.disableOn?n:u.magnificPopup.defaults).disableOn;if(i)if(u.isFunction(i)){if(!i.call(m))return!0}else if(I.width()<i)return!0;e.type&&(e.preventDefault(),m.isOpen)&&e.stopPropagation(),n.el=u(e.mfpEl),n.delegate&&(n.items=t.find(n.delegate)),m.open(n)}},updateStatus:function(e,t){var n;m.preloader&&(i!==e&&m.container.removeClass("mfp-s-"+i),n={status:e,text:t=t||"loading"!==e?t:m.st.tLoading},f("UpdateStatus",n),e=n.status,m.preloader.html(t=n.text),m.preloader.find("a").on("click",function(e){e.stopImmediatePropagation()}),m.container.addClass("mfp-s-"+e),i=e)},_checkIfClose:function(e){if(!u(e).hasClass(k)){var t=m.st.closeOnContentClick,n=m.st.closeOnBgClick;if(t&&n)return!0;if(!m.content||u(e).hasClass("mfp-close")||m.preloader&&e===m.preloader[0])return!0;if(e===m.content[0]||u.contains(m.content[0],e)){if(t)return!0}else if(n&&u.contains(document,e))return!0;return!1}},_addClassToMFP:function(e){m.bgOverlay.addClass(e),m.wrap.addClass(e)},_removeClassFromMFP:function(e){this.bgOverlay.removeClass(e),m.wrap.removeClass(e)},_hasScrollBar:function(e){return(m.isIE7?g.height():document.body.scrollHeight)>(e||I.height())},_setFocus:function(){(m.st.focus?m.content.find(m.st.focus).eq(0):m.wrap).focus()},_onFocusIn:function(e){if(e.target!==m.wrap[0]&&!u.contains(m.wrap[0],e.target))return m._setFocus(),!1},_parseMarkup:function(r,e,t){var a;t.data&&(e=u.extend(t.data,e)),f(w,[r,e,t]),u.each(e,function(e,t){if(void 0===t||!1===t)return!0;var n,i;1<(a=e.split("_")).length?0<(n=r.find(_+"-"+a[0])).length&&("replaceWith"===(i=a[1])?n[0]!==t[0]&&n.replaceWith(t):"img"===i?n.is("img")?n.attr("src",t):n.replaceWith(u("<img>").attr("src",t).attr("class",n.attr("class"))):n.attr(a[1],t)):r.find(_+"-"+e).html(t)})},_getScrollbarSize:function(){var e;return void 0===m.scrollbarSize&&((e=document.createElement("div")).style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(e),m.scrollbarSize=e.offsetWidth-e.clientWidth,document.body.removeChild(e)),m.scrollbarSize}},modules:[],open:function(e,t){return a(),(e=e?u.extend(!0,{},e):{}).isObj=!0,e.index=t||0,this.instance.open(e)},close:function(){return u.magnificPopup.instance&&u.magnificPopup.instance.close()},registerModule:function(e,t){t.options&&(u.magnificPopup.defaults[e]=t.options),u.extend(this.proto,t.proto),this.modules.push(e)},defaults:{disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close">&#215;</button>',tClose:"Close (Esc)",tLoading:"Loading...",autoFocusLast:!0}},u.fn.magnificPopup=function(e){a();var t,n,i,r=u(this);return"string"==typeof e?"open"===e?(t=$?r.data("magnificPopup"):r[0].magnificPopup,n=parseInt(arguments[1],10)||0,i=t.items?t.items[n]:(i=r,(i=t.delegate?i.find(t.delegate):i).eq(n)),m._openClick({mfpEl:i},r,t)):m.isOpen&&m[e].apply(m,Array.prototype.slice.call(arguments,1)):(e=u.extend(!0,{},e),$?r.data("magnificPopup",e):r[0].magnificPopup=e,m.addGroup(r,e)),r},"inline"),E=(u.magnificPopup.registerModule(F,{options:{hiddenClass:"hide",markup:"",tNotFound:"Content not found"},proto:{initInline:function(){m.types.push(F),c(b+"."+F,function(){o()})},getInline:function(e,t){var n,i,r;return o(),e.src?(n=m.st.inline,(i=u(e.src)).length?((r=i[0].parentNode)&&r.tagName&&(h||(s=n.hiddenClass,h=d(s),s="mfp-"+s),y=i.after(h).detach().removeClass(s)),m.updateStatus("ready")):(m.updateStatus("error",n.tNotFound),i=u("<div>")),e.inlineElement=i):(m.updateStatus("ready"),m._parseMarkup(t,{},e),t)}}}),"ajax");u.magnificPopup.registerModule(E,{options:{settings:null,cursor:"mfp-ajax-cur",tError:'<a href="%url%">The content</a> could not be loaded.'},proto:{initAjax:function(){m.types.push(E),n=m.st.ajax.cursor,c(b+"."+E,t),c("BeforeChange."+E,t)},getAjax:function(i){n&&u(document.body).addClass(n),m.updateStatus("loading");var e=u.extend({url:i.src,success:function(e,t,n){e={data:e,xhr:n};f("ParseAjax",e),m.appendContent(u(e.data),E),i.finished=!0,r(),m._setFocus(),setTimeout(function(){m.wrap.addClass(x)},16),m.updateStatus("ready"),f("AjaxContentAdded")},error:function(){r(),i.finished=i.loadError=!0,m.updateStatus("error",m.st.ajax.tError.replace("%url%",i.src))}},m.st.ajax.settings);return m.req=u.ajax(e),""}}});var z;u.magnificPopup.registerModule("image",{options:{markup:'<div class="mfp-figure"><div class="mfp-close"></div><figure><div class="mfp-img"></div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"></div><div class="mfp-counter"></div></div></figcaption></figure></div>',cursor:"mfp-zoom-out-cur",titleSrc:"title",verticalFit:!0,tError:'<a href="%url%">The image</a> could not be loaded.'},proto:{initImage:function(){var e=m.st.image,t=".image";m.types.push("image"),c(C+t,function(){"image"===m.currItem.type&&e.cursor&&u(document.body).addClass(e.cursor)}),c(b+t,function(){e.cursor&&u(document.body).removeClass(e.cursor),I.off("resize"+_)}),c("Resize"+t,m.resizeImage),m.isLowIE&&c("AfterChange",m.resizeImage)},resizeImage:function(){var e,t=m.currItem;t&&t.img&&m.st.image.verticalFit&&(e=0,m.isLowIE&&(e=parseInt(t.img.css("padding-top"),10)+parseInt(t.img.css("padding-bottom"),10)),t.img.css("max-height",m.wH-e))},_onImageHasSize:function(e){e.img&&(e.hasSize=!0,z&&clearInterval(z),e.isCheckingImgSize=!1,f("ImageHasSize",e),e.imgHidden)&&(m.content&&m.content.removeClass("mfp-loading"),e.imgHidden=!1)},findImageSize:function(t){function n(e){z&&clearInterval(z),z=setInterval(function(){0<r.naturalWidth?m._onImageHasSize(t):(200<i&&clearInterval(z),3===++i?n(10):40===i?n(50):100===i&&n(500))},e)}var i=0,r=t.img[0];n(1)},getImage:function(e,t){function n(){e&&(e.img[0].complete?(e.img.off(".mfploader"),e===m.currItem&&(m._onImageHasSize(e),m.updateStatus("ready")),e.hasSize=!0,e.loaded=!0,f("ImageLoadComplete")):++a<200?setTimeout(n,100):i())}function i(){e&&(e.img.off(".mfploader"),e===m.currItem&&(m._onImageHasSize(e),m.updateStatus("error",o.tError.replace("%url%",e.src))),e.hasSize=!0,e.loaded=!0,e.loadError=!0)}var r,a=0,o=m.st.image,l=t.find(".mfp-img");return l.length&&((r=document.createElement("img")).className="mfp-img",e.el&&e.el.find("img").length&&(r.alt=e.el.find("img").attr("alt")),e.img=u(r).on("load.mfploader",n).on("error.mfploader",i),r.src=e.src,l.is("img")&&(e.img=e.img.clone()),0<(r=e.img[0]).naturalWidth?e.hasSize=!0:r.width||(e.hasSize=!1)),m._parseMarkup(t,{title:function(e){if(e.data&&void 0!==e.data.title)return e.data.title;var t=m.st.image.titleSrc;if(t){if(u.isFunction(t))return t.call(m,e);if(e.el)return e.el.attr(t)||""}return""}(e),img_replaceWith:e.img},e),m.resizeImage(),e.hasSize?(z&&clearInterval(z),e.loadError?(t.addClass("mfp-loading"),m.updateStatus("error",o.tError.replace("%url%",e.src))):(t.removeClass("mfp-loading"),m.updateStatus("ready"))):(m.updateStatus("loading"),e.loading=!0,e.hasSize||(e.imgHidden=!0,t.addClass("mfp-loading"),m.findImageSize(e))),t}}});function S(e){var t;m.currTemplate[T]&&(t=m.currTemplate[T].find("iframe")).length&&(e||(t[0].src="//about:blank"),m.isIE8)&&t.css("display",e?"block":"none")}function P(e){var t=m.items.length;return t-1<e?e-t:e<0?t+e:e}function H(e,t,n){return e.replace(/%curr%/gi,t+1).replace(/%total%/gi,n)}u.magnificPopup.registerModule("zoom",{options:{enabled:!1,easing:"ease-in-out",duration:300,opener:function(e){return e.is("img")?e:e.find("img")}},proto:{initZoom:function(){var e,t,n,i,r,a,o=m.st.zoom,l=".zoom";o.enabled&&m.supportsTransition&&(t=o.duration,n=function(e){var e=e.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),t="all "+o.duration/1e3+"s "+o.easing,n={position:"fixed",zIndex:9999,left:0,top:0,"-webkit-backface-visibility":"hidden"},i="transition";return n["-webkit-"+i]=n["-moz-"+i]=n["-o-"+i]=n[i]=t,e.css(n),e},i=function(){m.content.css("visibility","visible")},c("BuildControls"+l,function(){m._allowZoom()&&(clearTimeout(r),m.content.css("visibility","hidden"),(e=m._getItemToZoom())?((a=n(e)).css(m._getOffset()),m.wrap.append(a),r=setTimeout(function(){a.css(m._getOffset(!0)),r=setTimeout(function(){i(),setTimeout(function(){a.remove(),e=a=null,f("ZoomAnimationEnded")},16)},t)},16)):i())}),c(B+l,function(){if(m._allowZoom()){if(clearTimeout(r),m.st.removalDelay=t,!e){if(!(e=m._getItemToZoom()))return;a=n(e)}a.css(m._getOffset(!0)),m.wrap.append(a),m.content.css("visibility","hidden"),setTimeout(function(){a.css(m._getOffset())},16)}}),c(b+l,function(){m._allowZoom()&&(i(),a&&a.remove(),e=null)}))},_allowZoom:function(){return"image"===m.currItem.type},_getItemToZoom:function(){return!!m.currItem.hasSize&&m.currItem.img},_getOffset:function(e){var e=e?m.currItem.img:m.st.zoom.opener(m.currItem.el||m.currItem),t=e.offset(),n=parseInt(e.css("padding-top"),10),i=parseInt(e.css("padding-bottom"),10),e=(t.top-=u(window).scrollTop()-n,{width:e.width(),height:($?e.innerHeight():e[0].offsetHeight)-i-n});return(O=void 0===O?void 0!==document.createElement("p").style.MozTransform:O)?e["-moz-transform"]=e.transform="translate("+t.left+"px,"+t.top+"px)":(e.left=t.left,e.top=t.top),e}}});var O,T="iframe",A=(u.magnificPopup.registerModule(T,{options:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen></iframe></div>',srcAction:"iframe_src",patterns:{youtube:{index:"youtube.com",id:"v=",src:"//www.youtube.com/embed/%id%?autoplay=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}}},proto:{initIframe:function(){m.types.push(T),c("BeforeChange",function(e,t,n){t!==n&&(t===T?S():n===T&&S(!0))}),c(b+"."+T,function(){S()})},getIframe:function(e,t){var n=e.src,i=m.st.iframe,r=(u.each(i.patterns,function(){if(-1<n.indexOf(this.index))return this.id&&(n="string"==typeof this.id?n.substr(n.lastIndexOf(this.id)+this.id.length,n.length):this.id.call(this,n)),n=this.src.replace("%id%",n),!1}),{});return i.srcAction&&(r[i.srcAction]=n),m._parseMarkup(t,r,e),m.updateStatus("ready"),t}}}),u.magnificPopup.registerModule("gallery",{options:{enabled:!1,arrowMarkup:'<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"></button>',preload:[0,2],navigateByImgClick:!0,arrows:!0,tPrev:"Previous (Left arrow key)",tNext:"Next (Right arrow key)",tCounter:"%curr% of %total%"},proto:{initGallery:function(){var a=m.st.gallery,e=".mfp-gallery";if(m.direction=!0,!a||!a.enabled)return!1;v+=" mfp-gallery",c(C+e,function(){a.navigateByImgClick&&m.wrap.on("click"+e,".mfp-img",function(){if(1<m.items.length)return m.next(),!1}),g.on("keydown"+e,function(e){37===e.keyCode?m.prev():39===e.keyCode&&m.next()})}),c("UpdateStatus"+e,function(e,t){t.text&&(t.text=H(t.text,m.currItem.index,m.items.length))}),c(w+e,function(e,t,n,i){var r=m.items.length;n.counter=1<r?H(a.tCounter,i.index,r):""}),c("BuildControls"+e,function(){var e,t;1<m.items.length&&a.arrows&&!m.arrowLeft&&(t=a.arrowMarkup,e=m.arrowLeft=u(t.replace(/%title%/gi,a.tPrev).replace(/%dir%/gi,"left")).addClass(k),t=m.arrowRight=u(t.replace(/%title%/gi,a.tNext).replace(/%dir%/gi,"right")).addClass(k),e.click(function(){m.prev()}),t.click(function(){m.next()}),m.container.append(e.add(t)))}),c(M+e,function(){m._preloadTimeout&&clearTimeout(m._preloadTimeout),m._preloadTimeout=setTimeout(function(){m.preloadNearbyImages(),m._preloadTimeout=null},16)}),c(b+e,function(){g.off(e),m.wrap.off("click"+e),m.arrowRight=m.arrowLeft=null})},next:function(){m.direction=!0,m.index=P(m.index+1),m.updateItemHTML()},prev:function(){m.direction=!1,m.index=P(m.index-1),m.updateItemHTML()},goTo:function(e){m.direction=e>=m.index,m.index=e,m.updateItemHTML()},preloadNearbyImages:function(){for(var e=m.st.gallery.preload,t=Math.min(e[0],m.items.length),n=Math.min(e[1],m.items.length),i=1;i<=(m.direction?n:t);i++)m._preloadItem(m.index+i);for(i=1;i<=(m.direction?t:n);i++)m._preloadItem(m.index-i)},_preloadItem:function(e){var t;e=P(e),m.items[e].preloaded||((t=m.items[e]).parsed||(t=m.parseEl(e)),f("LazyLoad",t),"image"===t.type&&(t.img=u('<img class="mfp-img" />').on("load.mfploader",function(){t.hasSize=!0}).on("error.mfploader",function(){t.hasSize=!0,t.loadError=!0,f("LazyLoadError",t)}).attr("src",t.src)),t.preloaded=!0)}}}),"retina");u.magnificPopup.registerModule(A,{options:{replaceSrc:function(e){return e.src.replace(/\.\w+$/,function(e){return"@2x"+e})},ratio:1},proto:{initRetina:function(){var n,i;1<window.devicePixelRatio&&(n=m.st.retina,i=n.ratio,1<(i=isNaN(i)?i():i))&&(c("ImageHasSize."+A,function(e,t){t.img.css({"max-width":t.img[0].naturalWidth/i,width:"100%"})}),c("ElementParse."+A,function(e,t){t.src=n.replaceSrc(t,i)}))}}}),a()}),!function(s){s.fn.unveil=function(e,t){var n,r=s(window),a=e||0,i=1<window.devicePixelRatio?"data-src-retina":"data-src",o=this;function l(){var e=o.filter(function(){var e,t,n,i=s(this);if(!i.is(":hidden"))return t=(e=r.scrollTop())+r.height(),i=(n=i.offset().top)+i.height(),e-a<=i&&n<=t+a});n=e.trigger("unveil"),o=o.not(n)}return this.one("unveil",function(){var e;(e=this.getAttribute(i)||this.getAttribute("data-src"))&&(this.setAttribute("src",e),"function"==typeof t)&&t.call(this)}),r.on("scroll.unveil resize.unveil lookup.unveil",l),l(),this}}(window.jQuery||window.Zepto),!function(e,t){var n;e.JSON&&("function"==typeof define&&define.amd?define(function(){return t(e)}):"object"==typeof module&&"object"==typeof module.exports?module.exports=t(e):(n=!e.Nette||!e.Nette.noInit,e.Nette=t(e),n&&e.Nette.initOnLoad()))}("undefined"!=typeof window?window:this,function(window){"use strict";var Nette={};function getHandler(t){return function(e){return t.call(this,e)}}Nette.formErrors=[],Nette.version="2.4",Nette.addEvent=function(e,t,n){e.addEventListener?e.addEventListener(t,n):"DOMContentLoaded"===t?e.attachEvent("onreadystatechange",function(){"complete"===e.readyState&&n.call(this)}):e.attachEvent("on"+t,getHandler(n))},Nette.getValue=function(e){if(e){if(e.tagName){if("radio"===e.type){for(var t=e.form.elements,n=0;n<t.length;n++)if(t[n].name===e.name&&t[n].checked)return t[n].value;return null}if("file"===e.type)return e.files||e.value;if("select"===e.tagName.toLowerCase()){var i=e.selectedIndex,r=e.options,a=[];if("select-one"===e.type)return i<0?null:r[i].value;for(n=0;n<r.length;n++)r[n].selected&&a.push(r[n].value);return a}if(e.name&&e.name.match(/\[\]$/)){t=e.form.elements[e.name].tagName?[e]:e.form.elements[e.name],a=[];for(n=0;n<t.length;n++)"checkbox"===t[n].type&&!t[n].checked||a.push(t[n].value);return a}return"checkbox"===e.type?e.checked:"textarea"===e.tagName.toLowerCase()?e.value.replace("\r",""):e.value.replace("\r","").replace(/^\s+|\s+$/g,"")}return e[0]?Nette.getValue(e[0]):null}return null},Nette.getEffectiveValue=function(e){var t=Nette.getValue(e);return t=e.getAttribute&&t===e.getAttribute("data-nette-empty-value")?"":t},Nette.validateControl=function(n,e,t,i,r){n=n.tagName?n:n[0],e=e||Nette.parseJSON(n.getAttribute("data-nette-rules")),i=void 0===i?{value:Nette.getEffectiveValue(n)}:i;for(var a=0,o=e.length;a<o;a++){var l=e[a],s=l.op.match(/(~)?([^?]+)/),u=l.control?n.form.elements.namedItem(l.control):n;if(l.neg=s[1],l.op=s[2],l.condition=!!l.rules,u)if("optional"===l.op)r=!Nette.validateRule(n,":filled",null,i);else if(!r||l.condition||":filled"===l.op){var c,u=u.tagName?u:u[0],s=n===u?i:{value:Nette.getEffectiveValue(u)},s=Nette.validateRule(u,l.op,l.arg,s);if(null!==s)if(l.neg&&(s=!s),l.condition&&s){if(!Nette.validateControl(n,l.rules,t,i,":blank"!==l.op&&r))return!1}else if(!l.condition&&!s)if(!Nette.isDisabled(u))return t||(c=Nette.isArray(l.arg)?l.arg:[l.arg],s=l.msg.replace(/%(value|\d+)/g,function(e,t){return Nette.getValue("value"===t?u:n.form.elements.namedItem(c[t].control))}),Nette.addError(u,s)),!1}}return!("number"===n.type&&!n.validity.valid&&(t||Nette.addError(n,"Please enter a valid value."),1))},Nette.validateForm=function(e,t){var n=e.form||e,i=!1;if(Nette.formErrors=[],n["nette-submittedBy"]&&null!==n["nette-submittedBy"].getAttribute("formnovalidate")){e=Nette.parseJSON(n["nette-submittedBy"].getAttribute("data-nette-validation-scope"));if(!e.length)return Nette.showFormErrors(n,[]),!0;i=new RegExp("^("+e.join("-|")+"-)")}for(var r,a={},o=0;o<n.elements.length;o++)if(!(r=n.elements[o]).tagName||r.tagName.toLowerCase()in{input:1,select:1,textarea:1,button:1}){if("radio"===r.type){if(a[r.name])continue;a[r.name]=!0}if(!(i&&!r.name.replace(/]\[|\[|]|$/g,"-").match(i)||Nette.isDisabled(r)||Nette.validateControl(r,null,t)||Nette.formErrors.length))return!1}e=!Nette.formErrors.length;return Nette.showFormErrors(n,Nette.formErrors),e},Nette.isDisabled=function(e){if("radio"!==e.type)return e.disabled;for(var t=0,n=e.form.elements;t<n.length;t++)if(n[t].name===e.name&&!n[t].disabled)return!1;return!0},Nette.addError=function(e,t){Nette.formErrors.push({element:e,message:t})},Nette.showFormErrors=function(e,t){for(var n,i=[],r=0;r<t.length;r++){var a=t[r].element,o=t[r].message;Nette.inArray(i,o)||(i.push(o),!n&&a.focus&&(n=a))}i.length&&(alert(i.join("\n")),n)&&n.focus()},Nette.expandRuleArgument=function(e,t){var n;return t&&t.control&&(e=e.elements.namedItem(t.control),n={value:Nette.getEffectiveValue(e)},Nette.validateControl(e,null,!0,n),t=n.value),t};var preventFiltering=!1;return Nette.validateRule=function(e,t,n,i){i=void 0===i?{value:Nette.getEffectiveValue(e)}:i,t=(t=(t=":"===t.charAt(0)?t.substr(1):t).replace("::","_")).replace(/\\/g,"");var r=Nette.isArray(n)?n.slice(0):[n];if(!preventFiltering){preventFiltering=!0;for(var a=0,o=r.length;a<o;a++)r[a]=Nette.expandRuleArgument(e.form,r[a]);preventFiltering=!1}return Nette.validators[t]?Nette.validators[t](e,Nette.isArray(n)?r:r[0],i.value,i):null},Nette.validators={filled:function(e,t,n){return!("number"!==e.type||!e.validity.badInput)||""!==n&&!1!==n&&null!==n&&(!Nette.isArray(n)||!!n.length)&&(!window.FileList||!(n instanceof window.FileList)||n.length)},blank:function(e,t,n){return!Nette.validators.filled(e,t,n)},valid:function(e){return Nette.validateControl(e,null,!0)},equal:function(e,t,n){if(void 0===t)return null;function i(e){return"number"==typeof e||"string"==typeof e?""+e:!0===e?"1":""}n=Nette.isArray(n)?n:[n],t=Nette.isArray(t)?t:[t];e:for(var r=0,a=n.length;r<a;r++){for(var o=0,l=t.length;o<l;o++)if(i(n[r])===i(t[o]))continue e;return!1}return!0},notEqual:function(e,t,n){return void 0===t?null:!Nette.validators.equal(e,t,n)},minLength:function(e,t,n){if("number"===e.type){if(e.validity.tooShort)return!1;if(e.validity.badInput)return null}return n.length>=t},maxLength:function(e,t,n){if("number"===e.type){if(e.validity.tooLong)return!1;if(e.validity.badInput)return null}return n.length<=t},length:function(e,t,n){if("number"===e.type){if(e.validity.tooShort||e.validity.tooLong)return!1;if(e.validity.badInput)return null}return(null===(t=Nette.isArray(t)?t:[t,t])[0]||n.length>=t[0])&&(null===t[1]||n.length<=t[1])},email:function(e,t,n){return/^("([ !#-[\]-~]|\\[ -~])+"|[-a-z0-9!#$%&'*+\/=?^_`{|}~]+(\.[-a-z0-9!#$%&'*+\/=?^_`{|}~]+)*)@([0-9a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,61}[0-9a-z\u00C0-\u02FF\u0370-\u1EFF])?\.)+[a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,17}[a-z\u00C0-\u02FF\u0370-\u1EFF])?$/i.test(n)},url:function(e,t,n,i){return/^[a-z\d+.-]+:/.test(n)||(n="http://"+n),!!/^https?:\/\/((([-_0-9a-z\u00C0-\u02FF\u0370-\u1EFF]+\.)*[0-9a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,61}[0-9a-z\u00C0-\u02FF\u0370-\u1EFF])?\.)?[a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,17}[a-z\u00C0-\u02FF\u0370-\u1EFF])?|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|\[[0-9a-f:]{3,39}\])(:\d{1,5})?(\/\S*)?$/i.test(n)&&(i.value=n,!0)},regexp:function(e,t,n){t="string"==typeof t&&t.match(/^\/(.*)\/([imu]*)$/);try{return t&&new RegExp(t[1],t[2].replace("u","")).test(n)}catch(e){}},pattern:function(e,t,n){try{return"string"==typeof t?new RegExp("^(?:"+t+")$").test(n):null}catch(e){}},integer:function(e,t,n){return("number"!==e.type||!e.validity.badInput)&&/^-?[0-9]+$/.test(n)},float:function(e,t,n,i){return!("number"===e.type&&e.validity.badInput||(n=n.replace(" ","").replace(",","."),!/^-?[0-9]*[.,]?[0-9]+$/.test(n))||(i.value=n,0))},min:function(e,t,n){if("number"===e.type){if(e.validity.rangeUnderflow)return!1;if(e.validity.badInput)return null}return null===t||parseFloat(n)>=t},max:function(e,t,n){if("number"===e.type){if(e.validity.rangeOverflow)return!1;if(e.validity.badInput)return null}return null===t||parseFloat(n)<=t},range:function(e,t,n){if("number"===e.type){if(e.validity.rangeUnderflow||e.validity.rangeOverflow)return!1;if(e.validity.badInput)return null}return Nette.isArray(t)?(null===t[0]||parseFloat(n)>=t[0])&&(null===t[1]||parseFloat(n)<=t[1]):null},submitted:function(e){return e.form["nette-submittedBy"]===e},fileSize:function(e,t,n){if(window.FileList)for(var i=0;i<n.length;i++)if(n[i].size>t)return!1;return!0},image:function(e,t,n){if(window.FileList&&n instanceof window.FileList)for(var i=0;i<n.length;i++){var r=n[i].type;if(r&&"image/gif"!==r&&"image/png"!==r&&"image/jpeg"!==r)return!1}return!0},static:function(e,t,n){return t}},Nette.toggleForm=function(e,t){for(Nette.toggles={},n=0;n<e.elements.length;n++)e.elements[n].tagName.toLowerCase()in{input:1,select:1,textarea:1,button:1}&&Nette.toggleControl(e.elements[n],null,null,!t);for(var n in Nette.toggles)Nette.toggle(n,Nette.toggles[n],t)},Nette.toggleControl=function(e,t,n,i,r){t=t||Nette.parseJSON(e.getAttribute("data-nette-rules")),r=void 0===r?{value:Nette.getEffectiveValue(e)}:r;for(var a=!1,o=[],l=function(){Nette.toggleForm(e.form,e)},s=0,u=t.length;s<u;s++){var c=t[s],d=c.op.match(/(~)?([^?]+)/),f=c.control?e.form.elements.namedItem(c.control):e;if(f){if(!1!==(p=n)){c.neg=d[1],c.op=d[2];var p,d=e===f?r:{value:Nette.getEffectiveValue(f)};if(null===(p=Nette.validateRule(f,c.op,c.arg,d)))continue;c.neg&&(p=!p),c.rules||(n=p)}if(c.rules&&Nette.toggleControl(e,c.rules,p,i,r)||c.toggle){if(a=!0,i)for(var m=!document.addEventListener,g=(f.tagName?f:f[0]).name,v=f.tagName?f.form.elements:f,h=0;h<v.length;h++)v[h].name!==g||Nette.inArray(o,v[h])||(Nette.addEvent(v[h],m&&v[h].type in{checkbox:1,radio:1}?"click":"change",l),o.push(v[h]));for(var y in c.toggle||[])Object.prototype.hasOwnProperty.call(c.toggle,y)&&(Nette.toggles[y]=Nette.toggles[y]||(c.toggle[y]?p:!p))}}}return a},Nette.parseJSON=function(s){return"{op"===(s||"").substr(0,3)?eval("["+s+"]"):JSON.parse(s||"[]")},Nette.toggle=function(e,t,n){e=document.getElementById(e);e&&(e.style.display=t?"":"none")},Nette.initForm=function(t){Nette.toggleForm(t),t.noValidate||(t.noValidate=!0,Nette.addEvent(t,"submit",function(e){Nette.validateForm(t)||(e&&e.stopPropagation?(e.stopPropagation(),e.preventDefault()):window.event&&(event.cancelBubble=!0,event.returnValue=!1))}))},Nette.initOnLoad=function(){Nette.addEvent(document,"DOMContentLoaded",function(){for(var e=0;e<document.forms.length;e++)for(var t=document.forms[e],n=0;n<t.elements.length;n++)if(t.elements[n].getAttribute("data-nette-rules")){Nette.initForm(t);break}Nette.addEvent(document.body,"click",function(e){for(var t=e.target||e.srcElement;t;){if(t.form&&t.type in{submit:1,image:1}){t.form["nette-submittedBy"]=t;break}t=t.parentNode}})})},Nette.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},Nette.inArray=function(e,t){if([].indexOf)return-1<e.indexOf(t);for(var n=0;n<e.length;n++)if(e[n]===t)return!0;return!1},Nette.webalize=function(e){e=e.toLowerCase();for(var t="",n=0;n<e.length;n++)t+=Nette.webalizeTable[e.charAt(n)]||e.charAt(n);return t.replace(/[^a-z0-9]+/g,"-").replace(/^-|-$/g,"")},Nette.webalizeTable={"á":"a","ä":"a","č":"c","ď":"d","é":"e","ě":"e","í":"i","ľ":"l","ň":"n","ó":"o","ô":"o","ř":"r","š":"s","ť":"t","ú":"u","ů":"u","ý":"y","ž":"z"},Nette}),$(document).ready(function(){$(".gallery").length&&($.extend(!0,$.magnificPopup.defaults,{tClose:"Zavřít",tLoading:"Nahrávám...",gallery:{tPrev:"Předchozí",tNext:"Následující",tCounter:"%curr% z %total%"},image:{tError:'<a href="%url%">Obrázek</a> nelze načíst.'},ajax:{tError:'<a href="%url%">Obsah</a> nelze načíst.'}}),$(".gallery").magnificPopup({delegate:"a",type:"image",removalDelay:300,mainClass:"mfp-fade",gallery:{enabled:!0,tCounter:""}}))}),$(document).ready(function(){var e,t=window.innerWidth;function n(){var e=window.innerWidth;e<992?($(".nav").addClass("nav--mobile"),$(".nav-product").not(".nav-product--hidden").hide(),$(".basket__content, .login__content").hide()):($(".nav").removeClass("nav--mobile"),$(".nav-product").not(".nav-product--hidden").show(),$(".basket__content, .login__content").show()),e<992&&$(".nav--mobile").click(function(){return $(".basket__content, .login__content").hide(),$(".nav-product, .header__nav").toggle(),!1}),e<992&&$(".login__header").click(function(){return $(".basket__content, .nav-product").hide(),$(".login__content").toggle(),!1})}$(window).resize(function(){t!==window.innerWidth&&n()}),n(),$(".product__image").length&&$(".product__image img").unveil(200),$(".nav-product--switch").length&&$(".nav-product--switch .nav-product__header").on("click",function(e){$(".nav-product--switch").removeClass("is-active"),$(this).parent().addClass("is-active")}),$(".tabs").length&&($(".tabs__content > div").not(".is-active").hide(),$(".tabs__name").on("click",function(e){e.preventDefault();e=$(this).attr("href");$(".tabs__content > .is-active").removeClass("is-active").hide(),$(e).addClass("is-active").show(),$(".tabs__name").removeClass("is-active"),$(this).addClass("is-active")})),$(".open-tab").length&&$(".open-tab").on("click",function(e){$(".tabs__name").removeClass("is-active"),$(".tabs__content > .is-active").removeClass("is-active").hide();var t=$(this).attr("href");$(t).addClass("is-active").show(),$('.tab[href="'+t+'"]').addClass("is-active").show()}),$(".alert--close").length&&($(".alert--close").append(' <span class="icon icon--close"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use></svg></span>'),$(".alert--close .icon--close").on("click",function(){$(this).parent().hide()})),$(".modal").length&&($(".modal--show").on("click",function(){var e="#"+$(this).attr("rel"),t=e+" .modal__body";$(e).show(),$(t).outerHeight()>window.innerHeight&&$(e).addClass("modal--reset")}),$(".modal__close").on("click",function(){return $(this).closest(".modal").hide(),!1})),$(".antispam").length&&(e=$(".antispam").attr("data-help"),$(".antispam").find("input").val(e),$(".antispam").hide()),$(".reveal").length&&($(".reveal").hide(),$(".reveal--show").not("label.reveal--show, input.reveal--show").on("click",function(){var e="#"+$(this).attr("rel");return $(e).toggle(),!1}),$("label.reveal--show, input.reveal--show").on("change",function(){var e="#"+$(this).attr("rel");$(e).toggle()})),$(".control--print").length&&$(".control--print").click(function(){return window.print(),!1}),$(".order-delivery").length&&($(".order-delivery__payment").hide(),$(".order-delivery__type").on("click",function(){$(".order-delivery__payment").hide();var e="#"+$(this).attr("rel"),t=($(e).toggle(),$(this).find("input[type=radio]"));$(t).length&&t.prop("checked",!0),$(e).find("input").filter(":visible:first").trigger("click")}),$(".order-delivery__type input").on("click",function(){$(this).closest(".order-delivery__type").trigger("click")}),0!=delIdSelected&&(e="#delid_"+delIdSelected,$(e).find("input").prop("checked",!0)),(0!=payIdSelected?(e='input[value="'+payIdSelected+'"]',$(e).closest(".order-delivery__payment").show(),$(e)):$(".order-delivery__type").first()).trigger("click")),991<t&&$(".slider").length&&$(".slider").slick({dots:!0,speed:500,slidesToShow:1,autoplay:!0,arrows:!1}),$(".gallery").length&&($.extend(!0,$.magnificPopup.defaults,{tClose:"Zavřít",tLoading:"Nahrávám...",gallery:{tPrev:"Předchozí",tNext:"Následující",tCounter:"%curr% z %total%"},image:{tError:'<a href="%url%">Obrázek</a> nelze načíst.'},ajax:{tError:'<a href="%url%">Obsah</a> nelze načíst.'}}),$(".gallery").magnificPopup({delegate:"a",type:"image",removalDelay:300,mainClass:"mfp-fade",gallery:{enabled:!0,tCounter:""}}))}),$(document).on("click",".control--plus",function(){var e=$(this).parent().find("input").val();((e=parseFloat(e))<=0||!$.isNumeric(e))&&(e=0),e+=1,$(this).parent().find("input").val(e).change()}),$(document).on("click",".control--minus",function(){var e=$(this).parent().find("input").val();((e=parseFloat(e))<=0||!$.isNumeric(e))&&(e=1),e-=1,$(this).parent().find("input").val(e).change()});
//# sourceMappingURL=scripts.js.map

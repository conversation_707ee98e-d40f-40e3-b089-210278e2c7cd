{"version": 3, "file": "scripts.js", "sources": ["www/js/scripts.js"], "sourcesContent": ["/*! Magnific Popup - v1.1.0 - 2016-02-20\r\n* http://dimsemenov.com/plugins/magnific-popup/\r\n* Copyright (c) 2016 <PERSON>; */\r\n;(function (factory) { \r\nif (typeof define === 'function' && define.amd) { \r\n // AMD. Register as an anonymous module. \r\n define(['jquery'], factory); \r\n } else if (typeof exports === 'object') { \r\n // Node/CommonJS \r\n factory(require('jquery')); \r\n } else { \r\n // Browser globals \r\n factory(window.jQuery || window.Zepto); \r\n } \r\n }(function($) { \r\n\r\n/*>>core*/\r\n/**\r\n * \r\n * Magnific Popup Core JS file\r\n * \r\n */\r\n\r\n\r\n/**\r\n * Private static constants\r\n */\r\nvar CLOSE_EVENT = 'Close',\r\n\tBEFORE_CLOSE_EVENT = 'BeforeClose',\r\n\tAFTER_CLOSE_EVENT = 'AfterClose',\r\n\tBEFORE_APPEND_EVENT = 'BeforeAppend',\r\n\tMARKUP_PARSE_EVENT = 'MarkupParse',\r\n\tOPEN_EVENT = 'Open',\r\n\tCHANGE_EVENT = 'Change',\r\n\tNS = 'mfp',\r\n\tEVENT_NS = '.' + NS,\r\n\tREADY_CLASS = 'mfp-ready',\r\n\tREMOVING_CLASS = 'mfp-removing',\r\n\tPREVENT_CLOSE_CLASS = 'mfp-prevent-close';\r\n\r\n\r\n/**\r\n * Private vars \r\n */\r\n/*jshint -W079 */\r\nvar mfp, // As we have only one instance of MagnificPopup object, we define it locally to not to use 'this'\r\n\tMagnificPopup = function(){},\r\n\t_isJQ = !!(window.jQuery),\r\n\t_prevStatus,\r\n\t_window = $(window),\r\n\t_document,\r\n\t_prevContentType,\r\n\t_wrapClasses,\r\n\t_currPopupType;\r\n\r\n\r\n/**\r\n * Private functions\r\n */\r\nvar _mfpOn = function(name, f) {\r\n\t\tmfp.ev.on(NS + name + EVENT_NS, f);\r\n\t},\r\n\t_getEl = function(className, appendTo, html, raw) {\r\n\t\tvar el = document.createElement('div');\r\n\t\tel.className = 'mfp-'+className;\r\n\t\tif(html) {\r\n\t\t\tel.innerHTML = html;\r\n\t\t}\r\n\t\tif(!raw) {\r\n\t\t\tel = $(el);\r\n\t\t\tif(appendTo) {\r\n\t\t\t\tel.appendTo(appendTo);\r\n\t\t\t}\r\n\t\t} else if(appendTo) {\r\n\t\t\tappendTo.appendChild(el);\r\n\t\t}\r\n\t\treturn el;\r\n\t},\r\n\t_mfpTrigger = function(e, data) {\r\n\t\tmfp.ev.triggerHandler(NS + e, data);\r\n\r\n\t\tif(mfp.st.callbacks) {\r\n\t\t\t// converts \"mfpEventName\" to \"eventName\" callback and triggers it if it's present\r\n\t\t\te = e.charAt(0).toLowerCase() + e.slice(1);\r\n\t\t\tif(mfp.st.callbacks[e]) {\r\n\t\t\t\tmfp.st.callbacks[e].apply(mfp, $.isArray(data) ? data : [data]);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t_getCloseBtn = function(type) {\r\n\t\tif(type !== _currPopupType || !mfp.currTemplate.closeBtn) {\r\n\t\t\tmfp.currTemplate.closeBtn = $( mfp.st.closeMarkup.replace('%title%', mfp.st.tClose ) );\r\n\t\t\t_currPopupType = type;\r\n\t\t}\r\n\t\treturn mfp.currTemplate.closeBtn;\r\n\t},\r\n\t// Initialize Magnific Popup only when called at least once\r\n\t_checkInstance = function() {\r\n\t\tif(!$.magnificPopup.instance) {\r\n\t\t\t/*jshint -W020 */\r\n\t\t\tmfp = new MagnificPopup();\r\n\t\t\tmfp.init();\r\n\t\t\t$.magnificPopup.instance = mfp;\r\n\t\t}\r\n\t},\r\n\t// CSS transition detection, http://stackoverflow.com/questions/7264899/detect-css-transitions-using-javascript-and-without-modernizr\r\n\tsupportsTransitions = function() {\r\n\t\tvar s = document.createElement('p').style, // 's' for style. better to create an element if body yet to exist\r\n\t\t\tv = ['ms','O','Moz','Webkit']; // 'v' for vendor\r\n\r\n\t\tif( s['transition'] !== undefined ) {\r\n\t\t\treturn true; \r\n\t\t}\r\n\t\t\t\r\n\t\twhile( v.length ) {\r\n\t\t\tif( v.pop() + 'Transition' in s ) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\t\r\n\t\treturn false;\r\n\t};\r\n\r\n\r\n\r\n/**\r\n * Public functions\r\n */\r\nMagnificPopup.prototype = {\r\n\r\n\tconstructor: MagnificPopup,\r\n\r\n\t/**\r\n\t * Initializes Magnific Popup plugin. \r\n\t * This function is triggered only once when $.fn.magnificPopup or $.magnificPopup is executed\r\n\t */\r\n\tinit: function() {\r\n\t\tvar appVersion = navigator.appVersion;\r\n\t\tmfp.isLowIE = mfp.isIE8 = document.all && !document.addEventListener;\r\n\t\tmfp.isAndroid = (/android/gi).test(appVersion);\r\n\t\tmfp.isIOS = (/iphone|ipad|ipod/gi).test(appVersion);\r\n\t\tmfp.supportsTransition = supportsTransitions();\r\n\r\n\t\t// We disable fixed positioned lightbox on devices that don't handle it nicely.\r\n\t\t// If you know a better way of detecting this - let me know.\r\n\t\tmfp.probablyMobile = (mfp.isAndroid || mfp.isIOS || /(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent) );\r\n\t\t_document = $(document);\r\n\r\n\t\tmfp.popupsCache = {};\r\n\t},\r\n\r\n\t/**\r\n\t * Opens popup\r\n\t * @param  data [description]\r\n\t */\r\n\topen: function(data) {\r\n\r\n\t\tvar i;\r\n\r\n\t\tif(data.isObj === false) { \r\n\t\t\t// convert jQuery collection to array to avoid conflicts later\r\n\t\t\tmfp.items = data.items.toArray();\r\n\r\n\t\t\tmfp.index = 0;\r\n\t\t\tvar items = data.items,\r\n\t\t\t\titem;\r\n\t\t\tfor(i = 0; i < items.length; i++) {\r\n\t\t\t\titem = items[i];\r\n\t\t\t\tif(item.parsed) {\r\n\t\t\t\t\titem = item.el[0];\r\n\t\t\t\t}\r\n\t\t\t\tif(item === data.el[0]) {\r\n\t\t\t\t\tmfp.index = i;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tmfp.items = $.isArray(data.items) ? data.items : [data.items];\r\n\t\t\tmfp.index = data.index || 0;\r\n\t\t}\r\n\r\n\t\t// if popup is already opened - we just update the content\r\n\t\tif(mfp.isOpen) {\r\n\t\t\tmfp.updateItemHTML();\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\tmfp.types = []; \r\n\t\t_wrapClasses = '';\r\n\t\tif(data.mainEl && data.mainEl.length) {\r\n\t\t\tmfp.ev = data.mainEl.eq(0);\r\n\t\t} else {\r\n\t\t\tmfp.ev = _document;\r\n\t\t}\r\n\r\n\t\tif(data.key) {\r\n\t\t\tif(!mfp.popupsCache[data.key]) {\r\n\t\t\t\tmfp.popupsCache[data.key] = {};\r\n\t\t\t}\r\n\t\t\tmfp.currTemplate = mfp.popupsCache[data.key];\r\n\t\t} else {\r\n\t\t\tmfp.currTemplate = {};\r\n\t\t}\r\n\r\n\r\n\r\n\t\tmfp.st = $.extend(true, {}, $.magnificPopup.defaults, data ); \r\n\t\tmfp.fixedContentPos = mfp.st.fixedContentPos === 'auto' ? !mfp.probablyMobile : mfp.st.fixedContentPos;\r\n\r\n\t\tif(mfp.st.modal) {\r\n\t\t\tmfp.st.closeOnContentClick = false;\r\n\t\t\tmfp.st.closeOnBgClick = false;\r\n\t\t\tmfp.st.showCloseBtn = false;\r\n\t\t\tmfp.st.enableEscapeKey = false;\r\n\t\t}\r\n\t\t\r\n\r\n\t\t// Building markup\r\n\t\t// main containers are created only once\r\n\t\tif(!mfp.bgOverlay) {\r\n\r\n\t\t\t// Dark overlay\r\n\t\t\tmfp.bgOverlay = _getEl('bg').on('click'+EVENT_NS, function() {\r\n\t\t\t\tmfp.close();\r\n\t\t\t});\r\n\r\n\t\t\tmfp.wrap = _getEl('wrap').attr('tabindex', -1).on('click'+EVENT_NS, function(e) {\r\n\t\t\t\tif(mfp._checkIfClose(e.target)) {\r\n\t\t\t\t\tmfp.close();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tmfp.container = _getEl('container', mfp.wrap);\r\n\t\t}\r\n\r\n\t\tmfp.contentContainer = _getEl('content');\r\n\t\tif(mfp.st.preloader) {\r\n\t\t\tmfp.preloader = _getEl('preloader', mfp.container, mfp.st.tLoading);\r\n\t\t}\r\n\r\n\r\n\t\t// Initializing modules\r\n\t\tvar modules = $.magnificPopup.modules;\r\n\t\tfor(i = 0; i < modules.length; i++) {\r\n\t\t\tvar n = modules[i];\r\n\t\t\tn = n.charAt(0).toUpperCase() + n.slice(1);\r\n\t\t\tmfp['init'+n].call(mfp);\r\n\t\t}\r\n\t\t_mfpTrigger('BeforeOpen');\r\n\r\n\r\n\t\tif(mfp.st.showCloseBtn) {\r\n\t\t\t// Close button\r\n\t\t\tif(!mfp.st.closeBtnInside) {\r\n\t\t\t\tmfp.wrap.append( _getCloseBtn() );\r\n\t\t\t} else {\r\n\t\t\t\t_mfpOn(MARKUP_PARSE_EVENT, function(e, template, values, item) {\r\n\t\t\t\t\tvalues.close_replaceWith = _getCloseBtn(item.type);\r\n\t\t\t\t});\r\n\t\t\t\t_wrapClasses += ' mfp-close-btn-in';\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif(mfp.st.alignTop) {\r\n\t\t\t_wrapClasses += ' mfp-align-top';\r\n\t\t}\r\n\r\n\t\r\n\r\n\t\tif(mfp.fixedContentPos) {\r\n\t\t\tmfp.wrap.css({\r\n\t\t\t\toverflow: mfp.st.overflowY,\r\n\t\t\t\toverflowX: 'hidden',\r\n\t\t\t\toverflowY: mfp.st.overflowY\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tmfp.wrap.css({ \r\n\t\t\t\ttop: _window.scrollTop(),\r\n\t\t\t\tposition: 'absolute'\r\n\t\t\t});\r\n\t\t}\r\n\t\tif( mfp.st.fixedBgPos === false || (mfp.st.fixedBgPos === 'auto' && !mfp.fixedContentPos) ) {\r\n\t\t\tmfp.bgOverlay.css({\r\n\t\t\t\theight: _document.height(),\r\n\t\t\t\tposition: 'absolute'\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t\r\n\r\n\t\tif(mfp.st.enableEscapeKey) {\r\n\t\t\t// Close on ESC key\r\n\t\t\t_document.on('keyup' + EVENT_NS, function(e) {\r\n\t\t\t\tif(e.keyCode === 27) {\r\n\t\t\t\t\tmfp.close();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t_window.on('resize' + EVENT_NS, function() {\r\n\t\t\tmfp.updateSize();\r\n\t\t});\r\n\r\n\r\n\t\tif(!mfp.st.closeOnContentClick) {\r\n\t\t\t_wrapClasses += ' mfp-auto-cursor';\r\n\t\t}\r\n\t\t\r\n\t\tif(_wrapClasses)\r\n\t\t\tmfp.wrap.addClass(_wrapClasses);\r\n\r\n\r\n\t\t// this triggers recalculation of layout, so we get it once to not to trigger twice\r\n\t\tvar windowHeight = mfp.wH = _window.height();\r\n\r\n\t\t\r\n\t\tvar windowStyles = {};\r\n\r\n\t\tif( mfp.fixedContentPos ) {\r\n            if(mfp._hasScrollBar(windowHeight)){\r\n                var s = mfp._getScrollbarSize();\r\n                if(s) {\r\n                    windowStyles.marginRight = s;\r\n                }\r\n            }\r\n        }\r\n\r\n\t\tif(mfp.fixedContentPos) {\r\n\t\t\tif(!mfp.isIE7) {\r\n\t\t\t\twindowStyles.overflow = 'hidden';\r\n\t\t\t} else {\r\n\t\t\t\t// ie7 double-scroll bug\r\n\t\t\t\t$('body, html').css('overflow', 'hidden');\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t\r\n\t\t\r\n\t\tvar classesToadd = mfp.st.mainClass;\r\n\t\tif(mfp.isIE7) {\r\n\t\t\tclassesToadd += ' mfp-ie7';\r\n\t\t}\r\n\t\tif(classesToadd) {\r\n\t\t\tmfp._addClassToMFP( classesToadd );\r\n\t\t}\r\n\r\n\t\t// add content\r\n\t\tmfp.updateItemHTML();\r\n\r\n\t\t_mfpTrigger('BuildControls');\r\n\r\n\t\t// remove scrollbar, add margin e.t.c\r\n\t\t$('html').css(windowStyles);\r\n\t\t\r\n\t\t// add everything to DOM\r\n\t\tmfp.bgOverlay.add(mfp.wrap).prependTo( mfp.st.prependTo || $(document.body) );\r\n\r\n\t\t// Save last focused element\r\n\t\tmfp._lastFocusedEl = document.activeElement;\r\n\t\t\r\n\t\t// Wait for next cycle to allow CSS transition\r\n\t\tsetTimeout(function() {\r\n\t\t\t\r\n\t\t\tif(mfp.content) {\r\n\t\t\t\tmfp._addClassToMFP(READY_CLASS);\r\n\t\t\t\tmfp._setFocus();\r\n\t\t\t} else {\r\n\t\t\t\t// if content is not defined (not loaded e.t.c) we add class only for BG\r\n\t\t\t\tmfp.bgOverlay.addClass(READY_CLASS);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// Trap the focus in popup\r\n\t\t\t_document.on('focusin' + EVENT_NS, mfp._onFocusIn);\r\n\r\n\t\t}, 16);\r\n\r\n\t\tmfp.isOpen = true;\r\n\t\tmfp.updateSize(windowHeight);\r\n\t\t_mfpTrigger(OPEN_EVENT);\r\n\r\n\t\treturn data;\r\n\t},\r\n\r\n\t/**\r\n\t * Closes the popup\r\n\t */\r\n\tclose: function() {\r\n\t\tif(!mfp.isOpen) return;\r\n\t\t_mfpTrigger(BEFORE_CLOSE_EVENT);\r\n\r\n\t\tmfp.isOpen = false;\r\n\t\t// for CSS3 animation\r\n\t\tif(mfp.st.removalDelay && !mfp.isLowIE && mfp.supportsTransition )  {\r\n\t\t\tmfp._addClassToMFP(REMOVING_CLASS);\r\n\t\t\tsetTimeout(function() {\r\n\t\t\t\tmfp._close();\r\n\t\t\t}, mfp.st.removalDelay);\r\n\t\t} else {\r\n\t\t\tmfp._close();\r\n\t\t}\r\n\t},\r\n\r\n\t/**\r\n\t * Helper for close() function\r\n\t */\r\n\t_close: function() {\r\n\t\t_mfpTrigger(CLOSE_EVENT);\r\n\r\n\t\tvar classesToRemove = REMOVING_CLASS + ' ' + READY_CLASS + ' ';\r\n\r\n\t\tmfp.bgOverlay.detach();\r\n\t\tmfp.wrap.detach();\r\n\t\tmfp.container.empty();\r\n\r\n\t\tif(mfp.st.mainClass) {\r\n\t\t\tclassesToRemove += mfp.st.mainClass + ' ';\r\n\t\t}\r\n\r\n\t\tmfp._removeClassFromMFP(classesToRemove);\r\n\r\n\t\tif(mfp.fixedContentPos) {\r\n\t\t\tvar windowStyles = {marginRight: ''};\r\n\t\t\tif(mfp.isIE7) {\r\n\t\t\t\t$('body, html').css('overflow', '');\r\n\t\t\t} else {\r\n\t\t\t\twindowStyles.overflow = '';\r\n\t\t\t}\r\n\t\t\t$('html').css(windowStyles);\r\n\t\t}\r\n\t\t\r\n\t\t_document.off('keyup' + EVENT_NS + ' focusin' + EVENT_NS);\r\n\t\tmfp.ev.off(EVENT_NS);\r\n\r\n\t\t// clean up DOM elements that aren't removed\r\n\t\tmfp.wrap.attr('class', 'mfp-wrap').removeAttr('style');\r\n\t\tmfp.bgOverlay.attr('class', 'mfp-bg');\r\n\t\tmfp.container.attr('class', 'mfp-container');\r\n\r\n\t\t// remove close button from target element\r\n\t\tif(mfp.st.showCloseBtn &&\r\n\t\t(!mfp.st.closeBtnInside || mfp.currTemplate[mfp.currItem.type] === true)) {\r\n\t\t\tif(mfp.currTemplate.closeBtn)\r\n\t\t\t\tmfp.currTemplate.closeBtn.detach();\r\n\t\t}\r\n\r\n\r\n\t\tif(mfp.st.autoFocusLast && mfp._lastFocusedEl) {\r\n\t\t\t$(mfp._lastFocusedEl).focus(); // put tab focus back\r\n\t\t}\r\n\t\tmfp.currItem = null;\t\r\n\t\tmfp.content = null;\r\n\t\tmfp.currTemplate = null;\r\n\t\tmfp.prevHeight = 0;\r\n\r\n\t\t_mfpTrigger(AFTER_CLOSE_EVENT);\r\n\t},\r\n\t\r\n\tupdateSize: function(winHeight) {\r\n\r\n\t\tif(mfp.isIOS) {\r\n\t\t\t// fixes iOS nav bars https://github.com/dimsemenov/Magnific-Popup/issues/2\r\n\t\t\tvar zoomLevel = document.documentElement.clientWidth / window.innerWidth;\r\n\t\t\tvar height = window.innerHeight * zoomLevel;\r\n\t\t\tmfp.wrap.css('height', height);\r\n\t\t\tmfp.wH = height;\r\n\t\t} else {\r\n\t\t\tmfp.wH = winHeight || _window.height();\r\n\t\t}\r\n\t\t// Fixes #84: popup incorrectly positioned with position:relative on body\r\n\t\tif(!mfp.fixedContentPos) {\r\n\t\t\tmfp.wrap.css('height', mfp.wH);\r\n\t\t}\r\n\r\n\t\t_mfpTrigger('Resize');\r\n\r\n\t},\r\n\r\n\t/**\r\n\t * Set content of popup based on current index\r\n\t */\r\n\tupdateItemHTML: function() {\r\n\t\tvar item = mfp.items[mfp.index];\r\n\r\n\t\t// Detach and perform modifications\r\n\t\tmfp.contentContainer.detach();\r\n\r\n\t\tif(mfp.content)\r\n\t\t\tmfp.content.detach();\r\n\r\n\t\tif(!item.parsed) {\r\n\t\t\titem = mfp.parseEl( mfp.index );\r\n\t\t}\r\n\r\n\t\tvar type = item.type;\r\n\r\n\t\t_mfpTrigger('BeforeChange', [mfp.currItem ? mfp.currItem.type : '', type]);\r\n\t\t// BeforeChange event works like so:\r\n\t\t// _mfpOn('BeforeChange', function(e, prevType, newType) { });\r\n\r\n\t\tmfp.currItem = item;\r\n\r\n\t\tif(!mfp.currTemplate[type]) {\r\n\t\t\tvar markup = mfp.st[type] ? mfp.st[type].markup : false;\r\n\r\n\t\t\t// allows to modify markup\r\n\t\t\t_mfpTrigger('FirstMarkupParse', markup);\r\n\r\n\t\t\tif(markup) {\r\n\t\t\t\tmfp.currTemplate[type] = $(markup);\r\n\t\t\t} else {\r\n\t\t\t\t// if there is no markup found we just define that template is parsed\r\n\t\t\t\tmfp.currTemplate[type] = true;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif(_prevContentType && _prevContentType !== item.type) {\r\n\t\t\tmfp.container.removeClass('mfp-'+_prevContentType+'-holder');\r\n\t\t}\r\n\r\n\t\tvar newContent = mfp['get' + type.charAt(0).toUpperCase() + type.slice(1)](item, mfp.currTemplate[type]);\r\n\t\tmfp.appendContent(newContent, type);\r\n\r\n\t\titem.preloaded = true;\r\n\r\n\t\t_mfpTrigger(CHANGE_EVENT, item);\r\n\t\t_prevContentType = item.type;\r\n\r\n\t\t// Append container back after its content changed\r\n\t\tmfp.container.prepend(mfp.contentContainer);\r\n\r\n\t\t_mfpTrigger('AfterChange');\r\n\t},\r\n\r\n\r\n\t/**\r\n\t * Set HTML content of popup\r\n\t */\r\n\tappendContent: function(newContent, type) {\r\n\t\tmfp.content = newContent;\r\n\r\n\t\tif(newContent) {\r\n\t\t\tif(mfp.st.showCloseBtn && mfp.st.closeBtnInside &&\r\n\t\t\t\tmfp.currTemplate[type] === true) {\r\n\t\t\t\t// if there is no markup, we just append close button element inside\r\n\t\t\t\tif(!mfp.content.find('.mfp-close').length) {\r\n\t\t\t\t\tmfp.content.append(_getCloseBtn());\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tmfp.content = newContent;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tmfp.content = '';\r\n\t\t}\r\n\r\n\t\t_mfpTrigger(BEFORE_APPEND_EVENT);\r\n\t\tmfp.container.addClass('mfp-'+type+'-holder');\r\n\r\n\t\tmfp.contentContainer.append(mfp.content);\r\n\t},\r\n\r\n\r\n\t/**\r\n\t * Creates Magnific Popup data object based on given data\r\n\t * @param  {int} index Index of item to parse\r\n\t */\r\n\tparseEl: function(index) {\r\n\t\tvar item = mfp.items[index],\r\n\t\t\ttype;\r\n\r\n\t\tif(item.tagName) {\r\n\t\t\titem = { el: $(item) };\r\n\t\t} else {\r\n\t\t\ttype = item.type;\r\n\t\t\titem = { data: item, src: item.src };\r\n\t\t}\r\n\r\n\t\tif(item.el) {\r\n\t\t\tvar types = mfp.types;\r\n\r\n\t\t\t// check for 'mfp-TYPE' class\r\n\t\t\tfor(var i = 0; i < types.length; i++) {\r\n\t\t\t\tif( item.el.hasClass('mfp-'+types[i]) ) {\r\n\t\t\t\t\ttype = types[i];\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\titem.src = item.el.attr('data-mfp-src');\r\n\t\t\tif(!item.src) {\r\n\t\t\t\titem.src = item.el.attr('href');\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\titem.type = type || mfp.st.type || 'inline';\r\n\t\titem.index = index;\r\n\t\titem.parsed = true;\r\n\t\tmfp.items[index] = item;\r\n\t\t_mfpTrigger('ElementParse', item);\r\n\r\n\t\treturn mfp.items[index];\r\n\t},\r\n\r\n\r\n\t/**\r\n\t * Initializes single popup or a group of popups\r\n\t */\r\n\taddGroup: function(el, options) {\r\n\t\tvar eHandler = function(e) {\r\n\t\t\te.mfpEl = this;\r\n\t\t\tmfp._openClick(e, el, options);\r\n\t\t};\r\n\r\n\t\tif(!options) {\r\n\t\t\toptions = {};\r\n\t\t}\r\n\r\n\t\tvar eName = 'click.magnificPopup';\r\n\t\toptions.mainEl = el;\r\n\r\n\t\tif(options.items) {\r\n\t\t\toptions.isObj = true;\r\n\t\t\tel.off(eName).on(eName, eHandler);\r\n\t\t} else {\r\n\t\t\toptions.isObj = false;\r\n\t\t\tif(options.delegate) {\r\n\t\t\t\tel.off(eName).on(eName, options.delegate , eHandler);\r\n\t\t\t} else {\r\n\t\t\t\toptions.items = el;\r\n\t\t\t\tel.off(eName).on(eName, eHandler);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t_openClick: function(e, el, options) {\r\n\t\tvar midClick = options.midClick !== undefined ? options.midClick : $.magnificPopup.defaults.midClick;\r\n\r\n\r\n\t\tif(!midClick && ( e.which === 2 || e.ctrlKey || e.metaKey || e.altKey || e.shiftKey ) ) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tvar disableOn = options.disableOn !== undefined ? options.disableOn : $.magnificPopup.defaults.disableOn;\r\n\r\n\t\tif(disableOn) {\r\n\t\t\tif($.isFunction(disableOn)) {\r\n\t\t\t\tif( !disableOn.call(mfp) ) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t} else { // else it's number\r\n\t\t\t\tif( _window.width() < disableOn ) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif(e.type) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// This will prevent popup from closing if element is inside and popup is already opened\r\n\t\t\tif(mfp.isOpen) {\r\n\t\t\t\te.stopPropagation();\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\toptions.el = $(e.mfpEl);\r\n\t\tif(options.delegate) {\r\n\t\t\toptions.items = el.find(options.delegate);\r\n\t\t}\r\n\t\tmfp.open(options);\r\n\t},\r\n\r\n\r\n\t/**\r\n\t * Updates text on preloader\r\n\t */\r\n\tupdateStatus: function(status, text) {\r\n\r\n\t\tif(mfp.preloader) {\r\n\t\t\tif(_prevStatus !== status) {\r\n\t\t\t\tmfp.container.removeClass('mfp-s-'+_prevStatus);\r\n\t\t\t}\r\n\r\n\t\t\tif(!text && status === 'loading') {\r\n\t\t\t\ttext = mfp.st.tLoading;\r\n\t\t\t}\r\n\r\n\t\t\tvar data = {\r\n\t\t\t\tstatus: status,\r\n\t\t\t\ttext: text\r\n\t\t\t};\r\n\t\t\t// allows to modify status\r\n\t\t\t_mfpTrigger('UpdateStatus', data);\r\n\r\n\t\t\tstatus = data.status;\r\n\t\t\ttext = data.text;\r\n\r\n\t\t\tmfp.preloader.html(text);\r\n\r\n\t\t\tmfp.preloader.find('a').on('click', function(e) {\r\n\t\t\t\te.stopImmediatePropagation();\r\n\t\t\t});\r\n\r\n\t\t\tmfp.container.addClass('mfp-s-'+status);\r\n\t\t\t_prevStatus = status;\r\n\t\t}\r\n\t},\r\n\r\n\r\n\t/*\r\n\t\t\"Private\" helpers that aren't private at all\r\n\t */\r\n\t// Check to close popup or not\r\n\t// \"target\" is an element that was clicked\r\n\t_checkIfClose: function(target) {\r\n\r\n\t\tif($(target).hasClass(PREVENT_CLOSE_CLASS)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tvar closeOnContent = mfp.st.closeOnContentClick;\r\n\t\tvar closeOnBg = mfp.st.closeOnBgClick;\r\n\r\n\t\tif(closeOnContent && closeOnBg) {\r\n\t\t\treturn true;\r\n\t\t} else {\r\n\r\n\t\t\t// We close the popup if click is on close button or on preloader. Or if there is no content.\r\n\t\t\tif(!mfp.content || $(target).hasClass('mfp-close') || (mfp.preloader && target === mfp.preloader[0]) ) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\r\n\t\t\t// if click is outside the content\r\n\t\t\tif(  (target !== mfp.content[0] && !$.contains(mfp.content[0], target))  ) {\r\n\t\t\t\tif(closeOnBg) {\r\n\t\t\t\t\t// last check, if the clicked element is in DOM, (in case it's removed onclick)\r\n\t\t\t\t\tif( $.contains(document, target) ) {\r\n\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else if(closeOnContent) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t\treturn false;\r\n\t},\r\n\t_addClassToMFP: function(cName) {\r\n\t\tmfp.bgOverlay.addClass(cName);\r\n\t\tmfp.wrap.addClass(cName);\r\n\t},\r\n\t_removeClassFromMFP: function(cName) {\r\n\t\tthis.bgOverlay.removeClass(cName);\r\n\t\tmfp.wrap.removeClass(cName);\r\n\t},\r\n\t_hasScrollBar: function(winHeight) {\r\n\t\treturn (  (mfp.isIE7 ? _document.height() : document.body.scrollHeight) > (winHeight || _window.height()) );\r\n\t},\r\n\t_setFocus: function() {\r\n\t\t(mfp.st.focus ? mfp.content.find(mfp.st.focus).eq(0) : mfp.wrap).focus();\r\n\t},\r\n\t_onFocusIn: function(e) {\r\n\t\tif( e.target !== mfp.wrap[0] && !$.contains(mfp.wrap[0], e.target) ) {\r\n\t\t\tmfp._setFocus();\r\n\t\t\treturn false;\r\n\t\t}\r\n\t},\r\n\t_parseMarkup: function(template, values, item) {\r\n\t\tvar arr;\r\n\t\tif(item.data) {\r\n\t\t\tvalues = $.extend(item.data, values);\r\n\t\t}\r\n\t\t_mfpTrigger(MARKUP_PARSE_EVENT, [template, values, item] );\r\n\r\n\t\t$.each(values, function(key, value) {\r\n\t\t\tif(value === undefined || value === false) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\tarr = key.split('_');\r\n\t\t\tif(arr.length > 1) {\r\n\t\t\t\tvar el = template.find(EVENT_NS + '-'+arr[0]);\r\n\r\n\t\t\t\tif(el.length > 0) {\r\n\t\t\t\t\tvar attr = arr[1];\r\n\t\t\t\t\tif(attr === 'replaceWith') {\r\n\t\t\t\t\t\tif(el[0] !== value[0]) {\r\n\t\t\t\t\t\t\tel.replaceWith(value);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if(attr === 'img') {\r\n\t\t\t\t\t\tif(el.is('img')) {\r\n\t\t\t\t\t\t\tel.attr('src', value);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tel.replaceWith( $('<img>').attr('src', value).attr('class', el.attr('class')) );\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tel.attr(arr[1], value);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t} else {\r\n\t\t\t\ttemplate.find(EVENT_NS + '-'+key).html(value);\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\r\n\t_getScrollbarSize: function() {\r\n\t\t// thx David\r\n\t\tif(mfp.scrollbarSize === undefined) {\r\n\t\t\tvar scrollDiv = document.createElement(\"div\");\r\n\t\t\tscrollDiv.style.cssText = 'width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;';\r\n\t\t\tdocument.body.appendChild(scrollDiv);\r\n\t\t\tmfp.scrollbarSize = scrollDiv.offsetWidth - scrollDiv.clientWidth;\r\n\t\t\tdocument.body.removeChild(scrollDiv);\r\n\t\t}\r\n\t\treturn mfp.scrollbarSize;\r\n\t}\r\n\r\n}; /* MagnificPopup core prototype end */\r\n\r\n\r\n\r\n\r\n/**\r\n * Public static functions\r\n */\r\n$.magnificPopup = {\r\n\tinstance: null,\r\n\tproto: MagnificPopup.prototype,\r\n\tmodules: [],\r\n\r\n\topen: function(options, index) {\r\n\t\t_checkInstance();\r\n\r\n\t\tif(!options) {\r\n\t\t\toptions = {};\r\n\t\t} else {\r\n\t\t\toptions = $.extend(true, {}, options);\r\n\t\t}\r\n\r\n\t\toptions.isObj = true;\r\n\t\toptions.index = index || 0;\r\n\t\treturn this.instance.open(options);\r\n\t},\r\n\r\n\tclose: function() {\r\n\t\treturn $.magnificPopup.instance && $.magnificPopup.instance.close();\r\n\t},\r\n\r\n\tregisterModule: function(name, module) {\r\n\t\tif(module.options) {\r\n\t\t\t$.magnificPopup.defaults[name] = module.options;\r\n\t\t}\r\n\t\t$.extend(this.proto, module.proto);\r\n\t\tthis.modules.push(name);\r\n\t},\r\n\r\n\tdefaults: {\r\n\r\n\t\t// Info about options is in docs:\r\n\t\t// http://dimsemenov.com/plugins/magnific-popup/documentation.html#options\r\n\r\n\t\tdisableOn: 0,\r\n\r\n\t\tkey: null,\r\n\r\n\t\tmidClick: false,\r\n\r\n\t\tmainClass: '',\r\n\r\n\t\tpreloader: true,\r\n\r\n\t\tfocus: '', // CSS selector of input to focus after popup is opened\r\n\r\n\t\tcloseOnContentClick: false,\r\n\r\n\t\tcloseOnBgClick: true,\r\n\r\n\t\tcloseBtnInside: true,\r\n\r\n\t\tshowCloseBtn: true,\r\n\r\n\t\tenableEscapeKey: true,\r\n\r\n\t\tmodal: false,\r\n\r\n\t\talignTop: false,\r\n\r\n\t\tremovalDelay: 0,\r\n\r\n\t\tprependTo: null,\r\n\r\n\t\tfixedContentPos: 'auto',\r\n\r\n\t\tfixedBgPos: 'auto',\r\n\r\n\t\toverflowY: 'auto',\r\n\r\n\t\tcloseMarkup: '<button title=\"%title%\" type=\"button\" class=\"mfp-close\">&#215;</button>',\r\n\r\n\t\ttClose: 'Close (Esc)',\r\n\r\n\t\ttLoading: 'Loading...',\r\n\r\n\t\tautoFocusLast: true\r\n\r\n\t}\r\n};\r\n\r\n\r\n\r\n$.fn.magnificPopup = function(options) {\r\n\t_checkInstance();\r\n\r\n\tvar jqEl = $(this);\r\n\r\n\t// We call some API method of first param is a string\r\n\tif (typeof options === \"string\" ) {\r\n\r\n\t\tif(options === 'open') {\r\n\t\t\tvar items,\r\n\t\t\t\titemOpts = _isJQ ? jqEl.data('magnificPopup') : jqEl[0].magnificPopup,\r\n\t\t\t\tindex = parseInt(arguments[1], 10) || 0;\r\n\r\n\t\t\tif(itemOpts.items) {\r\n\t\t\t\titems = itemOpts.items[index];\r\n\t\t\t} else {\r\n\t\t\t\titems = jqEl;\r\n\t\t\t\tif(itemOpts.delegate) {\r\n\t\t\t\t\titems = items.find(itemOpts.delegate);\r\n\t\t\t\t}\r\n\t\t\t\titems = items.eq( index );\r\n\t\t\t}\r\n\t\t\tmfp._openClick({mfpEl:items}, jqEl, itemOpts);\r\n\t\t} else {\r\n\t\t\tif(mfp.isOpen)\r\n\t\t\t\tmfp[options].apply(mfp, Array.prototype.slice.call(arguments, 1));\r\n\t\t}\r\n\r\n\t} else {\r\n\t\t// clone options obj\r\n\t\toptions = $.extend(true, {}, options);\r\n\r\n\t\t/*\r\n\t\t * As Zepto doesn't support .data() method for objects\r\n\t\t * and it works only in normal browsers\r\n\t\t * we assign \"options\" object directly to the DOM element. FTW!\r\n\t\t */\r\n\t\tif(_isJQ) {\r\n\t\t\tjqEl.data('magnificPopup', options);\r\n\t\t} else {\r\n\t\t\tjqEl[0].magnificPopup = options;\r\n\t\t}\r\n\r\n\t\tmfp.addGroup(jqEl, options);\r\n\r\n\t}\r\n\treturn jqEl;\r\n};\r\n\r\n/*>>core*/\r\n\r\n/*>>inline*/\r\n\r\nvar INLINE_NS = 'inline',\r\n\t_hiddenClass,\r\n\t_inlinePlaceholder,\r\n\t_lastInlineElement,\r\n\t_putInlineElementsBack = function() {\r\n\t\tif(_lastInlineElement) {\r\n\t\t\t_inlinePlaceholder.after( _lastInlineElement.addClass(_hiddenClass) ).detach();\r\n\t\t\t_lastInlineElement = null;\r\n\t\t}\r\n\t};\r\n\r\n$.magnificPopup.registerModule(INLINE_NS, {\r\n\toptions: {\r\n\t\thiddenClass: 'hide', // will be appended with `mfp-` prefix\r\n\t\tmarkup: '',\r\n\t\ttNotFound: 'Content not found'\r\n\t},\r\n\tproto: {\r\n\r\n\t\tinitInline: function() {\r\n\t\t\tmfp.types.push(INLINE_NS);\r\n\r\n\t\t\t_mfpOn(CLOSE_EVENT+'.'+INLINE_NS, function() {\r\n\t\t\t\t_putInlineElementsBack();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\tgetInline: function(item, template) {\r\n\r\n\t\t\t_putInlineElementsBack();\r\n\r\n\t\t\tif(item.src) {\r\n\t\t\t\tvar inlineSt = mfp.st.inline,\r\n\t\t\t\t\tel = $(item.src);\r\n\r\n\t\t\t\tif(el.length) {\r\n\r\n\t\t\t\t\t// If target element has parent - we replace it with placeholder and put it back after popup is closed\r\n\t\t\t\t\tvar parent = el[0].parentNode;\r\n\t\t\t\t\tif(parent && parent.tagName) {\r\n\t\t\t\t\t\tif(!_inlinePlaceholder) {\r\n\t\t\t\t\t\t\t_hiddenClass = inlineSt.hiddenClass;\r\n\t\t\t\t\t\t\t_inlinePlaceholder = _getEl(_hiddenClass);\r\n\t\t\t\t\t\t\t_hiddenClass = 'mfp-'+_hiddenClass;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// replace target inline element with placeholder\r\n\t\t\t\t\t\t_lastInlineElement = el.after(_inlinePlaceholder).detach().removeClass(_hiddenClass);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tmfp.updateStatus('ready');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tmfp.updateStatus('error', inlineSt.tNotFound);\r\n\t\t\t\t\tel = $('<div>');\r\n\t\t\t\t}\r\n\r\n\t\t\t\titem.inlineElement = el;\r\n\t\t\t\treturn el;\r\n\t\t\t}\r\n\r\n\t\t\tmfp.updateStatus('ready');\r\n\t\t\tmfp._parseMarkup(template, {}, item);\r\n\t\t\treturn template;\r\n\t\t}\r\n\t}\r\n});\r\n\r\n/*>>inline*/\r\n\r\n/*>>ajax*/\r\nvar AJAX_NS = 'ajax',\r\n\t_ajaxCur,\r\n\t_removeAjaxCursor = function() {\r\n\t\tif(_ajaxCur) {\r\n\t\t\t$(document.body).removeClass(_ajaxCur);\r\n\t\t}\r\n\t},\r\n\t_destroyAjaxRequest = function() {\r\n\t\t_removeAjaxCursor();\r\n\t\tif(mfp.req) {\r\n\t\t\tmfp.req.abort();\r\n\t\t}\r\n\t};\r\n\r\n$.magnificPopup.registerModule(AJAX_NS, {\r\n\r\n\toptions: {\r\n\t\tsettings: null,\r\n\t\tcursor: 'mfp-ajax-cur',\r\n\t\ttError: '<a href=\"%url%\">The content</a> could not be loaded.'\r\n\t},\r\n\r\n\tproto: {\r\n\t\tinitAjax: function() {\r\n\t\t\tmfp.types.push(AJAX_NS);\r\n\t\t\t_ajaxCur = mfp.st.ajax.cursor;\r\n\r\n\t\t\t_mfpOn(CLOSE_EVENT+'.'+AJAX_NS, _destroyAjaxRequest);\r\n\t\t\t_mfpOn('BeforeChange.' + AJAX_NS, _destroyAjaxRequest);\r\n\t\t},\r\n\t\tgetAjax: function(item) {\r\n\r\n\t\t\tif(_ajaxCur) {\r\n\t\t\t\t$(document.body).addClass(_ajaxCur);\r\n\t\t\t}\r\n\r\n\t\t\tmfp.updateStatus('loading');\r\n\r\n\t\t\tvar opts = $.extend({\r\n\t\t\t\turl: item.src,\r\n\t\t\t\tsuccess: function(data, textStatus, jqXHR) {\r\n\t\t\t\t\tvar temp = {\r\n\t\t\t\t\t\tdata:data,\r\n\t\t\t\t\t\txhr:jqXHR\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t_mfpTrigger('ParseAjax', temp);\r\n\r\n\t\t\t\t\tmfp.appendContent( $(temp.data), AJAX_NS );\r\n\r\n\t\t\t\t\titem.finished = true;\r\n\r\n\t\t\t\t\t_removeAjaxCursor();\r\n\r\n\t\t\t\t\tmfp._setFocus();\r\n\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tmfp.wrap.addClass(READY_CLASS);\r\n\t\t\t\t\t}, 16);\r\n\r\n\t\t\t\t\tmfp.updateStatus('ready');\r\n\r\n\t\t\t\t\t_mfpTrigger('AjaxContentAdded');\r\n\t\t\t\t},\r\n\t\t\t\terror: function() {\r\n\t\t\t\t\t_removeAjaxCursor();\r\n\t\t\t\t\titem.finished = item.loadError = true;\r\n\t\t\t\t\tmfp.updateStatus('error', mfp.st.ajax.tError.replace('%url%', item.src));\r\n\t\t\t\t}\r\n\t\t\t}, mfp.st.ajax.settings);\r\n\r\n\t\t\tmfp.req = $.ajax(opts);\r\n\r\n\t\t\treturn '';\r\n\t\t}\r\n\t}\r\n});\r\n\r\n/*>>ajax*/\r\n\r\n/*>>image*/\r\nvar _imgInterval,\r\n\t_getTitle = function(item) {\r\n\t\tif(item.data && item.data.title !== undefined)\r\n\t\t\treturn item.data.title;\r\n\r\n\t\tvar src = mfp.st.image.titleSrc;\r\n\r\n\t\tif(src) {\r\n\t\t\tif($.isFunction(src)) {\r\n\t\t\t\treturn src.call(mfp, item);\r\n\t\t\t} else if(item.el) {\r\n\t\t\t\treturn item.el.attr(src) || '';\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn '';\r\n\t};\r\n\r\n$.magnificPopup.registerModule('image', {\r\n\r\n\toptions: {\r\n\t\tmarkup: '<div class=\"mfp-figure\">'+\r\n\t\t\t\t\t'<div class=\"mfp-close\"></div>'+\r\n\t\t\t\t\t'<figure>'+\r\n\t\t\t\t\t\t'<div class=\"mfp-img\"></div>'+\r\n\t\t\t\t\t\t'<figcaption>'+\r\n\t\t\t\t\t\t\t'<div class=\"mfp-bottom-bar\">'+\r\n\t\t\t\t\t\t\t\t'<div class=\"mfp-title\"></div>'+\r\n\t\t\t\t\t\t\t\t'<div class=\"mfp-counter\"></div>'+\r\n\t\t\t\t\t\t\t'</div>'+\r\n\t\t\t\t\t\t'</figcaption>'+\r\n\t\t\t\t\t'</figure>'+\r\n\t\t\t\t'</div>',\r\n\t\tcursor: 'mfp-zoom-out-cur',\r\n\t\ttitleSrc: 'title',\r\n\t\tverticalFit: true,\r\n\t\ttError: '<a href=\"%url%\">The image</a> could not be loaded.'\r\n\t},\r\n\r\n\tproto: {\r\n\t\tinitImage: function() {\r\n\t\t\tvar imgSt = mfp.st.image,\r\n\t\t\t\tns = '.image';\r\n\r\n\t\t\tmfp.types.push('image');\r\n\r\n\t\t\t_mfpOn(OPEN_EVENT+ns, function() {\r\n\t\t\t\tif(mfp.currItem.type === 'image' && imgSt.cursor) {\r\n\t\t\t\t\t$(document.body).addClass(imgSt.cursor);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t_mfpOn(CLOSE_EVENT+ns, function() {\r\n\t\t\t\tif(imgSt.cursor) {\r\n\t\t\t\t\t$(document.body).removeClass(imgSt.cursor);\r\n\t\t\t\t}\r\n\t\t\t\t_window.off('resize' + EVENT_NS);\r\n\t\t\t});\r\n\r\n\t\t\t_mfpOn('Resize'+ns, mfp.resizeImage);\r\n\t\t\tif(mfp.isLowIE) {\r\n\t\t\t\t_mfpOn('AfterChange', mfp.resizeImage);\r\n\t\t\t}\r\n\t\t},\r\n\t\tresizeImage: function() {\r\n\t\t\tvar item = mfp.currItem;\r\n\t\t\tif(!item || !item.img) return;\r\n\r\n\t\t\tif(mfp.st.image.verticalFit) {\r\n\t\t\t\tvar decr = 0;\r\n\t\t\t\t// fix box-sizing in ie7/8\r\n\t\t\t\tif(mfp.isLowIE) {\r\n\t\t\t\t\tdecr = parseInt(item.img.css('padding-top'), 10) + parseInt(item.img.css('padding-bottom'),10);\r\n\t\t\t\t}\r\n\t\t\t\titem.img.css('max-height', mfp.wH-decr);\r\n\t\t\t}\r\n\t\t},\r\n\t\t_onImageHasSize: function(item) {\r\n\t\t\tif(item.img) {\r\n\r\n\t\t\t\titem.hasSize = true;\r\n\r\n\t\t\t\tif(_imgInterval) {\r\n\t\t\t\t\tclearInterval(_imgInterval);\r\n\t\t\t\t}\r\n\r\n\t\t\t\titem.isCheckingImgSize = false;\r\n\r\n\t\t\t\t_mfpTrigger('ImageHasSize', item);\r\n\r\n\t\t\t\tif(item.imgHidden) {\r\n\t\t\t\t\tif(mfp.content)\r\n\t\t\t\t\t\tmfp.content.removeClass('mfp-loading');\r\n\r\n\t\t\t\t\titem.imgHidden = false;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * Function that loops until the image has size to display elements that rely on it asap\r\n\t\t */\r\n\t\tfindImageSize: function(item) {\r\n\r\n\t\t\tvar counter = 0,\r\n\t\t\t\timg = item.img[0],\r\n\t\t\t\tmfpSetInterval = function(delay) {\r\n\r\n\t\t\t\t\tif(_imgInterval) {\r\n\t\t\t\t\t\tclearInterval(_imgInterval);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// decelerating interval that checks for size of an image\r\n\t\t\t\t\t_imgInterval = setInterval(function() {\r\n\t\t\t\t\t\tif(img.naturalWidth > 0) {\r\n\t\t\t\t\t\t\tmfp._onImageHasSize(item);\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif(counter > 200) {\r\n\t\t\t\t\t\t\tclearInterval(_imgInterval);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tcounter++;\r\n\t\t\t\t\t\tif(counter === 3) {\r\n\t\t\t\t\t\t\tmfpSetInterval(10);\r\n\t\t\t\t\t\t} else if(counter === 40) {\r\n\t\t\t\t\t\t\tmfpSetInterval(50);\r\n\t\t\t\t\t\t} else if(counter === 100) {\r\n\t\t\t\t\t\t\tmfpSetInterval(500);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, delay);\r\n\t\t\t\t};\r\n\r\n\t\t\tmfpSetInterval(1);\r\n\t\t},\r\n\r\n\t\tgetImage: function(item, template) {\r\n\r\n\t\t\tvar guard = 0,\r\n\r\n\t\t\t\t// image load complete handler\r\n\t\t\t\tonLoadComplete = function() {\r\n\t\t\t\t\tif(item) {\r\n\t\t\t\t\t\tif (item.img[0].complete) {\r\n\t\t\t\t\t\t\titem.img.off('.mfploader');\r\n\r\n\t\t\t\t\t\t\tif(item === mfp.currItem){\r\n\t\t\t\t\t\t\t\tmfp._onImageHasSize(item);\r\n\r\n\t\t\t\t\t\t\t\tmfp.updateStatus('ready');\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\titem.hasSize = true;\r\n\t\t\t\t\t\t\titem.loaded = true;\r\n\r\n\t\t\t\t\t\t\t_mfpTrigger('ImageLoadComplete');\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\t// if image complete check fails 200 times (20 sec), we assume that there was an error.\r\n\t\t\t\t\t\t\tguard++;\r\n\t\t\t\t\t\t\tif(guard < 200) {\r\n\t\t\t\t\t\t\t\tsetTimeout(onLoadComplete,100);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tonLoadError();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// image error handler\r\n\t\t\t\tonLoadError = function() {\r\n\t\t\t\t\tif(item) {\r\n\t\t\t\t\t\titem.img.off('.mfploader');\r\n\t\t\t\t\t\tif(item === mfp.currItem){\r\n\t\t\t\t\t\t\tmfp._onImageHasSize(item);\r\n\t\t\t\t\t\t\tmfp.updateStatus('error', imgSt.tError.replace('%url%', item.src) );\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\titem.hasSize = true;\r\n\t\t\t\t\t\titem.loaded = true;\r\n\t\t\t\t\t\titem.loadError = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\timgSt = mfp.st.image;\r\n\r\n\r\n\t\t\tvar el = template.find('.mfp-img');\r\n\t\t\tif(el.length) {\r\n\t\t\t\tvar img = document.createElement('img');\r\n\t\t\t\timg.className = 'mfp-img';\r\n\t\t\t\tif(item.el && item.el.find('img').length) {\r\n\t\t\t\t\timg.alt = item.el.find('img').attr('alt');\r\n\t\t\t\t}\r\n\t\t\t\titem.img = $(img).on('load.mfploader', onLoadComplete).on('error.mfploader', onLoadError);\r\n\t\t\t\timg.src = item.src;\r\n\r\n\t\t\t\t// without clone() \"error\" event is not firing when IMG is replaced by new IMG\r\n\t\t\t\t// TODO: find a way to avoid such cloning\r\n\t\t\t\tif(el.is('img')) {\r\n\t\t\t\t\titem.img = item.img.clone();\r\n\t\t\t\t}\r\n\r\n\t\t\t\timg = item.img[0];\r\n\t\t\t\tif(img.naturalWidth > 0) {\r\n\t\t\t\t\titem.hasSize = true;\r\n\t\t\t\t} else if(!img.width) {\r\n\t\t\t\t\titem.hasSize = false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tmfp._parseMarkup(template, {\r\n\t\t\t\ttitle: _getTitle(item),\r\n\t\t\t\timg_replaceWith: item.img\r\n\t\t\t}, item);\r\n\r\n\t\t\tmfp.resizeImage();\r\n\r\n\t\t\tif(item.hasSize) {\r\n\t\t\t\tif(_imgInterval) clearInterval(_imgInterval);\r\n\r\n\t\t\t\tif(item.loadError) {\r\n\t\t\t\t\ttemplate.addClass('mfp-loading');\r\n\t\t\t\t\tmfp.updateStatus('error', imgSt.tError.replace('%url%', item.src) );\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttemplate.removeClass('mfp-loading');\r\n\t\t\t\t\tmfp.updateStatus('ready');\r\n\t\t\t\t}\r\n\t\t\t\treturn template;\r\n\t\t\t}\r\n\r\n\t\t\tmfp.updateStatus('loading');\r\n\t\t\titem.loading = true;\r\n\r\n\t\t\tif(!item.hasSize) {\r\n\t\t\t\titem.imgHidden = true;\r\n\t\t\t\ttemplate.addClass('mfp-loading');\r\n\t\t\t\tmfp.findImageSize(item);\r\n\t\t\t}\r\n\r\n\t\t\treturn template;\r\n\t\t}\r\n\t}\r\n});\r\n\r\n/*>>image*/\r\n\r\n/*>>zoom*/\r\nvar hasMozTransform,\r\n\tgetHasMozTransform = function() {\r\n\t\tif(hasMozTransform === undefined) {\r\n\t\t\thasMozTransform = document.createElement('p').style.MozTransform !== undefined;\r\n\t\t}\r\n\t\treturn hasMozTransform;\r\n\t};\r\n\r\n$.magnificPopup.registerModule('zoom', {\r\n\r\n\toptions: {\r\n\t\tenabled: false,\r\n\t\teasing: 'ease-in-out',\r\n\t\tduration: 300,\r\n\t\topener: function(element) {\r\n\t\t\treturn element.is('img') ? element : element.find('img');\r\n\t\t}\r\n\t},\r\n\r\n\tproto: {\r\n\r\n\t\tinitZoom: function() {\r\n\t\t\tvar zoomSt = mfp.st.zoom,\r\n\t\t\t\tns = '.zoom',\r\n\t\t\t\timage;\r\n\r\n\t\t\tif(!zoomSt.enabled || !mfp.supportsTransition) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tvar duration = zoomSt.duration,\r\n\t\t\t\tgetElToAnimate = function(image) {\r\n\t\t\t\t\tvar newImg = image.clone().removeAttr('style').removeAttr('class').addClass('mfp-animated-image'),\r\n\t\t\t\t\t\ttransition = 'all '+(zoomSt.duration/1000)+'s ' + zoomSt.easing,\r\n\t\t\t\t\t\tcssObj = {\r\n\t\t\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t'-webkit-backface-visibility': 'hidden'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tt = 'transition';\r\n\r\n\t\t\t\t\tcssObj['-webkit-'+t] = cssObj['-moz-'+t] = cssObj['-o-'+t] = cssObj[t] = transition;\r\n\r\n\t\t\t\t\tnewImg.css(cssObj);\r\n\t\t\t\t\treturn newImg;\r\n\t\t\t\t},\r\n\t\t\t\tshowMainContent = function() {\r\n\t\t\t\t\tmfp.content.css('visibility', 'visible');\r\n\t\t\t\t},\r\n\t\t\t\topenTimeout,\r\n\t\t\t\tanimatedImg;\r\n\r\n\t\t\t_mfpOn('BuildControls'+ns, function() {\r\n\t\t\t\tif(mfp._allowZoom()) {\r\n\r\n\t\t\t\t\tclearTimeout(openTimeout);\r\n\t\t\t\t\tmfp.content.css('visibility', 'hidden');\r\n\r\n\t\t\t\t\t// Basically, all code below does is clones existing image, puts in on top of the current one and animated it\r\n\r\n\t\t\t\t\timage = mfp._getItemToZoom();\r\n\r\n\t\t\t\t\tif(!image) {\r\n\t\t\t\t\t\tshowMainContent();\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tanimatedImg = getElToAnimate(image);\r\n\r\n\t\t\t\t\tanimatedImg.css( mfp._getOffset() );\r\n\r\n\t\t\t\t\tmfp.wrap.append(animatedImg);\r\n\r\n\t\t\t\t\topenTimeout = setTimeout(function() {\r\n\t\t\t\t\t\tanimatedImg.css( mfp._getOffset( true ) );\r\n\t\t\t\t\t\topenTimeout = setTimeout(function() {\r\n\r\n\t\t\t\t\t\t\tshowMainContent();\r\n\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tanimatedImg.remove();\r\n\t\t\t\t\t\t\t\timage = animatedImg = null;\r\n\t\t\t\t\t\t\t\t_mfpTrigger('ZoomAnimationEnded');\r\n\t\t\t\t\t\t\t}, 16); // avoid blink when switching images\r\n\r\n\t\t\t\t\t\t}, duration); // this timeout equals animation duration\r\n\r\n\t\t\t\t\t}, 16); // by adding this timeout we avoid short glitch at the beginning of animation\r\n\r\n\r\n\t\t\t\t\t// Lots of timeouts...\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t_mfpOn(BEFORE_CLOSE_EVENT+ns, function() {\r\n\t\t\t\tif(mfp._allowZoom()) {\r\n\r\n\t\t\t\t\tclearTimeout(openTimeout);\r\n\r\n\t\t\t\t\tmfp.st.removalDelay = duration;\r\n\r\n\t\t\t\t\tif(!image) {\r\n\t\t\t\t\t\timage = mfp._getItemToZoom();\r\n\t\t\t\t\t\tif(!image) {\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tanimatedImg = getElToAnimate(image);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tanimatedImg.css( mfp._getOffset(true) );\r\n\t\t\t\t\tmfp.wrap.append(animatedImg);\r\n\t\t\t\t\tmfp.content.css('visibility', 'hidden');\r\n\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tanimatedImg.css( mfp._getOffset() );\r\n\t\t\t\t\t}, 16);\r\n\t\t\t\t}\r\n\r\n\t\t\t});\r\n\r\n\t\t\t_mfpOn(CLOSE_EVENT+ns, function() {\r\n\t\t\t\tif(mfp._allowZoom()) {\r\n\t\t\t\t\tshowMainContent();\r\n\t\t\t\t\tif(animatedImg) {\r\n\t\t\t\t\t\tanimatedImg.remove();\r\n\t\t\t\t\t}\r\n\t\t\t\t\timage = null;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t_allowZoom: function() {\r\n\t\t\treturn mfp.currItem.type === 'image';\r\n\t\t},\r\n\r\n\t\t_getItemToZoom: function() {\r\n\t\t\tif(mfp.currItem.hasSize) {\r\n\t\t\t\treturn mfp.currItem.img;\r\n\t\t\t} else {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// Get element postion relative to viewport\r\n\t\t_getOffset: function(isLarge) {\r\n\t\t\tvar el;\r\n\t\t\tif(isLarge) {\r\n\t\t\t\tel = mfp.currItem.img;\r\n\t\t\t} else {\r\n\t\t\t\tel = mfp.st.zoom.opener(mfp.currItem.el || mfp.currItem);\r\n\t\t\t}\r\n\r\n\t\t\tvar offset = el.offset();\r\n\t\t\tvar paddingTop = parseInt(el.css('padding-top'),10);\r\n\t\t\tvar paddingBottom = parseInt(el.css('padding-bottom'),10);\r\n\t\t\toffset.top -= ( $(window).scrollTop() - paddingTop );\r\n\r\n\r\n\t\t\t/*\r\n\r\n\t\t\tAnimating left + top + width/height looks glitchy in Firefox, but perfect in Chrome. And vice-versa.\r\n\r\n\t\t\t */\r\n\t\t\tvar obj = {\r\n\t\t\t\twidth: el.width(),\r\n\t\t\t\t// fix Zepto height+padding issue\r\n\t\t\t\theight: (_isJQ ? el.innerHeight() : el[0].offsetHeight) - paddingBottom - paddingTop\r\n\t\t\t};\r\n\r\n\t\t\t// I hate to do this, but there is no another option\r\n\t\t\tif( getHasMozTransform() ) {\r\n\t\t\t\tobj['-moz-transform'] = obj['transform'] = 'translate(' + offset.left + 'px,' + offset.top + 'px)';\r\n\t\t\t} else {\r\n\t\t\t\tobj.left = offset.left;\r\n\t\t\t\tobj.top = offset.top;\r\n\t\t\t}\r\n\t\t\treturn obj;\r\n\t\t}\r\n\r\n\t}\r\n});\r\n\r\n\r\n\r\n/*>>zoom*/\r\n\r\n/*>>iframe*/\r\n\r\nvar IFRAME_NS = 'iframe',\r\n\t_emptyPage = '//about:blank',\r\n\r\n\t_fixIframeBugs = function(isShowing) {\r\n\t\tif(mfp.currTemplate[IFRAME_NS]) {\r\n\t\t\tvar el = mfp.currTemplate[IFRAME_NS].find('iframe');\r\n\t\t\tif(el.length) {\r\n\t\t\t\t// reset src after the popup is closed to avoid \"video keeps playing after popup is closed\" bug\r\n\t\t\t\tif(!isShowing) {\r\n\t\t\t\t\tel[0].src = _emptyPage;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// IE8 black screen bug fix\r\n\t\t\t\tif(mfp.isIE8) {\r\n\t\t\t\t\tel.css('display', isShowing ? 'block' : 'none');\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n$.magnificPopup.registerModule(IFRAME_NS, {\r\n\r\n\toptions: {\r\n\t\tmarkup: '<div class=\"mfp-iframe-scaler\">'+\r\n\t\t\t\t\t'<div class=\"mfp-close\"></div>'+\r\n\t\t\t\t\t'<iframe class=\"mfp-iframe\" src=\"//about:blank\" frameborder=\"0\" allowfullscreen></iframe>'+\r\n\t\t\t\t'</div>',\r\n\r\n\t\tsrcAction: 'iframe_src',\r\n\r\n\t\t// we don't care and support only one default type of URL by default\r\n\t\tpatterns: {\r\n\t\t\tyoutube: {\r\n\t\t\t\tindex: 'youtube.com',\r\n\t\t\t\tid: 'v=',\r\n\t\t\t\tsrc: '//www.youtube.com/embed/%id%?autoplay=1'\r\n\t\t\t},\r\n\t\t\tvimeo: {\r\n\t\t\t\tindex: 'vimeo.com/',\r\n\t\t\t\tid: '/',\r\n\t\t\t\tsrc: '//player.vimeo.com/video/%id%?autoplay=1'\r\n\t\t\t},\r\n\t\t\tgmaps: {\r\n\t\t\t\tindex: '//maps.google.',\r\n\t\t\t\tsrc: '%id%&output=embed'\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\r\n\tproto: {\r\n\t\tinitIframe: function() {\r\n\t\t\tmfp.types.push(IFRAME_NS);\r\n\r\n\t\t\t_mfpOn('BeforeChange', function(e, prevType, newType) {\r\n\t\t\t\tif(prevType !== newType) {\r\n\t\t\t\t\tif(prevType === IFRAME_NS) {\r\n\t\t\t\t\t\t_fixIframeBugs(); // iframe if removed\r\n\t\t\t\t\t} else if(newType === IFRAME_NS) {\r\n\t\t\t\t\t\t_fixIframeBugs(true); // iframe is showing\r\n\t\t\t\t\t}\r\n\t\t\t\t}// else {\r\n\t\t\t\t\t// iframe source is switched, don't do anything\r\n\t\t\t\t//}\r\n\t\t\t});\r\n\r\n\t\t\t_mfpOn(CLOSE_EVENT + '.' + IFRAME_NS, function() {\r\n\t\t\t\t_fixIframeBugs();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\tgetIframe: function(item, template) {\r\n\t\t\tvar embedSrc = item.src;\r\n\t\t\tvar iframeSt = mfp.st.iframe;\r\n\r\n\t\t\t$.each(iframeSt.patterns, function() {\r\n\t\t\t\tif(embedSrc.indexOf( this.index ) > -1) {\r\n\t\t\t\t\tif(this.id) {\r\n\t\t\t\t\t\tif(typeof this.id === 'string') {\r\n\t\t\t\t\t\t\tembedSrc = embedSrc.substr(embedSrc.lastIndexOf(this.id)+this.id.length, embedSrc.length);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tembedSrc = this.id.call( this, embedSrc );\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tembedSrc = this.src.replace('%id%', embedSrc );\r\n\t\t\t\t\treturn false; // break;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tvar dataObj = {};\r\n\t\t\tif(iframeSt.srcAction) {\r\n\t\t\t\tdataObj[iframeSt.srcAction] = embedSrc;\r\n\t\t\t}\r\n\t\t\tmfp._parseMarkup(template, dataObj, item);\r\n\r\n\t\t\tmfp.updateStatus('ready');\r\n\r\n\t\t\treturn template;\r\n\t\t}\r\n\t}\r\n});\r\n\r\n\r\n\r\n/*>>iframe*/\r\n\r\n/*>>gallery*/\r\n/**\r\n * Get looped index depending on number of slides\r\n */\r\nvar _getLoopedId = function(index) {\r\n\t\tvar numSlides = mfp.items.length;\r\n\t\tif(index > numSlides - 1) {\r\n\t\t\treturn index - numSlides;\r\n\t\t} else  if(index < 0) {\r\n\t\t\treturn numSlides + index;\r\n\t\t}\r\n\t\treturn index;\r\n\t},\r\n\t_replaceCurrTotal = function(text, curr, total) {\r\n\t\treturn text.replace(/%curr%/gi, curr + 1).replace(/%total%/gi, total);\r\n\t};\r\n\r\n$.magnificPopup.registerModule('gallery', {\r\n\r\n\toptions: {\r\n\t\tenabled: false,\r\n\t\tarrowMarkup: '<button title=\"%title%\" type=\"button\" class=\"mfp-arrow mfp-arrow-%dir%\"></button>',\r\n\t\tpreload: [0,2],\r\n\t\tnavigateByImgClick: true,\r\n\t\tarrows: true,\r\n\r\n\t\ttPrev: 'Previous (Left arrow key)',\r\n\t\ttNext: 'Next (Right arrow key)',\r\n\t\ttCounter: '%curr% of %total%'\r\n\t},\r\n\r\n\tproto: {\r\n\t\tinitGallery: function() {\r\n\r\n\t\t\tvar gSt = mfp.st.gallery,\r\n\t\t\t\tns = '.mfp-gallery';\r\n\r\n\t\t\tmfp.direction = true; // true - next, false - prev\r\n\r\n\t\t\tif(!gSt || !gSt.enabled ) return false;\r\n\r\n\t\t\t_wrapClasses += ' mfp-gallery';\r\n\r\n\t\t\t_mfpOn(OPEN_EVENT+ns, function() {\r\n\r\n\t\t\t\tif(gSt.navigateByImgClick) {\r\n\t\t\t\t\tmfp.wrap.on('click'+ns, '.mfp-img', function() {\r\n\t\t\t\t\t\tif(mfp.items.length > 1) {\r\n\t\t\t\t\t\t\tmfp.next();\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t_document.on('keydown'+ns, function(e) {\r\n\t\t\t\t\tif (e.keyCode === 37) {\r\n\t\t\t\t\t\tmfp.prev();\r\n\t\t\t\t\t} else if (e.keyCode === 39) {\r\n\t\t\t\t\t\tmfp.next();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\r\n\t\t\t_mfpOn('UpdateStatus'+ns, function(e, data) {\r\n\t\t\t\tif(data.text) {\r\n\t\t\t\t\tdata.text = _replaceCurrTotal(data.text, mfp.currItem.index, mfp.items.length);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t_mfpOn(MARKUP_PARSE_EVENT+ns, function(e, element, values, item) {\r\n\t\t\t\tvar l = mfp.items.length;\r\n\t\t\t\tvalues.counter = l > 1 ? _replaceCurrTotal(gSt.tCounter, item.index, l) : '';\r\n\t\t\t});\r\n\r\n\t\t\t_mfpOn('BuildControls' + ns, function() {\r\n\t\t\t\tif(mfp.items.length > 1 && gSt.arrows && !mfp.arrowLeft) {\r\n\t\t\t\t\tvar markup = gSt.arrowMarkup,\r\n\t\t\t\t\t\tarrowLeft = mfp.arrowLeft = $( markup.replace(/%title%/gi, gSt.tPrev).replace(/%dir%/gi, 'left') ).addClass(PREVENT_CLOSE_CLASS),\r\n\t\t\t\t\t\tarrowRight = mfp.arrowRight = $( markup.replace(/%title%/gi, gSt.tNext).replace(/%dir%/gi, 'right') ).addClass(PREVENT_CLOSE_CLASS);\r\n\r\n\t\t\t\t\tarrowLeft.click(function() {\r\n\t\t\t\t\t\tmfp.prev();\r\n\t\t\t\t\t});\r\n\t\t\t\t\tarrowRight.click(function() {\r\n\t\t\t\t\t\tmfp.next();\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tmfp.container.append(arrowLeft.add(arrowRight));\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t_mfpOn(CHANGE_EVENT+ns, function() {\r\n\t\t\t\tif(mfp._preloadTimeout) clearTimeout(mfp._preloadTimeout);\r\n\r\n\t\t\t\tmfp._preloadTimeout = setTimeout(function() {\r\n\t\t\t\t\tmfp.preloadNearbyImages();\r\n\t\t\t\t\tmfp._preloadTimeout = null;\r\n\t\t\t\t}, 16);\r\n\t\t\t});\r\n\r\n\r\n\t\t\t_mfpOn(CLOSE_EVENT+ns, function() {\r\n\t\t\t\t_document.off(ns);\r\n\t\t\t\tmfp.wrap.off('click'+ns);\r\n\t\t\t\tmfp.arrowRight = mfp.arrowLeft = null;\r\n\t\t\t});\r\n\r\n\t\t},\r\n\t\tnext: function() {\r\n\t\t\tmfp.direction = true;\r\n\t\t\tmfp.index = _getLoopedId(mfp.index + 1);\r\n\t\t\tmfp.updateItemHTML();\r\n\t\t},\r\n\t\tprev: function() {\r\n\t\t\tmfp.direction = false;\r\n\t\t\tmfp.index = _getLoopedId(mfp.index - 1);\r\n\t\t\tmfp.updateItemHTML();\r\n\t\t},\r\n\t\tgoTo: function(newIndex) {\r\n\t\t\tmfp.direction = (newIndex >= mfp.index);\r\n\t\t\tmfp.index = newIndex;\r\n\t\t\tmfp.updateItemHTML();\r\n\t\t},\r\n\t\tpreloadNearbyImages: function() {\r\n\t\t\tvar p = mfp.st.gallery.preload,\r\n\t\t\t\tpreloadBefore = Math.min(p[0], mfp.items.length),\r\n\t\t\t\tpreloadAfter = Math.min(p[1], mfp.items.length),\r\n\t\t\t\ti;\r\n\r\n\t\t\tfor(i = 1; i <= (mfp.direction ? preloadAfter : preloadBefore); i++) {\r\n\t\t\t\tmfp._preloadItem(mfp.index+i);\r\n\t\t\t}\r\n\t\t\tfor(i = 1; i <= (mfp.direction ? preloadBefore : preloadAfter); i++) {\r\n\t\t\t\tmfp._preloadItem(mfp.index-i);\r\n\t\t\t}\r\n\t\t},\r\n\t\t_preloadItem: function(index) {\r\n\t\t\tindex = _getLoopedId(index);\r\n\r\n\t\t\tif(mfp.items[index].preloaded) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tvar item = mfp.items[index];\r\n\t\t\tif(!item.parsed) {\r\n\t\t\t\titem = mfp.parseEl( index );\r\n\t\t\t}\r\n\r\n\t\t\t_mfpTrigger('LazyLoad', item);\r\n\r\n\t\t\tif(item.type === 'image') {\r\n\t\t\t\titem.img = $('<img class=\"mfp-img\" />').on('load.mfploader', function() {\r\n\t\t\t\t\titem.hasSize = true;\r\n\t\t\t\t}).on('error.mfploader', function() {\r\n\t\t\t\t\titem.hasSize = true;\r\n\t\t\t\t\titem.loadError = true;\r\n\t\t\t\t\t_mfpTrigger('LazyLoadError', item);\r\n\t\t\t\t}).attr('src', item.src);\r\n\t\t\t}\r\n\r\n\r\n\t\t\titem.preloaded = true;\r\n\t\t}\r\n\t}\r\n});\r\n\r\n/*>>gallery*/\r\n\r\n/*>>retina*/\r\n\r\nvar RETINA_NS = 'retina';\r\n\r\n$.magnificPopup.registerModule(RETINA_NS, {\r\n\toptions: {\r\n\t\treplaceSrc: function(item) {\r\n\t\t\treturn item.src.replace(/\\.\\w+$/, function(m) { return '@2x' + m; });\r\n\t\t},\r\n\t\tratio: 1 // Function or number.  Set to 1 to disable.\r\n\t},\r\n\tproto: {\r\n\t\tinitRetina: function() {\r\n\t\t\tif(window.devicePixelRatio > 1) {\r\n\r\n\t\t\t\tvar st = mfp.st.retina,\r\n\t\t\t\t\tratio = st.ratio;\r\n\r\n\t\t\t\tratio = !isNaN(ratio) ? ratio : ratio();\r\n\r\n\t\t\t\tif(ratio > 1) {\r\n\t\t\t\t\t_mfpOn('ImageHasSize' + '.' + RETINA_NS, function(e, item) {\r\n\t\t\t\t\t\titem.img.css({\r\n\t\t\t\t\t\t\t'max-width': item.img[0].naturalWidth / ratio,\r\n\t\t\t\t\t\t\t'width': '100%'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t\t_mfpOn('ElementParse' + '.' + RETINA_NS, function(e, item) {\r\n\t\t\t\t\t\titem.src = st.replaceSrc(item, ratio);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n});\r\n\r\n/*>>retina*/\r\n _checkInstance(); }));\n/**\r\n * jQuery Unveil\r\n * A very lightweight jQuery plugin to lazy load images\r\n * http://luis-almeida.github.com/unveil\r\n *\r\n * Licensed under the MIT license.\r\n * Copyright 2013 Luís Almeida\r\n * https://github.com/luis-almeida\r\n */\r\n\r\n;(function($) {\r\n\r\n  $.fn.unveil = function(threshold, callback) {\r\n\r\n    var $w = $(window),\r\n        th = threshold || 0,\r\n        retina = window.devicePixelRatio > 1,\r\n        attrib = retina? \"data-src-retina\" : \"data-src\",\r\n        images = this,\r\n        loaded;\r\n\r\n    this.one(\"unveil\", function() {\r\n      var source = this.getAttribute(attrib);\r\n      source = source || this.getAttribute(\"data-src\");\r\n      if (source) {\r\n        this.setAttribute(\"src\", source);\r\n        if (typeof callback === \"function\") callback.call(this);\r\n      }\r\n    });\r\n\r\n    function unveil() {\r\n      var inview = images.filter(function() {\r\n        var $e = $(this);\r\n        if ($e.is(\":hidden\")) return;\r\n\r\n        var wt = $w.scrollTop(),\r\n            wb = wt + $w.height(),\r\n            et = $e.offset().top,\r\n            eb = et + $e.height();\r\n\r\n        return eb >= wt - th && et <= wb + th;\r\n      });\r\n\r\n      loaded = inview.trigger(\"unveil\");\r\n      images = images.not(loaded);\r\n    }\r\n\r\n    $w.on(\"scroll.unveil resize.unveil lookup.unveil\", unveil);\r\n\r\n    unveil();\r\n\r\n    return this;\r\n\r\n  };\r\n\r\n})(window.jQuery || window.Zepto);\r\n\n/**\r\n * NetteForms - simple form validation.\r\n *\r\n * This file is part of the Nette Framework (https://nette.org)\r\n * Copyright (c) 2004 David Grudl (https://davidgrudl.com)\r\n */\r\n\r\n(function(global, factory) {\r\n\tif (!global.JSON) {\r\n\t\treturn;\r\n\t}\r\n\r\n\tif (typeof define === 'function' && define.amd) {\r\n\t\tdefine(function() {\r\n\t\t\treturn factory(global);\r\n\t\t});\r\n\t} else if (typeof module === 'object' && typeof module.exports === 'object') {\r\n\t\tmodule.exports = factory(global);\r\n\t} else {\r\n\t\tvar init = !global.Nette || !global.Nette.noInit;\r\n\t\tglobal.Nette = factory(global);\r\n\t\tif (init) {\r\n\t\t\tglobal.Nette.initOnLoad();\r\n\t\t}\r\n\t}\r\n\r\n}(typeof window !== 'undefined' ? window : this, function(window) {\r\n\r\n\t'use strict';\r\n\r\n\tvar Nette = {};\r\n\r\n\tNette.formErrors = [];\r\n\tNette.version = '2.4';\r\n\r\n\r\n\t/**\r\n\t * Attaches a handler to an event for the element.\r\n\t */\r\n\tNette.addEvent = function(element, on, callback) {\r\n\t\tif (element.addEventListener) {\r\n\t\t\telement.addEventListener(on, callback);\r\n\t\t} else if (on === 'DOMContentLoaded') {\r\n\t\t\telement.attachEvent('onreadystatechange', function() {\r\n\t\t\t\tif (element.readyState === 'complete') {\r\n\t\t\t\t\tcallback.call(this);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\telement.attachEvent('on' + on, getHandler(callback));\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tfunction getHandler(callback) {\r\n\t\treturn function(e) {\r\n\t\t\treturn callback.call(this, e);\r\n\t\t};\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * Returns the value of form element.\r\n\t */\r\n\tNette.getValue = function(elem) {\r\n\t\tvar i;\r\n\t\tif (!elem) {\r\n\t\t\treturn null;\r\n\r\n\t\t} else if (!elem.tagName) { // RadioNodeList, HTMLCollection, array\r\n\t\t\treturn elem[0] ? Nette.getValue(elem[0]) : null;\r\n\r\n\t\t} else if (elem.type === 'radio') {\r\n\t\t\tvar elements = elem.form.elements; // prevents problem with name 'item' or 'namedItem'\r\n\t\t\tfor (i = 0; i < elements.length; i++) {\r\n\t\t\t\tif (elements[i].name === elem.name && elements[i].checked) {\r\n\t\t\t\t\treturn elements[i].value;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn null;\r\n\r\n\t\t} else if (elem.type === 'file') {\r\n\t\t\treturn elem.files || elem.value;\r\n\r\n\t\t} else if (elem.tagName.toLowerCase() === 'select') {\r\n\t\t\tvar index = elem.selectedIndex,\r\n\t\t\t\toptions = elem.options,\r\n\t\t\t\tvalues = [];\r\n\r\n\t\t\tif (elem.type === 'select-one') {\r\n\t\t\t\treturn index < 0 ? null : options[index].value;\r\n\t\t\t}\r\n\r\n\t\t\tfor (i = 0; i < options.length; i++) {\r\n\t\t\t\tif (options[i].selected) {\r\n\t\t\t\t\tvalues.push(options[i].value);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn values;\r\n\r\n\t\t} else if (elem.name && elem.name.match(/\\[\\]$/)) { // multiple elements []\r\n\t\t\tvar elements = elem.form.elements[elem.name].tagName ? [elem] : elem.form.elements[elem.name],\r\n\t\t\t\tvalues = [];\r\n\r\n\t\t\tfor (i = 0; i < elements.length; i++) {\r\n\t\t\t\tif (elements[i].type !== 'checkbox' || elements[i].checked) {\r\n\t\t\t\t\tvalues.push(elements[i].value);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn values;\r\n\r\n\t\t} else if (elem.type === 'checkbox') {\r\n\t\t\treturn elem.checked;\r\n\r\n\t\t} else if (elem.tagName.toLowerCase() === 'textarea') {\r\n\t\t\treturn elem.value.replace('\\r', '');\r\n\r\n\t\t} else {\r\n\t\t\treturn elem.value.replace('\\r', '').replace(/^\\s+|\\s+$/g, '');\r\n\t\t}\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Returns the effective value of form element.\r\n\t */\r\n\tNette.getEffectiveValue = function(elem) {\r\n\t\tvar val = Nette.getValue(elem);\r\n\t\tif (elem.getAttribute) {\r\n\t\t\tif (val === elem.getAttribute('data-nette-empty-value')) {\r\n\t\t\t\tval = '';\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn val;\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Validates form element against given rules.\r\n\t */\r\n\tNette.validateControl = function(elem, rules, onlyCheck, value, emptyOptional) {\r\n\t\telem = elem.tagName ? elem : elem[0]; // RadioNodeList\r\n\t\trules = rules || Nette.parseJSON(elem.getAttribute('data-nette-rules'));\r\n\t\tvalue = value === undefined ? {value: Nette.getEffectiveValue(elem)} : value;\r\n\r\n\t\tfor (var id = 0, len = rules.length; id < len; id++) {\r\n\t\t\tvar rule = rules[id],\r\n\t\t\t\top = rule.op.match(/(~)?([^?]+)/),\r\n\t\t\t\tcurElem = rule.control ? elem.form.elements.namedItem(rule.control) : elem;\r\n\r\n\t\t\trule.neg = op[1];\r\n\t\t\trule.op = op[2];\r\n\t\t\trule.condition = !!rule.rules;\r\n\r\n\t\t\tif (!curElem) {\r\n\t\t\t\tcontinue;\r\n\t\t\t} else if (rule.op === 'optional') {\r\n\t\t\t\temptyOptional = !Nette.validateRule(elem, ':filled', null, value);\r\n\t\t\t\tcontinue;\r\n\t\t\t} else if (emptyOptional && !rule.condition && rule.op !== ':filled') {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tcurElem = curElem.tagName ? curElem : curElem[0]; // RadioNodeList\r\n\t\t\tvar curValue = elem === curElem ? value : {value: Nette.getEffectiveValue(curElem)},\r\n\t\t\t\tsuccess = Nette.validateRule(curElem, rule.op, rule.arg, curValue);\r\n\r\n\t\t\tif (success === null) {\r\n\t\t\t\tcontinue;\r\n\t\t\t} else if (rule.neg) {\r\n\t\t\t\tsuccess = !success;\r\n\t\t\t}\r\n\r\n\t\t\tif (rule.condition && success) {\r\n\t\t\t\tif (!Nette.validateControl(elem, rule.rules, onlyCheck, value, rule.op === ':blank' ? false : emptyOptional)) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t} else if (!rule.condition && !success) {\r\n\t\t\t\tif (Nette.isDisabled(curElem)) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t\tif (!onlyCheck) {\r\n\t\t\t\t\tvar arr = Nette.isArray(rule.arg) ? rule.arg : [rule.arg],\r\n\t\t\t\t\t\tmessage = rule.msg.replace(/%(value|\\d+)/g, function(foo, m) {\r\n\t\t\t\t\t\t\treturn Nette.getValue(m === 'value' ? curElem : elem.form.elements.namedItem(arr[m].control));\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\tNette.addError(curElem, message);\r\n\t\t\t\t}\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (elem.type === 'number' && !elem.validity.valid) {\r\n\t\t\tif (!onlyCheck) {\r\n\t\t\t\tNette.addError(elem, 'Please enter a valid value.');\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Validates whole form.\r\n\t */\r\n\tNette.validateForm = function(sender, onlyCheck) {\r\n\t\tvar form = sender.form || sender,\r\n\t\t\tscope = false;\r\n\r\n\t\tNette.formErrors = [];\r\n\r\n\t\tif (form['nette-submittedBy'] && form['nette-submittedBy'].getAttribute('formnovalidate') !== null) {\r\n\t\t\tvar scopeArr = Nette.parseJSON(form['nette-submittedBy'].getAttribute('data-nette-validation-scope'));\r\n\t\t\tif (scopeArr.length) {\r\n\t\t\t\tscope = new RegExp('^(' + scopeArr.join('-|') + '-)');\r\n\t\t\t} else {\r\n\t\t\t\tNette.showFormErrors(form, []);\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tvar radios = {}, i, elem;\r\n\r\n\t\tfor (i = 0; i < form.elements.length; i++) {\r\n\t\t\telem = form.elements[i];\r\n\r\n\t\t\tif (elem.tagName && !(elem.tagName.toLowerCase() in {input: 1, select: 1, textarea: 1, button: 1})) {\r\n\t\t\t\tcontinue;\r\n\r\n\t\t\t} else if (elem.type === 'radio') {\r\n\t\t\t\tif (radios[elem.name]) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t\tradios[elem.name] = true;\r\n\t\t\t}\r\n\r\n\t\t\tif ((scope && !elem.name.replace(/]\\[|\\[|]|$/g, '-').match(scope)) || Nette.isDisabled(elem)) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tif (!Nette.validateControl(elem, null, onlyCheck) && !Nette.formErrors.length) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t}\r\n\t\tvar success = !Nette.formErrors.length;\r\n\t\tNette.showFormErrors(form, Nette.formErrors);\r\n\t\treturn success;\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Check if input is disabled.\r\n\t */\r\n\tNette.isDisabled = function(elem) {\r\n\t\tif (elem.type === 'radio') {\r\n\t\t\tfor (var i = 0, elements = elem.form.elements; i < elements.length; i++) {\r\n\t\t\t\tif (elements[i].name === elem.name && !elements[i].disabled) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn elem.disabled;\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Adds error message to the queue.\r\n\t */\r\n\tNette.addError = function(elem, message) {\r\n\t\tNette.formErrors.push({\r\n\t\t\telement: elem,\r\n\t\t\tmessage: message\r\n\t\t});\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Display error messages.\r\n\t */\r\n\tNette.showFormErrors = function(form, errors) {\r\n\t\tvar messages = [],\r\n\t\t\tfocusElem;\r\n\r\n\t\tfor (var i = 0; i < errors.length; i++) {\r\n\t\t\tvar elem = errors[i].element,\r\n\t\t\t\tmessage = errors[i].message;\r\n\r\n\t\t\tif (!Nette.inArray(messages, message)) {\r\n\t\t\t\tmessages.push(message);\r\n\r\n\t\t\t\tif (!focusElem && elem.focus) {\r\n\t\t\t\t\tfocusElem = elem;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (messages.length) {\r\n\t\t\talert(messages.join('\\n'));\r\n\r\n\t\t\tif (focusElem) {\r\n\t\t\t\tfocusElem.focus();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Expand rule argument.\r\n\t */\r\n\tNette.expandRuleArgument = function(form, arg) {\r\n\t\tif (arg && arg.control) {\r\n\t\t\tvar control = form.elements.namedItem(arg.control),\r\n\t\t\t\tvalue = {value: Nette.getEffectiveValue(control)};\r\n\t\t\tNette.validateControl(control, null, true, value);\r\n\t\t\targ = value.value;\r\n\t\t}\r\n\t\treturn arg;\r\n\t};\r\n\r\n\r\n\tvar preventFiltering = false;\r\n\r\n\t/**\r\n\t * Validates single rule.\r\n\t */\r\n\tNette.validateRule = function(elem, op, arg, value) {\r\n\t\tvalue = value === undefined ? {value: Nette.getEffectiveValue(elem)} : value;\r\n\r\n\t\tif (op.charAt(0) === ':') {\r\n\t\t\top = op.substr(1);\r\n\t\t}\r\n\t\top = op.replace('::', '_');\r\n\t\top = op.replace(/\\\\/g, '');\r\n\r\n\t\tvar arr = Nette.isArray(arg) ? arg.slice(0) : [arg];\r\n\t\tif (!preventFiltering) {\r\n\t\t\tpreventFiltering = true;\r\n\t\t\tfor (var i = 0, len = arr.length; i < len; i++) {\r\n\t\t\t\tarr[i] = Nette.expandRuleArgument(elem.form, arr[i]);\r\n\t\t\t}\r\n\t\t\tpreventFiltering = false;\r\n\t\t}\r\n\t\treturn Nette.validators[op]\r\n\t\t\t? Nette.validators[op](elem, Nette.isArray(arg) ? arr : arr[0], value.value, value)\r\n\t\t\t: null;\r\n\t};\r\n\r\n\r\n\tNette.validators = {\r\n\t\tfilled: function(elem, arg, val) {\r\n\t\t\tif (elem.type === 'number' && elem.validity.badInput) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\treturn val !== '' && val !== false && val !== null\r\n\t\t\t\t&& (!Nette.isArray(val) || !!val.length)\r\n\t\t\t\t&& (!window.FileList || !(val instanceof window.FileList) || val.length);\r\n\t\t},\r\n\r\n\t\tblank: function(elem, arg, val) {\r\n\t\t\treturn !Nette.validators.filled(elem, arg, val);\r\n\t\t},\r\n\r\n\t\tvalid: function(elem) {\r\n\t\t\treturn Nette.validateControl(elem, null, true);\r\n\t\t},\r\n\r\n\t\tequal: function(elem, arg, val) {\r\n\t\t\tif (arg === undefined) {\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\r\n\t\t\tfunction toString(val) {\r\n\t\t\t\tif (typeof val === 'number' || typeof val === 'string') {\r\n\t\t\t\t\treturn '' + val;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn val === true ? '1' : '';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tval = Nette.isArray(val) ? val : [val];\r\n\t\t\targ = Nette.isArray(arg) ? arg : [arg];\r\n\t\t\tloop:\r\n\t\t\tfor (var i1 = 0, len1 = val.length; i1 < len1; i1++) {\r\n\t\t\t\tfor (var i2 = 0, len2 = arg.length; i2 < len2; i2++) {\r\n\t\t\t\t\tif (toString(val[i1]) === toString(arg[i2])) {\r\n\t\t\t\t\t\tcontinue loop;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\r\n\t\tnotEqual: function(elem, arg, val) {\r\n\t\t\treturn arg === undefined ? null : !Nette.validators.equal(elem, arg, val);\r\n\t\t},\r\n\r\n\t\tminLength: function(elem, arg, val) {\r\n\t\t\tif (elem.type === 'number') {\r\n\t\t\t\tif (elem.validity.tooShort) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t} else if (elem.validity.badInput) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn val.length >= arg;\r\n\t\t},\r\n\r\n\t\tmaxLength: function(elem, arg, val) {\r\n\t\t\tif (elem.type === 'number') {\r\n\t\t\t\tif (elem.validity.tooLong) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t} else if (elem.validity.badInput) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn val.length <= arg;\r\n\t\t},\r\n\r\n\t\tlength: function(elem, arg, val) {\r\n\t\t\tif (elem.type === 'number') {\r\n\t\t\t\tif (elem.validity.tooShort || elem.validity.tooLong) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t} else if (elem.validity.badInput) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\targ = Nette.isArray(arg) ? arg : [arg, arg];\r\n\t\t\treturn (arg[0] === null || val.length >= arg[0]) && (arg[1] === null || val.length <= arg[1]);\r\n\t\t},\r\n\r\n\t\temail: function(elem, arg, val) {\r\n\t\t\treturn (/^(\"([ !#-[\\]-~]|\\\\[ -~])+\"|[-a-z0-9!#$%&'*+\\/=?^_`{|}~]+(\\.[-a-z0-9!#$%&'*+\\/=?^_`{|}~]+)*)@([0-9a-z\\u00C0-\\u02FF\\u0370-\\u1EFF]([-0-9a-z\\u00C0-\\u02FF\\u0370-\\u1EFF]{0,61}[0-9a-z\\u00C0-\\u02FF\\u0370-\\u1EFF])?\\.)+[a-z\\u00C0-\\u02FF\\u0370-\\u1EFF]([-0-9a-z\\u00C0-\\u02FF\\u0370-\\u1EFF]{0,17}[a-z\\u00C0-\\u02FF\\u0370-\\u1EFF])?$/i).test(val);\r\n\t\t},\r\n\r\n\t\turl: function(elem, arg, val, value) {\r\n\t\t\tif (!(/^[a-z\\d+.-]+:/).test(val)) {\r\n\t\t\t\tval = 'http://' + val;\r\n\t\t\t}\r\n\t\t\tif ((/^https?:\\/\\/((([-_0-9a-z\\u00C0-\\u02FF\\u0370-\\u1EFF]+\\.)*[0-9a-z\\u00C0-\\u02FF\\u0370-\\u1EFF]([-0-9a-z\\u00C0-\\u02FF\\u0370-\\u1EFF]{0,61}[0-9a-z\\u00C0-\\u02FF\\u0370-\\u1EFF])?\\.)?[a-z\\u00C0-\\u02FF\\u0370-\\u1EFF]([-0-9a-z\\u00C0-\\u02FF\\u0370-\\u1EFF]{0,17}[a-z\\u00C0-\\u02FF\\u0370-\\u1EFF])?|\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}|\\[[0-9a-f:]{3,39}\\])(:\\d{1,5})?(\\/\\S*)?$/i).test(val)) {\r\n\t\t\t\tvalue.value = val;\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t},\r\n\r\n\t\tregexp: function(elem, arg, val) {\r\n\t\t\tvar parts = typeof arg === 'string' ? arg.match(/^\\/(.*)\\/([imu]*)$/) : false;\r\n\t\t\ttry {\r\n\t\t\t\treturn parts && (new RegExp(parts[1], parts[2].replace('u', ''))).test(val);\r\n\t\t\t} catch (e) {}\r\n\t\t},\r\n\r\n\t\tpattern: function(elem, arg, val) {\r\n\t\t\ttry {\r\n\t\t\t\treturn typeof arg === 'string' ? (new RegExp('^(?:' + arg + ')$')).test(val) : null;\r\n\t\t\t} catch (e) {}\r\n\t\t},\r\n\r\n\t\tinteger: function(elem, arg, val) {\r\n\t\t\tif (elem.type === 'number' && elem.validity.badInput) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\treturn (/^-?[0-9]+$/).test(val);\r\n\t\t},\r\n\r\n\t\t'float': function(elem, arg, val, value) {\r\n\t\t\tif (elem.type === 'number' && elem.validity.badInput) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tval = val.replace(' ', '').replace(',', '.');\r\n\t\t\tif ((/^-?[0-9]*[.,]?[0-9]+$/).test(val)) {\r\n\t\t\t\tvalue.value = val;\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t},\r\n\r\n\t\tmin: function(elem, arg, val) {\r\n\t\t\tif (elem.type === 'number') {\r\n\t\t\t\tif (elem.validity.rangeUnderflow) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t} else if (elem.validity.badInput) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn arg === null || parseFloat(val) >= arg;\r\n\t\t},\r\n\r\n\t\tmax: function(elem, arg, val) {\r\n\t\t\tif (elem.type === 'number') {\r\n\t\t\t\tif (elem.validity.rangeOverflow) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t} else if (elem.validity.badInput) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn arg === null || parseFloat(val) <= arg;\r\n\t\t},\r\n\r\n\t\trange: function(elem, arg, val) {\r\n\t\t\tif (elem.type === 'number') {\r\n\t\t\t\tif (elem.validity.rangeUnderflow || elem.validity.rangeOverflow) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t} else if (elem.validity.badInput) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn Nette.isArray(arg) ?\r\n\t\t\t\t((arg[0] === null || parseFloat(val) >= arg[0]) && (arg[1] === null || parseFloat(val) <= arg[1])) : null;\r\n\t\t},\r\n\r\n\t\tsubmitted: function(elem) {\r\n\t\t\treturn elem.form['nette-submittedBy'] === elem;\r\n\t\t},\r\n\r\n\t\tfileSize: function(elem, arg, val) {\r\n\t\t\tif (window.FileList) {\r\n\t\t\t\tfor (var i = 0; i < val.length; i++) {\r\n\t\t\t\t\tif (val[i].size > arg) {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\r\n\t\timage: function (elem, arg, val) {\r\n\t\t\tif (window.FileList && val instanceof window.FileList) {\r\n\t\t\t\tfor (var i = 0; i < val.length; i++) {\r\n\t\t\t\t\tvar type = val[i].type;\r\n\t\t\t\t\tif (type && type !== 'image/gif' && type !== 'image/png' && type !== 'image/jpeg') {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\r\n\t\t'static': function (elem, arg, val) {\r\n\t\t\treturn arg;\r\n\t\t}\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Process all toggles in form.\r\n\t */\r\n\tNette.toggleForm = function(form, elem) {\r\n\t\tvar i;\r\n\t\tNette.toggles = {};\r\n\t\tfor (i = 0; i < form.elements.length; i++) {\r\n\t\t\tif (form.elements[i].tagName.toLowerCase() in {input: 1, select: 1, textarea: 1, button: 1}) {\r\n\t\t\t\tNette.toggleControl(form.elements[i], null, null, !elem);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tfor (i in Nette.toggles) {\r\n\t\t\tNette.toggle(i, Nette.toggles[i], elem);\r\n\t\t}\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Process toggles on form element.\r\n\t */\r\n\tNette.toggleControl = function(elem, rules, success, firsttime, value) {\r\n\t\trules = rules || Nette.parseJSON(elem.getAttribute('data-nette-rules'));\r\n\t\tvalue = value === undefined ? {value: Nette.getEffectiveValue(elem)} : value;\r\n\r\n\t\tvar has = false,\r\n\t\t\thandled = [],\r\n\t\t\thandler = function () {\r\n\t\t\t\tNette.toggleForm(elem.form, elem);\r\n\t\t\t},\r\n\t\t\tcurSuccess;\r\n\r\n\t\tfor (var id = 0, len = rules.length; id < len; id++) {\r\n\t\t\tvar rule = rules[id],\r\n\t\t\t\top = rule.op.match(/(~)?([^?]+)/),\r\n\t\t\t\tcurElem = rule.control ? elem.form.elements.namedItem(rule.control) : elem;\r\n\r\n\t\t\tif (!curElem) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tcurSuccess = success;\r\n\t\t\tif (success !== false) {\r\n\t\t\t\trule.neg = op[1];\r\n\t\t\t\trule.op = op[2];\r\n\t\t\t\tvar curValue = elem === curElem ? value : {value: Nette.getEffectiveValue(curElem)};\r\n\t\t\t\tcurSuccess = Nette.validateRule(curElem, rule.op, rule.arg, curValue);\r\n\t\t\t\tif (curSuccess === null) {\r\n\t\t\t\t\tcontinue;\r\n\r\n\t\t\t\t} else if (rule.neg) {\r\n\t\t\t\t\tcurSuccess = !curSuccess;\r\n\t\t\t\t}\r\n\t\t\t\tif (!rule.rules) {\r\n\t\t\t\t\tsuccess = curSuccess;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif ((rule.rules && Nette.toggleControl(elem, rule.rules, curSuccess, firsttime, value)) || rule.toggle) {\r\n\t\t\t\thas = true;\r\n\t\t\t\tif (firsttime) {\r\n\t\t\t\t\tvar oldIE = !document.addEventListener, // IE < 9\r\n\t\t\t\t\t\tname = curElem.tagName ? curElem.name : curElem[0].name,\r\n\t\t\t\t\t\tels = curElem.tagName ? curElem.form.elements : curElem;\r\n\r\n\t\t\t\t\tfor (var i = 0; i < els.length; i++) {\r\n\t\t\t\t\t\tif (els[i].name === name && !Nette.inArray(handled, els[i])) {\r\n\t\t\t\t\t\t\tNette.addEvent(els[i], oldIE && els[i].type in {checkbox: 1, radio: 1} ? 'click' : 'change', handler);\r\n\t\t\t\t\t\t\thandled.push(els[i]);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (var id2 in rule.toggle || []) {\r\n\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(rule.toggle, id2)) {\r\n\t\t\t\t\t\tNette.toggles[id2] = Nette.toggles[id2] || (rule.toggle[id2] ? curSuccess : !curSuccess);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn has;\r\n\t};\r\n\r\n\r\n\tNette.parseJSON = function(s) {\r\n\t\treturn (s || '').substr(0, 3) === '{op'\r\n\t\t\t? eval('[' + s + ']') // backward compatibility with Nette 2.0.x\r\n\t\t\t: JSON.parse(s || '[]');\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Displays or hides HTML element.\r\n\t */\r\n\tNette.toggle = function(id, visible, srcElement) {\r\n\t\tvar elem = document.getElementById(id);\r\n\t\tif (elem) {\r\n\t\t\telem.style.display = visible ? '' : 'none';\r\n\t\t}\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Setup handlers.\r\n\t */\r\n\tNette.initForm = function(form) {\r\n\t\tNette.toggleForm(form);\r\n\r\n\t\tif (form.noValidate) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tform.noValidate = true;\r\n\r\n\t\tNette.addEvent(form, 'submit', function(e) {\r\n\t\t\tif (!Nette.validateForm(form)) {\r\n\t\t\t\tif (e && e.stopPropagation) {\r\n\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\te.preventDefault();\r\n\t\t\t\t} else if (window.event) {\r\n\t\t\t\t\tevent.cancelBubble = true;\r\n\t\t\t\t\tevent.returnValue = false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t});\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * @private\r\n\t */\r\n\tNette.initOnLoad = function() {\r\n\t\tNette.addEvent(document, 'DOMContentLoaded', function() {\r\n\t\t\tfor (var i = 0; i < document.forms.length; i++) {\r\n\t\t\t\tvar form = document.forms[i];\r\n\t\t\t\tfor (var j = 0; j < form.elements.length; j++) {\r\n\t\t\t\t\tif (form.elements[j].getAttribute('data-nette-rules')) {\r\n\t\t\t\t\t\tNette.initForm(form);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tNette.addEvent(document.body, 'click', function(e) {\r\n\t\t\t\tvar target = e.target || e.srcElement;\r\n\t\t\t\twhile (target) {\r\n\t\t\t\t\tif (target.form && target.type in {submit: 1, image: 1}) {\r\n\t\t\t\t\t\ttarget.form['nette-submittedBy'] = target;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t\ttarget = target.parentNode;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Determines whether the argument is an array.\r\n\t */\r\n\tNette.isArray = function(arg) {\r\n\t\treturn Object.prototype.toString.call(arg) === '[object Array]';\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Search for a specified value within an array.\r\n\t */\r\n\tNette.inArray = function(arr, val) {\r\n\t\tif ([].indexOf) {\r\n\t\t\treturn arr.indexOf(val) > -1;\r\n\t\t} else {\r\n\t\t\tfor (var i = 0; i < arr.length; i++) {\r\n\t\t\t\tif (arr[i] === val) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t}\r\n\t};\r\n\r\n\r\n\t/**\r\n\t * Converts string to web safe characters [a-z0-9-] text.\r\n\t */\r\n\tNette.webalize = function(s) {\r\n\t\ts = s.toLowerCase();\r\n\t\tvar res = '', i, ch;\r\n\t\tfor (i = 0; i < s.length; i++) {\r\n\t\t\tch = Nette.webalizeTable[s.charAt(i)];\r\n\t\t\tres += ch ? ch : s.charAt(i);\r\n\t\t}\r\n\t\treturn res.replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');\r\n\t};\r\n\r\n\tNette.webalizeTable = {\\u00e1: 'a', \\u00e4: 'a', \\u010d: 'c', \\u010f: 'd', \\u00e9: 'e', \\u011b: 'e', \\u00ed: 'i', \\u013e: 'l', \\u0148: 'n', \\u00f3: 'o', \\u00f4: 'o', \\u0159: 'r', \\u0161: 's', \\u0165: 't', \\u00fa: 'u', \\u016f: 'u', \\u00fd: 'y', \\u017e: 'z'};\r\n\r\n\treturn Nette;\r\n}));\r\n\n// lightbox (Magnific Popup)\n$(document).ready(function () {\n  if ($('.gallery').length) {\n    // český překlad\n    $.extend(true, $.magnificPopup.defaults, {\n      tClose: 'Zavřít',\n      tLoading: 'Nahrávám...',\n      gallery: {\n        tPrev: 'Předchozí',\n        tNext: 'Následující',\n        tCounter: '%curr% z %total%',\n      },\n      image: {\n        tError: '<a href=\"%url%\">Obrázek</a> nelze načíst.',\n      },\n      ajax: {\n        tError: '<a href=\"%url%\">Obsah</a> nelze načíst.',\n      },\n    });\n\n    $('.gallery').magnificPopup({\n      delegate: 'a',\n      type: 'image',\n      removalDelay: 300,\n      mainClass: 'mfp-fade',\n      gallery: {\n        enabled: true,\n        tCounter: '',\n      },\n    });\n  }\n});\n\n$(document).ready(function () {\r\n  // obecne\r\n  var bodyWidth = window.innerWidth;\r\n\r\n  // při resize okna\r\n  $(window).resize(function () {\r\n    // osetreni, zda se velikost zmenila\r\n    if (bodyWidth !== window.innerWidth) {\r\n      mobileMenu();\r\n    }\r\n  });\r\n\r\n  // mobilní menu\r\n  function mobileMenu() {\r\n    // šířka okna\r\n    var bodyWidth = window.innerWidth;\r\n    // breakpoint mobilního menu\r\n    var respMenuWidth = 992;\r\n\r\n    if (bodyWidth < respMenuWidth) {\r\n      $('.nav').addClass('nav--mobile');\r\n      $('.nav-product').not('.nav-product--hidden').hide();\r\n      // skrytí košíku a přihlášení\r\n      $('.basket__content, .login__content').hide();\r\n    } else {\r\n      $('.nav').removeClass('nav--mobile');\r\n      $('.nav-product').not('.nav-product--hidden').show();\r\n      $('.basket__content, .login__content').show();\r\n    }\r\n\r\n    // zobrazení menu\r\n    if (bodyWidth < respMenuWidth) {\r\n      $('.nav--mobile').click(function () {\r\n        $('.basket__content, .login__content').hide();\r\n        $('.nav-product, .header__nav').toggle();\r\n        return false;\r\n      });\r\n    }\r\n\r\n    // zobrazení košíku\r\n    /*\r\n    if (bodyWidth < respMenuWidth) {\r\n      $('.basket__header').click(function() {\r\n        $('.nav-product, .login__content').hide();\r\n        $('.basket__content').toggle();\r\n        return false;\r\n      });\r\n    }\r\n    */\r\n\r\n    // zobrazení loginu\r\n    if (bodyWidth < respMenuWidth) {\r\n      $('.login__header').click(function () {\r\n        $('.basket__content, .nav-product').hide();\r\n        $('.login__content').toggle();\r\n        return false;\r\n      });\r\n    }\r\n  }\r\n  mobileMenu();\r\n\r\n  // lazy loading obrázků\r\n  if ($('.product__image').length) {\r\n    $('.product__image img').unveil(200);\r\n  }\r\n\r\n  // přepínání výrobců\r\n  if ($('.nav-product--switch').length) {\r\n    $('.nav-product--switch .nav-product__header').on(\r\n      'click',\r\n      function (event) {\r\n        $('.nav-product--switch').removeClass('is-active');\r\n        $(this).parent().addClass('is-active');\r\n      }\r\n    );\r\n  }\r\n\r\n  // taby\r\n  if ($('.tabs').length) {\r\n    $('.tabs__content > div').not('.is-active').hide();\r\n    $('.tabs__name').on('click', function (event) {\r\n      event.preventDefault();\r\n\r\n      var target = $(this).attr('href');\r\n\r\n      $('.tabs__content > .is-active').removeClass('is-active').hide();\r\n      $(target).addClass('is-active').show();\r\n\r\n      $('.tabs__name').removeClass('is-active');\r\n      $(this).addClass('is-active');\r\n    });\r\n  }\r\n\r\n  // otevřít tab přímo\r\n  if ($('.open-tab').length) {\r\n    $('.open-tab').on('click', function (event) {\r\n      $('.tabs__name').removeClass('is-active');\r\n      $('.tabs__content > .is-active').removeClass('is-active').hide();\r\n\r\n      var target = $(this).attr('href');\r\n\r\n      $(target).addClass('is-active').show();\r\n      $('.tab[href=\"' + target + '\"]')\r\n        .addClass('is-active')\r\n        .show();\r\n    });\r\n  }\r\n\r\n  // hláška s možností zavření\r\n  if ($('.alert--close').length) {\r\n    // přidání ikony\r\n    $('.alert--close').append(\r\n      ' <span class=\"icon icon--close\"><svg class=\"icon__svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"><use xlink:href=\"/img/icons.svg#close\" x=\"0\" y=\"0\" width=\"100%\" height=\"100%\"></use></svg></span>'\r\n    );\r\n    // zavření okna\r\n    $('.alert--close .icon--close').on('click', function () {\r\n      $(this).parent().hide();\r\n    });\r\n  }\r\n\r\n  // modal okna\r\n  if ($('.modal').length) {\r\n    // otevření okna\r\n    $('.modal--show').on('click', function () {\r\n      // zjištění ID okna z atributu rel\r\n      var modalName = '#' + $(this).attr('rel');\r\n      var modalBody = modalName + ' .modal__body';\r\n\r\n      // otevření konkrétního okna\r\n      $(modalName).show();\r\n\r\n      // zjištění výšky okna\r\n      var modalHeight = $(modalBody).outerHeight();\r\n\r\n      // pokud je výška obrazovky menší než okno, resetuje se pozice\r\n      if (modalHeight > window.innerHeight) {\r\n        $(modalName).addClass('modal--reset');\r\n      }\r\n    });\r\n\r\n    // zavření okna\r\n    $('.modal__close').on('click', function () {\r\n      $(this).closest('.modal').hide();\r\n      return false;\r\n    });\r\n  }\r\n\r\n  // antispam\r\n  if ($('.antispam').length) {\r\n    // zjištění ID z atributu rel\r\n    var antispamNumber = $('.antispam').attr('data-help');\r\n\r\n    // vypsání do inputu\r\n    $('.antispam').find('input').val(antispamNumber);\r\n\r\n    // skrytí celého bloku\r\n    $('.antispam').hide();\r\n  }\r\n\r\n  // otevírání skrytých částí formuláře\r\n  if ($('.reveal').length) {\r\n    // skrytí částí\r\n    $('.reveal').hide();\r\n\r\n    // standardní odkaz (A) nebo jakýkoliv blok\r\n    $('.reveal--show')\r\n      .not('label.reveal--show, input.reveal--show')\r\n      .on('click', function () {\r\n        // zjištění ID z atributu rel\r\n        var revealName = '#' + $(this).attr('rel');\r\n\r\n        // zobrazení skryté části\r\n        $(revealName).toggle();\r\n\r\n        return false;\r\n      });\r\n\r\n    // formulářový prvek...\r\n    $('label.reveal--show, input.reveal--show').on('change', function () {\r\n      // zjištění ID z atributu rel\r\n      var revealName = '#' + $(this).attr('rel');\r\n\r\n      // zobrazení skryté části\r\n      $(revealName).toggle();\r\n    });\r\n  }\r\n\r\n  // vytisknout\r\n  if ($('.control--print').length) {\r\n    $('.control--print').click(function () {\r\n      window.print();\r\n      return false;\r\n    });\r\n  }\r\n\r\n  // dopravy a platby\r\n  if ($('.order-delivery').length) {\r\n    // skrytí částí\r\n    $('.order-delivery__payment').hide();\r\n\r\n    // zvolení plateb po kliknutí na dopravu\r\n    $('.order-delivery__type').on('click', function () {\r\n      // skrytí zobrazených částí\r\n      $('.order-delivery__payment').hide();\r\n\r\n      // zjištění ID z atributu rel\r\n      var revealName = '#' + $(this).attr('rel');\r\n\r\n      // zobrazení skryté části\r\n      $(revealName).toggle();\r\n\r\n      // pokud obsahuje radio button\r\n      var has_checkbox = $(this).find('input[type=radio]');\r\n      if ($(has_checkbox).length) {\r\n        has_checkbox.prop('checked', true);\r\n      }\r\n\r\n      // automatické vybrání první volby\r\n      $(revealName).find('input').filter(':visible:first').trigger('click');\r\n    });\r\n\r\n    // při kliknutí na samotný radio button\r\n    $('.order-delivery__type input').on('click', function () {\r\n      $(this).closest('.order-delivery__type').trigger('click');\r\n    });\r\n\r\n    // zakliknout typ dopravy\r\n    if (delIdSelected != 0) {\r\n      var selectedDelivery = '#delid_' + delIdSelected;\r\n      $(selectedDelivery).find('input').prop('checked', true);\r\n    }\r\n\r\n    // zakliknout a zobrazit pokud je zvolena platba\r\n    if (payIdSelected != 0) {\r\n      var selectedPayment = 'input[value=\"' + payIdSelected + '\"]';\r\n      $(selectedPayment).closest('.order-delivery__payment').show();\r\n      $(selectedPayment).trigger('click');\r\n    } else {\r\n      // není zvolena platba, zaklikneme první možnost\r\n      $('.order-delivery__type').first().trigger('click');\r\n    }\r\n  }\r\n\r\n  // slider (Slick)\r\n  if (bodyWidth > 991 && $('.slider').length) {\r\n    $('.slider').slick({\r\n      dots: true,\r\n      speed: 500,\r\n      slidesToShow: 1,\r\n      autoplay: true,\r\n      arrows: false,\r\n    });\r\n  }\r\n\r\n  // lightbox (Magnific Popup)\r\n  if ($('.gallery').length) {\r\n    // český překlad\r\n    $.extend(true, $.magnificPopup.defaults, {\r\n      tClose: 'Zavřít',\r\n      tLoading: 'Nahrávám...',\r\n      gallery: {\r\n        tPrev: 'Předchozí',\r\n        tNext: 'Následující',\r\n        tCounter: '%curr% z %total%',\r\n      },\r\n      image: {\r\n        tError: '<a href=\"%url%\">Obrázek</a> nelze načíst.',\r\n      },\r\n      ajax: {\r\n        tError: '<a href=\"%url%\">Obsah</a> nelze načíst.',\r\n      },\r\n    });\r\n\r\n    $('.gallery').magnificPopup({\r\n      delegate: 'a',\r\n      type: 'image',\r\n      removalDelay: 300,\r\n      mainClass: 'mfp-fade',\r\n      gallery: {\r\n        enabled: true,\r\n        tCounter: '',\r\n      },\r\n    });\r\n  }\r\n});\r\n\r\n// přičítání počtu kusů\r\n$(document).on('click', '.control--plus', function () {\r\n  // zjištění, převod na číslo a ošetření\r\n  var quantity = $(this).parent().find('input').val();\r\n  quantity = parseFloat(quantity);\r\n  if (quantity <= 0 || !$.isNumeric(quantity)) {\r\n    quantity = 0;\r\n  }\r\n  // přičtení\r\n  quantity = quantity + 1;\r\n  // nastavení čísla\r\n  $(this).parent().find('input').val(quantity).change();\r\n});\r\n\r\n// odečítání počtu kusů\r\n$(document).on('click', '.control--minus', function () {\r\n  // zjištění, převod na číslo a ošetření\r\n  var quantity = $(this).parent().find('input').val();\r\n  quantity = parseFloat(quantity);\r\n  if (quantity <= 0 || !$.isNumeric(quantity)) {\r\n    quantity = 1;\r\n  }\r\n  // odečtení\r\n  quantity = quantity - 1;\r\n  // nastavení čísla\r\n  $(this).parent().find('input').val(quantity).change();\r\n});\r\n\r\n//# sourceMappingURL=scripts.js.map\r\n"], "names": ["factory", "define", "amd", "exports", "require", "window", "j<PERSON><PERSON><PERSON>", "Zepto", "$", "MagnificPopup", "_mfpOn", "name", "f", "mfp", "ev", "on", "NS", "EVENT_NS", "_getEl", "className", "appendTo", "html", "raw", "el", "document", "createElement", "innerHTML", "append<PERSON><PERSON><PERSON>", "_mfpTrigger", "e", "data", "<PERSON><PERSON><PERSON><PERSON>", "st", "callbacks", "char<PERSON>t", "toLowerCase", "slice", "apply", "isArray", "_getCloseBtn", "type", "_currPopupType", "currTemplate", "closeBtn", "closeMarkup", "replace", "tClose", "_checkInstance", "magnificPopup", "instance", "init", "_putInlineElementsBack", "_lastInlineElement", "_inlinePlaceholder", "after", "addClass", "_hiddenClass", "detach", "_removeAjaxCursor", "_ajaxCur", "body", "removeClass", "_destroyAjaxRequest", "req", "abort", "_prevStatus", "_document", "_prevContentType", "_wrapClasses", "CLOSE_EVENT", "BEFORE_CLOSE_EVENT", "MARKUP_PARSE_EVENT", "OPEN_EVENT", "CHANGE_EVENT", "READY_CLASS", "REMOVING_CLASS", "PREVENT_CLOSE_CLASS", "_isJQ", "_window", "INLINE_NS", "proto", "prototype", "constructor", "appVersion", "navigator", "isLowIE", "isIE8", "all", "addEventListener", "isAndroid", "test", "isIOS", "supportsTransition", "s", "style", "v", "undefined", "length", "pop", "probablyM<PERSON>ile", "userAgent", "popupsCache", "open", "isObj", "items", "toArray", "index", "item", "i", "parsed", "isOpen", "types", "mainEl", "eq", "key", "extend", "defaults", "fixedContentPos", "modal", "closeOnContentClick", "closeOnBgClick", "showCloseBtn", "enableEscapeKey", "bgOverlay", "close", "wrap", "attr", "_checkIfClose", "target", "container", "contentContainer", "preloader", "tLoading", "modules", "n", "toUpperCase", "call", "closeBtnInside", "template", "values", "close_replaceWith", "append", "alignTop", "css", "overflow", "overflowY", "overflowX", "top", "scrollTop", "position", "fixedBgPos", "height", "keyCode", "updateSize", "windowHeight", "wH", "windowStyles", "classesToadd", "_hasScrollBar", "_getScrollbarSize", "marginRight", "isIE7", "mainClass", "_addClassToMFP", "updateItemHTML", "add", "prependTo", "_lastFocusedEl", "activeElement", "setTimeout", "content", "_setFocus", "_onFocusIn", "removal<PERSON>elay", "_close", "classesToRemove", "empty", "_removeClassFromMFP", "off", "removeAttr", "currItem", "autoFocusLast", "focus", "prevHeight", "winHeight", "zoomLevel", "documentElement", "clientWidth", "innerWidth", "innerHeight", "parseEl", "newContent", "markup", "appendContent", "preloaded", "prepend", "find", "tagName", "src", "hasClass", "addGroup", "options", "e<PERSON><PERSON><PERSON>", "mfpEl", "this", "_openClick", "eName", "delegate", "midClick", "which", "ctrl<PERSON>ey", "metaKey", "altKey", "shift<PERSON>ey", "disableOn", "isFunction", "width", "preventDefault", "stopPropagation", "updateStatus", "status", "text", "stopImmediatePropagation", "closeOnContent", "closeOnBg", "contains", "cName", "scrollHeight", "_parseMarkup", "arr", "each", "value", "split", "replaceWith", "is", "scrollDiv", "scrollbarSize", "cssText", "offsetWidth", "<PERSON><PERSON><PERSON><PERSON>", "registerModule", "module", "push", "fn", "itemOpts", "jqEl", "parseInt", "arguments", "Array", "AJAX_NS", "hiddenClass", "tNotFound", "initInline", "getInline", "inlineSt", "parent", "inline", "parentNode", "inlineElement", "settings", "cursor", "tError", "initAjax", "ajax", "getAjax", "opts", "url", "success", "textStatus", "jqXHR", "temp", "xhr", "finished", "error", "loadError", "_imgInterval", "titleSrc", "verticalFit", "initImage", "imgSt", "image", "ns", "resizeImage", "decr", "img", "_onImageHasSize", "hasSize", "clearInterval", "isCheckingImgSize", "imgHidden", "findImageSize", "mfpSetInterval", "delay", "setInterval", "naturalWidth", "counter", "getImage", "onLoadComplete", "complete", "loaded", "guard", "onLoadError", "alt", "clone", "title", "img_replaceWith", "loading", "_fixIframeBugs", "isShowing", "IFRAME_NS", "_getLoopedId", "numSlides", "_replaceCurrTotal", "curr", "total", "enabled", "easing", "duration", "opener", "element", "initZoom", "getElToAnimate", "show<PERSON><PERSON><PERSON><PERSON><PERSON>", "openTimeout", "animatedImg", "zoomSt", "zoom", "newImg", "transition", "cssObj", "zIndex", "left", "-webkit-backface-visibility", "t", "_allowZoom", "clearTimeout", "_getItemToZoom", "_getOffset", "remove", "is<PERSON>arge", "offset", "paddingTop", "paddingBottom", "obj", "offsetHeight", "hasMozTransform", "MozTransform", "RETINA_NS", "srcAction", "patterns", "youtube", "id", "vimeo", "gmaps", "initIframe", "prevType", "newType", "getIframe", "embedSrc", "iframeSt", "iframe", "dataObj", "indexOf", "substr", "lastIndexOf", "arrowMarkup", "preload", "navigateByImgClick", "arrows", "tPrev", "tNext", "tCounter", "initGallery", "gSt", "gallery", "direction", "next", "prev", "l", "arrowLeft", "arrowRight", "click", "_preloadTimeout", "preloadNearbyImages", "goTo", "newIndex", "p", "preloadBefore", "Math", "min", "preloadAfter", "_preloadItem", "replaceSrc", "m", "ratio", "initRetina", "devicePixelRatio", "retina", "isNaN", "max-width", "unveil", "threshold", "callback", "$w", "th", "attrib", "images", "inview", "filter", "wt", "wb", "et", "$e", "eb", "trigger", "not", "one", "source", "getAttribute", "setAttribute", "global", "JSON", "Nette", "noInit", "initOnLoad", "<PERSON><PERSON><PERSON><PERSON>", "formErrors", "version", "addEvent", "attachEvent", "readyState", "getValue", "elem", "elements", "form", "checked", "files", "selectedIndex", "selected", "match", "getEffectiveValue", "val", "validateControl", "rules", "<PERSON><PERSON><PERSON><PERSON>", "emptyOptional", "parseJSON", "len", "rule", "op", "curE<PERSON>", "control", "namedItem", "neg", "condition", "validateRule", "curValue", "arg", "isDisabled", "message", "msg", "foo", "addError", "validity", "valid", "validateForm", "sender", "scope", "scopeArr", "showFormErrors", "RegExp", "join", "radios", "input", "select", "textarea", "button", "disabled", "errors", "focusElem", "messages", "inArray", "alert", "expandRuleArgument", "preventFiltering", "validators", "filled", "badInput", "FileList", "blank", "equal", "toString", "loop", "i1", "len1", "i2", "len2", "notEqual", "<PERSON><PERSON><PERSON><PERSON>", "tooShort", "max<PERSON><PERSON><PERSON>", "tooLong", "email", "regexp", "parts", "pattern", "integer", "float", "rangeUnderflow", "parseFloat", "max", "rangeOverflow", "range", "submitted", "fileSize", "size", "static", "toggleForm", "toggles", "toggleControl", "toggle", "firsttime", "has", "handled", "handler", "curSuccess", "oldIE", "els", "checkbox", "radio", "id2", "Object", "hasOwnProperty", "eval", "parse", "visible", "srcElement", "getElementById", "display", "initForm", "noValidate", "event", "cancelBubble", "returnValue", "forms", "j", "submit", "webalize", "res", "webalizeTable", "á", "ä", "č", "ď", "é", "ě", "í", "ľ", "ň", "ó", "ô", "ř", "š", "ť", "ú", "ů", "ý", "ž", "ready", "selectedPayment", "bodyWidth", "mobileMenu", "hide", "show", "resize", "modalName", "modalBody", "outerHeight", "closest", "antispamNumber", "reveal<PERSON>ame", "print", "has_checkbox", "prop", "delIdSelected", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "payIdSelected", "first", "slick", "dots", "speed", "slidesToShow", "autoplay", "quantity", "isNumeric", "change"], "mappings": "AAGC,CAAC,SAAUA,GACU,YAAlB,OAAOC,QAAyBA,OAAOC,IAE1CD,OAAO,CAAC,UAAWD,CAAO,EACI,UAAnB,OAAOG,QAElBH,EAAQI,QAAQ,QAAQ,CAAC,EAGzBJ,EAAQK,OAAOC,QAAUD,OAAOE,KAAK,CAErC,EAAE,SAASC,GAgCK,SAAhBC,KAaY,SAATC,EAAkBC,EAAMC,GAC1BC,EAAIC,GAAGC,GAAGC,EAAKL,EAAOM,EAAUL,CAAC,CAClC,CACS,SAATM,EAAkBC,EAAWC,EAAUC,EAAMC,GAC5C,IAAIC,EAAKC,SAASC,cAAc,KAAK,EAarC,OAZAF,EAAGJ,UAAY,OAAOA,EACnBE,IACFE,EAAGG,UAAYL,GAEZC,EAKMF,GACTA,EAASO,YAAYJ,CAAE,GALvBA,EAAKf,EAAEe,CAAE,EACNH,GACFG,EAAGH,SAASA,CAAQ,GAKfG,CACR,CACc,SAAdK,EAAuBC,EAAGC,GACzBjB,EAAIC,GAAGiB,eAAef,EAAKa,EAAGC,CAAI,EAE/BjB,EAAImB,GAAGC,YAETJ,EAAIA,EAAEK,OAAO,CAAC,EAAEC,YAAY,EAAIN,EAAEO,MAAM,CAAC,EACtCvB,EAAImB,GAAGC,UAAUJ,KACnBhB,EAAImB,GAAGC,UAAUJ,GAAGQ,MAAMxB,EAAKL,EAAE8B,QAAQR,CAAI,EAAIA,EAAO,CAACA,EAAK,CAGjE,CACe,SAAfS,EAAwBC,GAKvB,OAJGA,IAASC,GAAmB5B,EAAI6B,aAAaC,WAC/C9B,EAAI6B,aAAaC,SAAWnC,EAAGK,EAAImB,GAAGY,YAAYC,QAAQ,UAAWhC,EAAImB,GAAGc,MAAO,CAAE,EACrFL,EAAiBD,GAEX3B,EAAI6B,aAAaC,QACzB,CAEiB,SAAjBI,IACKvC,EAAEwC,cAAcC,YAEnBpC,EAAM,IAAIJ,GACNyC,KAAK,EACT1C,EAAEwC,cAAcC,SAAWpC,EAE7B,CA61ByB,SAAzBsC,IACIC,IACFC,EAAmBC,MAAOF,EAAmBG,SAASC,CAAY,CAAE,EAAEC,OAAO,EAC7EL,EAAqB,KAEvB,CA8DoB,SAApBM,IACIC,GACFnD,EAAEgB,SAASoC,IAAI,EAAEC,YAAYF,CAAQ,CAEvC,CACsB,SAAtBG,IACCJ,EAAkB,EACf7C,EAAIkD,KACNlD,EAAIkD,IAAIC,MAAM,CAEhB,CAv/BD,IAkBInD,EAGHoD,EAEAC,EACAC,EACAC,EACA3B,EA64BAe,EACAH,EACAD,EAmEAO,EA5+BGU,EAAc,QACjBC,EAAqB,cAGrBC,EAAqB,cACrBC,EAAa,OACbC,EAAe,SACfzD,EAAK,MACLC,EAAW,IAAMD,EACjB0D,EAAc,YACdC,EAAiB,eACjBC,EAAsB,oBAStBC,EAAQ,CAAC,CAAExE,OAAa,OAExByE,EAAUtE,EAAEH,MAAM,EAg5Bf0E,GA1IJvE,EAAEwC,cAAgB,CACjBC,SAAU,KACV+B,MAzrBDvE,EAAcwE,UAAY,CAEzBC,YAAazE,EAMbyC,KAAM,WACL,IAAIiC,EAAaC,UAAUD,WAC3BtE,EAAIwE,QAAUxE,EAAIyE,MAAQ9D,SAAS+D,KAAO,CAAC/D,SAASgE,iBACpD3E,EAAI4E,UAAY,YAAcC,KAAKP,CAAU,EAC7CtE,EAAI8E,MAAQ,qBAAuBD,KAAKP,CAAU,EAClDtE,EAAI+E,mBAnCiB,WACrB,IAAIC,EAAIrE,SAASC,cAAc,GAAG,EAAEqE,MACnCC,EAAI,CAAC,KAAK,IAAI,MAAM,UAErB,GAAwBC,KAAAA,IAApBH,EAAc,WACjB,MAAO,CAAA,EAGR,KAAOE,EAAEE,QACR,GAAIF,EAAEG,IAAI,EAAI,eAAgBL,EAC7B,MAAO,CAAA,EAIT,MAAO,CAAA,CACR,EAoB8C,EAI7ChF,EAAIsF,eAAkBtF,EAAI4E,WAAa5E,EAAI8E,OAAS,8EAA8ED,KAAKN,UAAUgB,SAAS,EAC1JlC,EAAY1D,EAAEgB,QAAQ,EAEtBX,EAAIwF,YAAc,EACnB,EAMAC,KAAM,SAASxE,GAId,GAAkB,CAAA,IAAfA,EAAKyE,MAAiB,CAExB1F,EAAI2F,MAAQ1E,EAAK0E,MAAMC,QAAQ,EAE/B5F,EAAI6F,MAAQ,EAGZ,IAFA,IACCC,EADGH,EAAQ1E,EAAK0E,MAEbI,EAAI,EAAGA,EAAIJ,EAAMP,OAAQW,CAAC,GAK7B,IAFCD,GAFDA,EAAOH,EAAMI,IACLC,OACAF,EAAKpF,GAAG,GAEboF,KAAS7E,EAAKP,GAAG,GAAI,CACvBV,EAAI6F,MAAQE,EACZ,KACD,CAEF,MACC/F,EAAI2F,MAAQhG,EAAE8B,QAAQR,EAAK0E,KAAK,EAAI1E,EAAK0E,MAAQ,CAAC1E,EAAK0E,OACvD3F,EAAI6F,MAAQ5E,EAAK4E,OAAS,EAI3B,GAAG7F,CAAAA,EAAIiG,OAAP,CAKAjG,EAAIkG,MAAQ,GACZ3C,EAAe,GACZtC,EAAKkF,QAAUlF,EAAKkF,OAAOf,OAC7BpF,EAAIC,GAAKgB,EAAKkF,OAAOC,GAAG,CAAC,EAEzBpG,EAAIC,GAAKoD,EAGPpC,EAAKoF,KACHrG,EAAIwF,YAAYvE,EAAKoF,OACxBrG,EAAIwF,YAAYvE,EAAKoF,KAAO,IAE7BrG,EAAI6B,aAAe7B,EAAIwF,YAAYvE,EAAKoF,MAExCrG,EAAI6B,aAAe,GAKpB7B,EAAImB,GAAKxB,EAAE2G,OAAO,CAAA,EAAM,GAAI3G,EAAEwC,cAAcoE,SAAUtF,CAAK,EAC3DjB,EAAIwG,gBAA6C,SAA3BxG,EAAImB,GAAGqF,gBAA6B,CAACxG,EAAIsF,eAAiBtF,EAAImB,GAAGqF,gBAEpFxG,EAAImB,GAAGsF,QACTzG,EAAImB,GAAGuF,oBAAsB,CAAA,EAC7B1G,EAAImB,GAAGwF,eAAiB,CAAA,EACxB3G,EAAImB,GAAGyF,aAAe,CAAA,EACtB5G,EAAImB,GAAG0F,gBAAkB,CAAA,GAMtB7G,EAAI8G,YAGP9G,EAAI8G,UAAYzG,EAAO,IAAI,EAAEH,GAAG,QAAQE,EAAU,WACjDJ,EAAI+G,MAAM,CACX,CAAC,EAED/G,EAAIgH,KAAO3G,EAAO,MAAM,EAAE4G,KAAK,WAAY,CAAC,CAAC,EAAE/G,GAAG,QAAQE,EAAU,SAASY,GACzEhB,EAAIkH,cAAclG,EAAEmG,MAAM,GAC5BnH,EAAI+G,MAAM,CAEZ,CAAC,EAED/G,EAAIoH,UAAY/G,EAAO,YAAaL,EAAIgH,IAAI,GAG7ChH,EAAIqH,iBAAmBhH,EAAO,SAAS,EACpCL,EAAImB,GAAGmG,YACTtH,EAAIsH,UAAYjH,EAAO,YAAaL,EAAIoH,UAAWpH,EAAImB,GAAGoG,QAAQ,GAKnE,IAAIC,EAAU7H,EAAEwC,cAAcqF,QAC9B,IAAIzB,EAAI,EAAGA,EAAIyB,EAAQpC,OAAQW,CAAC,GAAI,CACnC,IACA0B,GAAIA,EADID,EAAQzB,IACV1E,OAAO,CAAC,EAAEqG,YAAY,EAAID,EAAElG,MAAM,CAAC,EACzCvB,EAAI,OAAOyH,GAAGE,KAAK3H,CAAG,CACvB,CACAe,EAAY,YAAY,EAGrBf,EAAImB,GAAGyF,eAEL5G,EAAImB,GAAGyG,gBAGV/H,EAAO6D,EAAoB,SAAS1C,EAAG6G,EAAUC,EAAQhC,GACxDgC,EAAOC,kBAAoBrG,EAAaoE,EAAKnE,IAAI,CAClD,CAAC,EACD4B,GAAgB,qBALhBvD,EAAIgH,KAAKgB,OAAQtG,EAAa,CAAE,GAS/B1B,EAAImB,GAAG8G,WACT1E,GAAgB,kBAKdvD,EAAIwG,gBACNxG,EAAIgH,KAAKkB,IAAI,CACZC,SAAUnI,EAAImB,GAAGiH,UACjBC,UAAW,SACXD,UAAWpI,EAAImB,GAAGiH,SACnB,CAAC,EAEDpI,EAAIgH,KAAKkB,IAAI,CACZI,IAAKrE,EAAQsE,UAAU,EACvBC,SAAU,UACX,CAAC,EAEwB,CAAA,IAAtBxI,EAAImB,GAAGsH,aAA+C,SAAtBzI,EAAImB,GAAGsH,YAA0BzI,EAAIwG,kBACxExG,EAAI8G,UAAUoB,IAAI,CACjBQ,OAAQrF,EAAUqF,OAAO,EACzBF,SAAU,UACX,CAAC,EAKCxI,EAAImB,GAAG0F,iBAETxD,EAAUnD,GAAG,QAAUE,EAAU,SAASY,GACxB,KAAdA,EAAE2H,SACJ3I,EAAI+G,MAAM,CAEZ,CAAC,EAGF9C,EAAQ/D,GAAG,SAAWE,EAAU,WAC/BJ,EAAI4I,WAAW,CAChB,CAAC,EAGG5I,EAAImB,GAAGuF,sBACVnD,GAAgB,oBAGdA,GACFvD,EAAIgH,KAAKtE,SAASa,CAAY,EAI/B,IAAIsF,EAAe7I,EAAI8I,GAAK7E,EAAQyE,OAAO,EAGvCK,EAAe,GAsBfC,GApBAhJ,EAAIwG,iBACKxG,EAAIiJ,cAAcJ,CAAY,IACzB7D,EAAIhF,EAAIkJ,kBAAkB,KAE1BH,EAAaI,YAAcnE,GAK1ChF,EAAIwG,kBACFxG,EAAIoJ,MAIPzJ,EAAE,YAAY,EAAEuI,IAAI,WAAY,QAAQ,EAHxCa,EAAaZ,SAAW,UASPnI,EAAImB,GAAGkI,WA0C1B,OAzCGrJ,EAAIoJ,QACNJ,GAAgB,YAEdA,GACFhJ,EAAIsJ,eAAgBN,CAAa,EAIlChJ,EAAIuJ,eAAe,EAEnBxI,EAAY,eAAe,EAG3BpB,EAAE,MAAM,EAAEuI,IAAIa,CAAY,EAG1B/I,EAAI8G,UAAU0C,IAAIxJ,EAAIgH,IAAI,EAAEyC,UAAWzJ,EAAImB,GAAGsI,WAAa9J,EAAEgB,SAASoC,IAAI,CAAE,EAG5E/C,EAAI0J,eAAiB/I,SAASgJ,cAG9BC,WAAW,WAEP5J,EAAI6J,SACN7J,EAAIsJ,eAAezF,CAAW,EAC9B7D,EAAI8J,UAAU,GAGd9J,EAAI8G,UAAUpE,SAASmB,CAAW,EAInCR,EAAUnD,GAAG,UAAYE,EAAUJ,EAAI+J,UAAU,CAElD,EAAG,EAAE,EAEL/J,EAAIiG,OAAS,CAAA,EACbjG,EAAI4I,WAAWC,CAAY,EAC3B9H,EAAY4C,CAAU,EAEf1C,CAnMP,CAFCjB,EAAIuJ,eAAe,CAsMrB,EAKAxC,MAAO,WACF/G,EAAIiG,SACRlF,EAAY0C,CAAkB,EAE9BzD,EAAIiG,OAAS,CAAA,EAEVjG,EAAImB,GAAG6I,cAAgB,CAAChK,EAAIwE,SAAWxE,EAAI+E,oBAC7C/E,EAAIsJ,eAAexF,CAAc,EACjC8F,WAAW,WACV5J,EAAIiK,OAAO,CACZ,EAAGjK,EAAImB,GAAG6I,YAAY,GAEtBhK,EAAIiK,OAAO,EAEb,EAKAA,OAAQ,WACPlJ,EAAYyC,CAAW,EAEvB,IAAI0G,EAAkBpG,EAAiB,IAAMD,EAAc,IAE3D7D,EAAI8G,UAAUlE,OAAO,EACrB5C,EAAIgH,KAAKpE,OAAO,EAChB5C,EAAIoH,UAAU+C,MAAM,EAEjBnK,EAAImB,GAAGkI,YACTa,GAAmBlK,EAAImB,GAAGkI,UAAY,KAGvCrJ,EAAIoK,oBAAoBF,CAAe,EAEpClK,EAAIwG,kBACFuC,EAAe,CAACI,YAAa,EAAE,EAChCnJ,EAAIoJ,MACNzJ,EAAE,YAAY,EAAEuI,IAAI,WAAY,EAAE,EAElCa,EAAaZ,SAAW,GAEzBxI,EAAE,MAAM,EAAEuI,IAAIa,CAAY,GAG3B1F,EAAUgH,IAAI,oBAAkCjK,CAAQ,EACxDJ,EAAIC,GAAGoK,IAAIjK,CAAQ,EAGnBJ,EAAIgH,KAAKC,KAAK,QAAS,UAAU,EAAEqD,WAAW,OAAO,EACrDtK,EAAI8G,UAAUG,KAAK,QAAS,QAAQ,EACpCjH,EAAIoH,UAAUH,KAAK,QAAS,eAAe,EAGxCjH,CAAAA,EAAImB,GAAGyF,cACR5G,EAAImB,GAAGyG,gBAA0D,CAAA,IAAxC5H,EAAI6B,aAAa7B,EAAIuK,SAAS5I,OACrD3B,EAAI6B,aAAaC,UACnB9B,EAAI6B,aAAaC,SAASc,OAAO,EAIhC5C,EAAImB,GAAGqJ,eAAiBxK,EAAI0J,gBAC9B/J,EAAEK,EAAI0J,cAAc,EAAEe,MAAM,EAE7BzK,EAAIuK,SAAW,KACfvK,EAAI6J,QAAU,KACd7J,EAAI6B,aAAe,KACnB7B,EAAI0K,WAAa,EAEjB3J,EAzamB,YAyaU,CAC9B,EAEA6H,WAAY,SAAS+B,GAEpB,IAGKjC,EAHF1I,EAAI8E,OAEF8F,EAAYjK,SAASkK,gBAAgBC,YAActL,OAAOuL,WAC1DrC,EAASlJ,OAAOwL,YAAcJ,EAClC5K,EAAIgH,KAAKkB,IAAI,SAAUQ,CAAM,EAC7B1I,EAAI8I,GAAKJ,GAET1I,EAAI8I,GAAK6B,GAAa1G,EAAQyE,OAAO,EAGlC1I,EAAIwG,iBACPxG,EAAIgH,KAAKkB,IAAI,SAAUlI,EAAI8I,EAAE,EAG9B/H,EAAY,QAAQ,CAErB,EAKAwI,eAAgB,WACf,IAAIzD,EAAO9F,EAAI2F,MAAM3F,EAAI6F,OAYrBlE,GATJ3B,EAAIqH,iBAAiBzE,OAAO,EAEzB5C,EAAI6J,SACN7J,EAAI6J,QAAQjH,OAAO,GAGnBkD,EADGA,EAAKE,OAIEF,EAHH9F,EAAIiL,QAASjL,EAAI6F,KAAM,GAGflE,MA0BZuJ,GAxBJnK,EAAY,eAAgB,CAACf,EAAIuK,SAAWvK,EAAIuK,SAAS5I,KAAO,GAAIA,EAAK,EAIzE3B,EAAIuK,SAAWzE,EAEX9F,EAAI6B,aAAaF,KAChBwJ,EAASnL,CAAAA,CAAAA,EAAImB,GAAGQ,IAAQ3B,EAAImB,GAAGQ,GAAMwJ,OAGzCpK,EAAY,mBAAoBoK,CAAM,EAGrCnL,EAAI6B,aAAaF,GADfwJ,CAAAA,GACuBxL,EAAEwL,CAAM,GAOhC7H,GAAoBA,IAAqBwC,EAAKnE,MAChD3B,EAAIoH,UAAUpE,YAAY,OAAOM,EAAiB,SAAS,EAG3CtD,EAAI,MAAQ2B,EAAKN,OAAO,CAAC,EAAEqG,YAAY,EAAI/F,EAAKJ,MAAM,CAAC,GAAGuE,EAAM9F,EAAI6B,aAAaF,EAAK,GACvG3B,EAAIoL,cAAcF,EAAYvJ,CAAI,EAElCmE,EAAKuF,UAAY,CAAA,EAEjBtK,EAAY6C,EAAckC,CAAI,EAC9BxC,EAAmBwC,EAAKnE,KAGxB3B,EAAIoH,UAAUkE,QAAQtL,EAAIqH,gBAAgB,EAE1CtG,EAAY,aAAa,CAC1B,EAMAqK,cAAe,SAASF,EAAYvJ,IACnC3B,EAAI6J,QAAUqB,GAGVlL,EAAImB,GAAGyF,cAAgB5G,EAAImB,GAAGyG,gBACL,CAAA,IAA3B5H,EAAI6B,aAAaF,GAEb3B,EAAI6J,QAAQ0B,KAAK,YAAY,EAAEnG,QAClCpF,EAAI6J,QAAQ7B,OAAOtG,EAAa,CAAC,EAGlC1B,EAAI6J,QAAUqB,EAGflL,EAAI6J,QAAU,GAGf9I,EA5gBqB,cA4gBU,EAC/Bf,EAAIoH,UAAU1E,SAAS,OAAOf,EAAK,SAAS,EAE5C3B,EAAIqH,iBAAiBW,OAAOhI,EAAI6J,OAAO,CACxC,EAOAoB,QAAS,SAASpF,GACjB,IACClE,EADGmE,EAAO9F,EAAI2F,MAAME,GAUrB,IAAGC,EAPAA,EAAK0F,QACA,CAAE9K,GAAIf,EAAEmG,CAAI,CAAE,GAErBnE,EAAOmE,EAAKnE,KACL,CAAEV,KAAM6E,EAAM2F,IAAK3F,EAAK2F,GAAI,IAG5B/K,GAAI,CAIX,IAHA,IAAIwF,EAAQlG,EAAIkG,MAGRH,EAAI,EAAGA,EAAIG,EAAMd,OAAQW,CAAC,GACjC,GAAID,EAAKpF,GAAGgL,SAAS,OAAOxF,EAAMH,EAAE,EAAI,CACvCpE,EAAOuE,EAAMH,GACb,KACD,CAGDD,EAAK2F,IAAM3F,EAAKpF,GAAGuG,KAAK,cAAc,EAClCnB,EAAK2F,MACR3F,EAAK2F,IAAM3F,EAAKpF,GAAGuG,KAAK,MAAM,EAEhC,CAQA,OANAnB,EAAKnE,KAAOA,GAAQ3B,EAAImB,GAAGQ,MAAQ,SACnCmE,EAAKD,MAAQA,EACbC,EAAKE,OAAS,CAAA,EACdhG,EAAI2F,MAAME,GAASC,EACnB/E,EAAY,eAAgB+E,CAAI,EAEzB9F,EAAI2F,MAAME,EAClB,EAMA8F,SAAU,SAASjL,EAAIkL,GACP,SAAXC,EAAoB7K,GACvBA,EAAE8K,MAAQC,KACV/L,EAAIgM,WAAWhL,EAAGN,EAAIkL,CAAO,CAC9B,CAHA,IASIK,EAAQ,uBAJRL,EAAAA,GACO,IAIHzF,OAASzF,EAEdkL,EAAQjG,OACViG,EAAQlG,MAAQ,CAAA,EAChBhF,EAAG2J,IAAI4B,CAAK,EAAE/L,GAAG+L,EAAOJ,CAAQ,IAEhCD,EAAQlG,MAAQ,CAAA,EACbkG,EAAQM,SACVxL,EAAG2J,IAAI4B,CAAK,EAAE/L,GAAG+L,EAAOL,EAAQM,SAAWL,CAAQ,GAEnDD,EAAQjG,MAAQjF,GACb2J,IAAI4B,CAAK,EAAE/L,GAAG+L,EAAOJ,CAAQ,EAGnC,EACAG,WAAY,SAAShL,EAAGN,EAAIkL,GAC3B,IAAIO,GAAgChH,KAAAA,IAArByG,EAAQO,SAAyBP,EAAmBjM,EAAEwC,cAAcoE,UAA3B4F,SAGxD,GAAIA,GAAY,EAAc,IAAZnL,EAAEoL,OAAepL,EAAEqL,SAAWrL,EAAEsL,SAAWtL,EAAEuL,QAAUvL,EAAEwL,UAA3E,CAIIC,GAAkCtH,KAAAA,IAAtByG,EAAQa,UAA0Bb,EAAoBjM,EAAEwC,cAAcoE,UAA5BkG,UAE1D,GAAGA,EACF,GAAG9M,EAAE+M,WAAWD,CAAS,GACxB,GAAI,CAACA,EAAU9E,KAAK3H,CAAG,EACtB,MAAO,CAAA,CACR,MAEA,GAAIiE,EAAQ0I,MAAM,EAAIF,EACrB,MAAO,CAAA,EAKPzL,EAAEW,OACJX,EAAE4L,eAAe,EAGd5M,EAAIiG,SACNjF,EAAE6L,gBAAgB,EAIpBjB,EAAQlL,GAAKf,EAAEqB,EAAE8K,KAAK,EACnBF,EAAQM,WACVN,EAAQjG,MAAQjF,EAAG6K,KAAKK,EAAQM,QAAQ,GAEzClM,EAAIyF,KAAKmG,CAAO,CA7BhB,CA8BD,EAMAkB,aAAc,SAASC,EAAQC,GAE9B,IASK/L,EATFjB,EAAIsH,YACHlE,IAAgB2J,GAClB/M,EAAIoH,UAAUpE,YAAY,SAASI,CAAW,EAO3CnC,EAAO,CACV8L,OAAQA,EACRC,KALAA,EADGA,GAAmB,YAAXD,EAMLC,EALChN,EAAImB,GAAGoG,QAMf,EAEAxG,EAAY,eAAgBE,CAAI,EAEhC8L,EAAS9L,EAAK8L,OAGd/M,EAAIsH,UAAU9G,KAFdwM,EAAO/L,EAAK+L,IAEW,EAEvBhN,EAAIsH,UAAUiE,KAAK,GAAG,EAAErL,GAAG,QAAS,SAASc,GAC5CA,EAAEiM,yBAAyB,CAC5B,CAAC,EAEDjN,EAAIoH,UAAU1E,SAAS,SAASqK,CAAM,EACtC3J,EAAc2J,EAEhB,EAQA7F,cAAe,SAASC,GAEvB,GAAGxH,CAAAA,EAAEwH,CAAM,EAAEuE,SAAS3H,CAAmB,EAAzC,CAIA,IAAImJ,EAAiBlN,EAAImB,GAAGuF,oBACxByG,EAAYnN,EAAImB,GAAGwF,eAEvB,GAAGuG,GAAkBC,EACpB,MAAO,CAAA,EAIP,GAAG,CAACnN,EAAI6J,SAAWlK,EAAEwH,CAAM,EAAEuE,SAAS,WAAW,GAAM1L,EAAIsH,WAAaH,IAAWnH,EAAIsH,UAAU,GAChG,MAAO,CAAA,EAIR,GAAMH,IAAWnH,EAAI6J,QAAQ,IAAOlK,EAAEyN,SAASpN,EAAI6J,QAAQ,GAAI1C,CAAM,GAO9D,GAAG+F,EACT,MAAO,CAAA,CACR,MARC,GAAGC,GAEExN,EAAEyN,SAASzM,SAAUwG,CAAM,EAC9B,MAAO,CAAA,EAQX,MAAO,CAAA,CA3BP,CA4BD,EACAmC,eAAgB,SAAS+D,GACxBrN,EAAI8G,UAAUpE,SAAS2K,CAAK,EAC5BrN,EAAIgH,KAAKtE,SAAS2K,CAAK,CACxB,EACAjD,oBAAqB,SAASiD,GAC7BtB,KAAKjF,UAAU9D,YAAYqK,CAAK,EAChCrN,EAAIgH,KAAKhE,YAAYqK,CAAK,CAC3B,EACApE,cAAe,SAAS0B,GACvB,OAAW3K,EAAIoJ,MAAQ/F,EAAUqF,OAAO,EAAI/H,SAASoC,KAAKuK,eAAiB3C,GAAa1G,EAAQyE,OAAO,EACxG,EACAoB,UAAW,YACT9J,EAAImB,GAAGsJ,MAAQzK,EAAI6J,QAAQ0B,KAAKvL,EAAImB,GAAGsJ,KAAK,EAAErE,GAAG,CAAC,EAAIpG,EAAIgH,MAAMyD,MAAM,CACxE,EACAV,WAAY,SAAS/I,GACpB,GAAIA,EAAEmG,SAAWnH,EAAIgH,KAAK,IAAM,CAACrH,EAAEyN,SAASpN,EAAIgH,KAAK,GAAIhG,EAAEmG,MAAM,EAEhE,OADAnH,EAAI8J,UAAU,EACP,CAAA,CAET,EACAyD,aAAc,SAAS1F,EAAUC,EAAQhC,GACxC,IAAI0H,EACD1H,EAAK7E,OACP6G,EAASnI,EAAE2G,OAAOR,EAAK7E,KAAM6G,CAAM,GAEpC/G,EAAY2C,EAAoB,CAACmE,EAAUC,EAAQhC,EAAM,EAEzDnG,EAAE8N,KAAK3F,EAAQ,SAASzB,EAAKqH,GAC5B,GAAavI,KAAAA,IAAVuI,GAAiC,CAAA,IAAVA,EACzB,MAAO,CAAA,EAGR,IACKhN,EAGCuG,EAJU,GADhBuG,EAAMnH,EAAIsH,MAAM,GAAG,GACZvI,OAGS,GAFX1E,EAAKmH,EAAS0D,KAAKnL,EAAW,IAAIoN,EAAI,EAAE,GAEtCpI,SAEO,iBADR6B,EAAOuG,EAAI,IAEX9M,EAAG,KAAOgN,EAAM,IAClBhN,EAAGkN,YAAYF,CAAK,EAEH,QAATzG,EACNvG,EAAGmN,GAAG,KAAK,EACbnN,EAAGuG,KAAK,MAAOyG,CAAK,EAEpBhN,EAAGkN,YAAajO,EAAE,OAAO,EAAEsH,KAAK,MAAOyG,CAAK,EAAEzG,KAAK,QAASvG,EAAGuG,KAAK,OAAO,CAAC,CAAE,EAG/EvG,EAAGuG,KAAKuG,EAAI,GAAIE,CAAK,GAKvB7F,EAAS0D,KAAKnL,EAAW,IAAIiG,CAAG,EAAE7F,KAAKkN,CAAK,CAE9C,CAAC,CACF,EAEAxE,kBAAmB,WAElB,IACK4E,EAML,OAPyB3I,KAAAA,IAAtBnF,EAAI+N,iBACFD,EAAYnN,SAASC,cAAc,KAAK,GAClCqE,MAAM+I,QAAU,iFAC1BrN,SAASoC,KAAKjC,YAAYgN,CAAS,EACnC9N,EAAI+N,cAAgBD,EAAUG,YAAcH,EAAUhD,YACtDnK,SAASoC,KAAKmL,YAAYJ,CAAS,GAE7B9N,EAAI+N,aACZ,CAED,EAWCvG,QAAS,GAET/B,KAAM,SAASmG,EAAS/F,GAWvB,OAVA3D,EAAe,GAKd0J,EAHGA,EAGOjM,EAAE2G,OAAO,CAAA,EAAM,GAAIsF,CAAO,EAF1B,IAKHlG,MAAQ,CAAA,EAChBkG,EAAQ/F,MAAQA,GAAS,EAClBkG,KAAK3J,SAASqD,KAAKmG,CAAO,CAClC,EAEA7E,MAAO,WACN,OAAOpH,EAAEwC,cAAcC,UAAYzC,EAAEwC,cAAcC,SAAS2E,MAAM,CACnE,EAEAoH,eAAgB,SAASrO,EAAMsO,GAC3BA,EAAOxC,UACTjM,EAAEwC,cAAcoE,SAASzG,GAAQsO,EAAOxC,SAEzCjM,EAAE2G,OAAOyF,KAAK5H,MAAOiK,EAAOjK,KAAK,EACjC4H,KAAKvE,QAAQ6G,KAAKvO,CAAI,CACvB,EAEAyG,SAAU,CAKTkG,UAAW,EAEXpG,IAAK,KAEL8F,SAAU,CAAA,EAEV9C,UAAW,GAEX/B,UAAW,CAAA,EAEXmD,MAAO,GAEP/D,oBAAqB,CAAA,EAErBC,eAAgB,CAAA,EAEhBiB,eAAgB,CAAA,EAEhBhB,aAAc,CAAA,EAEdC,gBAAiB,CAAA,EAEjBJ,MAAO,CAAA,EAEPwB,SAAU,CAAA,EAEV+B,aAAc,EAEdP,UAAW,KAEXjD,gBAAiB,OAEjBiC,WAAY,OAEZL,UAAW,OAEXrG,YAAa,0EAEbE,OAAQ,cAERsF,SAAU,aAEViD,cAAe,CAAA,CAEhB,CACD,EAIA7K,EAAE2O,GAAGnM,cAAgB,SAASyJ,GAC7B1J,EAAe,EAEf,IAOGqM,EACA1I,EAGAF,EAXC6I,EAAO7O,EAAEoM,IAAI,EA2CjB,MAxCuB,UAAnB,OAAOH,EAEK,SAAZA,GAED2C,EAAWvK,EAAQwK,EAAKvN,KAAK,eAAe,EAAIuN,EAAK,GAAGrM,cACxD0D,EAAQ4I,SAASC,UAAU,GAAI,EAAE,GAAK,EAGtC/I,EADE4I,EAAS5I,MACH4I,EAAS5I,MAAME,IAEvBF,EAAQ6I,GAEP7I,EADE4I,EAASrC,SACHvG,EAAM4F,KAAKgD,EAASrC,QAAQ,EAE7BvG,GAAMS,GAAIP,CAAM,GAEzB7F,EAAIgM,WAAW,CAACF,MAAMnG,CAAK,EAAG6I,EAAMD,CAAQ,GAEzCvO,EAAIiG,QACNjG,EAAI4L,GAASpK,MAAMxB,EAAK2O,MAAMvK,UAAU7C,MAAMoG,KAAK+G,UAAW,CAAC,CAAC,GAKlE9C,EAAUjM,EAAE2G,OAAO,CAAA,EAAM,GAAIsF,CAAO,EAOjC5H,EACFwK,EAAKvN,KAAK,gBAAiB2K,CAAO,EAElC4C,EAAK,GAAGrM,cAAgByJ,EAGzB5L,EAAI2L,SAAS6C,EAAM5C,CAAO,GAGpB4C,CACR,EAMgB,UAqEZI,GA1DJjP,EAAEwC,cAAcgM,eAAejK,EAAW,CACzC0H,QAAS,CACRiD,YAAa,OACb1D,OAAQ,GACR2D,UAAW,mBACZ,EACA3K,MAAO,CAEN4K,WAAY,WACX/O,EAAIkG,MAAMmI,KAAKnK,CAAS,EAExBrE,EAAO2D,EAAY,IAAIU,EAAW,WACjC5B,EAAuB,CACxB,CAAC,CACF,EAEA0M,UAAW,SAASlJ,EAAM+B,GAIzB,IACKoH,EACHvO,EAKIwO,EAPN,OAFA5M,EAAuB,EAEpBwD,EAAK2F,KACHwD,EAAWjP,EAAImB,GAAGgO,QACrBzO,EAAKf,EAAEmG,EAAK2F,GAAG,GAEVrG,SAGD8J,EAASxO,EAAG,GAAG0O,aACNF,EAAO1D,UACfhJ,IACHG,EAAesM,EAASJ,YACxBrM,EAAqBnC,EAAOsC,CAAY,EACxCA,EAAe,OAAOA,GAGvBJ,EAAqB7B,EAAG+B,MAAMD,CAAkB,EAAEI,OAAO,EAAEI,YAAYL,CAAY,GAGpF3C,EAAI8M,aAAa,OAAO,IAExB9M,EAAI8M,aAAa,QAASmC,EAASH,SAAS,EAC5CpO,EAAKf,EAAE,OAAO,GAGfmG,EAAKuJ,cAAgB3O,IAItBV,EAAI8M,aAAa,OAAO,EACxB9M,EAAIuN,aAAa1F,EAAU,GAAI/B,CAAI,EAC5B+B,EACR,CACD,CACD,CAAC,EAKa,QAcdlI,EAAEwC,cAAcgM,eAAeS,EAAS,CAEvChD,QAAS,CACR0D,SAAU,KACVC,OAAQ,eACRC,OAAQ,sDACT,EAEArL,MAAO,CACNsL,SAAU,WACTzP,EAAIkG,MAAMmI,KAAKO,CAAO,EACtB9L,EAAW9C,EAAImB,GAAGuO,KAAKH,OAEvB1P,EAAO2D,EAAY,IAAIoL,EAAS3L,CAAmB,EACnDpD,EAAO,gBAAkB+O,EAAS3L,CAAmB,CACtD,EACA0M,QAAS,SAAS7J,GAEdhD,GACFnD,EAAEgB,SAASoC,IAAI,EAAEL,SAASI,CAAQ,EAGnC9C,EAAI8M,aAAa,SAAS,EAE1B,IAAI8C,EAAOjQ,EAAE2G,OAAO,CACnBuJ,IAAK/J,EAAK2F,IACVqE,QAAS,SAAS7O,EAAM8O,EAAYC,GAC/BC,EAAO,CACVhP,KAAKA,EACLiP,IAAIF,CACL,EAEAjP,EAAY,YAAakP,CAAI,EAE7BjQ,EAAIoL,cAAezL,EAAEsQ,EAAKhP,IAAI,EAAG2N,CAAQ,EAEzC9I,EAAKqK,SAAW,CAAA,EAEhBtN,EAAkB,EAElB7C,EAAI8J,UAAU,EAEdF,WAAW,WACV5J,EAAIgH,KAAKtE,SAASmB,CAAW,CAC9B,EAAG,EAAE,EAEL7D,EAAI8M,aAAa,OAAO,EAExB/L,EAAY,kBAAkB,CAC/B,EACAqP,MAAO,WACNvN,EAAkB,EAClBiD,EAAKqK,SAAWrK,EAAKuK,UAAY,CAAA,EACjCrQ,EAAI8M,aAAa,QAAS9M,EAAImB,GAAGuO,KAAKF,OAAOxN,QAAQ,QAAS8D,EAAK2F,GAAG,CAAC,CACxE,CACD,EAAGzL,EAAImB,GAAGuO,KAAKJ,QAAQ,EAIvB,OAFAtP,EAAIkD,IAAMvD,EAAE+P,KAAKE,CAAI,EAEd,EACR,CACD,CACD,CAAC,EAKD,IAAIU,EAiBJ3Q,EAAEwC,cAAcgM,eAAe,QAAS,CAEvCvC,QAAS,CACRT,OAAQ,iOAYRoE,OAAQ,mBACRgB,SAAU,QACVC,YAAa,CAAA,EACbhB,OAAQ,oDACT,EAEArL,MAAO,CACNsM,UAAW,WACV,IAAIC,EAAQ1Q,EAAImB,GAAGwP,MAClBC,EAAK,SAEN5Q,EAAIkG,MAAMmI,KAAK,OAAO,EAEtBxO,EAAO8D,EAAWiN,EAAI,WACI,UAAtB5Q,EAAIuK,SAAS5I,MAAoB+O,EAAMnB,QACzC5P,EAAEgB,SAASoC,IAAI,EAAEL,SAASgO,EAAMnB,MAAM,CAExC,CAAC,EAED1P,EAAO2D,EAAYoN,EAAI,WACnBF,EAAMnB,QACR5P,EAAEgB,SAASoC,IAAI,EAAEC,YAAY0N,EAAMnB,MAAM,EAE1CtL,EAAQoG,IAAI,SAAWjK,CAAQ,CAChC,CAAC,EAEDP,EAAO,SAAS+Q,EAAI5Q,EAAI6Q,WAAW,EAChC7Q,EAAIwE,SACN3E,EAAO,cAAeG,EAAI6Q,WAAW,CAEvC,EACAA,YAAa,WACZ,IAIKC,EAJDhL,EAAO9F,EAAIuK,SACXzE,GAASA,EAAKiL,KAEf/Q,EAAImB,GAAGwP,MAAMH,cACXM,EAAO,EAER9Q,EAAIwE,UACNsM,EAAOrC,SAAS3I,EAAKiL,IAAI7I,IAAI,aAAa,EAAG,EAAE,EAAIuG,SAAS3I,EAAKiL,IAAI7I,IAAI,gBAAgB,EAAE,EAAE,GAE9FpC,EAAKiL,IAAI7I,IAAI,aAAclI,EAAI8I,GAAGgI,CAAI,EAExC,EACAE,gBAAiB,SAASlL,GACtBA,EAAKiL,MAEPjL,EAAKmL,QAAU,CAAA,EAEZX,GACFY,cAAcZ,CAAY,EAG3BxK,EAAKqL,kBAAoB,CAAA,EAEzBpQ,EAAY,eAAgB+E,CAAI,EAE7BA,EAAKsL,aACJpR,EAAI6J,SACN7J,EAAI6J,QAAQ7G,YAAY,aAAa,EAEtC8C,EAAKsL,UAAY,CAAA,EAIpB,EAKAC,cAAe,SAASvL,GAIL,SAAjBwL,EAA0BC,GAEtBjB,GACFY,cAAcZ,CAAY,EAG3BA,EAAekB,YAAY,WACJ,EAAnBT,EAAIU,aACNzR,EAAIgR,gBAAgBlL,CAAI,GAIZ,IAAV4L,GACFR,cAAcZ,CAAY,EAIZ,IADfoB,EAAAA,EAECJ,EAAe,EAAE,EACI,KAAZI,EACTJ,EAAe,EAAE,EACI,MAAZI,GACTJ,EAAe,GAAG,EAEpB,EAAGC,CAAK,CACT,CA3BD,IAAIG,EAAU,EACbX,EAAMjL,EAAKiL,IAAI,GA4BhBO,EAAe,CAAC,CACjB,EAEAK,SAAU,SAAS7L,EAAM+B,GAKN,SAAjB+J,IACI9L,IACEA,EAAKiL,IAAI,GAAGc,UACf/L,EAAKiL,IAAI1G,IAAI,YAAY,EAEtBvE,IAAS9F,EAAIuK,WACfvK,EAAIgR,gBAAgBlL,CAAI,EAExB9F,EAAI8M,aAAa,OAAO,GAGzBhH,EAAKmL,QAAU,CAAA,EACfnL,EAAKgM,OAAS,CAAA,EAEd/Q,EAAY,mBAAmB,GAK/BgR,EAAAA,EACW,IACVnI,WAAWgI,EAAe,GAAG,EAE7BI,EAAY,EAIhB,CAGc,SAAdA,IACIlM,IACFA,EAAKiL,IAAI1G,IAAI,YAAY,EACtBvE,IAAS9F,EAAIuK,WACfvK,EAAIgR,gBAAgBlL,CAAI,EACxB9F,EAAI8M,aAAa,QAAS4D,EAAMlB,OAAOxN,QAAQ,QAAS8D,EAAK2F,GAAG,CAAE,GAGnE3F,EAAKmL,QAAU,CAAA,EACfnL,EAAKgM,OAAS,CAAA,EACdhM,EAAKuK,UAAY,CAAA,EAEnB,CA7CD,IAmDKU,EAnDDgB,EAAQ,EA8CXrB,EAAQ1Q,EAAImB,GAAGwP,MAGZjQ,EAAKmH,EAAS0D,KAAK,UAAU,EAqDjC,OApDG7K,EAAG0E,UACD2L,EAAMpQ,SAASC,cAAc,KAAK,GAClCN,UAAY,UACbwF,EAAKpF,IAAMoF,EAAKpF,GAAG6K,KAAK,KAAK,EAAEnG,SACjC2L,EAAIkB,IAAMnM,EAAKpF,GAAG6K,KAAK,KAAK,EAAEtE,KAAK,KAAK,GAEzCnB,EAAKiL,IAAMpR,EAAEoR,CAAG,EAAE7Q,GAAG,iBAAkB0R,CAAc,EAAE1R,GAAG,kBAAmB8R,CAAW,EACxFjB,EAAItF,IAAM3F,EAAK2F,IAIZ/K,EAAGmN,GAAG,KAAK,IACb/H,EAAKiL,IAAMjL,EAAKiL,IAAImB,MAAM,GAIL,GADtBnB,EAAMjL,EAAKiL,IAAI,IACRU,aACN3L,EAAKmL,QAAU,CAAA,EACLF,EAAIpE,QACd7G,EAAKmL,QAAU,CAAA,IAIjBjR,EAAIuN,aAAa1F,EAAU,CAC1BsK,MAnNS,SAASrM,GACpB,GAAGA,EAAK7E,MAA4BkE,KAAAA,IAApBW,EAAK7E,KAAKkR,MACzB,OAAOrM,EAAK7E,KAAKkR,MAElB,IAAI1G,EAAMzL,EAAImB,GAAGwP,MAAMJ,SAEvB,GAAG9E,EAAK,CACP,GAAG9L,EAAE+M,WAAWjB,CAAG,EAClB,OAAOA,EAAI9D,KAAK3H,EAAK8F,CAAI,EACnB,GAAGA,EAAKpF,GACd,OAAOoF,EAAKpF,GAAGuG,KAAKwE,CAAG,GAAK,EAE9B,CACA,MAAO,EACR,EAqMoB3F,CAAI,EACrBsM,gBAAiBtM,EAAKiL,GACvB,EAAGjL,CAAI,EAEP9F,EAAI6Q,YAAY,EAEb/K,EAAKmL,SACJX,GAAcY,cAAcZ,CAAY,EAExCxK,EAAKuK,WACPxI,EAASnF,SAAS,aAAa,EAC/B1C,EAAI8M,aAAa,QAAS4D,EAAMlB,OAAOxN,QAAQ,QAAS8D,EAAK2F,GAAG,CAAE,IAElE5D,EAAS7E,YAAY,aAAa,EAClChD,EAAI8M,aAAa,OAAO,KAK1B9M,EAAI8M,aAAa,SAAS,EAC1BhH,EAAKuM,QAAU,CAAA,EAEXvM,EAAKmL,UACRnL,EAAKsL,UAAY,CAAA,EACjBvJ,EAASnF,SAAS,aAAa,EAC/B1C,EAAIqR,cAAcvL,CAAI,IAGhB+B,CACR,CACD,CACD,CAAC,EAqMiB,SAAjByK,EAA0BC,GACzB,IACK7R,EADFV,EAAI6B,aAAa2Q,KACf9R,EAAKV,EAAI6B,aAAa2Q,GAAWjH,KAAK,QAAQ,GAC5CnG,SAEDmN,IACH7R,EAAG,GAAG+K,IARG,iBAYPzL,EAAIyE,QACN/D,EAAGwH,IAAI,UAAWqK,EAAY,QAAU,MAAM,CAIlD,CA2FkB,SAAfE,EAAwB5M,GAC1B,IAAI6M,EAAY1S,EAAI2F,MAAMP,OAC1B,OAAWsN,EAAY,EAApB7M,EACKA,EAAQ6M,EACL7M,EAAQ,EACX6M,EAAY7M,EAEbA,CACR,CACoB,SAApB8M,EAA6B3F,EAAM4F,EAAMC,GACxC,OAAO7F,EAAKhL,QAAQ,WAAY4Q,EAAO,CAAC,EAAE5Q,QAAQ,YAAa6Q,CAAK,CACrE,CA7SDlT,EAAEwC,cAAcgM,eAAe,OAAQ,CAEtCvC,QAAS,CACRkH,QAAS,CAAA,EACTC,OAAQ,cACRC,SAAU,IACVC,OAAQ,SAASC,GAChB,OAAOA,EAAQrF,GAAG,KAAK,EAAIqF,EAAUA,EAAQ3H,KAAK,KAAK,CACxD,CACD,EAEApH,MAAO,CAENgP,SAAU,WACT,IAECxC,EAMGqC,EACHI,EAiBAC,EAGAC,EACAC,EA9BGC,EAASxT,EAAImB,GAAGsS,KACnB7C,EAAK,QAGF4C,EAAOV,SAAY9S,EAAI+E,qBAIvBiO,EAAWQ,EAAOR,SACrBI,EAAiB,SAASzC,GACzB,IAAI+C,EAAS/C,EAAMuB,MAAM,EAAE5H,WAAW,OAAO,EAAEA,WAAW,OAAO,EAAE5H,SAAS,oBAAoB,EAC/FiR,EAAa,OAAQH,EAAOR,SAAS,IAAM,KAAOQ,EAAOT,OACzDa,EAAS,CACRpL,SAAU,QACVqL,OAAQ,KACRC,KAAM,EACNxL,IAAK,EACLyL,8BAA+B,QAChC,EACAC,EAAI,aAKL,OAHAJ,EAAO,WAAWI,GAAKJ,EAAO,QAAQI,GAAKJ,EAAO,MAAMI,GAAKJ,EAAOI,GAAKL,EAEzED,EAAOxL,IAAI0L,CAAM,EACVF,CACR,EACAL,EAAkB,WACjBrT,EAAI6J,QAAQ3B,IAAI,aAAc,SAAS,CACxC,EAIDrI,EAAO,gBAAgB+Q,EAAI,WACvB5Q,EAAIiU,WAAW,IAEjBC,aAAaZ,CAAW,EACxBtT,EAAI6J,QAAQ3B,IAAI,aAAc,QAAQ,GAItCyI,EAAQ3Q,EAAImU,eAAe,KAO3BZ,EAAcH,EAAezC,CAAK,GAEtBzI,IAAKlI,EAAIoU,WAAW,CAAE,EAElCpU,EAAIgH,KAAKgB,OAAOuL,CAAW,EAE3BD,EAAc1J,WAAW,WACxB2J,EAAYrL,IAAKlI,EAAIoU,WAAY,CAAA,CAAK,CAAE,EACxCd,EAAc1J,WAAW,WAExByJ,EAAgB,EAEhBzJ,WAAW,WACV2J,EAAYc,OAAO,EACnB1D,EAAQ4C,EAAc,KACtBxS,EAAY,oBAAoB,CACjC,EAAG,EAAE,CAEN,EAAGiS,CAAQ,CAEZ,EAAG,EAAE,GAxBJK,EAAgB,EA6BnB,CAAC,EACDxT,EAAO4D,EAAmBmN,EAAI,WAC7B,GAAG5Q,EAAIiU,WAAW,EAAG,CAMpB,GAJAC,aAAaZ,CAAW,EAExBtT,EAAImB,GAAG6I,aAAegJ,EAEnB,CAACrC,EAAO,CAEV,GAAG,EADHA,EAAQ3Q,EAAImU,eAAe,GAE1B,OAEDZ,EAAcH,EAAezC,CAAK,CACnC,CAEA4C,EAAYrL,IAAKlI,EAAIoU,WAAW,CAAA,CAAI,CAAE,EACtCpU,EAAIgH,KAAKgB,OAAOuL,CAAW,EAC3BvT,EAAI6J,QAAQ3B,IAAI,aAAc,QAAQ,EAEtC0B,WAAW,WACV2J,EAAYrL,IAAKlI,EAAIoU,WAAW,CAAE,CACnC,EAAG,EAAE,CACN,CAED,CAAC,EAEDvU,EAAO2D,EAAYoN,EAAI,WACnB5Q,EAAIiU,WAAW,IACjBZ,EAAgB,EACbE,GACFA,EAAYc,OAAO,EAEpB1D,EAAQ,KAEV,CAAC,EACF,EAEAsD,WAAY,WACX,MAA6B,UAAtBjU,EAAIuK,SAAS5I,IACrB,EAEAwS,eAAgB,WACf,MAAGnU,CAAAA,CAAAA,EAAIuK,SAAS0G,SACRjR,EAAIuK,SAASwG,GAItB,EAGAqD,WAAY,SAASE,GACpB,IAEC5T,EADE4T,EACGtU,EAAIuK,SAASwG,IAEb/Q,EAAImB,GAAGsS,KAAKR,OAAOjT,EAAIuK,SAAS7J,IAAMV,EAAIuK,QAAQ,EAGpDgK,EAAS7T,EAAG6T,OAAO,EACnBC,EAAa/F,SAAS/N,EAAGwH,IAAI,aAAa,EAAE,EAAE,EAC9CuM,EAAgBhG,SAAS/N,EAAGwH,IAAI,gBAAgB,EAAE,EAAE,EASpDwM,GARJH,EAAOjM,KAAS3I,EAAEH,MAAM,EAAE+I,UAAU,EAAIiM,EAQ9B,CACT7H,MAAOjM,EAAGiM,MAAM,EAEhBjE,QAAS1E,EAAQtD,EAAGsK,YAAY,EAAItK,EAAG,GAAGiU,cAAgBF,EAAgBD,CAC3E,GASA,OA9KAI,EADsBzP,KAAAA,IAApByP,EACmEzP,KAAAA,IAAnDxE,SAASC,cAAc,GAAG,EAAEqE,MAAM4P,aAE9CD,GAuKLF,EAAI,kBAAoBA,EAAe,UAAI,aAAeH,EAAOT,KAAO,MAAQS,EAAOjM,IAAM,OAE7FoM,EAAIZ,KAAOS,EAAOT,KAClBY,EAAIpM,IAAMiM,EAAOjM,KAEXoM,CACR,CAED,CACD,CAAC,EArLD,IAAIE,EA6LApC,EAAY,SAmRZsC,GA/PJnV,EAAEwC,cAAcgM,eAAeqE,EAAW,CAEzC5G,QAAS,CACRT,OAAQ,6JAKR4J,UAAW,aAGXC,SAAU,CACTC,QAAS,CACRpP,MAAO,cACPqP,GAAI,KACJzJ,IAAK,yCACN,EACA0J,MAAO,CACNtP,MAAO,aACPqP,GAAI,IACJzJ,IAAK,0CACN,EACA2J,MAAO,CACNvP,MAAO,iBACP4F,IAAK,mBACN,CACD,CACD,EAEAtH,MAAO,CACNkR,WAAY,WACXrV,EAAIkG,MAAMmI,KAAKmE,CAAS,EAExB3S,EAAO,eAAgB,SAASmB,EAAGsU,EAAUC,GACzCD,IAAaC,IACZD,IAAa9C,EACfF,EAAe,EACNiD,IAAY/C,GACrBF,EAAe,CAAA,CAAI,EAKtB,CAAC,EAEDzS,EAAO2D,EAAc,IAAMgP,EAAW,WACrCF,EAAe,CAChB,CAAC,CACF,EAEAkD,UAAW,SAAS1P,EAAM+B,GACzB,IAAI4N,EAAW3P,EAAK2F,IAChBiK,EAAW1V,EAAImB,GAAGwU,OAgBlBC,GAdJjW,EAAE8N,KAAKiI,EAASV,SAAU,WACzB,GAAoC,CAAC,EAAlCS,EAASI,QAAS9J,KAAKlG,KAAM,EAS/B,OARGkG,KAAKmJ,KAENO,EADqB,UAAnB,OAAO1J,KAAKmJ,GACHO,EAASK,OAAOL,EAASM,YAAYhK,KAAKmJ,EAAE,EAAEnJ,KAAKmJ,GAAG9P,OAAQqQ,EAASrQ,MAAM,EAE7E2G,KAAKmJ,GAAGvN,KAAMoE,KAAM0J,CAAS,GAG1CA,EAAW1J,KAAKN,IAAIzJ,QAAQ,OAAQyT,CAAS,EACtC,CAAA,CAET,CAAC,EAEa,IAQd,OAPGC,EAASX,YACXa,EAAQF,EAASX,WAAaU,GAE/BzV,EAAIuN,aAAa1F,EAAU+N,EAAS9P,CAAI,EAExC9F,EAAI8M,aAAa,OAAO,EAEjBjF,CACR,CACD,CACD,CAAC,EAuBDlI,EAAEwC,cAAcgM,eAAe,UAAW,CAEzCvC,QAAS,CACRkH,QAAS,CAAA,EACTkD,YAAa,oFACbC,QAAS,CAAC,EAAE,GACZC,mBAAoB,CAAA,EACpBC,OAAQ,CAAA,EAERC,MAAO,4BACPC,MAAO,yBACPC,SAAU,mBACX,EAEAnS,MAAO,CACNoS,YAAa,WAEZ,IAAIC,EAAMxW,EAAImB,GAAGsV,QAChB7F,EAAK,eAIN,GAFA5Q,EAAI0W,UAAY,CAAA,EAEb,CAACF,GAAO,CAACA,EAAI1D,QAAU,MAAO,CAAA,EAEjCvP,GAAgB,eAEhB1D,EAAO8D,EAAWiN,EAAI,WAElB4F,EAAIN,oBACNlW,EAAIgH,KAAK9G,GAAG,QAAQ0Q,EAAI,WAAY,WACnC,GAAsB,EAAnB5Q,EAAI2F,MAAMP,OAEZ,OADApF,EAAI2W,KAAK,EACF,CAAA,CAET,CAAC,EAGFtT,EAAUnD,GAAG,UAAU0Q,EAAI,SAAS5P,GACjB,KAAdA,EAAE2H,QACL3I,EAAI4W,KAAK,EACe,KAAd5V,EAAE2H,SACZ3I,EAAI2W,KAAK,CAEX,CAAC,CACF,CAAC,EAED9W,EAAO,eAAe+Q,EAAI,SAAS5P,EAAGC,GAClCA,EAAK+L,OACP/L,EAAK+L,KAAO2F,EAAkB1R,EAAK+L,KAAMhN,EAAIuK,SAAS1E,MAAO7F,EAAI2F,MAAMP,MAAM,EAE/E,CAAC,EAEDvF,EAAO6D,EAAmBkN,EAAI,SAAS5P,EAAGkS,EAASpL,EAAQhC,GAC1D,IAAI+Q,EAAI7W,EAAI2F,MAAMP,OAClB0C,EAAO4J,QAAc,EAAJmF,EAAQlE,EAAkB6D,EAAIF,SAAUxQ,EAAKD,MAAOgR,CAAC,EAAI,EAC3E,CAAC,EAEDhX,EAAO,gBAAkB+Q,EAAI,WAC5B,IAEEkG,EACAC,EAHoB,EAAnB/W,EAAI2F,MAAMP,QAAcoR,EAAIL,QAAU,CAACnW,EAAI8W,YACzC3L,EAASqL,EAAIR,YAChBc,EAAY9W,EAAI8W,UAAYnX,EAAGwL,EAAOnJ,QAAQ,YAAawU,EAAIJ,KAAK,EAAEpU,QAAQ,UAAW,MAAM,CAAE,EAAEU,SAASqB,CAAmB,EAC/HgT,EAAa/W,EAAI+W,WAAapX,EAAGwL,EAAOnJ,QAAQ,YAAawU,EAAIH,KAAK,EAAErU,QAAQ,UAAW,OAAO,CAAE,EAAEU,SAASqB,CAAmB,EAEnI+S,EAAUE,MAAM,WACfhX,EAAI4W,KAAK,CACV,CAAC,EACDG,EAAWC,MAAM,WAChBhX,EAAI2W,KAAK,CACV,CAAC,EAED3W,EAAIoH,UAAUY,OAAO8O,EAAUtN,IAAIuN,CAAU,CAAC,EAEhD,CAAC,EAEDlX,EAAO+D,EAAagN,EAAI,WACpB5Q,EAAIiX,iBAAiB/C,aAAalU,EAAIiX,eAAe,EAExDjX,EAAIiX,gBAAkBrN,WAAW,WAChC5J,EAAIkX,oBAAoB,EACxBlX,EAAIiX,gBAAkB,IACvB,EAAG,EAAE,CACN,CAAC,EAGDpX,EAAO2D,EAAYoN,EAAI,WACtBvN,EAAUgH,IAAIuG,CAAE,EAChB5Q,EAAIgH,KAAKqD,IAAI,QAAQuG,CAAE,EACvB5Q,EAAI+W,WAAa/W,EAAI8W,UAAY,IAClC,CAAC,CAEF,EACAH,KAAM,WACL3W,EAAI0W,UAAY,CAAA,EAChB1W,EAAI6F,MAAQ4M,EAAazS,EAAI6F,MAAQ,CAAC,EACtC7F,EAAIuJ,eAAe,CACpB,EACAqN,KAAM,WACL5W,EAAI0W,UAAY,CAAA,EAChB1W,EAAI6F,MAAQ4M,EAAazS,EAAI6F,MAAQ,CAAC,EACtC7F,EAAIuJ,eAAe,CACpB,EACA4N,KAAM,SAASC,GACdpX,EAAI0W,UAAaU,GAAYpX,EAAI6F,MACjC7F,EAAI6F,MAAQuR,EACZpX,EAAIuJ,eAAe,CACpB,EACA2N,oBAAqB,WAMpB,IALA,IAAIG,EAAIrX,EAAImB,GAAGsV,QAAQR,QACtBqB,EAAgBC,KAAKC,IAAIH,EAAE,GAAIrX,EAAI2F,MAAMP,MAAM,EAC/CqS,EAAeF,KAAKC,IAAIH,EAAE,GAAIrX,EAAI2F,MAAMP,MAAM,EAG3CW,EAAI,EAAGA,IAAM/F,EAAI0W,UAAYe,EAAeH,GAAgBvR,CAAC,GAChE/F,EAAI0X,aAAa1X,EAAI6F,MAAME,CAAC,EAE7B,IAAIA,EAAI,EAAGA,IAAM/F,EAAI0W,UAAYY,EAAgBG,GAAe1R,CAAC,GAChE/F,EAAI0X,aAAa1X,EAAI6F,MAAME,CAAC,CAE9B,EACA2R,aAAc,SAAS7R,GAGtB,IAIIC,EANJD,EAAQ4M,EAAa5M,CAAK,EAEvB7F,EAAI2F,MAAME,GAAOwF,aAIhBvF,EAAO9F,EAAI2F,MAAME,IACZG,SACRF,EAAO9F,EAAIiL,QAASpF,CAAM,GAG3B9E,EAAY,WAAY+E,CAAI,EAEX,UAAdA,EAAKnE,OACPmE,EAAKiL,IAAMpR,EAAE,yBAAyB,EAAEO,GAAG,iBAAkB,WAC5D4F,EAAKmL,QAAU,CAAA,CAChB,CAAC,EAAE/Q,GAAG,kBAAmB,WACxB4F,EAAKmL,QAAU,CAAA,EACfnL,EAAKuK,UAAY,CAAA,EACjBtP,EAAY,gBAAiB+E,CAAI,CAClC,CAAC,EAAEmB,KAAK,MAAOnB,EAAK2F,GAAG,GAIxB3F,EAAKuF,UAAY,CAAA,EAClB,CACD,CACD,CAAC,EAMe,UAEhB1L,EAAEwC,cAAcgM,eAAe2G,EAAW,CACzClJ,QAAS,CACR+L,WAAY,SAAS7R,GACpB,OAAOA,EAAK2F,IAAIzJ,QAAQ,SAAU,SAAS4V,GAAK,MAAO,MAAQA,CAAG,CAAC,CACpE,EACAC,MAAO,CACR,EACA1T,MAAO,CACN2T,WAAY,WACX,IAEK3W,EAGJ0W,EAL4B,EAA1BrY,OAAOuY,mBAEL5W,EAAKnB,EAAImB,GAAG6W,OACfH,EAAQ1W,EAAG0W,MAID,GAARA,EAFMI,MAAMJ,CAAK,EAAYA,EAAM,EAAdA,MAGvBhY,EAAO,gBAAuBiV,EAAW,SAAS9T,EAAG8E,GACpDA,EAAKiL,IAAI7I,IAAI,CACZgQ,YAAapS,EAAKiL,IAAI,GAAGU,aAAeoG,EACxClL,MAAS,MACV,CAAC,CACF,CAAC,EACD9M,EAAO,gBAAuBiV,EAAW,SAAS9T,EAAG8E,GACpDA,EAAK2F,IAAMtK,EAAGwW,WAAW7R,EAAM+R,CAAK,CACrC,CAAC,EAIJ,CACD,CACD,CAAC,EAGA3V,EAAe,CAAG,CAAE,EAWpB,CAAA,SAAUvC,GAETA,EAAE2O,GAAG6J,OAAS,SAASC,EAAWC,GAEhC,IAKIvG,EALAwG,EAAK3Y,EAAEH,MAAM,EACb+Y,EAAKH,GAAa,EAElBI,EADmC,EAA1BhZ,OAAOuY,iBACC,kBAAoB,WACrCU,EAAS1M,KAYb,SAASoM,IACP,IAAIO,EAASD,EAAOE,OAAO,WACzB,IAGIC,EACAC,EACAC,EALAC,EAAKpZ,EAAEoM,IAAI,EACf,GAAIgN,CAAAA,EAAGlL,GAAG,SAAS,EAOnB,OAJIgL,GADAD,EAAKN,EAAG/P,UAAU,GACR+P,EAAG5P,OAAO,EAEpBsQ,GADAF,EAAKC,EAAGxE,OAAO,EAAEjM,KACPyQ,EAAGrQ,OAAO,EAEXkQ,EAAKL,GAAXS,GAAiBF,GAAMD,EAAKN,CACrC,CAAC,EAEDzG,EAAS4G,EAAOO,QAAQ,QAAQ,EAChCR,EAASA,EAAOS,IAAIpH,CAAM,CAC5B,CAMA,OA9BA/F,KAAKoN,IAAI,SAAU,WACjB,IACAC,GACIA,EAFSrN,KAAKsN,aAAab,CAAM,GAClBzM,KAAKsN,aAAa,UAAU,KAE7CtN,KAAKuN,aAAa,MAAOF,CAAM,EACP,YAApB,OAAOf,IAAyBA,EAAS1Q,KAAKoE,IAAI,CAE1D,CAAC,EAmBDuM,EAAGpY,GAAG,4CAA6CiY,CAAM,EAEzDA,EAAO,EAEApM,IAET,CAED,EAAEvM,OAAOC,QAAUD,OAAOE,KAAK,EAShC,CAAC,SAAS6Z,EAAQpa,GACjB,IAWKkD,EAXAkX,EAAOC,OAIU,YAAlB,OAAOpa,QAAyBA,OAAOC,IAC1CD,OAAO,WACN,OAAOD,EAAQoa,CAAM,CACtB,CAAC,EAC2B,UAAlB,OAAOnL,QAAiD,UAA1B,OAAOA,OAAO9O,QACtD8O,OAAO9O,QAAUH,EAAQoa,CAAM,GAE3BlX,EAAO,CAACkX,EAAOE,OAAS,CAACF,EAAOE,MAAMC,OAC1CH,EAAOE,MAAQta,EAAQoa,CAAM,EACzBlX,GACHkX,EAAOE,MAAME,WAAW,GAI3B,EAAoB,aAAlB,OAAOna,OAAyBA,OAASuM,KAAM,SAASvM,QAEzD,aAEA,IAAIia,MAAQ,GAwBZ,SAASG,WAAWvB,GACnB,OAAO,SAASrX,GACf,OAAOqX,EAAS1Q,KAAKoE,KAAM/K,CAAC,CAC7B,CACD,CA1BAyY,MAAMI,WAAa,GACnBJ,MAAMK,QAAU,MAMhBL,MAAMM,SAAW,SAAS7G,EAAShT,EAAImY,GAClCnF,EAAQvO,iBACXuO,EAAQvO,iBAAiBzE,EAAImY,CAAQ,EACpB,qBAAPnY,EACVgT,EAAQ8G,YAAY,qBAAsB,WACd,aAAvB9G,EAAQ+G,YACX5B,EAAS1Q,KAAKoE,IAAI,CAEpB,CAAC,EAEDmH,EAAQ8G,YAAY,KAAO9Z,EAAI0Z,WAAWvB,CAAQ,CAAC,CAErD,EAaAoB,MAAMS,SAAW,SAASC,GAEzB,GAAKA,EAGE,CAAA,GAAKA,EAAK3O,QAGV,CAAA,GAAkB,UAAd2O,EAAKxY,KAAkB,CAEjC,IADA,IAAIyY,EAAWD,EAAKE,KAAKD,SACpBrU,EAAI,EAAGA,EAAIqU,EAAShV,OAAQW,CAAC,GACjC,GAAIqU,EAASrU,GAAGjG,OAASqa,EAAKra,MAAQsa,EAASrU,GAAGuU,QACjD,OAAOF,EAASrU,GAAG2H,MAGrB,OAAO,IAER,CAAO,GAAkB,SAAdyM,EAAKxY,KACf,OAAOwY,EAAKI,OAASJ,EAAKzM,MAEpB,GAAmC,WAA/ByM,EAAK3O,QAAQlK,YAAY,EAAgB,CACnD,IAAIuE,EAAQsU,EAAKK,cAChB5O,EAAUuO,EAAKvO,QACf9D,EAAS,GAEV,GAAkB,eAAdqS,EAAKxY,KACR,OAAOkE,EAAQ,EAAI,KAAO+F,EAAQ/F,GAAO6H,MAG1C,IAAK3H,EAAI,EAAGA,EAAI6F,EAAQxG,OAAQW,CAAC,GAC5B6F,EAAQ7F,GAAG0U,UACd3S,EAAOuG,KAAKzC,EAAQ7F,GAAG2H,KAAK,EAG9B,OAAO5F,CAER,CAAO,GAAIqS,EAAKra,MAAQqa,EAAKra,KAAK4a,MAAM,OAAO,EAAG,CAC7CN,EAAWD,EAAKE,KAAKD,SAASD,EAAKra,MAAM0L,QAAU,CAAC2O,GAAQA,EAAKE,KAAKD,SAASD,EAAKra,MACvFgI,EAAS,GAEV,IAAK/B,EAAI,EAAGA,EAAIqU,EAAShV,OAAQW,CAAC,GACR,aAArBqU,EAASrU,GAAGpE,MAAuByY,CAAAA,EAASrU,GAAGuU,SAClDxS,EAAOuG,KAAK+L,EAASrU,GAAG2H,KAAK,EAG/B,OAAO5F,CAER,CAAO,MAAkB,aAAdqS,EAAKxY,KACRwY,EAAKG,QAE6B,aAA/BH,EAAK3O,QAAQlK,YAAY,EAC5B6Y,EAAKzM,MAAM1L,QAAQ,KAAM,EAAE,EAG3BmY,EAAKzM,MAAM1L,QAAQ,KAAM,EAAE,EAAEA,QAAQ,aAAc,EAAE,CAC7D,CAjDC,OAAOmY,EAAK,GAAKV,MAAMS,SAASC,EAAK,EAAE,EAAI,IAiD5C,CApDC,OAAO,IAqDT,EAMAV,MAAMkB,kBAAoB,SAASR,GAClC,IAAIS,EAAMnB,MAAMS,SAASC,CAAI,EAM7B,OAHES,EAFET,EAAKd,cACJuB,IAAQT,EAAKd,aAAa,wBAAwB,EAC/C,GAGDuB,CACR,EAMAnB,MAAMoB,gBAAkB,SAASV,EAAMW,EAAOC,EAAWrN,EAAOsN,GAC/Db,EAAOA,EAAK3O,QAAU2O,EAAOA,EAAK,GAClCW,EAAQA,GAASrB,MAAMwB,UAAUd,EAAKd,aAAa,kBAAkB,CAAC,EACtE3L,EAAkBvI,KAAAA,IAAVuI,EAAsB,CAACA,MAAO+L,MAAMkB,kBAAkBR,CAAI,CAAC,EAAIzM,EAEvE,IAAK,IAAIwH,EAAK,EAAGgG,EAAMJ,EAAM1V,OAAQ8P,EAAKgG,EAAKhG,CAAE,GAAI,CACpD,IAAIiG,EAAOL,EAAM5F,GAChBkG,EAAKD,EAAKC,GAAGV,MAAM,aAAa,EAChCW,EAAUF,EAAKG,QAAUnB,EAAKE,KAAKD,SAASmB,UAAUJ,EAAKG,OAAO,EAAInB,EAMvE,GAJAgB,EAAKK,IAAMJ,EAAG,GACdD,EAAKC,GAAKA,EAAG,GACbD,EAAKM,UAAY,CAAC,CAACN,EAAKL,MAEnBO,EAEE,GAAgB,aAAZF,EAAKC,GACfJ,EAAgB,CAACvB,MAAMiC,aAAavB,EAAM,UAAW,KAAMzM,CAAK,OAE1D,GAAIsN,CAAAA,GAAkBG,EAAKM,WAAyB,YAAZN,EAAKC,GAA7C,CAKP,IAkBM5N,EAnBN6N,EAAUA,EAAQ7P,QAAU6P,EAAUA,EAAQ,GAC1CM,EAAWxB,IAASkB,EAAU3N,EAAQ,CAACA,MAAO+L,MAAMkB,kBAAkBU,CAAO,CAAC,EACjFvL,EAAU2J,MAAMiC,aAAaL,EAASF,EAAKC,GAAID,EAAKS,IAAKD,CAAQ,EAElE,GAAgB,OAAZ7L,EAMJ,GAJWqL,EAAKK,MACf1L,EAAU,CAACA,GAGRqL,EAAKM,WAAa3L,GACrB,GAAI,CAAC2J,MAAMoB,gBAAgBV,EAAMgB,EAAKL,MAAOC,EAAWrN,EAAmB,WAAZyN,EAAKC,IAA0BJ,CAAa,EAC1G,MAAO,CAAA,CACR,MACM,GAAI,CAACG,EAAKM,WAAa,CAAC3L,EAC9B,GAAI2J,CAAAA,MAAMoC,WAAWR,CAAO,EAU5B,OAPKN,IACAvN,EAAMiM,MAAMhY,QAAQ0Z,EAAKS,GAAG,EAAIT,EAAKS,IAAM,CAACT,EAAKS,KACpDE,EAAUX,EAAKY,IAAI/Z,QAAQ,gBAAiB,SAASga,EAAKpE,GACzD,OAAO6B,MAAMS,SAAe,UAANtC,EAAgByD,EAAUlB,EAAKE,KAAKD,SAASmB,UAAU/N,EAAIoK,GAAG0D,OAAO,CAAC,CAC7F,CAAC,EACF7B,MAAMwC,SAASZ,EAASS,CAAO,GAEzB,CAAA,CA3BR,CA6BD,CAEA,MAAA,EAAkB,WAAd3B,EAAKxY,MAAsBwY,CAAAA,EAAK+B,SAASC,QACvCpB,GACJtB,MAAMwC,SAAS9B,EAAM,6BAA6B,EAE5C,GAIT,EAMAV,MAAM2C,aAAe,SAASC,EAAQtB,GACrC,IAAIV,EAAOgC,EAAOhC,MAAQgC,EACzBC,EAAQ,CAAA,EAIT,GAFA7C,MAAMI,WAAa,GAEfQ,EAAK,sBAAqF,OAA7DA,EAAK,qBAAqBhB,aAAa,gBAAgB,EAAY,CAC/FkD,EAAW9C,MAAMwB,UAAUZ,EAAK,qBAAqBhB,aAAa,6BAA6B,CAAC,EACpG,GAAIkD,CAAAA,EAASnX,OAIZ,OADAqU,MAAM+C,eAAenC,EAAM,EAAE,EACtB,CAAA,EAHPiC,EAAQ,IAAIG,OAAO,KAAOF,EAASG,KAAK,IAAI,EAAI,IAAI,CAKtD,CAIA,IAFA,IAAoBvC,EAAhBwC,EAAS,GAER5W,EAAI,EAAGA,EAAIsU,EAAKD,SAAShV,OAAQW,CAAC,GAGtC,GAAIoU,EAFJA,EAAOE,EAAKD,SAASrU,IAEZyF,SAAa2O,EAAK3O,QAAQlK,YAAY,GAAK,CAACsb,MAAO,EAAGC,OAAQ,EAAGC,SAAU,EAAGC,OAAQ,CAAC,EAAhG,CAGO,GAAkB,UAAd5C,EAAKxY,KAAkB,CACjC,GAAIgb,EAAOxC,EAAKra,MACf,SAED6c,EAAOxC,EAAKra,MAAQ,CAAA,CACrB,CAEA,GAAI,EAACwc,GAAS,CAACnC,EAAKra,KAAKkC,QAAQ,cAAe,GAAG,EAAE0Y,MAAM4B,CAAK,GAAM7C,MAAMoC,WAAW1B,CAAI,GAItFV,MAAMoB,gBAAgBV,EAAM,KAAMY,CAAS,GAAMtB,MAAMI,WAAWzU,QACtE,MAAO,CAAA,CAPR,CAUG0K,EAAU,CAAC2J,MAAMI,WAAWzU,OAEhC,OADAqU,MAAM+C,eAAenC,EAAMZ,MAAMI,UAAU,EACpC/J,CACR,EAMA2J,MAAMoC,WAAa,SAAS1B,GAC3B,GAAkB,UAAdA,EAAKxY,KAQT,OAAOwY,EAAK6C,SAPX,IAAK,IAAIjX,EAAI,EAAGqU,EAAWD,EAAKE,KAAKD,SAAUrU,EAAIqU,EAAShV,OAAQW,CAAC,GACpE,GAAIqU,EAASrU,GAAGjG,OAASqa,EAAKra,MAAQ,CAACsa,EAASrU,GAAGiX,SAClD,MAAO,CAAA,EAGT,MAAO,CAAA,CAGT,EAMAvD,MAAMwC,SAAW,SAAS9B,EAAM2B,GAC/BrC,MAAMI,WAAWxL,KAAK,CACrB6E,QAASiH,EACT2B,QAASA,CACV,CAAC,CACF,EAMArC,MAAM+C,eAAiB,SAASnC,EAAM4C,GAIrC,IAHA,IACCC,EADGC,EAAW,GAGNpX,EAAI,EAAGA,EAAIkX,EAAO7X,OAAQW,CAAC,GAAI,CACvC,IAAIoU,EAAO8C,EAAOlX,GAAGmN,QACpB4I,EAAUmB,EAAOlX,GAAG+V,QAEhBrC,MAAM2D,QAAQD,EAAUrB,CAAO,IACnCqB,EAAS9O,KAAKyN,CAAO,EAEjB,CAACoB,GAAa/C,EAAK1P,QACtByS,EAAY/C,GAGf,CAEIgD,EAAS/X,SACZiY,MAAMF,EAAST,KAAK,IAAI,CAAC,EAErBQ,IACHA,EAAUzS,MAAM,CAGnB,EAMAgP,MAAM6D,mBAAqB,SAASjD,EAAMuB,GACzC,IAEElO,EAIF,OANIkO,GAAOA,EAAIN,UACVA,EAAUjB,EAAKD,SAASmB,UAAUK,EAAIN,OAAO,EAChD5N,EAAQ,CAACA,MAAO+L,MAAMkB,kBAAkBW,CAAO,CAAC,EACjD7B,MAAMoB,gBAAgBS,EAAS,KAAM,CAAA,EAAM5N,CAAK,EAChDkO,EAAMlO,EAAMA,OAENkO,CACR,EAGA,IAAI2B,iBAAmB,CAAA,EAqavB,OAhaA9D,MAAMiC,aAAe,SAASvB,EAAMiB,EAAIQ,EAAKlO,GAC5CA,EAAkBvI,KAAAA,IAAVuI,EAAsB,CAACA,MAAO+L,MAAMkB,kBAAkBR,CAAI,CAAC,EAAIzM,EAMvE0N,GADAA,GAFCA,EADoB,MAAjBA,EAAG/Z,OAAO,CAAC,EACT+Z,EAAGtF,OAAO,CAAC,EAEZsF,GAAGpZ,QAAQ,KAAM,GAAG,GACjBA,QAAQ,MAAO,EAAE,EAEzB,IAAIwL,EAAMiM,MAAMhY,QAAQma,CAAG,EAAIA,EAAIra,MAAM,CAAC,EAAI,CAACqa,GAC/C,GAAI,CAAC2B,iBAAkB,CACtBA,iBAAmB,CAAA,EACnB,IAAK,IAAIxX,EAAI,EAAGmV,EAAM1N,EAAIpI,OAAQW,EAAImV,EAAKnV,CAAC,GAC3CyH,EAAIzH,GAAK0T,MAAM6D,mBAAmBnD,EAAKE,KAAM7M,EAAIzH,EAAE,EAEpDwX,iBAAmB,CAAA,CACpB,CACA,OAAO9D,MAAM+D,WAAWpC,GACrB3B,MAAM+D,WAAWpC,GAAIjB,EAAMV,MAAMhY,QAAQma,CAAG,EAAIpO,EAAMA,EAAI,GAAIE,EAAMA,MAAOA,CAAK,EAChF,IACJ,EAGA+L,MAAM+D,WAAa,CAClBC,OAAQ,SAAStD,EAAMyB,EAAKhB,GAC3B,MAAIT,EAAc,WAAdA,EAAKxY,MAAqBwY,CAAAA,EAAK+B,SAASwB,WAG7B,KAAR9C,GAAsB,CAAA,IAARA,GAAyB,OAARA,IACjC,CAACnB,MAAMhY,QAAQmZ,CAAG,GAAK,CAAC,CAACA,EAAIxV,UAC7B,CAAC5F,OAAOme,UAAY,EAAE/C,aAAepb,OAAOme,WAAa/C,EAAIxV,OACnE,EAEAwY,MAAO,SAASzD,EAAMyB,EAAKhB,GAC1B,MAAO,CAACnB,MAAM+D,WAAWC,OAAOtD,EAAMyB,EAAKhB,CAAG,CAC/C,EAEAuB,MAAO,SAAShC,GACf,OAAOV,MAAMoB,gBAAgBV,EAAM,KAAM,CAAA,CAAI,CAC9C,EAEA0D,MAAO,SAAS1D,EAAMyB,EAAKhB,GAC1B,GAAYzV,KAAAA,IAARyW,EACH,OAAO,KAGR,SAASkC,EAASlD,GACjB,MAAmB,UAAf,OAAOA,GAAmC,UAAf,OAAOA,EAC9B,GAAKA,EAEG,CAAA,IAARA,EAAe,IAAM,EAE9B,CAEAA,EAAMnB,MAAMhY,QAAQmZ,CAAG,EAAIA,EAAM,CAACA,GAClCgB,EAAMnC,MAAMhY,QAAQma,CAAG,EAAIA,EAAM,CAACA,GAClCmC,EACA,IAAK,IAAIC,EAAK,EAAGC,EAAOrD,EAAIxV,OAAQ4Y,EAAKC,EAAMD,CAAE,GAAI,CACpD,IAAK,IAAIE,EAAK,EAAGC,EAAOvC,EAAIxW,OAAQ8Y,EAAKC,EAAMD,CAAE,GAChD,GAAIJ,EAASlD,EAAIoD,EAAG,IAAMF,EAASlC,EAAIsC,EAAG,EACzC,SAASH,EAGX,MAAO,CAAA,CACR,CACA,MAAO,CAAA,CACR,EAEAK,SAAU,SAASjE,EAAMyB,EAAKhB,GAC7B,OAAezV,KAAAA,IAARyW,EAAoB,KAAO,CAACnC,MAAM+D,WAAWK,MAAM1D,EAAMyB,EAAKhB,CAAG,CACzE,EAEAyD,UAAW,SAASlE,EAAMyB,EAAKhB,GAC9B,GAAkB,WAAdT,EAAKxY,KAAmB,CAC3B,GAAIwY,EAAK+B,SAASoC,SACjB,MAAO,CAAA,EACD,GAAInE,EAAK+B,SAASwB,SACxB,OAAO,IAET,CACA,OAAO9C,EAAIxV,QAAUwW,CACtB,EAEA2C,UAAW,SAASpE,EAAMyB,EAAKhB,GAC9B,GAAkB,WAAdT,EAAKxY,KAAmB,CAC3B,GAAIwY,EAAK+B,SAASsC,QACjB,MAAO,CAAA,EACD,GAAIrE,EAAK+B,SAASwB,SACxB,OAAO,IAET,CACA,OAAO9C,EAAIxV,QAAUwW,CACtB,EAEAxW,OAAQ,SAAS+U,EAAMyB,EAAKhB,GAC3B,GAAkB,WAAdT,EAAKxY,KAAmB,CAC3B,GAAIwY,EAAK+B,SAASoC,UAAYnE,EAAK+B,SAASsC,QAC3C,MAAO,CAAA,EACD,GAAIrE,EAAK+B,SAASwB,SACxB,OAAO,IAET,CAEA,OAAmB,QADnB9B,EAAMnC,MAAMhY,QAAQma,CAAG,EAAIA,EAAM,CAACA,EAAKA,IAC3B,IAAehB,EAAIxV,QAAUwW,EAAI,MAAmB,OAAXA,EAAI,IAAehB,EAAIxV,QAAUwW,EAAI,GAC3F,EAEA6C,MAAO,SAAStE,EAAMyB,EAAKhB,GAC1B,MAAO,gUAAkU/V,KAAK+V,CAAG,CAClV,EAEA/K,IAAK,SAASsK,EAAMyB,EAAKhB,EAAKlN,GAI7B,MAHK,gBAAkB7I,KAAK+V,CAAG,IAC9BA,EAAM,UAAYA,GAEf,CAAA,CAAA,sWAAwW/V,KAAK+V,CAAG,IACnXlN,EAAMA,MAAQkN,EACP,CAAA,EAGT,EAEA8D,OAAQ,SAASvE,EAAMyB,EAAKhB,GACvB+D,EAAuB,UAAf,OAAO/C,GAAmBA,EAAIlB,MAAM,oBAAoB,EACpE,IACC,OAAOiE,GAAS,IAAKlC,OAAOkC,EAAM,GAAIA,EAAM,GAAG3c,QAAQ,IAAK,EAAE,CAAE,EAAE6C,KAAK+V,CAAG,CAC9D,CAAX,MAAO5Z,IACV,EAEA4d,QAAS,SAASzE,EAAMyB,EAAKhB,GAC5B,IACC,MAAsB,UAAf,OAAOgB,EAAmB,IAAKa,OAAO,OAASb,EAAM,IAAK,EAAE/W,KAAK+V,CAAG,EAAI,IACnE,CAAX,MAAO5Z,IACV,EAEA6d,QAAS,SAAS1E,EAAMyB,EAAKhB,GAC5B,OAAkB,WAAdT,EAAKxY,MAAqBwY,CAAAA,EAAK+B,SAASwB,WAGrC,aAAe7Y,KAAK+V,CAAG,CAC/B,EAEAkE,MAAS,SAAS3E,EAAMyB,EAAKhB,EAAKlN,GACjC,MAAA,EAAkB,WAAdyM,EAAKxY,MAAqBwY,EAAK+B,SAASwB,WAG5C9C,EAAMA,EAAI5Y,QAAQ,IAAK,EAAE,EAAEA,QAAQ,IAAK,GAAG,EACvC,CAAA,wBAA0B6C,KAAK+V,CAAG,KACrClN,EAAMA,MAAQkN,EACP,GAGT,EAEApD,IAAK,SAAS2C,EAAMyB,EAAKhB,GACxB,GAAkB,WAAdT,EAAKxY,KAAmB,CAC3B,GAAIwY,EAAK+B,SAAS6C,eACjB,MAAO,CAAA,EACD,GAAI5E,EAAK+B,SAASwB,SACxB,OAAO,IAET,CACA,OAAe,OAAR9B,GAAgBoD,WAAWpE,CAAG,GAAKgB,CAC3C,EAEAqD,IAAK,SAAS9E,EAAMyB,EAAKhB,GACxB,GAAkB,WAAdT,EAAKxY,KAAmB,CAC3B,GAAIwY,EAAK+B,SAASgD,cACjB,MAAO,CAAA,EACD,GAAI/E,EAAK+B,SAASwB,SACxB,OAAO,IAET,CACA,OAAe,OAAR9B,GAAgBoD,WAAWpE,CAAG,GAAKgB,CAC3C,EAEAuD,MAAO,SAAShF,EAAMyB,EAAKhB,GAC1B,GAAkB,WAAdT,EAAKxY,KAAmB,CAC3B,GAAIwY,EAAK+B,SAAS6C,gBAAkB5E,EAAK+B,SAASgD,cACjD,MAAO,CAAA,EACD,GAAI/E,EAAK+B,SAASwB,SACxB,OAAO,IAET,CACA,OAAOjE,MAAMhY,QAAQma,CAAG,GACV,OAAXA,EAAI,IAAeoD,WAAWpE,CAAG,GAAKgB,EAAI,MAAmB,OAAXA,EAAI,IAAeoD,WAAWpE,CAAG,GAAKgB,EAAI,IAAO,IACvG,EAEAwD,UAAW,SAASjF,GACnB,OAAOA,EAAKE,KAAK,uBAAyBF,CAC3C,EAEAkF,SAAU,SAASlF,EAAMyB,EAAKhB,GAC7B,GAAIpb,OAAOme,SACV,IAAK,IAAI5X,EAAI,EAAGA,EAAI6U,EAAIxV,OAAQW,CAAC,GAChC,GAAI6U,EAAI7U,GAAGuZ,KAAO1D,EACjB,MAAO,CAAA,EAIV,MAAO,CAAA,CACR,EAEAjL,MAAO,SAAUwJ,EAAMyB,EAAKhB,GAC3B,GAAIpb,OAAOme,UAAY/C,aAAepb,OAAOme,SAC5C,IAAK,IAAI5X,EAAI,EAAGA,EAAI6U,EAAIxV,OAAQW,CAAC,GAAI,CACpC,IAAIpE,EAAOiZ,EAAI7U,GAAGpE,KAClB,GAAIA,GAAiB,cAATA,GAAiC,cAATA,GAAiC,eAATA,EAC3D,MAAO,CAAA,CAET,CAED,MAAO,CAAA,CACR,EAEA4d,OAAU,SAAUpF,EAAMyB,EAAKhB,GAC9B,OAAOgB,CACR,CACD,EAMAnC,MAAM+F,WAAa,SAASnF,EAAMF,GAGjC,IADAV,MAAMgG,QAAU,GACX1Z,EAAI,EAAGA,EAAIsU,EAAKD,SAAShV,OAAQW,CAAC,GAClCsU,EAAKD,SAASrU,GAAGyF,QAAQlK,YAAY,GAAK,CAACsb,MAAO,EAAGC,OAAQ,EAAGC,SAAU,EAAGC,OAAQ,CAAC,GACzFtD,MAAMiG,cAAcrF,EAAKD,SAASrU,GAAI,KAAM,KAAM,CAACoU,CAAI,EAIzD,IARA,IAAIpU,KAQM0T,MAAMgG,QACfhG,MAAMkG,OAAO5Z,EAAG0T,MAAMgG,QAAQ1Z,GAAIoU,CAAI,CAExC,EAMAV,MAAMiG,cAAgB,SAASvF,EAAMW,EAAOhL,EAAS8P,EAAWlS,GAC/DoN,EAAQA,GAASrB,MAAMwB,UAAUd,EAAKd,aAAa,kBAAkB,CAAC,EACtE3L,EAAkBvI,KAAAA,IAAVuI,EAAsB,CAACA,MAAO+L,MAAMkB,kBAAkBR,CAAI,CAAC,EAAIzM,EASvE,IAPA,IAAImS,EAAM,CAAA,EACTC,EAAU,GACVC,EAAU,WACTtG,MAAM+F,WAAWrF,EAAKE,KAAMF,CAAI,CACjC,EAGQjF,EAAK,EAAGgG,EAAMJ,EAAM1V,OAAQ8P,EAAKgG,EAAKhG,CAAE,GAAI,CACpD,IAAIiG,EAAOL,EAAM5F,GAChBkG,EAAKD,EAAKC,GAAGV,MAAM,aAAa,EAChCW,EAAUF,EAAKG,QAAUnB,EAAKE,KAAKD,SAASmB,UAAUJ,EAAKG,OAAO,EAAInB,EAEvE,GAAKkB,EAAL,CAKA,GAAgB,CAAA,KADhB2E,EAAalQ,GACU,CACtBqL,EAAKK,IAAMJ,EAAG,GACdD,EAAKC,GAAKA,EAAG,GACb,IACA4E,EADIrE,EAAWxB,IAASkB,EAAU3N,EAAQ,CAACA,MAAO+L,MAAMkB,kBAAkBU,CAAO,CAAC,EAElF,GAAmB,QAAf2E,EADSvG,MAAMiC,aAAaL,EAASF,EAAKC,GAAID,EAAKS,IAAKD,CAAQ,GAEnE,SAEUR,EAAKK,MACfwE,EAAa,CAACA,GAEV7E,EAAKL,QACThL,EAAUkQ,EAEZ,CAEA,GAAK7E,EAAKL,OAASrB,MAAMiG,cAAcvF,EAAMgB,EAAKL,MAAOkF,EAAYJ,EAAWlS,CAAK,GAAMyN,EAAKwE,OAAQ,CAEvG,GADAE,EAAM,CAAA,EACFD,EAKH,IAJA,IAAIK,EAAQ,CAACtf,SAASgE,iBACrB7E,GAAOub,EAAQ7P,QAAU6P,EAAeA,EAAQ,IAAfvb,KACjCogB,EAAM7E,EAAQ7P,QAAU6P,EAAQhB,KAAKD,SAAWiB,EAExCtV,EAAI,EAAGA,EAAIma,EAAI9a,OAAQW,CAAC,GAC5Bma,EAAIna,GAAGjG,OAASA,GAAS2Z,MAAM2D,QAAQ0C,EAASI,EAAIna,EAAE,IACzD0T,MAAMM,SAASmG,EAAIna,GAAIka,GAASC,EAAIna,GAAGpE,OAAQ,CAACwe,SAAU,EAAGC,MAAO,CAAC,EAAI,QAAU,SAAUL,CAAO,EACpGD,EAAQzR,KAAK6R,EAAIna,EAAE,GAItB,IAAK,IAAIsa,KAAOlF,EAAKwE,QAAU,GAC1BW,OAAOlc,UAAUmc,eAAe5Y,KAAKwT,EAAKwE,OAAQU,CAAG,IACxD5G,MAAMgG,QAAQY,GAAO5G,MAAMgG,QAAQY,KAASlF,EAAKwE,OAAOU,GAAOL,EAAa,CAACA,GAGhF,CAtCA,CAuCD,CACA,OAAOH,CACR,EAGApG,MAAMwB,UAAY,SAASjW,GAC1B,MAAkC,SAA1BA,GAAK,IAAI8Q,OAAO,EAAG,CAAC,EACzB0K,KAAK,IAAMxb,EAAI,GAAG,EAClBwU,KAAKiH,MAAMzb,GAAK,IAAI,CACxB,EAMAyU,MAAMkG,OAAS,SAASzK,EAAIwL,EAASC,GAChCxG,EAAOxZ,SAASigB,eAAe1L,CAAE,EACjCiF,IACHA,EAAKlV,MAAM4b,QAAUH,EAAU,GAAK,OAEtC,EAMAjH,MAAMqH,SAAW,SAASzG,GACzBZ,MAAM+F,WAAWnF,CAAI,EAEjBA,EAAK0G,aAIT1G,EAAK0G,WAAa,CAAA,EAElBtH,MAAMM,SAASM,EAAM,SAAU,SAASrZ,GAClCyY,MAAM2C,aAAa/B,CAAI,IACvBrZ,GAAKA,EAAE6L,iBACV7L,EAAE6L,gBAAgB,EAClB7L,EAAE4L,eAAe,GACPpN,OAAOwhB,QACjBA,MAAMC,aAAe,CAAA,EACrBD,MAAME,YAAc,CAAA,GAGvB,CAAC,EACF,EAMAzH,MAAME,WAAa,WAClBF,MAAMM,SAASpZ,SAAU,mBAAoB,WAC5C,IAAK,IAAIoF,EAAI,EAAGA,EAAIpF,SAASwgB,MAAM/b,OAAQW,CAAC,GAE3C,IADA,IAAIsU,EAAO1Z,SAASwgB,MAAMpb,GACjBqb,EAAI,EAAGA,EAAI/G,EAAKD,SAAShV,OAAQgc,CAAC,GAC1C,GAAI/G,EAAKD,SAASgH,GAAG/H,aAAa,kBAAkB,EAAG,CACtDI,MAAMqH,SAASzG,CAAI,EACnB,KACD,CAIFZ,MAAMM,SAASpZ,SAASoC,KAAM,QAAS,SAAS/B,GAE/C,IADA,IAAImG,EAASnG,EAAEmG,QAAUnG,EAAE2f,WACpBxZ,GAAQ,CACd,GAAIA,EAAOkT,MAAQlT,EAAOxF,OAAQ,CAAC0f,OAAQ,EAAG1Q,MAAO,CAAC,EAAG,CACxDxJ,EAAOkT,KAAK,qBAAuBlT,EACnC,KACD,CACAA,EAASA,EAAOiI,UACjB,CACD,CAAC,CACF,CAAC,CACF,EAMAqK,MAAMhY,QAAU,SAASma,GACxB,MAA+C,mBAAxC0E,OAAOlc,UAAU0Z,SAASnW,KAAKiU,CAAG,CAC1C,EAMAnC,MAAM2D,QAAU,SAAS5P,EAAKoN,GAC7B,GAAI,GAAG/E,QACN,MAA0B,CAAC,EAApBrI,EAAIqI,QAAQ+E,CAAG,EAEtB,IAAK,IAAI7U,EAAI,EAAGA,EAAIyH,EAAIpI,OAAQW,CAAC,GAChC,GAAIyH,EAAIzH,KAAO6U,EACd,MAAO,CAAA,EAGT,MAAO,CAAA,CAET,EAMAnB,MAAM6H,SAAW,SAAStc,GACzBA,EAAIA,EAAE1D,YAAY,EAElB,IADA,IAAIigB,EAAM,GACLxb,EAAI,EAAGA,EAAIf,EAAEI,OAAQW,CAAC,GAE1Bwb,GADK9H,MAAM+H,cAAcxc,EAAE3D,OAAO0E,CAAC,IAClBf,EAAE3D,OAAO0E,CAAC,EAE5B,OAAOwb,EAAIvf,QAAQ,cAAe,GAAG,EAAEA,QAAQ,SAAU,EAAE,CAC5D,EAEAyX,MAAM+H,cAAgB,CAACC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,IAAKC,IAAQ,GAAG,EAExPjJ,KACR,CAAE,EAGF9Z,EAAEgB,QAAQ,EAAEgiB,MAAM,WACZhjB,EAAE,UAAU,EAAEyF,SAEhBzF,EAAE2G,OAAO,CAAA,EAAM3G,EAAEwC,cAAcoE,SAAU,CACvCtE,OAAQ,SACRsF,SAAU,cACVkP,QAAS,CACPL,MAAO,YACPC,MAAO,cACPC,SAAU,kBACZ,EACA3F,MAAO,CACLnB,OAAQ,2CACV,EACAE,KAAM,CACJF,OAAQ,yCACV,CACF,CAAC,EAED7P,EAAE,UAAU,EAAEwC,cAAc,CAC1B+J,SAAU,IACVvK,KAAM,QACNqI,aAAc,IACdX,UAAW,WACXoN,QAAS,CACP3D,QAAS,CAAA,EACTwD,SAAU,EACZ,CACF,CAAC,EAEL,CAAC,EAED3W,EAAEgB,QAAQ,EAAEgiB,MAAM,WAEhB,IAwOQC,EAxOJC,EAAYrjB,OAAOuL,WAWvB,SAAS+X,IAEP,IAAID,EAAYrjB,OAAOuL,WAInB8X,EAFgB,KAGlBljB,EAAE,MAAM,EAAE+C,SAAS,aAAa,EAChC/C,EAAE,cAAc,EAAEuZ,IAAI,sBAAsB,EAAE6J,KAAK,EAEnDpjB,EAAE,mCAAmC,EAAEojB,KAAK,IAE5CpjB,EAAE,MAAM,EAAEqD,YAAY,aAAa,EACnCrD,EAAE,cAAc,EAAEuZ,IAAI,sBAAsB,EAAE8J,KAAK,EACnDrjB,EAAE,mCAAmC,EAAEqjB,KAAK,GAI1CH,EAdgB,KAelBljB,EAAE,cAAc,EAAEqX,MAAM,WAGtB,OAFArX,EAAE,mCAAmC,EAAEojB,KAAK,EAC5CpjB,EAAE,4BAA4B,EAAEggB,OAAO,EAChC,CAAA,CACT,CAAC,EAeCkD,EAlCgB,KAmClBljB,EAAE,gBAAgB,EAAEqX,MAAM,WAGxB,OAFArX,EAAE,gCAAgC,EAAEojB,KAAK,EACzCpjB,EAAE,iBAAiB,EAAEggB,OAAO,EACrB,CAAA,CACT,CAAC,CAEL,CArDAhgB,EAAEH,MAAM,EAAEyjB,OAAO,WAEXJ,IAAcrjB,OAAOuL,YACvB+X,EAAW,CAEf,CAAC,EAiDDA,EAAW,EAGPnjB,EAAE,iBAAiB,EAAEyF,QACvBzF,EAAE,qBAAqB,EAAEwY,OAAO,GAAG,EAIjCxY,EAAE,sBAAsB,EAAEyF,QAC5BzF,EAAE,2CAA2C,EAAEO,GAC7C,QACA,SAAU8gB,GACRrhB,EAAE,sBAAsB,EAAEqD,YAAY,WAAW,EACjDrD,EAAEoM,IAAI,EAAEmD,OAAO,EAAExM,SAAS,WAAW,CACvC,CACF,EAIE/C,EAAE,OAAO,EAAEyF,SACbzF,EAAE,sBAAsB,EAAEuZ,IAAI,YAAY,EAAE6J,KAAK,EACjDpjB,EAAE,aAAa,EAAEO,GAAG,QAAS,SAAU8gB,GACrCA,EAAMpU,eAAe,EAEjBzF,EAASxH,EAAEoM,IAAI,EAAE9E,KAAK,MAAM,EAEhCtH,EAAE,6BAA6B,EAAEqD,YAAY,WAAW,EAAE+f,KAAK,EAC/DpjB,EAAEwH,CAAM,EAAEzE,SAAS,WAAW,EAAEsgB,KAAK,EAErCrjB,EAAE,aAAa,EAAEqD,YAAY,WAAW,EACxCrD,EAAEoM,IAAI,EAAErJ,SAAS,WAAW,CAC9B,CAAC,GAIC/C,EAAE,WAAW,EAAEyF,QACjBzF,EAAE,WAAW,EAAEO,GAAG,QAAS,SAAU8gB,GACnCrhB,EAAE,aAAa,EAAEqD,YAAY,WAAW,EACxCrD,EAAE,6BAA6B,EAAEqD,YAAY,WAAW,EAAE+f,KAAK,EAE/D,IAAI5b,EAASxH,EAAEoM,IAAI,EAAE9E,KAAK,MAAM,EAEhCtH,EAAEwH,CAAM,EAAEzE,SAAS,WAAW,EAAEsgB,KAAK,EACrCrjB,EAAE,cAAgBwH,EAAS,IAAI,EAC5BzE,SAAS,WAAW,EACpBsgB,KAAK,CACV,CAAC,EAICrjB,EAAE,eAAe,EAAEyF,SAErBzF,EAAE,eAAe,EAAEqI,OACjB,qMACF,EAEArI,EAAE,4BAA4B,EAAEO,GAAG,QAAS,WAC1CP,EAAEoM,IAAI,EAAEmD,OAAO,EAAE6T,KAAK,CACxB,CAAC,GAICpjB,EAAE,QAAQ,EAAEyF,SAEdzF,EAAE,cAAc,EAAEO,GAAG,QAAS,WAE5B,IAAIgjB,EAAY,IAAMvjB,EAAEoM,IAAI,EAAE9E,KAAK,KAAK,EACpCkc,EAAYD,EAAY,gBAG5BvjB,EAAEujB,CAAS,EAAEF,KAAK,EAGArjB,EAAEwjB,CAAS,EAAEC,YAAY,EAGzB5jB,OAAOwL,aACvBrL,EAAEujB,CAAS,EAAExgB,SAAS,cAAc,CAExC,CAAC,EAGD/C,EAAE,eAAe,EAAEO,GAAG,QAAS,WAE7B,OADAP,EAAEoM,IAAI,EAAEsX,QAAQ,QAAQ,EAAEN,KAAK,EACxB,CAAA,CACT,CAAC,GAICpjB,EAAE,WAAW,EAAEyF,SAEbke,EAAiB3jB,EAAE,WAAW,EAAEsH,KAAK,WAAW,EAGpDtH,EAAE,WAAW,EAAE4L,KAAK,OAAO,EAAEqP,IAAI0I,CAAc,EAG/C3jB,EAAE,WAAW,EAAEojB,KAAK,GAIlBpjB,EAAE,SAAS,EAAEyF,SAEfzF,EAAE,SAAS,EAAEojB,KAAK,EAGlBpjB,EAAE,eAAe,EACduZ,IAAI,wCAAwC,EAC5ChZ,GAAG,QAAS,WAEX,IAAIqjB,EAAa,IAAM5jB,EAAEoM,IAAI,EAAE9E,KAAK,KAAK,EAKzC,OAFAtH,EAAE4jB,CAAU,EAAE5D,OAAO,EAEd,CAAA,CACT,CAAC,EAGHhgB,EAAE,wCAAwC,EAAEO,GAAG,SAAU,WAEvD,IAAIqjB,EAAa,IAAM5jB,EAAEoM,IAAI,EAAE9E,KAAK,KAAK,EAGzCtH,EAAE4jB,CAAU,EAAE5D,OAAO,CACvB,CAAC,GAIChgB,EAAE,iBAAiB,EAAEyF,QACvBzF,EAAE,iBAAiB,EAAEqX,MAAM,WAEzB,OADAxX,OAAOgkB,MAAM,EACN,CAAA,CACT,CAAC,EAIC7jB,EAAE,iBAAiB,EAAEyF,SAEvBzF,EAAE,0BAA0B,EAAEojB,KAAK,EAGnCpjB,EAAE,uBAAuB,EAAEO,GAAG,QAAS,WAErCP,EAAE,0BAA0B,EAAEojB,KAAK,EAGnC,IAAIQ,EAAa,IAAM5jB,EAAEoM,IAAI,EAAE9E,KAAK,KAAK,EAMrCwc,GAHJ9jB,EAAE4jB,CAAU,EAAE5D,OAAO,EAGFhgB,EAAEoM,IAAI,EAAER,KAAK,mBAAmB,GAC/C5L,EAAE8jB,CAAY,EAAEre,QAClBqe,EAAaC,KAAK,UAAW,CAAA,CAAI,EAInC/jB,EAAE4jB,CAAU,EAAEhY,KAAK,OAAO,EAAEoN,OAAO,gBAAgB,EAAEM,QAAQ,OAAO,CACtE,CAAC,EAGDtZ,EAAE,6BAA6B,EAAEO,GAAG,QAAS,WAC3CP,EAAEoM,IAAI,EAAEsX,QAAQ,uBAAuB,EAAEpK,QAAQ,OAAO,CAC1D,CAAC,EAGoB,GAAjB0K,gBACEC,EAAmB,UAAYD,cACnChkB,EAAEikB,CAAgB,EAAErY,KAAK,OAAO,EAAEmY,KAAK,UAAW,CAAA,CAAI,IAInC,GAAjBG,eACEjB,EAAkB,gBAAkBiB,cAAgB,KACxDlkB,EAAEijB,CAAe,EAAES,QAAQ,0BAA0B,EAAEL,KAAK,EAC5DrjB,EAAEijB,CAAe,GAGjBjjB,EAAE,uBAAuB,EAAEmkB,MAAM,GAHd7K,QAAQ,OAAO,GAQtB,IAAZ4J,GAAmBljB,EAAE,SAAS,EAAEyF,QAClCzF,EAAE,SAAS,EAAEokB,MAAM,CACjBC,KAAM,CAAA,EACNC,MAAO,IACPC,aAAc,EACdC,SAAU,CAAA,EACVhO,OAAQ,CAAA,CACV,CAAC,EAICxW,EAAE,UAAU,EAAEyF,SAEhBzF,EAAE2G,OAAO,CAAA,EAAM3G,EAAEwC,cAAcoE,SAAU,CACvCtE,OAAQ,SACRsF,SAAU,cACVkP,QAAS,CACPL,MAAO,YACPC,MAAO,cACPC,SAAU,kBACZ,EACA3F,MAAO,CACLnB,OAAQ,2CACV,EACAE,KAAM,CACJF,OAAQ,yCACV,CACF,CAAC,EAED7P,EAAE,UAAU,EAAEwC,cAAc,CAC1B+J,SAAU,IACVvK,KAAM,QACNqI,aAAc,IACdX,UAAW,WACXoN,QAAS,CACP3D,QAAS,CAAA,EACTwD,SAAU,EACZ,CACF,CAAC,EAEL,CAAC,EAGD3W,EAAEgB,QAAQ,EAAET,GAAG,QAAS,iBAAkB,WAExC,IAAIkkB,EAAWzkB,EAAEoM,IAAI,EAAEmD,OAAO,EAAE3D,KAAK,OAAO,EAAEqP,IAAI,IAE9CwJ,EADOpF,WAAWoF,CAAQ,IACd,GAAK,CAACzkB,EAAE0kB,UAAUD,CAAQ,KACxCA,EAAW,GAGbA,GAAsB,EAEtBzkB,EAAEoM,IAAI,EAAEmD,OAAO,EAAE3D,KAAK,OAAO,EAAEqP,IAAIwJ,CAAQ,EAAEE,OAAO,CACtD,CAAC,EAGD3kB,EAAEgB,QAAQ,EAAET,GAAG,QAAS,kBAAmB,WAEzC,IAAIkkB,EAAWzkB,EAAEoM,IAAI,EAAEmD,OAAO,EAAE3D,KAAK,OAAO,EAAEqP,IAAI,IAE9CwJ,EADOpF,WAAWoF,CAAQ,IACd,GAAK,CAACzkB,EAAE0kB,UAAUD,CAAQ,KACxCA,EAAW,GAGbA,GAAsB,EAEtBzkB,EAAEoM,IAAI,EAAEmD,OAAO,EAAE3D,KAAK,OAAO,EAAEqP,IAAIwJ,CAAQ,EAAEE,OAAO,CACtD,CAAC"}
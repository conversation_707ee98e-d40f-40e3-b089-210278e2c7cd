<p><PERSON><PERSON><PERSON><PERSON><PERSON> zákazník<PERSON>,<br>
děkujeme Vám za Vaši objednávku na rodinném eshopu www.pstrosivejce.cz s číslem s číslem {$orderRow->ordcode}.
Objednávka byla právě přijata k vyřízení. O jejím prů<PERSON>hu a odeslání Vás budeme informovat.
</p>

<p>

Cena objednávky s DPH: {$orderRow->ordpricevat|formatPrice}<br>

{if !empty($orderRow->ordnote)}
<strong>{_'Poznámka'}:</strong><br>
<strong style="color: red;">{$orderRow->ordnote|nl2br|noescape}</strong><br>
{/if}

<br>
<strong>{_'Zvolená doprava'}:</strong>{$delMode->delname}<br>
{if !empty($orderRow->orddeldatedel)}
<strong style="color: red">{_'Zvolený datum doručení'}:</strong> {$orderRow->orddeldatedel|date:'d.m.Y'}<br>
{/if}
{if $delMode->delcode=='ULOZENKA'}
Odběrné místo: {$enum_ulozenka[$orderRow->orddelspec]}
{else}
{$delMode->deltext1|nl2br|noescape}
{/if}
<br>
<strong>{_'Platba'}:</strong> {$payMode->delname}<br>
{if $payMode->delcode == 'paybefore'}
{* udaje pro platbu predem *}
<strong>Údaje pro platbu předem:</strong><br>
Číslo účtu: {if $orderRow->ordcurid==1}{$presenter->config["SERVER_ACCNO"]}{/if}<br>
Variabilní symbol: {$orderRow->ordcode}<br>
Částka: {$orderRow->ordpricevat|formatPrice}<br>
{/if}

<strong>{if !empty($orderRow->ordstname)}Fakturační adresa:{else}Fakturační a současně doručovací adresa:{/if}</strong><br>
{_'Jméno'}: {$orderRow->ordiname}<br>
{_'Přijmení'}: {$orderRow->ordilname}<br>
{if $lang=='cs'}{_'Firma'}: {$orderRow->ordifirname}<br>{/if}
{_'Ulice'}: {$orderRow->ordistreet}<br>
{_'Číslo popisné'}: {$orderRow->ordistreetno}<br>
{_'Město, obec'}: {$orderRow->ordicity}<br>
{_'PSČ'}: {$orderRow->ordipostcode}<br>
{_'Telefon'}: {$orderRow->ordtel}<br>
{_'Email'}: <a href="mailto:{$orderRow->ordmail}">{$orderRow->ordmail}</a><br>
IČ: {$orderRow->ordic}, DIČ: {$orderRow->orddic}<br>
{if !empty($orderRow->ordstname)}
<br>
<strong>Dodací adresa:</strong><br>
Jméno, přijmení: {$orderRow->ordstname} {$orderRow->ordstlname}<br>
Firma: {$orderRow->ordstfirname}<br>
Ulice: {$orderRow->ordststreet} {$orderRow->ordststreetno}<br>
Město, obec: {$orderRow->ordstcity}<br>
PSČ: {$orderRow->ordstpostcode}<br>
{/if}
</p>
<strong>{_'Položky objednávky'}:</strong><br>
<table>
<tr>
  <td><strong>Katalogové číslo</strong></td>
  <td><strong>Název</strong></td>
  <td><strong>Kusy</strong></td>
  <td><strong>Sleva</strong></td>
  <td><strong>Cena s DPH</strong></td>
</tr>
{php
$sum = 0;
$eggItemsCnt = 0;
$itemsCnt = 0;
}
{foreach $ordItemRows as $row}
{php
  $sum += ($row->oriqty*$row->oriprice);

  if ($row->oritypid == 0) {
    $itemsCnt ++;
  }

  if ((strpos($row->oriprocode, 'VEJCE') === 0)) {
    $eggItemsCnt ++;
  }
}

<tr>
  <td>{$row->oriprocode}</td>
  <td>{$row->oriname}</td>
  <td>{$row->oriqty} {_'ks'}</td>
  <td></td>
  <td>{if $row->oriprice != 0}{$row->oriprice|formatPrice}{else}ZDARMA{/if}</td>
</tr>

{/foreach}
</table>
<br>
<table>
<tr>
  <td><strong>{_'Celková cena s DPH'}: </strong></td>
  <td>{$orderRow->ordpricevat|formatPrice}</td>
</tr>
</table>

{if $eggItemsCnt > 0 && $itemsCnt > $eggItemsCnt}
  <p style="color: darkred">Objednali jste vajíčko, které pštros snáší pouze pár měsíců v roce. O přesném datu doručení budeme informovat pár dnů předem.</p>
{/if}

{include 'mailOrderInfo.latte' 'order' => $orderRow}

{include 'mailFooter.latte'}

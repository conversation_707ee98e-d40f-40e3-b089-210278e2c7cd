{* 3 => 'Odeslána' *}
{default $onlyEgg = FALSE}
<p>
  <PERSON><PERSON><PERSON><PERSON> den,<br>
  va<PERSON><PERSON> objedn<PERSON> č. {$orderRow->ordcode} byla <strong>ZAŘAZENA K DORUČENÍ</strong>.
</p>

{include 'mailOrderInfo.latte' 'order' => $orderRow}

<p>
  V<PERSON><PERSON> způsob doručení: <strong>{$delMode->delname}</strong><br>
  Datum odběru: <strong>{if $onlyEgg}{$orderRow->orddeldatedel|getMonthName}{else}{$orderRow->orddeldatedel|date:'d.m.Y'}{/if}</strong><br>
  {if !empty($orderRow->ordparcode)}
    {_'Vaše číslo balíku je'}: {$orderRow->ordparcode}.<br>
    {if isset($parcelURL)}{_'Balík můžete sledovat'} <a href="{$parcelURL|noescape}">{_'na stránk<PERSON>ch přepravce'}</a>.<br>{/if}
  {/if}

  {_'POZOR! Ohledně převzetí zásilky prosím čtěte pozorně doplňkové informace ke zvolenému způsobu dopravy.'}
</p>
<p>{$delMode->deltext2|nl2br|noescape}</p>
<p>{$delMode->deltext1|nl2br|noescape}</p>

{include 'mailFooter.latte'}

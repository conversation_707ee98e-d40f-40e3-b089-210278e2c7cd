{layout NULL}
{php
  $color_main = 'c0392b';
  $color_secondary = 'cd5d01';
  $color_back = 'fff0e5';
}
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>{$mailing->mamsubject} - {$config["SERVER_NAMESHORT"]}</title>
</head>
<body style="line-height:1.3;background:white;font-family: Arial, Helvetica, sans-serif;font-size: 14px;color: #000;margin-top:0px;" link="#000000" vlink="#000000" alink="#000000">
<div style="width:630px;margin:0 auto">

  {if empty($mailing->mamimage)}
  <!-- hlavička -->
  <table width="630" border="0" cellspacing="0" cellpadding="0" bgcolor="#{$color_back}" style="background-color:#{$color_back};border-bottom:3px solid #{$color_main};" align="center">
    <tr>
      {php
        $topImg = __DIR__ . '/../../img/newsletter-top.jpg';
        $topExist = file_exists($topImg);
      }
      {if $topExist}
      <td height="131" align="center" valign="top">
        <a href="{$baseUri}/c/0/{$mamid}/{$usrid}"><img src="{$baseUri}/img/newsletter-top.jpg" height="131" alt="{$config["SERVER_NAMESHORT"]}" style="border:none" ></a>
      </td>
      {else}
      <td align="center" valign="top">
        <p><strong style="color:#{$color_main};font-size:25px;font-weight:bold;">{$config["SERVER_NAMESHORT"]}</strong></p>
      </td>
      {/if}
    </tr>
  </table>
  {/if}

  <!-- zobrazení na webu -->
  {if $isMail}<p align="center">Nezobrazuje-li se vám e-mail správně, klikněte <a style="text-decoration:underline;color:#{$color_main};" href="{$baseUri}/c/3/{$mailing->mamid}/{$usrid}/{$loKey}"><font style="color:#{$color_main};">na tento odkaz</font></a>.</p>{/if}

  <!-- obrázek -->
  {if !empty($mailing->mamimage)}
  <a href="{$baseUri}/c/0/{$mamid}/{$usrid}"><img src="{$baseUri}{$mailing->mamimage}" /></a>
  {/if}

  <!-- úvodní text -->
  {if !empty($mailing->mambody)}
  <div align="center" style="text-align:center;padding:0 20px;line-height:1.5;"><center>
    <br>
    {$mailing->mambody|noescape}
  </center></div>
  {/if}
  <!-- úvodní text -->

  <!-- výpis produktů -->
  <table width="630" border="0" cellspacing="0" cellpadding="0" align="center">
    {foreach $products as $key => $product}
    <tr>
      <td width="140px" align="left" valign="top">
      <br>
      {php
        $picName = ($product->propicname != "" ? trim($product->propicname).'.jpg' : $product->procode.'.jpg');
      }
      <a style="display:block;width:100%;height:100%;color:black;text-decoration:none " href="{$baseUri}/c/1/{$mailing->mamid}/{$usrid}/{$product->proid}">
        <img style="float:left;border:none;margin-bottom:20px;margin-right:10px;" src="{$baseUri}/pic/product/list/{$picName}">
      </a>
      </td>
      <td align="left" valign="top">
        <br>
        <a style="display:block;width:100%;height:100%;color:#{$color_main};text-decoration:none" href="{$baseUri}/c/1/{$mailing->mamid}/{$usrid}/{$product->proid}">
        <strong><font size="3" style="color:#{$color_main};" color="#{$color_main}">{$product->proname}</font></strong><br>
        <strong><font style="color:#cc0000;" color="#cc0000">{$product->promamdesc1}</font></strong><br>
        <p style="margin-top:5px;"><font size="3" style="color:black;" color="black">{$product->promamdesc2|striptags|truncate:200|noescape}</font></p>
        <p style="margin-top:0px;margin-bottom:5px;"><strong><font color="#cc0000" style="color:#cc0000;" size="4">{$product->proprice1a|formatPrice}</font></strong></p>
        <p style="margin-top:0px;margin-bottom:3px;">
        <a style="display:block;float:left;width:110px;margin-right:5px;height:25px;padding-top:8px;color:white;text-decoration:none;border-radius:3px;background-color:#{$color_secondary};background:#{$color_secondary};text-align:center;" href="{$baseUri}/c/1/{$mailing->mamid}/{$usrid}/{$product->proid}"><strong style="text-decoration:none;color:white;">ZOBRAZIT</strong></a>
        <a style="display:block;float:left;width:110px;margin-right:5px;height:25px;padding-top:8px;color:white;text-decoration:none;border-radius:3px;background-color:#{$color_main};background:#{$color_main};text-align:center;" href="{$baseUri}/c/1/{$mailing->mamid}/{$usrid}/{$product->proid}"><strong style="text-decoration:none;color:white;">KOUPIT</strong></a>
        </p>
      </a>
      </td>
    </tr>
    {/foreach}
  </table>
  <!-- výpis produktů -->

  <!-- patička -->
  {if !empty($mailing->mamfooter)}
  <div align="center" style="text-align:center;padding:0 20px;line-height:1.5;"><center>
    <br>
    {$mailing->mamfooter|noescape}
  </center></div>
  {/if}
  <br>
  <table width="100%" border="0" cellspacing="0" cellpadding="10" bgcolor="#{$color_back}" style="background-color:#{$color_back};border-top:3px solid #{$color_main};" align="center">
    <tr>
      <td align="center" valign="middle">
        <br>
        <strong>Kontakt:</strong>
          <a style="text-decoration:underline;color:#{$color_main};" href="mailto:{$config["SERVER_MAIL"]}"><font style="color:#{$color_main};">{$config["SERVER_MAIL"]}</font></a>,
          <a style="text-decoration:underline;color:#{$color_main};" href="{$baseUri}/c/0/{$mamid}/{$usrid}"><font style="color:#{$color_main};">{$config["SERVER_NAMESHORT"]}</font></a>,
        <br><br>
        <strong>Provozovatel:</strong> {$config["INVOICE_VENDOR_R1"]},<br>{$config["INVOICE_VENDOR_R2"]}, {$config["INVOICE_VENDOR_R3"]}
        <br><br>
        Toto obchodní sdělení bylo zasláno na základě Vašeho souhlasu při registraci na <a style="text-decoration:underline;color:#{$color_main};" href="https://www.$config["SERVER_NAMESHORT"]"><font style="color:#{$color_main};">{$config["SERVER_NAMESHORT"]}</font></a>.<br>
        Můžete se kdykoliv <a style="text-decoration:underline;color:#{$color_main};" href="{$baseUri}/c/2/{$mailing->mamid}/{$usrid}/{$loKey}"><font style="color:#{$color_main};">odhlásit z odběru zde</font></a>.<br><br></td>

    </tr>
  </table>

</div>
{* zaloguju zda precetl email *}
{if $isMail}<img src="{$baseUri}/c/4/{$mamid}/{$usrid}" width="1px" height="1px" style="border:none"/>{/if}
</body>
</html>

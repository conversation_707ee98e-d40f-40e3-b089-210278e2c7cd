{$pageTitle  = '<PERSON><PERSON><PERSON>'}
{$pageRobots = "nofollow,noindex"}

{block #content}
<div class="user article">

  <div class="user__menu">
    {include @userMenu.latte}
  </div>

  {if $userRow->usrmailverified === 0}
    <div class="alert alert--danger" role="alert">
      <p>
        K dokončení registrace je nutné ověřit Váš email. Vyhledejte naši žádost o ověření na emailové adrese, kterou jste zadali při registraci ({$userRow->usrmail}) a postupujte podle pokynů v tomto emailu.<br>
        <br>
        Pokud jste nedostali ověřovací email, nejprve zkontrolujte, zda jste zadali email správně. Pokud je email správně, klikněte na <a href="{plink sendVerification}">poslat znovu ověřovací email</a>. Email bude zas<PERSON>án na adresu <strong>{$userRow->usrmail}</strong>.<br>
        <br>
        <PERSON><PERSON><PERSON>ň také zkontrolujte, zda tento email nezapadl do nevyžádané pošty (SPAM).<br>
        <br>
        Procedura ověření emailu je pro Vaši bezpečnost. Děkujeme za spolupráci.
      </p>
    </div>

    <h2>Zde můžete zadat ověřovací kód který Vám byl zaslán do emailu.</h2>
    {form setVerifyCodeForm}
      {input usrmailvcode size=>6} {input submit}
      {/form}
  {/if}

  <h2>{_'Objednávky'}</h2>

  {if count($openedOrders) > 0}
  {foreach $openedOrders as $row}
  {if $iterator->isFirst()}
  <table class="table" cellpadding="3" cellspacing="0" border="1">
  {/if}
    <tr>
      <td><a href="{plink order $row->ordid}">{$row->ordcode}</a></td>
      <td>{$row->orddatec|date:'%d.%m.%Y'}</td>
      <td>{$row->ordprice|formatPrice}</td>
      <td> <a href="{plink copyOrder, $row->ordid}">objednat znovu</a> </td>
    </tr>
  {if $iterator->isLast()}
  </table>
  {/if}
  {/foreach}
  {else}
  <p>{_'Žádné otevřené objednávky'}</p>
  {/if}

  <h2>{_'Vyřízené objednávky'}</h2>

  {if count($closedOrders) > 0}
  {foreach $closedOrders as $row}
  {if $iterator->isFirst()}
  <table class="table" cellpadding="3" cellspacing="0" border="1">
  {/if}
    <tr>
      <td><a href="{plink order $row->ordid}">{$row->ordcode}</a></td>
      <td>{$row->orddatec|date:'%d.%m.%Y'}</td>
      <td>{$row->ordprice|formatPrice}</td>
      <td> <a href="{plink copyOrder, $row->ordid}">objednat znovu</a> </td>
    </tr>
  {if $iterator->isLast()}
  </table>
  {/if}
  {/foreach}
  {else}
  <p>{_'Žádné uzavřené objednávky'}</p>
  {/if}

</div>
{/block}

{*<!-- footer start -->*}
<footer class="footer" role="contentinfo">

  <div class="footer__map">

    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3050.************!2d17.99074096294458!3d49.98575269857343!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x471163b41ef4e3d1%3A0x10ad1c34d714dd37!2sSlu%C5%BEovice%20154%2C%20747%2028%20Slu%C5%BEovice!5e0!3m2!1scs!2scz!4v1634548922934!5m2!1scs!2scz" width="100%" height="400" frameborder="0" style="border:0;" allowfullscreen="" loading="lazy"></iframe>

  </div>

  <div class="container-fluid">

    <div class="row">

      <div class="col-sm-5">

        <nav role="navigation">

          <ul class="footer__products">

            {*<!-- menu catalog start -->*}
            {include @pageMenuCatalog.latte}
            {*<!-- menu catalog start -->*}

          </ul>

        </nav>

      </div>

      <div class="col-sm-2 center">

        <a href="{$baseUri}" title="{$presenter->config["SERVER_NAME"]}" class="footer__logo"><img src="{$baseUri}/img/logo.svg" alt="{$presenter->config["SERVER_NAME"]}"></a>

      </div>

      <div class="col-sm-5 center">

        <div class="footer__contact">

          <p>
            <span class="icon icon--phone">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#phone" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
            <a href="tel:{$config["SERVER_PHONE"]|formatPhoneNumber}">{$config["SERVER_PHONE"]}</a><br>
            <span class="icon icon--email">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#email" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
            <a href="mailto:{$config["SERVER_MAIL"]}">{$config["SERVER_MAIL"]}</a><br>
            <strong>{$config["INVOICE_VENDOR_R1"]}</strong><br>
              {$config["INVOICE_VENDOR_R2"]}<br>
              {$config["INVOICE_VENDOR_R3"]}
          </p>

        </div>

      </div>

    </div>

    <div class="row">

      <div class="col-xs-12 center">

        {foreach $menuFooter as $row}
          {if $iterator->first}
        <ul class="footer__nav">
          {/if}
          <li>
            {if !empty($row->menurl)}
            <a href="{$row->menurl}">{$row->menname}</a>
          {else}
            <a href="{plink Page:detail, $row->pagid, $row->pagurlkey}">{$row->menname}</a>
          {/if}
          </li>
          {if $iterator->last}
        </ul>
          {/if}
        {/foreach}

      </div>

    </div>

  </div>

</footer>

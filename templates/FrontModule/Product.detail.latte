{* Product.detail.latte *}

{* nastaveni promennych *}
{$pageTitle       = (!empty($product->protitle) ? $product->protitle : $product->proname)}
{$pageKeywords    = $product->prokeywords}
{$pageDescription = (empty($productMasterData->prodescription) ? $productMasterData->prodescs|striptags : $productMasterData->prodescription)}
{$pageImageCustom= $baseUri . "/" . ($productMasterData|getProductPicName:'440x440')}

{* drobky pro detal zbozi *}
{block #crumb}
<div class="breadcrumb">

  <div class="container-fluid">

    <a href="{$baseUri}">Úvod</a>
    {ifset $catalogPath}
    {foreach $catalogPath as $row}
      <span>»</span>
      <a href="{plink Catalog:detail, $row->catid, ($row|getCatKey)}">{$row->catname}</a>
    {/foreach}
    {/ifset}
    <span>»</span> <strong>{$product->proname}</strong>

  </div>

</div>
{/block}

{block #content}

<script>

  fbq('track', 'ViewContent', {
    content_ids: [{$product->proid}],
    content_type: 'product',
    value: {$product->proprice},
    currency: {$curKey}
  });
</script>

{*<!-- product detail start -->*}
<div class="product-detail">

{if $product->prostatus == 0}

  {* ritch snippets http://schema.org/Product *}
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Product",
    "name": {$product->proname},
    "image": {$baseUri . '/' . ($productMasterData|getProductPicName:'440x440')|noescape},
    "description": {$pageDescription},
    "manufacturer": {$manufacturer->manname},
    "url": {plink '//this'},
    "offers": {
      "@type": "Offer",
      "availability": {if $product->proaccess == 0}"http://schema.org/InStock"{else}"http://schema.org/OutOfStock"{/if},
      "price": {$product->proprice},
      "priceCurrency": "CZK"
    }
  }
  </script>

  <div class="row">

    {*<!-- hlavní část produktu start -->*}
    <div class="col-sm-5">

      {*<!-- foto produktu start -->*}
      <div class="product-detail__image gallery">
        <a href="{$baseUri}/{$productMasterData|getProductPicName:'src'}" title="{$product->proname}">
          <img src="{$baseUri}/{($productMasterData|getProductPicName:'440x440')|noescape}" alt="{$product->proname}" itemprop="image">
        </a>
      </div>
      {*<!-- foto produktu end -->*}

      {*<!-- další fotografie start -->*}
      <div class="product-detail__gallery gallery">
        {foreach $images as $row}
        <a href="{$baseUri}/{($productMasterData|getProductPicName:'src':TRUE:$row["name"])}" title="{$product->proname}">
          <img src="{$baseUri}/{($productMasterData|getProductPicName:'280x280':TRUE:$row["name"])}" alt="{$product->proname}">
        </a>
        {/foreach}
      </div>
      {*<!-- další fotografie end -->*}

    </div>
    {*<!-- hlavní část produktu end -->*}

    {*<!-- boční lišta start -->*}
    <div class="col-sm-7">

      <div class="product-detail__content">

        <h1 class="product-detail__header">{$product->proname} {if $adminLogIn}<a href="{plink :Admin:Product:edit, $product->proid}" target="admin" class="control control--success"><span class="icon icon--wheel"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#wheel" x="0" y="0" width="100%" height="100%"></use></svg></span></a>{/if}</h1>

        {*<!-- popis start -->*}
        <div class="product-detail__description">
          {$productMasterData->prodescs|noescape}
        </div>
        {*<!-- popis end -->*}

        {* top 2 hodnocení - vyberu náhodně nejlépe hodnocené *}
        {foreach $commentsTop as $com}
          {if $iterator->first}
            <div class="product-detail__rating">
              <h3>Hodnocení našich zákazníků:</h3>
          {/if}
          <p>
            <strong>{$com->cmtnick}</strong>: {for $i = 1; $i <= $com->cmtrate; $i++}&starf;{/for} {$com->cmttext|truncate:40|noescape}
          </p>
          {if $iterator->last}
              <p><a href="#comm">více recenzí &raquo;</a> </p>
            </div>
          {/if}
        {/foreach}

      </div>

      {* štítky *}
      {include @productLabel.latte, product => $product}

      {*<!-- price start -->*}
      <div class="product-detail__price">
        <strong>{_'Cena:'} {$product->proprice|formatPrice} {if $config["PRICEVAT"] == 'inclvat'} s DPH {else} bez DPH {/if}</strong>
        {if $product->proprice < $product->propricecom}
        <br>
        ušetříte {($product->propricecom - $product->proprice)|formatPrice} (původní cena <strike>{$product->propricecom|formatPrice}</strike>)
        {/if}
      </div>
      {*<!-- price end -->*}

      {if count($subItems) == 0}
        <div class="product-detail__buy">
        {if ($config["CHECK_STOCK"] == 1 && $productMasterData->proaccess == 0) || $config["CHECK_STOCK"] == 0}
          {if ($product|isEgg)}
            {form basketAddForm}
              {label oricnt} {input oricnt} {label orimonth} {input orimonth} {input buy class=>'btn btn--buy btn--big'}
            {/form}
          {else}
          <a href="{plink Basket:add $product->proid}" class="btn btn--buy btn--big">Koupit</a>
          {/if}
        {else}
        Zboží co není skladem nelze zakoupit.
        {/if}
        </div>
      {/if}

      {*<!-- product info start -->*}
      <div class="product-detail__info">

        <p>
          {_'Termín dodání'}:
          {if $product->proaccess != 100 OR ($product->proaccess == 100 AND empty($product->proaccesstext))}
            <span class="stock{if $product->proaccess == 0} stock--available{else} stock--unavailable{/if}">{$enum_proaccess[$product->proaccess]}</span>
          {else}
            <span class="stock stock--unavailable">{$product->proaccesstext}</span>
          {/if}
        </p>

        {* text produktu *}
        {$productMasterData->prodesc|noescape}

        {* youtube videa *}
        {if !empty($product->provideo)}
        <h2>Prohlédněte si video</h2>
        <div id="video"><div><iframe width="560" height="315" src="https://www.youtube.com/embed/{$product->provideo}"  allowfullscreen></iframe></div></div>
        {/if}

      </div>
      {*<!-- product info end -->*}

      {*<!-- sociální sítě start -->*}
      <div class="share">
        <strong>{_'Sdílejte na:'}</strong>
        <a href="https://www.facebook.com/sharer/sharer.php?u={plink '//this'}" class="share--facebook" title="sdílet na Facebooku"><span class="icon icon--facebook"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#facebook" x="0" y="0" width="100%" height="100%"></use></svg></span></a>
        <a href="https://twitter.com/intent/tweet?url={plink '//this'}&text={$product->proname}" class="share--twitter" title="sdílet na Twitteru"><span class="icon icon--twitter"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#twitter" x="0" y="0" width="100%" height="100%"></use></svg></span></a>
      </div>
      {*<!-- sociální sítě end -->*}

      {*<!-- helpers start -->
      <div class="product-detail__helpers">

        <a class="btn btn--small" href="{plink compareAdd, $product->proid}">
          <span class="icon icon--wheel"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#wheel" x="0" y="0" width="100%" height="100%"></use></svg></span>
          {_'Porovnat'}
        </a>
        <a class="btn btn--small" href="{plink bookmarkAdd, $product->proid}">
          <span class="icon icon--star"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#star" x="0" y="0" width="100%" height="100%"></use></svg></span>
          {_'Přidat k oblíbeným'}
        </a>
        <a class="btn btn--small control--print" href="#">
          <span class="icon icon--print"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#print" x="0" y="0" width="100%" height="100%"></use></svg></span>
          {_'Vytisknout'}
        </a>
        <a class="btn btn--small" href="#tab4" class="opentab">
          <span class="icon icon--chat"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#chat" x="0" y="0" width="100%" height="100%"></use></svg></span>
          {_'Dotazy a komentáře'}
        </a>

        <!-- watchdogs start -->
        <div class="product-detail__watchdog">

          {form watchDogPrice}
          <p>
            <strong><span class="icon icon--dog"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#dog" x="0" y="0" width="100%" height="100%"></use></svg></span> {_'Hlídat cenu:'}</strong><br>
            {input dogmail 'placeholder'=>'Zadejte Váš e-mail', class => 'input--50'}
            {input send class => 'btn btn--small'}
          </p>
          {/form}

          {if $product->proaccess > 0}
              {form watchDogStore}
              <p>
                <strong><span class="icon icon--dog"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#dog" x="0" y="0" width="100%" height="100%"></use></svg></span> {_'Hlídat naskladnění:'}</strong><br>
                {input dogmail 'placeholder'=>'Zadejte Váš e-mail', class => 'input--50'}
                {input send class => 'btn btn--small'}
              </p>
              {/form}
          {/if}

        </div>
        <!-- watchdogs end -->

      </div>
      <!-- helpers start -->*}

    </div>
    {*<!-- boční lišta end -->*}

  </div>

  <div class="row">

    {*<!-- variants start -->*}
    <div class="col-xs-12 product-detail__variants">

      {if count($subItems) > 0}
        {form basketAddFormVar}
        {* vypise chyby formulare *}
        {include ../@formErrors.latte form=>$form}

        {foreach $subItems as $row}
        {php
          if ($productMasterData->proaccess == 100) $row->proaccess = $productMasterData->proaccess;
        }
        {if $iterator->isFirst()}
          {if $row->promasid > 0}<h2>Vyberte si variantu produktu</h2>{/if}
          <table class="table" cellpadding="3" cellspacing="0" border="1">
          <tr>
            <th>{if $row->promasid > 0}Název varianty{else}Název zboží{/if}</th>
            <th>Cena s DPH</th>
            <th>Dostupnost</th>
            <th>Počet kusů</th>
          </tr>
        {/if}
        <tr>
          <td>
            <span class="table__product">
            {if $row->proid != $product->proid}<a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{/if}
            {if $row->promasid > 0}
              <img src="{$baseUri}/{($row|getProductPicName:'140x140')|noescape}" alt="{$row->proname}">
              {$row->proname}
            {else}
              {$product->proname}
            {/if}
            {if $row->proid != $product->proid}</a>{/if}
            {if $adminLogIn}<a href="{plink :Admin:Product:edit, $row->proid}" target="admin" class="control control--success"><span class="icon icon--wheel"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#wheel" x="0" y="0" width="100%" height="100%"></use></svg></span></a>{/if}
            </span>
          </td>
          <td><strong>{$row->proprice|formatPrice}</strong></td>
          <td>
            {if $row->proaccess == 100}
              <span class="stock stock--unavailable">{if !empty($row->proaccesstext)}{$row->proaccesstext}{else}Není skladem{/if}</span>
            {elseif $row->proaccess == 0}
              <span class="stock stock--available">Skladem {$row->proqty|getQty}</span>
            {/if}
          </td>
          <td>
            {ifset $form[$row->proid]}
            <span class="control--count">
              {php echo $form[$row->proid]['oricnt']->control->size(3)->class('clearonfocus') }
              <span class="control control--plus"><span class="icon icon--plus"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#plus" x="0" y="0" width="100%" height="100%"></use></svg></span></span> <span class="control control--minus"><span class="icon icon--minus"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#minus" x="0" y="0" width="100%" height="100%"></use></svg></span></span>
            </span>
            {/ifset}
          </td>
        </tr>
        {if $iterator->isLast()}
        </table>
        {if ($config["CHECK_STOCK"] == 1 && $productMasterData->proaccess == 0) || $config["CHECK_STOCK"] == 0}
        <p>{php echo $form['buy']->control->class('btn btn--buy btn--big'); }</p>
        {/if}
        {/if}
        {/foreach}
        {/form basketAddFormVar}
      {/if}

    </div>
    {*<!-- variants end -->*}

  </div>

  <div class="product-detail__tabs">

    <div class="tabs" id="tabs">

      <div class="row">
        <div class="col-xs-12 col-sm-3"><a href="#comm" class="tabs__name is-active">{_'Hodnocení'}</a></div>
      </div>

    </div>

    <div class="row tabs__content">

      <div class="col-xs-12 is-active" id="comm">

        <div class="tabs__into article">

          {*<!-- vypis komentaru start -->*}
          <div class="comments">
          {foreach $comments as $com}
            <div class="comments__item">
              <p>
                <strong>{$com->cmtnick}</strong>
                  {$com->cmtdatec|date:'d.m.Y H:m'} {for $i = 1; $i <= $com->cmtrate; $i++}&starf;{/for} {if $adminLogIn}<a href="{plink :Admin:Comment:edit, $com->cmtid}" target="admin" class="control control--success"><span class="icon icon--wheel"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#wheel" x="0" y="0" width="100%" height="100%"></use></svg></span></a>{/if}<br>
                {$com->cmttext|commentStripTags:($com->cmtmail==$presenter->config["SERVER_MAIL"])|noescape}
              </p>

            </div>
            {if $iterator->isLast()}
            {/if}
          {/foreach}

          {form commentForm}
          {* vypise chyby formulare *}
          {include ../@formErrors.latte form=>$form}

          {*<!-- formular zadani start -->*}
          {* formular pro zadani komentaru *}
          <fieldset>

            <legend>{_'Přidat hodnocení'}</legend>

            <p>
              {label cmtnick class=>required} <span class="order__star">*</span><br>
              {input cmtnick}
            </p>
            <p>
              {label cmtmail class=>required} <span class="order__star">*</span> <small>email nebude zveřejněn</small><br>
              {input cmtmail}
            </p>
            <p>
              {label cmtrate class=>required} <span class="order__star">*</span><br>

              <div class="star-rating">
                <fieldset>
                  {input cmtrate:5 id=>'star5'}<label for="star5">5</label>
                  {input cmtrate:4 id=>'star4'}<label for="star4">4</label>
                  {input cmtrate:3 id=>'star3'}<label for="star3">3</label>
                  {input cmtrate:2 id=>'star2'}<label for="star2">2</label>
                  {input cmtrate:1 id=>'star1'}<label for="star1">1</label>
                </fieldset>
              </div>
            </p>
            <p>
              {label cmttext class=>required} <span class="order__star">*</span><br>
              {input cmttext}
            </p>
            <p>
              {label recaptcha}<br>
              {input recaptcha}
            </p>
            <p>
            {input submit value=>"Odeslat recenzi"}

            </p>

          </fieldset>

          {/form}
          {*<!-- formular zadani end -->*}

        </div>

      </div>

    </div>

  </div>
  {*<!-- vypis komentaru end -->*}

</div>


{else}
  <p><strong>Tato položka byla vyřazena z nabídky.</strong></p>

  {* výpis produktů *}
  {include @productsList.latte, products => $footerTopProducts, title => 'Nejoblíbenější produkty'}

{/if}

</div>
{*<!-- product detail end -->*}

{* <!-- prislusenstvi zbozi start --> *}
{if count($proAccess) > 0}
  <h2>Příslušenství</h2>
  {include @productsList.latte, products => $proAccess, title => 'Příslušenství'}
{/if}
{* <!-- prislusenstvi zbozi end --> *}

{/block}

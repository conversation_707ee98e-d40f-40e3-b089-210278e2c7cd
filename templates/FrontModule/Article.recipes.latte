{$pageTitle       = (empty($article->arttitle) ? $article->artname :$article->arttitle)}
{$pageKeywords    = $article->artkeywords}
{$pageDescription = $article->artdescription}
{$pageImageCustom = $baseUri."/".($article|getArtPicNameBg:'440x293':FALSE)}

{block #crumb}
<div class="breadcrumb">

  <div class="container-fluid">

    <a href="{$baseUri}">Úvod</a>
    <span>»</span>
    <strong>{$pageTitle}</strong> {if $adminLogIn} <a href="{plink :Admin:Article:edit, $article->artid}" target="admin">E</a>{/if}

  </div>

</div>
{/block}


{block #content}
<section role="region">
  <h2 class="category__header">{$pageTitle}</h2>

  <div class="row row--autoclear row--flex">

  {foreach $rows as $row}
  <div class="col-xs-6 col-sm-6 col-md-4 col-lg-4 product__wrapper">

    <article class="product" role="article">

      <h2 class="product__header">
        <a href="{plink Article:detail $row->artid, ($row->arturlkey|getUrlKey:$row->artname)}" title="{$row->artname}">{$row->artname}</a>
      </h2>

      <div class="product__image">

        <a href="{plink Article:detail $row->artid, ($row->arturlkey|getUrlKey:$row->artname)}">
          <img src="{$baseUri}/img/no.jpg" data-src="{$baseUri}/{($row|getArtPicName:'354x354')|noescape}" alt="{$row->artname}">
        </a>

      </div>
    </article>

  </div>

  {/foreach}
  </div>

  <!-- stránkování start -->
  {control paginator}
  <!-- stránkování end -->

</section>
{/block}

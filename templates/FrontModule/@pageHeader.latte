{*<!-- <PERSON><PERSON><PERSON><PERSON><PERSON> start -->*}
<header class="header" role="banner">

  <a href="{$baseUri}" title="{$presenter->config["SERVER_NAME"]}" class="header__title"><img src="{$baseUri}/img/logo_admin.png" alt="{$presenter->config["SERVER_NAME"]}"></a>

  <div class="header__contact">

    <div class="container-fluid">

      <p class="header__social">
        <a href="https://www.instagram.com/pstrosivrsekwieder/" target="_blank">
          <span class="icon icon--instagram">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#instagram" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </a>
        <a href="http://facebook.com/P%C5%A1tros%C3%AD-Vr%C5%A1ek-<PERSON>ieder-555113197963926/?ref=aymt_homepage_panel" target="_blank">
          <span class="icon icon--facebook">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#facebook" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </a>
      </p>

      <p>
        <a href="tel:+************">
          <span class="icon icon--phone">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#phone" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
          +420 737 258 560
        </a>

        <a href="mailto:<EMAIL>">
          <span class="icon icon--email">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#email" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
          <EMAIL>
        </a>
      </p>

    </div>

  </div>

  <div class="container-fluid">

    {*<!-- hlavní menu start -->*}
    <div class="header__nav">

      <ul>
        {foreach $menuTopLeft as $row}
          {include @menuItem.latte 'menuItem' => $row}
        {/foreach}
        <li class="header__center">
          <a href="{$baseUri}" title="{$presenter->config["SERVER_NAME"]}" class="header__logo"><img src="{$baseUri}/img/logo.svg" alt="{$presenter->config["SERVER_NAME"]}"></a>
        </li>

        {foreach $menuTopRight as $row}
          {include @menuItem.latte 'menuItem' => $row}
        {/foreach}
      </ul>

    </div>
    {*<!-- hlavní menu end -->*}

    <div class="header__controls">

      {*<!-- přihlášení uživatele start -->*}
      <div class="login">

        <p class="login__header">
          <span class="icon icon--man">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#man" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </p>

        {if $userRow->usrid > 0}
        <p class="login__content">
          <a href="{plink User:default}">{if !empty($userRow->usriname)}{$userRow->usriname} {$userRow->usrilname}{else}{$userRow->usrmail}{/if}</a>
          <a href="{plink User:logout}">{_'odhlásit'}</a>
        </p>
        {else}
        <p class="login__content">
          <a href="{plink User:login}">{_'přihlášení'}</a>
          <a href="{plink User:add}">{_'registrace'}</a>
        </p>
        {/if}

      </div>
      {*<!-- přihlášení uživatele end -->*}

      {*<!-- košík start -->*}
      <div class="basket">

        {snippet basketWindow}

          {if $basketItemsCnt > 0}
            <a href="{plink Basket:default}">
              <p class="basket__header">
                <span class="icon icon--basket">
                  <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="{$baseUri}/img/icons.svg#basket" x="0" y="0" width="100%" height="100%"></use>
                  </svg>
                </span>
                <span class="basket__count">{$basketItemsCnt}</span>
              </p>
              <p class="basket__content">
                <strong>{$basketItemsCnt}&nbsp;{_'ks'}</strong>
                {_'za'}
                <strong>{$basketPriceSum|formatPrice}</strong>
              </p>
            </a>
          {else}
          <a href="#">
            <p class="basket__header">
              <span class="icon icon--basket">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#basket" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </p>
            <p class="basket__content">{_'je prázdný'}</p>
          </a>
          {/if}

        {/snippet}

      </div>
      {*<!-- košík end -->*}

    </div>

    {*<!-- vyhledávání start -->*}
    <div class="search">

      {form searchForm}
        {input fulltext 'id'=>'bfulltext', 'class'=>'search__input', placeholder => 'Hledej...'}
        <button type="submit" name="quickSearch" id="frm-searchForm-quickSearch" value="Hledat" class="search__submit">Vyhledat</button>
      {/form searchForm}

    </div>
    {*<!-- vyhledávání end -->*}

  </div>

</header>
{*<!-- hlavička end -->*}

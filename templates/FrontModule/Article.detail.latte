{$pageTitle       = (empty($article->arttitle) ? $article->artname :$article->arttitle)}
{$pageKeywords    = $article->artkeywords}
{$pageDescription = $article->artdescription}
{$pageImageCustom = $baseUri . "/" . ($article|getArtPicNameBg:'440x293':FALSE)}

{* drobky pro detal článku *}
{block #crumb}
<div class="breadcrumb">

  <div class="container-fluid">

    <a href="{$baseUri}">Úvod</a>
    <span>»</span>
    {if $article->arttypid===4}
    <a href="{plink Article:detail $recipe->artid, ($recipe->arturlkey|getUrlKey:$recipe->artname)}">{$recipe->artname}</a>
    <span>»</span>
    {/if}
    <strong>{$pageTitle}</strong> {if $adminLogIn} <a href="{plink :Admin:Article:edit, $article->artid}" target="admin">E</a>{/if}

  </div>

</div>
{/block}

{block #content}

  <div class="article">

    <div class="article__perex">

      {* hlavní text *}
      {$article->artbody|noescape}

    </div>

    <div class="row">

      <div class="col-xs-12 col-sm-7">

        {* blok vedle *}
        {$article->artbody2|noescape}

      </div>

      <div class="col-xs-12 col-sm-5">

        {* hlavni obrazek *}
        <p class="article__image"><img src="{$baseUri}/{$article|getArtPicName:'440x293'}" title="{$pageTitle}"></p>

      </div>

    </div>

    {foreach $images as $row}
    {if $iterator->isFirst()}
      <div class="article__images gallery">
    {/if}
        <a href="{$baseUri}/files/{$row->atafilename}" title="{$row->ataname}"><span><img src="{$baseUri}/files/{$row->atafilename}" alt="{$row->ataname}"></span></a>
    {if $iterator->isLast()}
      </div>
    {/if}
    {/foreach}

    {foreach $attachments as $row}
    {if $iterator->isFirst()}
    <div class="article__attachements">

      <h3>Přílohy</h3>

      <ul>
    {/if}
        <li><a href="{$baseUri}/files/{$row->atafilename}" target="_blank"><span class="icon icon--download"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#download" x="0" y="0" width="100%" height="100%"></use></svg></span>{$row->ataname} ({$row->atasize|bytes})</a></li>
    {if $iterator->isLast()}
      </ul>

    </div>
    {/if}
    {/foreach}

  </div>

{/block}

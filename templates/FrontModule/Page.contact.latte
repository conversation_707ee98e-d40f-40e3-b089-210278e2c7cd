{$pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{$pageKeywords    = $page->pagkeywords}
{$pageDescription = $page->pagdescription}

{block #content}

  <div class="article article--contact">

    <h1>{$page->pagname}</h1>

	<h2></h2>
	<h3>{_'Kontaktní adresa a kamenný obchod'}</h3>

	<div class="article__into">

	    {$page->pagbody|noescape}

	</div>

    {form contactForm}
    {* vypise chyby formulare *}
    {include ../@formErrors.latte form=>$form}

	<fieldset>

		<legend>Napište nám</legend>

		<p>
			{php echo $form['conname']->getLabel() }:<br>
			{php echo $form['conname']->getControl()->size(60) }
		</p>

		<p>
			{php echo $form['conmail']->getLabel()->class('required') }:<br>
			{php echo $form['conmail']->getControl()->size(60) }
		</p>

		<p>
			{php echo $form['congsm']->getLabel() }:<br>
			{php echo $form['congsm']->getControl()->size(30) }
		</p>
		<p>
			{php echo $form['connote']->getLabel() }<br>
			{php echo $form['connote']->getControl() }
		</p>
    <p>
			{php echo $form['recaptcha']->getLabel() }:<br>
			{php echo $form['recaptcha']->getControl() }
		</p>
		{php echo $form['save']->getControl()->value('Odeslat formulář') }

	</fieldset>

	{/form}

  </div>

{/block}

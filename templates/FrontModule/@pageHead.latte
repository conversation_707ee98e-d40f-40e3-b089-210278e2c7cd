<!DOCTYPE html>
<!--[if IE 8]>         <html class="no-js lt-ie9" lang="cs" prefix="og: http://ogp.me/ns#"> <![endif]-->
<!--[if IE 9]>         <html class="no-js ie9" lang="cs" prefix="og: http://ogp.me/ns#"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="cs" prefix="og: http://ogp.me/ns#"> <!--<![endif]-->
<head>

  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <title>{$pageTitle} - {$presenter->config["SERVER_NAMESHORT"]}</title>
  <meta name="description" content="{$pageDescription|strip|truncate:160, ''}">

  {*<!-- preload webových fontů -->*}
  <link rel="preload" as="font" type="font/woff2" href="{$baseUri}/fonts/lora-v16-latin-ext_latin-regular.woff2" crossorigin="crossorigin">
  <link rel="preload" as="font" type="font/woff2" href="{$baseUri}/fonts/lora-v16-latin-ext_latin-700.woff2" crossorigin="crossorigin">

  {*<!-- informace pro roboty -->*}
  {if isset($pageRobots)}
  <meta name="robots" content="{$pageRobots}">
  <meta name="googlebot" content="{$pageRobots}">
  {else}
  <meta name="robots" content="index,follow">
  <meta name="googlebot" content="index,follow,snippet,archive">
  {/if}

  <link rel="stylesheet" href="{$baseUri}/css/styles.css" type="text/css">

  {*<!-- favikony -->*}
  <link rel="apple-touch-icon" sizes="152x152" href="{$baseUri}/favicons/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="{$baseUri}/favicons/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="{$baseUri}/favicons/favicon-16x16.png">
  <link rel="manifest" href="{$baseUri}/favicons/site.webmanifest">
  <link rel="mask-icon" href="{$baseUri}/favicons/safari-pinned-tab.svg" color="#684f40">
  <link rel="shortcut icon" href="{$baseUri}/favicons/favicon.ico">
  <meta name="msapplication-TileColor" content="#684f40">
  <meta name="msapplication-config" content="{$baseUri}/favicons/browserconfig.xml">
  <meta name="theme-color" content="#684f40">

  {*<!-- vyloučení duplicit -->*}
  {if isset($canonicalUrl)}
  <link rel="canonical" href="{$canonicalUrl}">
  {/if}
  {*<!-- sociální sítě -->*}
  <meta property="og:title" itemprop="name" name="twitter:title" content="{$pageTitle} - {$presenter->config["SERVER_NAMESHORT"]}">
  <meta property="og:url" name="twitter:url" content="{plink //this}">
  <meta property="og:image" itemprop="image" name="twitter:image" content="{$pageImage}">
  <meta property="og:description" itemprop="description" name="twitter:description" content="{$pageDescription|strip|truncate:160, ''}">
  <meta name="google-site-verification" content="UAzlrsYYrDTkkAgppt0aqcw_OQ1LZcmCxtxMZSX85fQ" />
  {if !empty($neonParameters["facebook"]["pixelCodeId"])}
    <!-- Facebook Pixel Code -->

    <script>
    !function(f,b,e,v,n,t,s)
    {
    if(f.fbq)return;n=f.fbq=function(){
      n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)
    };
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)
    }(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', {$neonParameters["facebook"]["pixelCodeId"]|noescape});
    fbq('track', 'PageView');
    </script>

    <noscript><img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id={$neonParameters["facebook"]["pixelCodeId"]|noescape}&ev=PageView&noscript=1"
    /></noscript>

    <!-- End Facebook Pixel Code -->
  {/if}

</head>

{if !empty($neonParameters["google"]["ua"])}
  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id={$neonParameters["google"]["ua"]}"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', {$neonParameters["google"]["ua"]|noescape});
  </script>
{/if}

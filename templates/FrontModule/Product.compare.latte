{$pageTitle = 'Porovnání zboží'}

{block #content}

  <div class="article">

    <h1>{$pageTitle}</h1>

    {if count($products) > 0}
    <table class="table table--compare" cellpadding="3" cellspacing="0" border="1">
      <tr>
      <th>Produkt</th>
      {foreach $products as $pro}
      <td>
        <a href="{plink Product:detail, $pro->proid, ($pro|getProKey)}">
          <img src="{$baseUri}/{($pro|getProductPicName:'280x280')|noescape}" alt="{$pro->proname}" width="150"><br>
          <strong>{$pro->proname}</strong>
        </a>
      </td>
      {/foreach}
      </tr>
      <tr>
        <th>Cena</th>
            {foreach $products as $pro}
            <td><strong>{$pro->proprice|formatPrice}</strong></td>
            {/foreach}
      </tr>
      <tr>
        <th>Popis</th>
            {foreach $products as $pro}
        <td>{$pro->prodesc}</td>
            {/foreach}
      </tr>
      {foreach $paramsAll as $row}
      <tr>
        <th>{$row->prpname}</th>
        {foreach $products as $pro}
        <td>{ifset $pro["params"][$row->prpname]["prpvalue"]}{$pro["params"][$row->prpname]["prpvalue"]}{/ifset}</td>
        {/foreach}
      </tr>
      {/foreach}
      <tr>
        <th>Koupit</th>
          {foreach $products as $pro}
          <td class="center">
            <a href="{plink Product:detail, $pro->proid, ($pro|getProKey)}" class="btn btn--small btn--buy">Koupit</a>
            <a href="{plink Product:detail, $pro->proid, ($pro|getProKey)}" class="btn btn--small btn--info">Detail</a>
            <a href="{plink Product:compareDelete, $pro->proid}" class="btn btn--small btn--danger">Vymazat</a>
          </td>
          {/foreach}
      </tr>


    </table>
    {else}
    <p>Vyberte nejprve nějaké zboží k porovnání.</p>
    {/if}

</div>
{/block}

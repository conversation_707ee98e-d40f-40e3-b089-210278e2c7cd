{default $pageTitle=('Souhrn objednávky')}
{default pageRobots      => "nofollow,noindex"}

{* v kosiku drobecky nejsou *}
{block #crumb}
{/block}

{block #content}
<div class="order order--4">

  <div class="row">

    <div class="col-xs-12">

      <h1 class="order__header">{_'Souhrn objednávky'}</h1>

    </div>

  </div>

  <div class="row order-progress">

    <div class="col-xs-12 col-sm-3 order-progress__item"><a href="{plink default}">{_'Nákupní k<PERSON>'}</a></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><a href="{plink orderDelMode}">{_'Doprava a platba'}</a></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><a href="{plink orderContact}">{_'Dodac<PERSON> údaje'}</a></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><span class="is-active">{_'Souhrn objednávky'}</span></div>

  </div>

 <div class="order__article">

  <h2>{_'Souhrn informací Vaší objednávky'}</h2>
   <table class="order__table" cellpadding="3" cellspacing="0" border="1">
     <tbody>
     {foreach $basket->items as $id=>$value}
       <tr>
         <td class="order__product">
           <a href="{plink Product:detail, $productRows[$id]->proid, ($productRows[$id]|getProKey)}">
             <img src="{$baseUri}/{($productRows[$id]|getProductPicNameMaster:'280x280')|noescape}" alt="{$productRows[$id]->proname}">
           </a>
           <a href="{plink Product:detail, $productRows[$id]->proid, ($productRows[$id]|getProKey)}">
             {$productRows[$id]->proname}
             {if $basket->products[$id]->procode == 'VEJCE' && !empty($basket->ordmonth)}<br><small>objednáno na měsíc {$enumEggMonths[$basket->ordmonth]}</small>{/if}
           </a>
           {*nezahrnuje se do slevy*}
           {if $productRows[$id]->pronotdisc == 1}<span class="order__star">*</span>{/if}
         </td>
         <td class="order__count">{$value} ks
           {if (int)$productRows[$id]->proqty > 0 && (int)$productRows[$id]->proqty < (int)$basket->items[$id]}<br />Pouze {$productRows[$id]->proqty}ks{/if}
         </td>
         <td class="order__price">
           {($productRows[$id]->proprice*$value)|formatPrice}
         </td>
       </tr>
     {/foreach}
     </tbody>
     <tfoot>
     <tr class="order__sumprice">
       <td  class="order__product" colspan="2">{$delivery->delname}, {$payment->delname}{if !empty($basket->contact["orddeldatedel"])}, <strong>termín dodání:</strong> {$basket->contact["orddeldatedel"]|date:'d.m.Y'}{/if}</td>
       <td class="order__price">{$delivery->delprice|formatPrice}</td>
     </tr>

     {if !empty($basket->coupon)}
          <tr class="order__discount">
            <td class="order__product"><strong>{_'Slevový kupón'} {$basket->coupon->coucode}</strong></td>
            {if $basket->discountVal > 0}
            <td>
              <strong>{_'sleva'} {if $basket->discountVal > 0 && $basket->discountPer > 0}{$basket->discountPer}%{/if}</strong>
              {if $basket->coupon->couvaluelimit > 0}
                {if $basket->coupon->couvaluelimit > $basket->priceSumVatDisc}
                  Objednejte ještě minimálně za {($basket->coupon->couvaluelimit - $basket->priceSumVatDisc)|formatPrice}
                {else}

                {/if}
              {/if}
            </td>
            <td class="order__price"><strong>{if $basket->discountVal > 0}- {$basket->discountVal|formatPrice}{else}bez slevy{/if}</strong></td>
            {/if}
            <td><a href="{plink Basket:deleteCoupon}" class="control control--remove" onclick="return DeleteConfirmFront({_'Opravdu chcete smazat slevový kupón'});" title="{_'Odstranit slevový kupón z nákupního košíku'}">x</a></td>
          </tr>
          {/if}
     </tfoot>
   </table>

  <h2>{if empty($formData['ordstname'])}{_'Fakturační a současně doručovací adresa'}{$formData['ordstlname']}{else}Fakturační adresa{/if}</h2>

  <table class="table table--vertical" cellpadding="3" cellspacing="0" border="1">
    <tbody>
      <tr>
        <th>Jméno, příjmení:</th>
        <td>{$formData['ordiname']} {$formData['ordilname']}</td>
      </tr><tr>
        <th>Název firmy:</th>
        <td>{$formData['ordifirname']}</td>
      </tr><tr>
        <th>Ulice:</th>
        <td>{$formData['ordistreet']} {$formData['ordistreetno']}</td>
      </tr><tr>
        <th>PSČ, město:</th>
        <td>{$formData['ordipostcode']} {$formData['ordicity']}</td>
      </tr><tr>
        <th>Email:</th>
        <td>{$formData['ordmail']}</td>
      </tr><tr>
        <th>Telefon:</th>
        <td>{$formData['ordtel']}</td>
      </tr><tr>
        <th>IČ:</th>
        <td>{$formData['ordic']}</td>
      </tr><tr>
        <th>DIČ:</th>
        <td>{$formData['orddic']}</td>
      </tr>
    </tbody>
  </table>

  {if !empty($formData['ordstname'])}

    <h2>{_'Adresa dodání'}</h2>

    <table class="table table--vertical" cellpadding="3" cellspacing="0" border="1">
      <tbody>
        <tr>
          <td>Jméno, příjmení:</td>
          <td>{$formData['ordstname']} {$formData['ordstlname']}</td>
        </tr><tr>
          <td>Název firmy:</td>
          <td>{$formData['ordstfirname']}</td>
        </tr><tr>
          <td>Ulice:</td>
          <td>{$formData['ordststreet']} {$formData['ordststreetno']}</td>
        </tr><tr>
          <td>PSČ, město:</td>
          <td>{$formData['ordstpostcode']} {$formData['ordstcity']}</td>
        </tr>
      </tbody>
    </table>

  {/if}

  {if !empty($formData['ordnote'])}

    <h2>{_'Vzkaz k objednávce'}</h2>

    <table class="table table--vertical" cellpadding="3" cellspacing="0" border="1">
      <tbody>
        <tr>
          <td>{$formData['ordnote']|nl2br|noescape}</td>
        </tr>
      </tbody>
    </table>

  {/if}

   <div class="order__price-final">
  <div class="container-fluid">
    {_'CENA CELKEM'} (včetně DPH)
    <strong>{($basket->priceSumVat + $delivery->delprice - $basket->discountVal)|formatPrice}</strong>
  </div>
  </div>

  {form orderSumarizeForm}

    <div class="row order-controls">

      <div class="col-xs-12 col-sm-4">
        <a href="{plink 'orderContact'}" class="btn btn--back btn--big"><span class="icon icon--arrow-left"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#arrow-left" x="0" y="0" width="100%" height="100%"></use></svg></span> {_'O krok zpět'}</a>
      </div>

      <div class="col-xs-12 col-sm-8">
        <p>
          Odesláním formuláře potvrzujete souhlas s <a href="#">Obchodními podmínkami</a>
        </p>
        {ifset $form["maillist"]}
        <p>{input maillist} {label maillist /}</p>
        {/ifset}
        <button type="submit" id="frm-orderSumarizeForm-submit" name="_submit" class="btn--big btn--buy">Závazně objednat</button>
      </div>

    </div>

  {/form}

 </div>

</div>
{/block}

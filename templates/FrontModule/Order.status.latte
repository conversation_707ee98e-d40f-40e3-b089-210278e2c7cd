{$pageTitle  = 'Objednávka '.$order->ordcode}
{$pageRobots = "nofollow,noindex"}

{block #content}

  <div class="container">
    <h1>{$pageTitle}</h1>
  </div>

  <main class="order" role="main">

    <div class="container">
{if $order->ordpublished != 1}
  {ifset $textBlocks["objednavka_odeslana"]}
    {$textBlocks["objednavka_odeslana"]|noescape}
  {else}
    <h1>Děkujeme za vaší objednávku</h1>
    <p>Vaše objednávka byla úspěšně dokončena.</p>
    <p><strong>č<PERSON>lo obje<PERSON>ky: {$order->ordcode}</strong></p>
  {/ifset}
{else}
    <p>Vaše objednávka je ve stavu: <strong>{$enum_ordstatus[$order->ordstatus]|lower}</strong>.</p>
    {if $order->ordpaystatus === 1}
      <p>Ob<PERSON><PERSON><PERSON><PERSON><PERSON> je <strong>uh<PERSON>na.</strong></p>
    {/if}

    <p>
      <h3><PERSON><PERSON><PERSON><PERSON> způsob dopravy:</h3>
      <h4>{$delivery->delname}</h4>
      {$delivery->deltext1|nl2br|noescape}<br>
      {if !empty($order->orddeldatedel)}
        <h4>Předpokládaný termín doručení: <strong>{$order->orddeldatedel|date:'d.m.Y'}</strong></h4>
      {/if}
      {if !empty($order->ordparcode)}
        {_'Vaše číslo balíku je'}: {$order->ordparcode}.<br>
        {php
          if (!empty($delivery->delurlparcel)) {
            $url = str_replace('#CODE#', $order->ordparcode, $delivery->delurlparcel);
          }
        }
        {if !empty($url)}{_'Balík můžete sledovat'} <a href="{$url|noescape}">{_'na stránkách přepravce'}</a>.<br>{/if}
      {/if}
      <h3>Zvolený způsob platby:</h3>
      <h4>{$payment->delname}</h4>
    </p>
{/if}

{if $payment->delcode === 'creditcard' && $order->ordpaystatus === 0}
  <br />
  {php $link = $presenter->link(':Front:GoPay:create', $order->ordid, substr(md5($order->ordid . $order->ordcode), 0, 6))}
  <p>{_'Zvolil jsi on-line platbu'} <strong>{_'kartou'}</strong>. <a class="btn btn--info" id="final" href="{plink ':Front:GoPay:create', $order->ordid, substr(md5($order->ordid . $order->ordcode), 0, 6)}">{_'Zaplatit on-line'}</a></p>
  {if $order->ordpublished != 1}
  <script type="text/JavaScript">
    {*setTimeout("location.href = {$link|noescape};",3000);*}
  </script>
  {/if}
{/if}

{if $order->ordpaystatus === 0}
  {if !empty($qrPlatba)}
  <br />
  <h3>Zvolili jste platbu <strong>převodem na náš účet</strong>.</h3>
  <strong>QR platební kód pro vaši mobilní banku:</strong><br />
  <img src="https://quickchart.io/qr?text={$qrPlatba|noescape}" title="QR Platba" /><br>
  {/if}
  {if $payment->delcode == 'paybefore'}
  <br>
  <strong>Platební údaje:</strong><br />
  Číslo účtu: {$presenter->config["SERVER_ACCNO"]}<br />
  Variabilní symbol: {$order->ordcode}<br />
  Částka: {$order->ordpricevat|formatPrice}
  {/if}
{/if}


  </main>

{* Měření *}
{if $order->ordpublished != 1}
  {* Google Analytics eComerce *}
{php
  $dphSum = $order->ordpriceinclvat - $order->ordpricenovat;
  $proIds = array();
  $productsSum = 0;
}
<script type="text/javascript">
    gtag('event', 'purchase', {
        "transaction_id": {$order->ordcode|noescape},
        "affiliation": 'eShop' + {$presenter->config["SERVER_NAMESHORT"]},
        "value": {$order->ordpricenovat|noescape},
        "currency": "CZK",
        "tax": {$dphSum|noescape},
        "shipping": {$order->orddelprice|noescape},
        "items": [
            {foreach $order->items as $row}

              {php
                $proIds[] = $row->oriproid;
                $productsSum += ($row->oriprice * $row->oriqty);
              }

            {
                "id": {$row->oriproid},
                "name": {$row->oriname},
                "brand": {$row->manname},
                "category": {$row->catpath},
                "quantity": {$row->oriqty|noescape},
                "price": {$row->oripricenovat|noescape}
            }
            {if !$iterator->last},{/if}
            {/foreach}
        ]
    });
</script>

  {* Heureka konverze PPC *}
  {if !empty($neonParameters["heureka"]["KeyMereniKonverzi"])}
  <script type="text/javascript">
  var _hrq = _hrq || [];
      _hrq.push(['setKey', {$neonParameters["heureka"]["KeyMereniKonverzi"]}]);
      _hrq.push(['setOrderId', {$order->ordid}]);
  {foreach $order->items as $row}
      _hrq.push(['addProduct', {$row->oriname}, {$row->oriprice}, {$row->oriqty}]);
  {/foreach}
      _hrq.push(['trackOrder']);

  (function() {
      var ho = document.createElement('script'); ho.type = 'text/javascript'; ho.async = true;
      ho.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.heureka.cz/direct/js/cache/1-roi-async.js';
      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ho, s);
  })();
  </script>
  {/if}

  {if !empty($neonParameters["google"]["conversionId"])}
  <!-- Google Code for Objednavka Conversion Page -->
  <script type="text/javascript">
    /* <![CDATA[ */
    var google_conversion_id = {$neonParameters["google"]["conversionId"]};
    var google_conversion_language = "cs";
    var google_conversion_format = "1";
    var google_conversion_color = "ffffff";
    var google_conversion_label = {$neonParameters["google"]["conversionLabel"]};
    var google_conversion_value = {$order->ordpricevat|noescape};
    var google_conversion_currency = "CZK";
    var google_remarketing_only = false;
    /* ]]> */
  </script>
  <script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
  </script>
  <noscript>
    <div style="display:inline;">
      <img height="1" width="1" style="border-style:none;" alt="" src="//www.googleadservices.com/pagead/conversion/1030312203/?value={$order->ordpricevat|noescape}&amp;currency_code=CZK&amp;label=ivjtCM_6rQEQi6Kl6wM&amp;guid=ON&amp;script=0"/>
    </div>
  </noscript>
  {/if}

  {if !empty($neonParameters["zbozicz"]["IdMereniKonverzi"])}
  <script>
    (function(w,d,s,u,n,k,c,t){
      w.ZboziConversionObject=n;w[n]=w[n]||function(){
      (w[n].q=w[n].q||[]).push(arguments)
      };
      w[n].key=k;c=d.createElement(s);
      t=d.getElementsByTagName(s)[0];c.async=1;c.src=u;t.parentNode.insertBefore(c,t)
    })(window,document,"script","https://www.zbozi.cz/conversion/js/conv.js","zbozi",{$neonParameters["zbozicz"]["IdMereniKonverzi"]|noescape});

     zbozi("setOrder",{
        "orderId": {$order->ordcode},
        "totalPrice": {$order->ordpriceinclvat}
     });
     zbozi("send");
  </script>
  {/if}


  {if !empty($neonParameters["sklik"]["IdMereniKonverzi"])}
  <iframe width="119" height="22" frameborder="0" scrolling="no" src="//c.imedia.cz/checkConversion?c={$neonParameters["sklik"]["IdMereniKonverzi"]|noescape}&color=ffffff&v={$order->ordpricevat|noescape}"></iframe>
  {/if}

  {php
    $proIdsStr = '["'.implode('","', $proIds).'"]';
  }
  <script>
    fbq('track', 'Purchase', {
      content_ids: {$proIdsStr},
      content_type: 'product_group',
      value: {$productsSum},
      currency: {$curKey}
    });
  </script>
{/if}

{/block}

{$pageTitle  = 'Ko<PERSON><PERSON>'}
{$pageRobots = "nofollow,noindex"}

{php
$GLOBALS["ecommProId"] = '';
$GLOBALS["ecommPageType"] = 'cart';
$GLOBALS["ecommTotalValue"] = 0;
}

{* v kosiku drobecky nejsou *}
{block #crumb}
{/block}

{block #content}

{php
  $proIds = array();
}
{if $presenter->getParam('ok') != 1}
<div class="order order--1">

  <div class="row">

    <div class="col-xs-12">

      <h1 class="order__header">{_'<PERSON><PERSON><PERSON> nákupní ko<PERSON>'}</h1>

    </div>

  </div>

  {snippet basket}
  {if count($basket->items) > 0}

    <div class="row order-progress">

      <div class="col-xs-12 col-sm-3 order-progress__item"><span class="is-active">{_'Nákup<PERSON><PERSON> k<PERSON>'}</span></div>
      <div class="col-xs-12 col-sm-3 order-progress__item"><span>{_'Doprava a platba'}</span></div>
      <div class="col-xs-12 col-sm-3 order-progress__item"><span>{_'Dodací údaje'}</span></div>
      <div class="col-xs-12 col-sm-3 order-progress__item"><span>{_'Souhrn objednávky'}</span></div>

    </div>

    {form basketForm}
    <div class="row order-content">

      <div class="col-xs-12">

        {php $form->render('errors') }

        {*<p>Akční zboží označené červenou hvězdičkou se do slev <strong>nezapočitává</strong>.</p>*}

        {if $basket->durId > 1}
          <p>Vybrané zboží vyžaduje speciální dopravu, proto nabídka doprav v následujícím kroku bude omezena!</p>
        {/if}

        <table class="order__table" cellpadding="3" cellspacing="0" border="1">
        <tbody>
        {foreach $basket->items as $id=>$value}
          {php
            $proIds[] = $basket->products[$id]->proid;
            $GLOBALS["ecommTotalValue"] += ($basket->products[$id]->proprice * (int)$basket->items[$id]);
          }
          <tr>
            <td class="order__product">
              <a href="{plink Product:detail, $basket->products[$id]->proid, ($basket->products[$id]|getProKey)}">
                <img src="{$baseUri}/{($basket->products[$id]|getProductPicNameMaster:'280x280')|noescape}" alt="{$basket->products[$id]->proname}">
              </a>
              <a href="{plink Product:detail, $basket->products[$id]->proid, ($basket->products[$id]|getProKey)}">
              {$basket->products[$id]->proname}
              {if ($basket->products[$id]|isEgg) && !empty($basket->ordmonth)}<br><small>objednáno na měsíc {$enumEggMonths[$basket->ordmonth]}</small>{/if}
              </a>
              {*nezahrnuje se do slevy*}
              {if $basket->products[$id]->pronotdisc == 1}<span class="order__star">*</span>{/if}
            </td>
            <td class="order__count">
              <span class="control--count">
                {php echo $form['count_'.$basket->products[$id]->proid]->getControl() }
                <span class="control control--plus"><span class="icon icon--plus"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#plus" x="0" y="0" width="100%" height="100%"></use></svg></span></span> <span class="control control--minus"><span class="icon icon--minus"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#minus" x="0" y="0" width="100%" height="100%"></use></svg></span></span>
              </span>
              {if (int)$basket->products[$id]->proqty > 0 && (int)$basket->products[$id]->proqty < (int)$basket->items[$id]}<br />Pouze {$basket->products[$id]->proqty}ks{/if}
              <a href="{plink Basket:delete, $basket->products[$id]->proid}" class="control control--remove" onclick="return DeleteConfirmFront({_'Opravdu chcete smazat položku'} {$basket->products[$id]->proname});" title="{_'Odstranit z nákupního košíku'}"><span class="icon icon--close"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use></svg></span></a>
            </td>
            <td class="order__price">
              {($basket->products[$id]->proprice*$value)|formatPrice}
            </td>
          </tr>
        {/foreach}
        </tbody>
        <tfoot>
          <tr class="order__price-sum">
            <td>{_'Cena s DPH'}</td>
            <td colspan="2" class="order__price">{$basket->priceSumVat|formatPrice}</td>
          </tr>
          {if !empty($basket->coupon)}
          <tr class="order__discount">
            <td class="order__product"><strong>{_'Slevový kupón'} {$basket->coupon->coucode}</strong></td>
            {if $basket->discountVal > 0}
            <td>
              <strong>{_'sleva'} {if $basket->discountVal > 0 && $basket->discountPer > 0}{$basket->discountPer}%{/if}</strong>
              {if $basket->coupon->couvaluelimit > 0}
                {if $basket->coupon->couvaluelimit > $basket->priceSumVatDisc}
                  Objednejte ještě minimálně za {($basket->coupon->couvaluelimit - $basket->priceSumVatDisc)|formatPrice}
                {else}

                {/if}
              {/if}
            </td>
            <td class="order__price"><strong>{if $basket->discountVal > 0}- {$basket->discountVal|formatPrice}{else}bez slevy{/if}</strong></td>
            {/if}
            <td><a href="{plink Basket:deleteCoupon}" class="control control--remove" onclick="return DeleteConfirmFront({_'Opravdu chcete smazat slevový kupón'});" title="{_'Odstranit slevový kupón z nákupního košíku'}">x</a></td>
          </tr>
          {/if}
        </tfoot>
        </table>

        {ifset $form['coupon']}
          <div class="order__coupon">
            Slevový kupón: {php echo $form['coupon']->getControl()->class('input--25')  } {php echo $form['recalc']->getControl()->class('btn')  }
          </div>
        {/ifset}

      </div>

    </div>

    <div class="order__price-final">
      {_'CENA CELKEM'} (včetně DPH)
      <strong>{$basket->priceSumTotalVat|formatPrice}</strong>
    </div>

    <div class="order-info">

      {if ($delFreeLimitByBasket !== NULL)}

      {if ((bool)$basket->delFree) || $delFreeLimitByBasket <= $basket->priceSumVat}
        {* POŠTOVNÉ - je dosaženo *}
        <p class="order-info__item order-info--delivery"><strong>Doprava zdarma</strong> - poštovné {if (!empty(delFreeNameByBasket))} pro dopravu <strong>{$delFreeNameByBasket}</strong>{/if} je na nás</p>
      {else}
        {* POŠTOVNÉ - není dosaženo *}
        <p class="order-info__item order-info--delivery"><strong>Doprava zdarma</strong> - <span>objednejte ještě za {($delFreeLimitByBasket-$basket->priceSumVat)|formatPrice}</span> a získáte dopravu zdarma.</p>
      {/if}
      {/if}
      {if $basket->weightSum > $presenter->config["WEIGHT_LIMIT"]}
        <p class="order-info__item order-info--weight">
          <strong>Vaše objednávka přesahuje váhový limit {$presenter->config["WEIGHT_LIMIT"]}Kg</strong>, pro odeslání běžnou přepravní službou. Cena za dopravu bude stanovena po odeslání objednávky na základě <a href="{plink Page:detail 'doprava-platba'}">platného ceníku pro dodávku zboží</a>.
        </p>
      {/if}

      {if !empty($textBlocks["basket_add"])}
        <div class="order-info__item order-info--text">
          {$textBlocks["basket_add"]|noescape}
        </div>
      {/if}

    </div>

    <div class="order__place">

        <span class="icon icon--location">
          <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <use xlink:href="{$baseUri}/img/icons.svg#location" x="0" y="0" width="100%" height="100%"></use>
          </svg>
        </span>

        <strong>Zadejte oblast doručení:</strong> {input ordregid}<br>
        <em>Podle oblasti doručení Vám můžeme nabídnout nejvhodnější dopravu.</em>
    </div>

    <iframe src="https://www.google.com/maps/d/u/0/embed?mid=1frmXbsjqstj6EAMEBtclR-TfgSioXs0&ehbc=2E312F&z=6" width="640" height="480"></iframe>

    <div class="row order-controls">

      <div class="col-xs-12 col-sm-4">
       <a href="javascript:history.go(-1)" class="btn btn--big btn--back"><span class="icon icon--arrow-left"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#arrow-left" x="0" y="0" width="100%" height="100%"></use></svg></span> {_'Pokračovat v nákupu'}</a>
      </div>

      <div class="col-xs-12 col-sm-8">
        <button id="frm-basketForm-recalc" name="recalc" class="btn--big btn--back"><span class="icon icon--calc"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#calc" x="0" y="0" width="100%" height="100%"></use></svg></span> {_'Přepočítat'}</button>
        <button id="frm-basketForm-makeorder" name="makeorder" class="btn--big btn--buy">{_'Pokračovat k dopravě a platbě'} <span class="icon icon--arrow-right"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#arrow-right" x="0" y="0" width="100%" height="100%"></use></svg></span></button>
      </div>

    </div>

    {/form}

  {else}
    <p class="order__empty">{_'Košík je prázdný'}...</p>
  {/if}
  {/snippet}
  {else}

  {/if}

</div>

  {php
  $GLOBALS["ecommProId"] = '["'.implode('","', $proIds).'"]';
  }
  <script>
    fbq('track', 'AddToCart', {
      content_ids: {$GLOBALS["ecommProId"]},
      content_type: 'product_group',
      value: {$GLOBALS["ecommTotalValue"]},
      currency: {$curKey}
    });
  </script>


  {if !empty($GLOBALS["ecommProId"])}
    <script type="text/javascript">
        var google_tag_params = {
            ecomm_prodid: {$GLOBALS["ecommProId"]},
            ecomm_pagetype: {$GLOBALS["ecommPageType"]},
            ecomm_totalvalue: {$GLOBALS["ecommTotalValue"]},
        };
    </script>
  {/if}

{/block}

{block #footerBlock}
  {include parent}
{* našeptávač PSČ *}
<script src="{$baseUri|noescape}/js/libs/jquery.autocomplete.js" type="text/javascript"></script>
<script>
  if ($('#postcode_ac').length) {
    $('#postcode_ac').autocomplete({
      serviceUrl: {$presenter->link('postCodesAc')},
      onSelect: function (suggestion) {
          $('#postcode_ac').val(suggestion.postcode);
          $('#postname_ac').val(suggestion.place);
      }
    })
  }

  $(function () {
    $('body').on('change', '.countInput', function () {
      naja.makeRequest('POST', {link basketChangeCount!}, { proId: $(this).data("proid"), qty: $(this).val()},  { history:false });
    });
  });
</script>

{/block}

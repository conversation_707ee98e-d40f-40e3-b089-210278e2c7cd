{$pageTitle       = $catNameFull}
{$pageKeywords    = $catalogData->catkeywords}
{$pageDescription = $catalogData->catdescription}
{$pageImageCustom = ""}
{php
$canonicalUrl = $presenter->link('//detail', array('id'=>$catalogData->catid, 'key'=> ($catalogData|getCatKey), 't'=>array(), 'f'=>array(), 'o'=>'', 'pF'=>'', 'pT'=>'', 'm'=>array()));

$GLOBALS["ecommProId"] = '';
$GLOBALS["ecommPageType"] = 'category';
$GLOBALS["ecommTotalValue"] = 0;
}

{* drobky pro catalog *}
{block #crumb}
<div class="breadcrumb">

  <div class="container-fluid">

    <a href="{$baseUri}">Úvod</a>
    {foreach $catalogPath as $row}
       <span>»</span>
      {if $iterator->isLast()}
        <strong>{$row->catname}</strong>
      {else}
        <a href="{plink Catalog:detail, $row->catid, ($row|getCatKey)}">{$row->catname}</a>
      {/if}
    {/foreach}

  </div>

</div>
{/block}

{block #content}

{* <!-- kategorie produktu start --> *}
<div class="category">

  {* <!-- popis start --> *}
  {if !empty($catalogData->catdesc)}
    <div class="category__description">
    <div class="row">

      {* <!-- obrázek start --> *}
      <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 center">
        {php $catImg = ($catalogData|getCatalogPicName:'500x300':FALSE)}
        {if !empty($catImg)}
          <img src="{$baseUri}/{$catImg}" alt="{$catalogData->catname}" height="100">
        {/if}
      </div>
      {* <!-- obrázek end --> *}

      <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6">
          {$catalogData->catdesc|noescape}
      </div>

    </div>
    </div>
  {/if}
  {* <!-- popis end --> *}


  {* <!-- podkategorie start --> *}
  {if !empty($catalogSubItems)}
  <div class="row row--autoclear">
  {foreach $catalogSubItems as $key => $row}
    {if $iterator->isFirst()}
    {/if}
      <div class="col-sm-6 col-md-4 col-lg-3 category__item">
        <a href="{plink Catalog:detail, $row->catid, ($row|getCatKey)}">
          {$row->catname}
        </a>
      </div>
    {if $iterator->isLast()}
    {/if}
  {/foreach}
  </div>
  {/if}
  {* <!-- podkategorie end --> *}

  {* <!-- filtrování start -->
  {form catalogSearchForm}
    <fieldset class="filter">
      <legend>Filtrování</legend>

    <div class="alert alert--danger" n:if="$form->hasErrors()">
    <ul>
      <li n:foreach="$form->errors as $error">{($error)}</li>
    </ul>
    </div>

    <p>
    {foreach $form['t']->components as $item}
      {label $item: class => "label label--filter"}{input $item:} {$item->caption}{/label}
    {/foreach}
    </p>

    <p>
    {foreach $form['m']->components  as $item}
      {label $item: class => "label label--filter"}{input $item:} {$item->caption}{/label}
    {/foreach}
    </p>

    <p>
      <label><strong>Cenové rozpětí:</strong></label>
      {input pF class=>"input--25"} - {input pT class=>"input--25"}
    </p>

    <p>{input search class => 'btn'}</p>

  </fieldset>
  {/form}
  *}

  {* <!-- filtrování výsledky start --> *}
  {* hodnoty ve filtru *}
  {php
    $filterNotEmpty = (!empty($formVals["m"]) || !empty($formVals["t"]) || !empty($formVals["pF"]) || !empty($formVals["pT"]));
  }
  {if $filterNotEmpty}
    <div class="filter__results">
    <strong>Je vybráno:</strong>
  {/if}

  {foreach $formVals as $key => $value}
  {if $key == 'm'}
    {foreach $value as $key => $name}
    {if $iterator->isFirst()}
    <strong>Výrobci:</strong>
    {/if}
    <span class="label label--result">{$name} <a href="{plink 'this', 'um'=>$key}"> <span class="icon icon--close"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use></svg></span></a></span>
    {/foreach}
  {elseif $key == 't'}
    {foreach $value as $key => $name}
    {if $iterator->isFirst()}
    <strong>Typ:</strong>
    {/if}
    <span class="label label--result">{$name} <a href="{plink 'this', 'ut'=>$key}"> <span class="icon icon--close"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use></svg></span></a></span>
    {if $iterator->isLast()}
    {/if}
    {/foreach}
  {/if}
  {/foreach}

  {if !empty($formVals["pF"]) || !empty($formVals["pT"])}
    <strong>Cena:</strong>
    {if !empty($formVals["pF"])}
      <span class="label label--result">od: {$formVals["pF"]|formatPrice} <a href="{plink 'this', 'pF'=>NULL}"> <span class="icon icon--close"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use></svg></span></a></span>
    {/if}

    {if !empty($formVals["pT"])}
      <span class="label label--result">do: {$formVals["pT"]|formatPrice} <a href="{plink 'this', 'pT'=>NULL}"> <span class="icon icon--close"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use></svg></span></a></span>
    {/if}
  {/if}
  {if $filterNotEmpty}
    <span class="label label--result">Vymazat vše <a href="{plink 'this', 'm'=>NULL, 't'=>NULL, 'pF'=>NULL, 'pT'=>NULL}"> <span class="icon icon--close"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use></svg></span></a></span>
    </div>
  {/if}

  {* <!-- filtrování výsledky end --> *}

  {foreach $saleStatProducts as $key => $row}
    {if $iterator->isFirst()}
      <h2>{_'Nejprodávanější zboží v kategorii'} {$rootCatalog->catname}</h2>
      <table>
      <tbody>
    {/if}
      <tr>
        <td>{$iterator->getCounter()}</td>
        <td><img src="{$baseUri}/{($row|getProductPicName:'280x280')}" alt="{$row->proname}" width="60" height="60"></td>
        <td><a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{$row->proname} {$row->manname}</a></td>
        <td>{$row->proprice|formatPrice}</td>
      </tr>
    {if $iterator->isLast()}
      </tbody>
      </table>
    {/if}
  {/foreach}

</div>
{* <!-- kategorie produktu end --> *}

{* <!-- výpis produktů start --> *}
{include @productsList.latte, products => $productsData, showPagination => TRUE}
{* <!-- výpis produktů end --> *}

  {if !empty($GLOBALS["ecommProId"])}
    <script type="text/javascript">
      var google_tag_params = {
        ecomm_prodid: {$GLOBALS["ecommProId"]},
        ecomm_pagetype: {$GLOBALS["ecommPageType"]},
        ecomm_totalvalue: {$GLOBALS["ecommTotalValue"]},
      };
    </script>
  {/if}

{/block}

{default $pageTitle=('<PERSON><PERSON><PERSON> k<PERSON> - v<PERSON><PERSON><PERSON>r dopravy a platby')}
{default pageRobots      => "nofollow,noindex"}

{* v kosiku drobecky nejsou *}
{block #crumb}
{/block}

{block #content}

<script>
  var delIdSelected={(!empty($delid) ? $delid : 0)};
  var payIdSelected={(!empty($payid) ? $payid : 0)};
</script>

<div class="order order--2">

  <div class="row">

    <div class="col-xs-12">

      <h1 class="order__header">{$pageTitle}</h1>

    </div>

  </div>

  <div class="row order-progress">

    <div class="col-xs-12 col-sm-3 order-progress__item"><a href="{plink default}">{_'Nákupní ko<PERSON>'}</a></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><span class="is-active">{_'Doprava a platba'}</span></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><span>{_'Dodací údaje'}</span></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><span>{_'Souhrn objednávky'}</span></div>

  </div>

  {if $onlyPerDelivery}
      <div class="alert alert--danger" role="alert"><p>
          Na Vaši adresu nejsme schopni <strong>zboží doručit</strong>, objednávku si můžete <strong>vyzvednout osobně na jednom z našich odběrných míst níže</strong>.
      </p></div>
  {/if}

  {if $basket->eggTypeOrder === 'mix'}
      <div class="alert alert--info" style="background: #e08700;" role="alert"><p>
          Do košíku jste přidali vajíčko, které pštros snáší pouze pár měsíců v roce. O přesném datu doručení vas budeme informovat pár dnů předem.<br>
          Zbytek objednávky doručíme v termín, který si vybarete.
      </p></div>
  {/if}


  <div class="row">

    {form orderDelModeForm}
    {* vykresleni chyb pokud ma vypnuty JS *}
    {include ../@formErrors.latte form=>$form}

    <div class="row row--inner order-delivery">

      <div class="col-xs-12 col-md-6">

        <h2>Vyberte si způsob doručení</h2>

        <div class="order-delivery__types">

          {foreach $delModes as $row}
          <div class="order-delivery__item">

            <div class="order-delivery__type" id="delid_{$row->delid}" rel="payments_{$row->delid}">

              <h3 class="order-delivery__name">
                <input type="radio" name="delid_type">
                <img src="{$baseUri}/{$row|getDelModePicName}" alt="{$row->delname}">
                {$row->delname}
                <small>{if $row->delprice == 0}(doprava ZDARMA){else}cena {$row->delprice|formatPrice}{/if}</small>
                {if $row->delcode == 'HEUREKAPOINT'}
                  <br /><br />Výběr pobočky:
                {php echo $form['orddelspec']->control }
                {/if}
              </h3>
              <div>
                {label 'term_' . $row->delid /} {input 'term_' . $row->delid}
              </div>
              <div class="order-delivery__content">
                {if !empty($row->deltext) || !empty($row->delurlmap)}
                <p class="order-delivery__description">
                  {if !empty($row->deltext)}{$row->deltext|nl2br|noescape}{/if}
                  {if !empty($row->delurlmap)}<a class="map" href="{$row->delurlmap|noescape}" target="_blank">Orientační mapa</a>{/if}
                </p>
                {/if}
              </div>

            </div>

          </div>
          {/foreach}

        </div>

      </div>

      <div class="col-xs-12 col-md-6">

        <h2>Vyberte si způsob platby</h2>

        <div class="order-delivery__payments">

          {foreach $delModes as $row}
            <div class="order-delivery__payment" id="payments_{$row->delid}">
                <ul>
                {foreach $payModes[$row->delid] as $irow}
                  <li>
                    {label orddelid:$irow->delid}
                      {input orddelid:$irow->delid}
                      {$irow->delname} - <strong>{if $irow->delprice == 0}ZDARMA{else} cena {$irow->delprice|formatPrice}{/if}</strong>
                    {/label}
                  </li>
                {/foreach}
                </ul>
                <p><iframe src="https://www.google.com/maps/d/u/0/embed?mid=1frmXbsjqstj6EAMEBtclR-TfgSioXs0&ehbc=2E312F&z=6" width="640" height="480"></iframe></p>
            </div>
          {/foreach}
        </div>

      </div>

    </div>

    <div class="row row--inner">

      <div class="col-xs-12 order__price-delivery">
        Cena za dopravu a platbu:
        <strong id="priceDeliveryVat">{0|formatPrice}</strong>
      </div>

      <div class="col-xs-12 order__price-final">
        Cena celkem včetně dopravy
        <strong id="priceSumTotalVat">{$basket->priceSumTotalVat|formatPrice}</strong>
      </div>

    </div>

    <div class="row row--inner order-controls">

      <div class="col-xs-12 col-sm-6">
       <a href="{plink Basket:default}" class="btn btn--secondary btn--big"><span class="icon icon--arrow-left"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#arrow-left" x="0" y="0" width="100%" height="100%"></use></svg></span> {_'zpět do košíku'}</a>
      </div>

      <div class="col-xs-12 col-sm-6">
        <button type="submit" id="frm-orderDelModeForm-submit" name="_submit" class="btn btn--buy btn--big">{_'Pokračovat k dodacím údajům'} <span class="icon icon--arrow-right"><svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="/img/icons.svg#arrow-right" x="0" y="0" width="100%" height="100%"></use></svg></span></button>
      </div>

    </div>

  {/form}

</div>

  <script>
    fbq('track', 'InitiateCheckout');
  </script>

{/block}

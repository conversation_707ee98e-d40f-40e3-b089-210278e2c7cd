<div class="col-xs-6 col-sm-6 col-md-4 col-lg-4 product__wrapper">

  <article class="product" role="article">

    <h2 class="product__header">
      <a href="{plink Product:detail, $product->proid, ($product|getProKey)}" title="{$product->proname}">{$product->proname}</a>
    </h2>

    <div class="product__image">

      {* <!-- štítky --> *}
      {include @productLabel.latte, product => $product}
      {* <!-- štítky --> *}

      <a href="{plink Product:detail, $product->proid, ($product|getProKey)}">
        <img src="{$baseUri}/img/no.jpg" data-src="{$baseUri}/{$product|getProductPicName:'354x354'}" alt="{$product->proname}">
      </a>

      <div class="product__price">

        {if $product->proismaster==1} od {/if}<strong>{$product->proprice|formatPrice}</strong> {_'včetně DPH'}

        {*
        {if $product->proaccess == 0}
          <br><span class="stock stock--available">Skladem</span>
        {else}
          <br><span class="stock stock--unavailable">Nedostupné</span>
        {/if}
        *}

      </div>

      <div class="product__controls">
        {if $product->proismaster==1 || ($product|isEgg)}
          <a href="{plink Product:detail, $product->proid, ($product|getProKey)}" class="btn btn--buy">
            <span class="icon icon--basket">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#basket" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
          </a>
        {else}

          {if ($config["CHECK_STOCK"] == 1 && $product->proaccess == 0) || $config["CHECK_STOCK"] == 0}
          <a href="{plink Basket:add, $product->proid}" class="btn btn--buy">
            <span class="icon icon--basket">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#basket" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
          </a>
          {/if}

        {/if}
      </div>

    </div>

    {*
    <div class="product__info">
      {$product->prodescs|truncate:200}
    </div>
    *}

  </article>

</div>

      {* pagination *}
      {if $paginator->pageCount > 1}
        <nav class="pagination" role="navigation">
        {if $paginator->page > 1}
          <a href="{link this, 'page' => $paginator->page - 1}" class="pagination__prev">Předchozí</a>
        {/if}
        {foreach $steps as $step}
          {if $step == $paginator->page}
            <span class="paginator__current">2</span>
          {else}
            <a href="{link this, 'page' => $step}">{$step}</a>
          {/if}
        {/foreach}
        {if $paginator->page < $paginator->pageCount}
          <a href="{link this, 'page' => $paginator->page + 1}" class="pagination__next">Následující</a>
        {/if}
        </nav>
      {/if}

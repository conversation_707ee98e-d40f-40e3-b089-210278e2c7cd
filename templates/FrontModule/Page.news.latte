{$pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{$pageKeywords    = $page->pagkeywords}
{$pageDescription = $page->pagdescription}

{block #content}

  {$page->pagbody|noescape}
   <h1><span>P<PERSON>eh<PERSON> novinek</span></h1>
  <div id="newsbox">
  {* vypis novinek *}
  {foreach $rows as $row}
 <div class="news">
  <a href="{plink New:detail $row->newid, ($row->newtitle|webalize)}"><img src="{$baseUri}/{($row|getNewPicName:'280x280')|noescape}" alt="{$row->newtitle}" /></a>
  <h3><a href="{plink New:detail $row->newid, ($row->newtitle|webalize)}">{$row->newtitle}</a></h3>
  <p>{$row->newannot}</p>
  <p class="detail"><a href="{plink New:detail $row->newid, ($row->newtitle|webalize)}"><PERSON><PERSON>t více</a></p>
  </div>
  {/foreach}
  </div>

  <!-- stránkování start -->
  {control paginator}
  <!-- stránkování end -->

{/block}

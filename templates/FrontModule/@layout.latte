{default $urlkey => ''}
{default $lng    => $lang}

{default pageTitle       => $presenter->config["INDEX_TITLE"]}
{default pageDescription => $presenter->config["INDEX_DESC"]}
{default pageImage       => $baseUri."/img/content-header-back.jpg"}

{if !empty($pageImageCustom) && $pageImageCustom != $baseUri."/"}
  {php $pageImage = $pageImageCustom;}
{/if}

{include @pageHead.latte pageTitle => $pageTitle, pageDescription => $pageDescription, pageImage => $pageImage}

<body>
{* Page Header *}
{snippetArea header}
{include @pageHeader.latte}
{/snippetArea}
{if $presenter->isLinkCurrent('Basket:*')}

{else}

  {if $presenter->isLinkCurrent('Homepage:default')}

    {include @homepageSlider.latte}

  {else}

<div class="content-header">

  <h1>{$pageTitle}</h1>

  <p>{$pageDescription}</p>

  {* jak změním tady ten obrázek záhlaví? src pro ten obrázek záhlaví bude src="{$pageImage}" *}
  {* vypiš tu prosím, ale pouze pokud bude nastaven, jinak nevypisovat nic: style="background-image:url(/img/content-header-back.jpg);" *}
  <span class="content-header__image" {if !empty($pageImage)}style="background-image:url({$pageImage|noescape});"{/if}>&nbsp;</span>

</div>
  {/if}

{*<!-- hlavní menu start -->*}
{include @pageMenuTop.latte}
{*<!-- hlavní menu end -->*}
{/if}

{*<!-- drobečková navigace start -->*}
{block #crumb}
<div class="breadcrumb">

  <div class="container-fluid">

    <a href="{plink Homepage:default}">Úvod</a> <span>»</span> <strong>{$pageTitle}</strong>

  </div>

</div>
{/block}
{*<!-- drobečková navigace end -->*}

<div class="container-fluid">

    <div class="row">

      {* <!-- produktové menu start --> *}
      <div class="col-sm-4 col-md-3 nav-product__wrapper">
        {*include @pageMenuProducts.latte*}
      </div>
      {* <!-- produktové menu end --> *}

      {if !$showMenuLeft}
      <div class="col-xs-12">
      {else}
      <div class="col-sm-8 col-md-9">
      {/if}

        {*<!-- obsah start -->*}
        <main class="content" role="main">

          {*<!-- chybové hlášky start -->*}
          {foreach $flashes as $flash}
            <div class="alert alert--{$flash->type}" role="alert"><p>{$flash->message}</p></div>
          {/foreach}
          {*<!-- chybové hlášky end -->*}

          {include #content}

        </main>
        {*<!-- obsah end -->*}

      </div>

    </div>

  </div>

  {* patička *}
  {include @pageFooter.latte}

  <script src="{$baseUri}/js/libs/svg4everybody.min.js"></script>
  <script>svg4everybody();</script>

  <script src="{$baseUri}/js/libs/jquery-1.12.4.min.js"></script>
  {ifCurrent Homepage:default}<script src="{$baseUri}/js/libs/slick/slick.min.js"></script>{/ifCurrent}

  <script src="https://www.google.com/recaptcha/api.js"></script>

  <script src="https://unpkg.com/naja@2/dist/Naja.min.js"></script>
  <script type="text/javascript">
    naja.initialize();
  </script>

  {ifCurrent Basket:orderDelMode}
  <script>

  function number_format(num) {
    return parseInt( num ).toLocaleString('cs-CZ');
  }

  $("input:radio[name='orddelid']").change(function() {
    var payModeName = '';
    var payModePrice = 0;
    var payModeName = '';
    var delModePrice = 0;
    var priceSum = {$priceSumVat};
    var delid = $(this).val();
    {foreach $payModesJs as $pay}
      {if isset($delModes[$pay->delmasid])}
      if (delid == {$pay->delid|noescape}) {
    payModeName = {$pay->delname};
    payModePrice = {$pay->delprice|noescape};
    delModeName = {$delModes[$pay->delmasid]->delname};
    delModePrice = {$delModes[$pay->delmasid]->delprice|noescape};
      }
      {/if}
    {/foreach}

    delPrice = delModePrice + payModePrice;
    priceSum = priceSum + delPrice;
    $( "#priceSumTotalVat" ).html(number_format(priceSum)+' '+{$presenter->curCodes[$presenter->curId]});
    $( "#priceDeliveryVat" ).html(number_format(delPrice)+' '+{$presenter->curCodes[$presenter->curId]});
    $( "#deliveryName" ).html(delModeName+', '+payModeName);
  })

  </script>
  {/ifCurrent}

  {block #footerBlock}
    <script src="{$baseUri}/js/scripts.js"></script>
  {/block}
</body>
</html>

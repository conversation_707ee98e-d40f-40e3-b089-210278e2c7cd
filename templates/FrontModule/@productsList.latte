{default $showPagination = FALSE}
{default $showFilter = TRUE}

{php
  $proIds = array();
}
{* <!-- výpis produktů start --> *}
<section role="region">
  {if $showFilter}
  <div class="row filter__row">
    {if $showPagination}
    <div class="col-xs-12 col-sm-8">
      {control paginatorTop}
    </div>
    {/if}

    <div class="col-xs-12 col-sm-4">

      {* řazení *}
      {if $showPagination}
          {* <!-- řazení start --> *}
          {php
            $o = $presenter->o;
          }

          <select onchange="location.assign(this.value)">

            <option value="{plink 'this', 'o'=>NULL}">Výchozí řazení</option>

              {if $o=='pa'}<option value="{plink 'this', 'o'=>'pa'}" selected="selected">nejlevnější</option>
              {else}<option value="{plink 'this', 'o'=>'pa'}">nejlevněj<PERSON><PERSON></option>{/if}

            {if $o=='pd'}<option value="{plink 'this', 'o'=>'pd'}" selected="selected">nejdražší</option>
              {else}<option value="{plink 'this', 'o'=>'pd'}">nejdražší</option>{/if}

            {if $o=='na'}<option value="{plink 'this', 'o'=>'na'}" selected="selected">A - Z</option>
              {else}<option value="{plink 'this', 'o'=>'na'}">A - Z</option>{/if}

            {if $o=='nd'}<option value="{plink 'this', 'o'=>'nd'}" selected="selected">Z - A</option>
              {else}<option value="{plink 'this', 'o'=>'nd'}">Z - A</option>{/if}

          </select>
        {/if}
        {* <!-- řazení end --> *}

    </div>

  </div>
  {/if}

  {if !empty($title)}
  <h2 class="category__header">{$title}</h2>
  {/if}

  {foreach $products as $row}
  {php
    $proIds[] = $row->proid;
    $GLOBALS["ecommTotalValue"] += $row->proprice;
    }
    {if $iterator->isFirst()}
      <div class="row row--autoclear row--flex">
    {/if}

    {* jedna položka *}
    {include @productItem.latte, product => $row}

    {if $iterator->isLast()}
      </div>
    {/if}
  {/foreach}

  {if count($products) == 0}<p>Nejsou zde žádné výrobky</p>{/if}

  {* stránkování, řazení dole *}
  {* řazení *}
  {if $showPagination}
  <div class="category__filters category__filters--bottom">
    {* <!-- stránkování start --> *}
    {control paginator}
    {* <!-- stránkování end --> *}
  </div>
  {/if}

</section>
{* <!-- výpis produktů end --> *}


{php
$GLOBALS["ecommProId"] = '["'.implode('","', $proIds).'"]';
}

{$pageTitle       = (empty($article->arttitle) ? $article->artname :$article->arttitle)}
{$pageKeywords    = $article->artkeywords}
{$pageDescription = $article->artdescription}
{$pageImageCustom = $baseUri . "/" . ($article|getArtPicNameBg:'440x293':FALSE)}

{* drobky pro detal článku *}
{block #crumb}
<div class="breadcrumb">

  <div class="container-fluid">

    <a href="{$baseUri}">Úvod</a>
    <span>»</span>
    {if $article->arttypid===4}
    <a href="{plink Article:detail $recipe->artid, ($recipe->arturlkey|getUrlKey:$recipe->artname)}">{$recipe->artname}</a>
    <span>»</span>
    {/if}
    <strong>{$pageTitle}</strong> {if $adminLogIn} <a href="{plink :Admin:Article:edit, $article->artid}" target="admin">E</a>{/if}

  </div>

</div>
{/block}

{block #content}

  <div class="where">

    {if !empty($article->artbody3)}
    <div class="where__news center">

      <h2 class="home__title"><span>Aktuální akce</span></h2>

      <div class="row">

        <div class="col-xs-12 col-sm-6">

          {* Aktuální akce *}
          {$article->artbody3|noescape}

        </div>

        <div class="col-xs-12 col-sm-6">

          <p class="where__photo"><img src="{$baseUri}/{$article|getArtPicName:'440x293'}" title="{$pageTitle}"></p>

        </div>

    </div>
    {/if}

    <div class="where__farm center">

      <h2 class="home__title"><span>Farma Oryx - osobní odběr</span></h2>

      <div class="row">

        <div class="col-xs-12 col-sm-6">

          {* Popis *}
          {$article->artbody|noescape}

        </div>

        <div class="col-xs-12 col-sm-6">

          <p class="where__photo"><img src="{$baseUri}/{$article|getArtPicNameBlock:'440x293'}" title="Farma Oryx - osobní odběr"></p>

        </div>

    </div>

    <div class="where__online center">

      <h2 class="home__title"><span>Objednávejte online</span></h2>

      <div class="row">

        <div class="col-xs-12 col-sm-6">

          {* Objednávejte online *}
          {$article->artbody4|noescape}

        </div>

        <div class="col-xs-12 col-sm-6">

          {* Objednávejte online *}
          {$article->artbody2|noescape}

        </div>

    </div>

    {foreach $images as $row}
      {if $iterator->isFirst()}
      <div class="where__other">

        <h2 class="home__title"><span>Najdete nás také</span></h2>

        <div class="row row--autoclear">
      {/if}
          <div class="col-xs-12 col-sm-4 col-md-4 col-lg-4 where__item">

            <p>{$row->ataname}</p>
            <p><a href="{$row->ataurl}" target="_blank"><img src="{$baseUri}/files/{$row->atafilename}" alt="{$row->ataname}"></a></p>

          </div>

      {if $iterator->isLast()}

      </div>

    </div>
      {/if}
    {/foreach}
    </div>

  </div>

{/block}

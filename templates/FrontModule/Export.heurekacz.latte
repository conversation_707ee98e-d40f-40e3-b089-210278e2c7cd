{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP>
{foreach $rows as $row}
<SHOPITEM>
<ITEM_ID>{$row->proid}</ITEM_ID>
<MANUFACTURER>{$row->manname}</MANUFACTURER>
{if !empty($row->procode2)}<EAN>{$row->procode2}</EAN>{/if}
{php
  $arr = explode('-', $row->proname2);
  $vol = "";
  if (!empty($arr[0]) && !empty($arr[1])) {
    $vol = ' '.trim($arr[0]);
  }
  if (!empty($row->pronames)) {
    $proname = $row->manname.' '.$row->pronames;
  } else {
    if ($row->promasid > 0) {
      $proname = $row->manname.' '.$row->proname.' '.$row->proname2;
    } else {
      $proname = $row->manname.' '.$row->proname.$vol;
    }
  }
}

<PRODUCTNAME>{$proname}</PRODUCTNAME>
<PRODUCT>{$proname} {if !empty($row->progift)}({$row->progift}){/if}</PRODUCT>
{if $view == 'heurekaczfull'}
<DESCRIPTION>{$row->prodesc}</DESCRIPTION>
{else}
<DESCRIPTION>{$row->prodescs}</DESCRIPTION>
{/if}
<URL>{plink //:Front:Product:detail, $row->proid, ($row|getProKey)}</URL>
<ITEM_TYPE>new</ITEM_TYPE>
<DELIVERY_DATE>{$row->proaccess}</DELIVERY_DATE>
<PRICE_VAT>{$row->proprice1a}</PRICE_VAT>
{if $view == 'heurekaczfull'}
<VAT>{$row->provat}</VAT>
{/if}
{if !empty($row->promasid)}
<ITEMGROUP_ID>{$row->promasid}</ITEMGROUP_ID>
{/if}
{php
  if (!empty($row->proidmas)) {
    $fileName = ($row->propicnamemas != "" ? trim($row->propicnamemas).'.jpg' : $row->procodemas.'.jpg');
  } else {
    $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
  }
  $fileName = rawurlencode($fileName);
}
<IMGURL>{$baseUri}/pic/product/src/{$fileName}</IMGURL>
{php
$catPath = "";
if (!empty($row->catid)) {
  $catPath = str_replace('|', ' | ', $row->catpath);
  $catPathIds = explode('|', trim($row->catpathids, '|'));
  $catPathIds = array_reverse($catPathIds); //zacnu prohledavat od konce vetve
  If (!isset($heurekaPath[$row->catid])) {
    foreach ($catPathIds as $catid) {
      if (isset($heurekaCatalogs[$catid])) {
        $heurekaPath[$row->catid] = $heurekaCatalogs[$catid]->catpathheureka;
        break;
      }
    }
  }
}
}
{if isset($heurekaPath[$row->catid]) && $view != 'heurekaczfull'}
<CATEGORYTEXT>{$heurekaPath[$row->catid]}</CATEGORYTEXT>
{else}
<CATEGORYTEXT>{$catPath}</CATEGORYTEXT>
{/if}
{if !empty($row->proorigin)}
<PARAM>
  <PARAM_NAME>Distribuce</PARAM_NAME>
  <VAL>{$row->proorigin}</VAL>
</PARAM>
{/if}
{php
  $delCost = -1;
}
{foreach $delModes as $key=>$rowd}
  <DELIVERY>
    <DELIVERY_ID>{$rowd->delcode}</DELIVERY_ID>
    {if isset($rowd->paymodes["paybefore"])}
    {php
      if ($row->prodelfree == 1) {
        $payCost = 0;
        $delCost = 0;
      } else {
        $payCost = ($rowd->paymodes["paybefore"]->delpricelimitfrom > 0 && $rowd->paymodes["paybefore"]->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->paymodes["paybefore"]->delprice);
        $delCost = ($rowd->delpricelimitfrom > 0 && $rowd->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->delprice);
      }
    }
    <DELIVERY_PRICE>{$payCost+$delCost}</DELIVERY_PRICE>
    {/if}
    {if isset($rowd->paymodes["dobirka"])}
    {php
      if ($row->prodelfree == 1) {
        $payCost = 0;
        $delCost = 0;
      } else {
        $payCost = ($rowd->paymodes["dobirka"]->delpricelimitfrom > 0 && $rowd->paymodes["dobirka"]->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->paymodes["dobirka"]->delprice);
        $delCost = ($rowd->delpricelimitfrom > 0 && $rowd->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->delprice);
      }
    }
    <DELIVERY_PRICE_COD>{$payCost+$delCost}</DELIVERY_PRICE_COD>
    {/if}
  </DELIVERY>
{/foreach}
{if $row->procpcheureka > 0}<HEUREKA_CPC>{$row->procpcheureka|formatNumber:2}</HEUREKA_CPC>{/if}
</SHOPITEM>
{/foreach}
</SHOP>

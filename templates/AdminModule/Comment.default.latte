{var title = '<PERSON>cenz<PERSON>'}

{block #content}

<p><a href="{plink Comment:edit, 0}" class="btn btn-primary btn-sm">Přidat novou recenzi</a></p>

{form searchForm class=>'form-inline'}
<div class="panel panel-primary panel-custom">

    <div class="panel-heading"><strong>Filtrace</strong></div>

    <div class="panel-body">

      <div class="form-group">{label text /}: {input text class=>'form-control'}</div>
      <div class="form-group">{label usrmail /}: {input usrmail class=>'form-control'}</div>
      {*<div class="form-group">{label procode /}: {input procode class=>'form-control'}</div>*}
      <div class="form-group">{input search class=>"btn btn-primary"} {input clear class=>"btn"}</div>

    </div>

  </div>
{/form}

  <table class="table table-condensed table-hover table-bordered">
  <tr>
    {*<th>Kód produktu</th>*}
    <th>Datum/čas</th>
    <th>Nick</th>
    <th>Titulek</th>
    <th>Text</th>
    <th colspan="3"></th>
  </tr>
  {foreach $comments as $row}
    <tr valign="top">
      {*<td>{$row->procode}</td>*}
      <td>{$row->cmtdatec|date:'d.m.Y H:i'}</td>
      <td>{$row->cmtnick}{if !empty($row->cmtmail)}<br>{$row->cmtmail}{/if}</td>
      <td>{$row->cmtsubj}</td>
      <td>
      {$row->cmttext|nl2br|noescape}</td>
      <td><a href="{plink Comment:edit, $row->cmtid}" title="editovat komentář">{('edit'|glyph)|noescape}</a></td>
      <td><a href="{plink Comment:delete, $row->cmtid}" onclick="return DeleteConfirm('komentář {$row->cmtsubj|noescape}');">{('delete'|glyph)|noescape}</a></td>
    </tr>
  {/foreach}
  </table>
  {control paginator}
{/block}

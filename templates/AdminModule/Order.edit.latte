{var $title = 'Objednávka č. '.$dataRow->ordcode.' '.$dataRow->ordiname.' '.$dataRow->ordilname}

{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('oriname', 'oriproid');
          $("#"+newId).val(ui.item.id);
          newId = id.replace('oriname', 'oriprice');
          $("#"+newId).val(ui.item.price);
        }
        return false;
      }
    });
  });
  </script>
  {*
  {if !empty($dataRow->ordinvcode)}
  <p><b>Faktura č.: </b>{$dataRow->ordinvcode} <a target="invoicePrint" href="{plink Order:printInvoice, $dataRow->ordid}"><img src="{$baseUri}/admin/ico/pdf.png" width="16" height="16" /></a>  | <a href="{plink Order:printInvoice, $dataRow->ordid, 'D'}">{('export'|glyph)|noescape}</a></p>
  {else}
  <p>Není přiřazen kód faktury. <a href="{plink makeInvoice, $dataRow->ordid}">Vystavit fakturu</a></p>
  {/if}
  *}
  {if $ordPayType->delcode == 'creditcard'}
    {*
    <p>
      <a href="{plink startFioPayment, $dataRow->ordid, substr(md5($dataRow->ordid . $dataRow->ordcode), 0, 6)}">Platba kartou</a>
      {if !empty($dataRow->ordpaysesid) && $dataRow->ordpaystatus == 1}  <a href="{plink reverseFioPayment, $dataRow->ordid}">Zrušit platbu</a>{/if}
    </p>
    *}
    <p>
      <a href="{plink :Front:GoPay:create, $dataRow->ordid, substr(md5($dataRow->ordid . $dataRow->ordcode), 0, 6)}">Platba kartou GoPay</a>
    </p>
  {/if}

  {control orderChangeStateForm}

  {ifset $parcelHistory}
    {foreach $parcelHistory as $item}
      {if $iterator->first}
      <h4>Status zásilky
        <a href="https://www.geis-group.cz/cs/sledovani-zasilky?p={$dataRow->ordparcode}" target="geis">
        {('send'|glyph:'sledovat balík')|noescape}
      </a>
      </h4>
      <table class="table table-condensed table-hover table-bordered">
      {/if}
        <tr>
          <td>{$item["date"]|date:'d.m.Y H:i:s'}</td>
          <td>{$item["description"]}</td>
        </tr>
      {if $iterator->last}
      </table>
      {/if}
    {/foreach}
  {/ifset}

  <h4 id="edititems">Položky objednávky</h4>
  {form ordItemsEditForm}
    <table class="table table-condensed table-hover table-bordered">
      <tr>
        <th>ID zboží</th>
        <th>Katalogové č.</th>
        <th>název</th>
        <th width="150">cena/sleva</th>
        <th>počet</th>
        <th colspan="2"></th>
      </tr>
      {foreach  $form['items']->getComponents() as $cont}
      {var $oriid=$form['items'][$cont->name]['oriid']->value}
      {if $cont->name != 'delivery'}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        <td>{php echo $form['items'][$cont->name]['oriproid']->control }</td>
        <td>{$ordItems[$oriid]->oriprocode}</td>
        <td>
          {php echo $form['items'][$cont->name]['oriname']->control->size(45) }
        </td>
        <td style="text-align: center">{php echo $form['items'][$cont->name]['oriprice']->control }
          {if $ordItems[$oriid]->oripricereal > 0 && $ordItems[$oriid]->oriweightreal > 0}
            {$ordItems[$oriid]->oriweightreal|formatWeight}/{$ordItems[$oriid]->oripricereal|formatPrice}
          {/if}
        </td>
        <td>{php echo $form['items'][$cont->name]['oriqty']->control }</td>
        <td>
          <a href="{plink :Front:Product:detail, $ordItems[$oriid]->oriproid, ($ordItems[$oriid]|getProKey)}">{('front'|glyph)|noescape}</a>
          <a href="{plink Product:edit, $ordItems[$oriid]->oriproid}">{('edit'|glyph)|noescape}</a>
        </td>
        <td><a href="{plink Order:deleteItem, $form['items'][$cont->name]['oriid']->value, (int)$presenter->getParameter('id')}" onclick="return DeleteConfirm('položku objednávky {$form['items'][$cont->name]['oriname']->value|noescape}');"> {('delete'|glyph)|noescape} SMAZAT </a></td>
      </tr>
      {else}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        <td>doprava</td>
        <td></td>
        <td>{php echo $form['items'][$cont->name]['oriname']->control->size(80) } </td>
        <td>
          Cena: {$ordItems[$oriid]->oriprice|formatPrice}<br />
          {php echo $form['items'][$cont->name]['oripricemaster']->control->size(3) }
        </td>
        <td>1</td>
        <td colspan="2"></td>
      </tr>
      {/if}
      {/foreach}
      {if $ordItemDisc}
      <tr>
        <th colspan="10">Sleva</th>
      </tr>
      <tr>
        <td></td>
        <td></td>
        <td>{$ordItemDisc->oriname}</td>
        <td>{$ordItemDisc->oriprice}</td>
        <td>1</td>
        <td></td>
        <td colspan="2"></td>
      </tr>
      {/if}
      <tr>
        <th colspan="7">nová položka</th>
      </tr>
      <tr>
        <td>{php echo $form['newitem']['oriproid']->control }</td>
        <td></td>
        <td>{php echo $form['newitem']['oriname']->control->size(80) } </td>
        <td>{php echo $form['newitem']['oriprice']->control }</td>
        <td>{php echo $form['newitem']['oriqty']->control }</td>
        <td colspan="2"></td>
      </tr>
      <tr>
        <td colspan="8">{input saveitems}</td>
      </tr>
    </table>
  {/form}

  {control orderEditForm}

  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie objednávky</h3>
    <table class="table table-condensed table-hover table-bordered">
    {/if}
    <tr>
    <td>{$row->orldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_ordstatus[$row->orlstatus]}</td>
    <td>{$row->orladmname}</td>
    <td>{$row->orlnote}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}

  {ifset $apiStatus}
  <h3>Status balíku dle API přepravce: {$apiStatus}</h3>
  {/ifset}

  {foreach $eetRows as $row}
    {if $iterator->isFirst()}
    <h3>EET log</h3>
    <table class="table table-condensed table-hover table-bordered">
      <th>Datum</th>
      <th>Provozovna</th>
      <th>Pokladna</th>
      <th>FIK</th>
      <th>BKP</th>
    {/if}
    <tr>
    <td>{$row->logdatec|date:'d.m.Y H:i:s'}</td>
    <td>{$row->logprovozid}</td>
    <td>{$row->logpoklid}</td>
    <td>{$row->logfik}</td>
    <td>{$row->logbkp}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}

  {/block}

{block #footerBlock}

  <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>

{/block}

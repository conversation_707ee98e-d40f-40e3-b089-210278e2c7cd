{var title = 'Trasa rozvozu' . ($id > 0 ? ": ". $enum_delid[$id] : "")}

{block #content}

  <p><a href="{plink pathBatchUpdate}">Import tras</a></p>

  <h3></h3>

  {form searchForm class=>'form-inline'}
  <div class="panel panel-primary panel-custom">

    <div class="panel-heading"><strong>Filtrace</strong></div>

    <div class="panel-body">

      <div class="input-group">
        <span class="input-group-addon">{label name /}</span>
        {input name class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label postcode /}</span>
        {input postcode class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label delid /}</span>
        {input delid class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label status /}</span>
        {input status class=>'form-control'}
      </div>

      <br>

      <div class="input-group">
        <span class="input-group-addon">{label orderby /}</span>
        {input orderby class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label orderbytype /}</span>
        {input orderbytype class=>'form-control'}
      </div>

      {input search class=>"btn btn-success"}
      {input clear class=>"btn btn-default"}

    </div>

  </div>

  <div class="panel panel-primary panel-custom">

    <div class="panel-heading"><strong>Hromadná aktualizace</strong> <small> - nastaví hodnoty u nafiltrovaných položek</small></div>


    <div class="panel-body">

      <div class="input-group">
        <span class="input-group-addon">{label uprice /}</span>
        {input uprice class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label ustatus /}</span>
        {input ustatus class=>'form-control'}
      </div>

      {input update class=>"btn btn-danger"} {input delete class=>"btn btn-danger"}

    </div>

  </div>

    <input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše

  {foreach $rows as $row}
    {if $iterator->first}
    <table class="table table-condensed table-hover table-bordered">
      <tr>
        <th></th>
        <th>Doprava</th>
        <th>PSČ</th>
        <th>Místo</th>
        <th>Pošta</th>
        <th>Okres</th>
        <th>Cena</th>
        <th>Status</th>
      </tr>
    {/if}
      <tr>
        <td><input type="checkbox" class="id_chk" name="dpiid[{$row->dpiid}]" value="{$row->dpiid}"></td>
        <td>{$enum_delid[$row->dpidelid]}</td>
        <td>{$row->dpipostcode}</td>
        <td>{$row->dpiplacename}</td>
        <td>{$row->dpipostname}</td>
        <td>{$row->dpidistrictname}</td>
        <td>{$row->dpiprice|formatPrice}</td>
        <td>{$enum_dpistatus[$row->dpistatus]}</td>
      </tr>
    {if $iterator->last}
    </table>
    {/if}
  {/foreach}

    <script>
    $("#checkAll").click(function(){
      $('.id_chk').not(this).prop('checked', this.checked);
    });
  </script>

  {/form}

  {control paginator}

{/block}

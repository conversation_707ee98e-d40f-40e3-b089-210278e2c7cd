{var title = 'Editace článku'}

{block #content}
  {form editForm}
  {* vypise chyby formulare *}
  {include ../@formErrors.latte form=>$form}
    <div class="form-group">
      <div class="col-sm-2 control-label">{label arttypid /}</div>
      <div class="col-sm-10">{input arttypid}</div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label">{label imageBg /}</div>
      <div class="col-sm-10">
        {if $id > 0}
        {php
        $img = ($dataRow|getArtPicNameBg:'600x150':FALSE);
        }
        {/if}
        {if !empty($img)}
          <img src="{$baseUri}/{$img}?{time()}" title="pozadí hlavičky" />
        {else}
          obrázek nenastaven
        {/if}

        {input imageBg} <small>obrázek změníte tak, že jej přepí<PERSON>ete jiným</small></div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label">{label imageMain /}</div>
      <div class="col-sm-10">
        {if $id > 0}
        {php
        $img = ($dataRow|getArtPicName:'274x229':FALSE);
        }
        {/if}
        {if !empty($img)}
          <img src="{$baseUri}/{$img}?{time()}" title="hlavní obrázek" />
        {else}
          obrázek nenastaven
        {/if}

        {input imageMain} <small>obrázek změníte tak, že jej přepíšete jiným</small>
      </div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label">{label imageBlock /}</div>
      <div class="col-sm-10">
        {if $id > 0}
        {php
        $img = ($dataRow|getArtPicNameBlock:'274x229':FALSE);
        }
        {/if}
        {if !empty($img)}
          <img src="{$baseUri}/{$img}?{time()}" title="hlavní obrázek" />
        {else}
          obrázek nenastaven
        {/if}

        {input imageBlock} <small>obrázek změníte tak, že jej přepíšete jiným</small>
      </div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label">{label artname /}</div>
      <div class="col-sm-10">{input artname}</div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label">{label arttitle /}</div>
      <div class="col-sm-10">{input arttitle}</div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label">{label artdescription /}</div>
      <div class="col-sm-10">{input artdescription}</div>
    </div>

    {ifset $form["artbody3"]}
      <div class="form-group">
        <div class="col-sm-2 control-label">{label artbody3 /}</div>
        <div class="col-sm-10">{input artbody3}</div>
      </div>
    {/ifset}

    <div class="form-group">
      <div class="col-sm-2 control-label">{label artbody /}</div>
      <div class="col-sm-10">{input artbody}</div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label">{label artbody2 /}</div>
      <div class="col-sm-10">{input artbody2}</div>
    </div>

    {ifset $form["artbody4"]}
      <div class="form-group">
        <div class="col-sm-2 control-label">{label artbody4 /}</div>
        <div class="col-sm-10">{input artbody4}</div>
      </div>
    {/ifset}

    <div class="form-group">
      <div class="col-sm-2 control-label">{label artstatus /}</div>
      <div class="col-sm-10">{input artstatus}</div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label">{label arturlkey /}</div>
      <div class="col-sm-10">{input arturlkey}</div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label"></div>
      <div class="col-sm-10">{input save}</div>
    </div>

  {/form}

  {foreach $images as $row}
    {if $iterator->isFirst()}
      {if $dataRow->artid === 13}
      <h3>Prodejny</h3>
      {else}
      <h3>Přiložené obrázky</h3>
      {/if}

    {/if}
    <img src="{$baseUri}/files/{$row->atafilename}" height="180" alt="{$row->ataname}" title="{$row->ataname}" /> <a href="{plink DeleteAttachment, $row["ataid"], $id}">{('delete'|glyph)|noescape}</a>
    {if $iterator->isLast()}
    <br />
    {/if}
  {/foreach}
  {foreach $attachments as $row}
    {if $iterator->isFirst()}
     <h3>Ostatní přílohy</h3>
    {/if}
    <a href="{$baseUri}/files/{$row->atafilename}" target="_blank">{$row->ataname}</a> <img src="{$baseUri}/admin/ico/{$row->atatype}.png" width="16" height="16" alt="{$row->atatype}" /> <a href="{plink DeleteAttachment, $row["ataid"], $id}">{('delete'|glyph)|noescape}</a><br />
  {/foreach}

  {if $dataRow->artid === 13}
  <h3>Přidat prodejnu</h3>
  {else}
  <h3>Přidat obrázek</h3>
  {/if}
  {form uploadForm}
  {* vypise chyby formulare *}
  {include ../@formErrors.latte form=>$form}
    <div class="form-group">
      <div class="col-sm-2 control-label">{label ataname /}</div>
      <div class="col-sm-10">{input ataname}</div>
    </div>
    {if $dataRow->artid === 13}
    <div class="form-group">
      <div class="col-sm-2 control-label">{label ataurl /}</div>
      <div class="col-sm-10">{input ataurl}</div>
    </div>
    {/if}
    <div class="form-group">
      <div class="col-sm-2 control-label">{label file /}</div>
      <div class="col-sm-10">{input file}</div>
    </div>
    <div class="form-group">
      <div class="col-sm-2 control-label"></div>
      <div class="col-sm-10">{input save}</div>
    </div>
  {/form}
{/block}

{var title = 'Stav skladu'}

{block #content}

 <h3>{block #title}Stav skladu{/block}</h3>
  <div class="panel panel-primary panel-custom">

  <div class="panel-heading"><strong>Filtrace</strong></div>

  <div class="panel-body">

  {form searchStoreForm class=>'form-inline'}

    <div class="input-group">
      <span class="input-group-addon">{label procode /}</span>
      {input procode class=>'form-control', size=>30}
    </div>

    {input search class=>"btn btn-success"}
    {input search_ean class=>"btn btn-primary"}
    {input clear class=>"btn btn-default"}

  {/form}

  </div>

</div>

 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Kód</th>
    {if $byEans}
    <th>EAN</th>
    {/if}
    <th>Název</th>
    <th>Počet kusů</th>
    <th>Hmotnost/kus</th>
    <th>Skutečná hmotnost</th>
  </tr>
  {php
    $sumQty = 0;
    $sumWeight = 0;
    $sumWeightReal = 0;
  }
  {foreach $rows as $row}
    {php
      $sumQty += $row->stiqty;
      $sumWeight += $row->proweight*$row->stiqty;
      $sumWeightReal += ($row->stiweight*$row->stiqty);
    }

  <tr>
    <td>{$row->procode}</td>
    {if $byEans}
    <td>{$row->stiean}</td>
    {/if}
    <td>{$row->proname}</td>
    <td style="text-align: right">{$row->stiqty}</td>
    <td style="text-align: right">{if $row->proweight > 0}{$row->proweight|formatWeight}{/if}</td>
    <td style="text-align: right">{if $row->stiweight > 0}{$row->stiweight|formatWeight}{/if}</td>
  </tr>
  {/foreach}
  <tr style="background-color: silver">
    <th>Celkem:</th>
    <th></th>
    {if $byEans}
    <th></th>
    {/if}
    <th style="text-align: right">{$sumQty}</th>
    <th style="text-align: right">{$sumWeight|formatWeight}</th>
    <th style="text-align: right">{$sumWeightReal|formatWeight}</th>
  </tr>
  </table>
  {* strankovani *}
  {control paginator}
{/block}

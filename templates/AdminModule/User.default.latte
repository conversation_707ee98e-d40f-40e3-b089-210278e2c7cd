{var title = 'Zákazníci'}

{block #content}

  <div class="panel panel-primary panel-custom">

    <div class="panel-heading"><strong>Filtrace</strong></div>

    <div class="panel-body">

    {form searchForm class=>'form-inline'}

      <div class="input-group">
        <span class="input-group-addon">{label ic /}</span>
        {input ic class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label iname /}</span>
        {input iname class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label firname /}</span>
        {input firname class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label email /}</span>
        {input email class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label status /}</span>
        {input status class=>'form-control'}
      </div>

      <br>

      <div class="input-group">
        <span class="input-group-addon">{label orderby /}</span>
        {input orderby class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label orderbytype /}</span>
        {input orderbytype class=>'form-control'}
      </div>

      {input search class=>"btn btn-success"}
      {input clear class=>"btn btn-default"}

    {/form}

    </div>

  </div>

  {* strankovani *}
  {control paginator}
  <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Jméno</th>
    <th>Firma</th>
    <th>IČ</th>
    <th>Sleva</th>
    <th>Cena</th>
    <th>Obrat</th>
    <th>E-mail</th>
    <th>Mobil</th>
    <th>Mail</th>
    <th>Souhlas</th>
    <th>Status</th>
    <th></th>
    <th></th>
  </tr>
{foreach $dataRows as $row}
  <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
    <td>{$row->usriname} {$row->usrilname}</td>
    <td>{$row->usrifirname}</td>
    <td>{$row->usric}</td>
    <td>{$row->usrdiscount}%</td>
    <td>{$enum_usrprccat[$row->usrprccat]}</td>
    <td style="text-align: right;">{$row->ordpricevatsum|formatPrice}</td>
    <td>{$row->usrmail}</td>
    <td>{$row->usrtel}</td>
    <td>{if $row->usrmailverified === 1}{('active'|glyph)|noescape}{else}{('delete'|glyph)|noescape}{/if}</td>
    <td>{if $row->aprove === 1}{('active'|glyph)|noescape}{else}{('delete'|glyph)|noescape}{/if}</td>
    <td>{$enum_usrstatus[$row->usrstatus]}</td>
    <td><a href="{plink User:edit, $row->usrid}">{('edit'|glyph)|noescape}</a></td>
    <td><a href="{plink User:delete, $row->usrid}" onclick="return DeleteConfirm('účet {$row->usrmail}');">{('delete'|glyph)|noescape}</a></td>
  </tr>
{/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}

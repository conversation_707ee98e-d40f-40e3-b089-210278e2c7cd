{var title = 'Slevové kupóny'}

{block #content}
 <p><a href="{plink edit 0}">Nový slevový kupón</a></p>

<div class="panel panel-primary panel-custom">

  <div class="panel-heading"><strong>Filtrace</strong></div>

  <div class="panel-body">

  {form searchForm class=>'form-inline'}
    <div class="input-group">
      <span class="input-group-addon">{label code /}</span>
      {input code class=>'form-control'}
    </div>

    <div class="input-group">
      <span class="input-group-addon">{label codegroup /}</span>
      {input codegroup class=>'form-control'}
    </div>

    <div class="input-group">
      <span class="input-group-addon">{label mail /}</span>
      {input mail class=>'form-control'}
    </div>

    <div class="input-group">
      <span class="input-group-addon">{label status /}</span>
      {input status class=>'form-control'}
    </div>

    {input search class=>"btn btn-success"}
    {input clear class=>"btn btn-default"}
    {input export class=>"btn btn-default"}

  {/form}

  </div>

</div>

  <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Kód</th>
    <th>Kód skupiny</th>
    <th>Hodnota</th>
    <th>Platnost</th>
    <th>Počet použití</th>
    <th>Status</th>
    <th colspan="3"></th>
  </tr>
  {foreach $dataRows as $row}
    {php
    $style = "";
    if ($row->coustatus == 0) {
    } else if ($row->coustatus == 1) {
      $style = "silver";
    }
    }
    <tr {if !empty($style)} style="background-color: {$style|noescape}"{/if}>
      <td>{$row->coucode}</td>
      <td>{$row->coucodegroup}</td>
      <td>{if $row->couvalue > 0}{$row->couvalue}{$row->couvalueunit}{/if}</td>
      <td>{$row->couvalidto|date:'d.m.Y'}</td>
    <td>{$row->coucounter}</td>
      <td>{$enum_coustatus[$row->coustatus]}</td>
      <td><a href="{plink edit, $row->couid}">{('edit'|glyph)|noescape}</a></td>
      <td><a href="{plink Order:default, 'sCoupon'=>$row->coucode}">{('info'|glyph:'Vypsat objednávky s tímto kupónem')|noescape}</a></td>
      <td><a href="{plink stats, $row->couid}">{('stats'|glyph)|noescape}</a></td>
    </tr>
  {/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}

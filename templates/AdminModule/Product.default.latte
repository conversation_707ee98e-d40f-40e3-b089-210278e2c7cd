{var $title = 'Seznam zboží'}

{block #content}
  {foreach $prosNotInCatalog as $row}
    {if $iterator->isFirst()}
    <strong style="color: red;">Položky které nejsou zařazeny v katalogu</strong><br>
    {/if}
    <a href="{plink Product:edit, $row->proid}">{$row->proname}</a> <a href="{plink Product:edit, $row->proid}">{('edit'|glyph)|noescape}<br>
    {if $iterator->isLast()}
    <br>
    {/if}
  {/foreach}

  <div class="panel panel-primary panel-custom">

    <div class="panel-heading"><strong>Filtrace</strong></div>

    <div class="panel-body">

    {form searchForm class=>'form-inline'}

      <div class="input-group">
        <span class="input-group-addon">{label code /}</span>
        {input code class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label code2 /}</span>
        {input code2 class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label name /}</span>
        {input name class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label manid /}</span>
        {input manid class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label catid /}</span>
        {input catid class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label status /}</span>
        {input status class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label orderby /}</span>
        {input orderby class=>'form-control'}
      </div>
       <br>
      {label typid class=>"checkbox-inline"} {input typid} {/label}
      {label typid2 class=>"checkbox-inline"} {input typid2} {/label}
      {label typid3 class=>"checkbox-inline"} {input typid3} {/label}
      {label typid4 class=>"checkbox-inline"} {input typid4} {/label}
      {label typid5 class=>"checkbox-inline"} {input typid5} {/label}

      {input search class=>"btn btn-success"}
      {input clear class=>"btn btn-default"}

    {/form}

    </div>

  </div>

 {control paginator}
  {snippet table-data}
{form listEditForm}
 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Kat. č.</th>
    <th>Název</th>
    <th>Doprava</th>
    <th>Cena</th>
    <th>Skladem</th>
    <th>Dostupnost</th>
    <th>Status</th>
    <th>Pořadí</th>
    <th colspan="3"></th>
  </tr>
  {foreach $dataRows as $row}
    {var $container = $form[$row->proid]}
    {php
      $delIds = array();
      if (!empty($row->prodelids)) {
        $delIds = explode(",", trim($row->prodelids, ","));
      }
    }
    <tr{if !$iterator->isOdd()} bgcolor="#D0D0D0"{/if}>
      <td>
          {$row->procode}<br>
          {$row->procode2}
      </td>
      <td style="{if $row->proismaster==1}background-color:#C0C0FF{elseif (int)$row->promasid > 0}background-color:#FCB633{/if}">{$row->proname} {$row->proname2}</td>
      <td>
        {foreach $delIds as $delid}
          {$enum_delid[$delid]}{if !$iterator->last}<br>{/if}
        {/foreach}
      </td>
      <td>{$row->proprice1a|formatPriceByCurId:1}</td>
      <td>
        {$row->proqty} ks
        {php echo $container["proqty"]->getControl()->addAttributes(array('size'=>1)) }
      </td>
      <td>
        {$enum_proaccess[$row->proaccess]}
        {php echo $container["proaccess"]->getControl()->addAttributes(array('size'=>1)) }
        {if !empty($row->prodatefrom)}
            <span style="background-color: yellow; padding: 3px">{$row->prodatefrom|date:'d.m.Y'}-
            {$row->prodateto|date:'d.m.Y'}</span>
        {/if}
      </td>
      <td>
        {php echo $container["prostatuschange"]->getControl()->addAttributes(array('title'=>'změnit status')) } {if $row->prostatus == 0} {'active'|glyph|noescape} {else} {'blocked'|glyph|noescape} {/if}
      </td>
      <td>
        {$row->proorder}
        {php echo $container["proorder"]->getControl()->addAttributes(array('size'=>1)) }
      </td>
      <td><a href="{plink Product:edit, $row->proid}">{('edit'|glyph)|noescape}</a></td>
      <td><a href="{plink :Front:Product:detail, $row->proid, ($row|getProKey)}">{('front'|glyph)|noescape}</a></td>
      <td><a href="{plink Product:delete, $row->proid}" onclick="return DeleteConfirm('položku {$row->proname}');">{('delete'|glyph)|noescape}</a></td>
    </tr>
  {/foreach}
  </table>
  {input save class=>"btn btn-primary"}
  {/form listEditForm}
    {/snippet}
  {control paginator}
{/block}

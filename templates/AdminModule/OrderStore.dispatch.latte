{var $title = 'K vyskladnění'}

{block #content}
  <a href="{plink Order:default}"><PERSON><PERSON><PERSON> objednáve<PERSON></a>

  <form action="{plink eanForm}" method="get">
  {foreach $orders as $ordId => $row}

    <h3 {if $row->ordmode === 'w'} style="background-color: darkorange"{/if}>
      {$row->ordcode}, {$row->ordiname} {$row->ordilname} {if !empty($row->ordifirname)}, firma: {$row->ordifirname}{/if}
      <a href="{plink Order:edit, $row->ordid}">{('edit'|glyph)|noescape}</a>
    </h3>

    {foreach $items[$ordId] as $irow}
      {if $iterator->first}
      <table class="table table-condensed table-hover table-bordered">
        <tr>
          <th></th>
          <th>Kód</th>
          <th>Název</th>
          <th>Ks.</th>
          <th>Ks. zbývá</th>
          <th>Hmotnost/kus</th>
          <th>Hmotnost skut.</th>
          <th>Cena/kus</th>
          <th>Cena skut.</th>
          <th></th>
        </tr>
      {/if}
        <tr>
          <td><input class="radio_input" type="radio" name="oriid" value="{$irow->oriid}"></td>
          <td>{$irow->procode}</td>
          <td>
            {$irow->proname}
            {ifset $eans[$irow->oriid]}
            {foreach $eans[$irow->oriid] as $erow}
              {if $iterator->first}
                <table class="table table-condensed table-hover table-bordered" style="padding: 0; margin: 0;">
              {/if}
                  <tr>
                    <td>{$erow->stiean}</td>
                    <td style="text-align: right">{$erow->stiweight|formatWeight}</td>
                  </tr>
              {if $iterator->last}
                </table>
              {/if}
            {/foreach}
              {/ifset}
          </td>
          <td style="text-align: right">{$irow->oriqty}</td>
          <td style="text-align: right">{$irow->orifill}</td>
          <td style="text-align: right">{$irow->oriweight|formatWeight}</td>
          <td style="text-align: right">{$irow->oriweightreal|formatWeight}</td>
          <td style="text-align: right">{$irow->oriprice|formatPrice}</td>
          <td style="text-align: right">{$irow->oripricereal|formatPrice}</td>
          <td>{if $irow->orifill===0}{('ok'|glyph)|noescape}{/if}</td>
        </tr>
      {if $iterator->last}
      </table>
      {/if}
    {/foreach}
  {/foreach}

  <label>EAN:
      <input type="text" name="ean" id="ean">
    </label>
    <p>Dle zadaného EANu systém vyhledá vhodnou položku a vyskladní ji.</p>
  <input type="submit" name="Odeslat">
    <input type="hidden" name="ids" value="{$presenter->ids}">
  </form>

  <script>
    $('#ean').focus();

    $(document).ready(function() {
      $("input[name=radio_input]:radio").click(function() {
        $('#ean').focus();
      });
    });

  </script>

{/block}

<script>
    function noteSH(id) {
        if ($('#'+id).is(':visible')) {
            $('#'+id).hide();
        } else {
            $('#'+id).show();
        }
        return false;
    }
</script>

<form method="get" action="{plink Order:batchAction}">
<input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše
<table class="table table-condensed table-hover table-bordered">
  <tr>
    <th> </th>
    <th> </th>
    <th>Č. obj.</th>
    <th>Zákazník</th>
    <th>Firma</th>
    <th>Datum</th>
    <th>Doprava, platba</th>
    <th>Pozn.</th>
    <th>Status</th>
    <th>Cena s DPH</th>
    <th>Zapl.</th>
    <th colspan="4"></th>
  </tr>
  {php
    $colors[0] = "#FFFFFF";
    $colors[1] = "#FFFFC0";
    $colors[2] = "#FFBE7D";
    $colors[3] = "#C0C0FF";
    $colors[4] = "#C0FFC0";
    $colors[5] = "#FFC0C0";
    $colors[6] = "chartreuse";
    $colors[7] = "#C0C0C0";
    $colors[8] = "#BCDCB8";

    $sum = 0;
  }

  {foreach $dataRows as $row}
  {php
    $sum += $row->ordpricevat;

    $istreet = $row->ordistreet;
    $istreetno = $row->ordistreetno;
    $icity = $row->ordicity;
    if (!empty($row->ordstname) || !empty($row->ordstfirname)) {
      $istreet = $row->ordststreet;
      $istreetno = $row->ordststreetno;
      $icity = $row->ordstcity;
    }
    $wazeLink = urlencode(trim($istreet . " " . $istreetno) . " " . $icity);
  }
  {var $style= (!$iterator->isOdd() ? 'bgcolor="#F0F0F0"' : '')}

  <tr {$style|noescape}>
    <td><input class="ordid_chk" type="checkbox" name="ordid[{$row->ordid}]" value="{$row->ordid}" ></td>
    <td>
      {if !empty($row->ordparcode) && $row->delcodemas === 'ULOZENKA'}
      <a href="https://tracking.ulozenka.cz/{$row->ordparcode}/" target="zasilkovna">
        {('send'|glyph:'Už existuje balík - sledovat balík')|noescape}
      </a>
      {/if}
      {if !empty($row->ordparcode) && $row->delcodemas === 'GEIS'}
      <a href="https://www.geis-group.cz/cs/sledovani-zasilky?p={$row->ordparcode}" target="geis">
        {('send'|glyph:'Už existuje balík - sledovat balík')|noescape}
      </a>
      {/if}

    </td>
    <td
      {if $row->ordmode === 'w'} style="background-color: darkorange"{/if}>
      {$row->ordcode}
      {if isset($row->otherOrdersCnt) && $row->otherOrdersCnt > 0}
        <br><a href="{plink this 'sPhone'=>$row->ordtel}"><span style="color: red">[další {$row->otherOrdersCnt}]</span></a>
      {/if}
    </td>
    <td>
      {$row->ordiname} {$row->ordilname}|{$row->ordstname} {$row->ordstlname}<br>
      <a href="tel:{$row->ordtel|formatPhoneNumber}"><span style="font-color: black;">{$row->ordtel|formatPhoneNumber}</span></a><br>
      <a href="https://waze.com/ul?q={$wazeLink}">{$istreet} {$istreetno}, {$icity}</a><br>
    </td>
    <td>{$row->ordifirname}|{$row->ordstfirname}</td>
    <td>
      {$row->orddatec|date:'d.m.Y H:i'}{if !empty($row->orddeldatedel)}<br>Odběr: {$row->orddeldatedel|date:'d.m.Y'}{/if}
    </td>
    <td>{$row->delnamemas}, {$row->delname}</td>
    <td>{$row->ordnote|nl2br|noescape}</td>
    <td style="color: black; background-color: {$colors[$row->ordstatus]|noescape};">
    <strong>{$enum_ordstatus[$row->ordstatus]}</strong>
    </td>
    <td style="text-align: right;white-space:nowrap">{$row->ordpricevat|formatPriceByCurId:$row->ordcurid}</td>
    <td>{if $row->ordpaystatus === 1 || $row->paycode === 'cash'}{('ok'|glyph:'ZAPLACENO')|noescape}{/if}</td>
    <td><a href="{plink Order:edit, $row->ordid}">{('edit'|glyph)|noescape}</a></td>
    <td>{if $row->ordusrid > 0}<a href="{plink User:edit, $row->ordusrid}">{('user'|glyph)|noescape}</a>{/if}</td>
    <td>
      {*if $row->ordfilled === 1*}
        {if empty($row->ordinvcode)}
          <a href="{plink Order:postIdokladInvoice, $row->ordid}" target="idoklad">{('export'|glyph)|noescape}</a>
        {elseif !empty($row->ordcode2)}
          <a href="https://app.idoklad.cz/IssuedInvoice/Detail/{$row->ordcode2}" target="idoklad">{('front'|glyph:'iDoklad')|noescape}</a>
        {/if}
      {*/if*}
    </td>
    <td><a href="{plink //:Front:Order:status ($row|getOrderStatusKey)}">{('info'|glyph:'Status')|noescape}</a></td>
  </tr>
      {if isset($row["items"])}
          <tr {$style|noescape}>
            <td colspan="2"></td>
            <td>Položky:</td>
            <td colspan="5">
              {foreach $row["items"] as $oItem}
                {if $iterator->first}
                <table  class="table table-condensed table-hover table-bordered" style="padding: 0px; margin: 0px" width="90%">
                {/if}
                  <tr>
                    <td>{$oItem->oriname}</td>
                    <td>{$oItem->oriqty}ks</td>
                    <td>{$oItem->oriprice|formatPrice}/ks</td>
                  </tr>
                {if $iterator->last}
                </table>
                {/if}
              {/foreach}
            </td>
            <td colspan="7"></td>
          </tr>
      {/if}
  {/foreach}

  <tr>
    <td colspan="9" ><strong>Celkem:</strong></td>
    <td style="text-align: right;"><strong>{$sum|formatPrice}</strong></td>
    <td colspan="9" ></td>
  </tr>
  </table>
  <strong>Hromadné úpravy vybraných objednávek:</strong><br>
  {foreach $enum_ordstatus as $key => $text}
    {if $iterator->isFirst()}
    <select name="new_status">
    {/if}
    <option value="{$key}" {if isset($row->ordstatus) && $key==$row->ordstatus} selected="selected"{/if}>{$text}</option>
    {if $iterator->isLast()}
    </select>
    {/if}
  {/foreach}
  <input type="submit" class="btn btn-primary btn-sm" name="change_status" value="Nastavit status"> | <input type="submit" class="btn btn-primary btn-sm" name="sms_send" value="Odeslat SMS info"> |
  {*<input type="submit" class="btn btn-primary btn-sm" name="vyskladnit" value="Vyskladnit">*}
  Vybrané: <input type="submit" class="btn btn-primary btn-sm" name="export_orders" value="objednávky do PDF">

  {if $identity->isAllowed('Admin:DeliveryModeCS', 'zasilkovna') || $identity->isAllowed('Admin:DeliveryModeCS', 'ulozenka') || $identity->isAllowed('Admin:DeliveryModeCS', 'geis')}

    {if $identity->isAllowed('Admin:DeliveryModeCS', 'zasilkovna')}
      <input type="submit" class="btn btn-primary btn-sm" name="export_zasilkovna" value="Odeslat do Zásilkovny">
    {/if}
    {if $identity->isAllowed('Admin:DeliveryModeCS', 'ulozenka')}
      <input type="submit" class="btn btn-primary btn-sm" name="export_ulozenka" value="Odeslat do Uloženky">
    {/if}
    {if $identity->isAllowed('Admin:DeliveryModeCS', 'geis')}
      <br>
      <strong>GEIS balíky:</strong><br>
      <select name="geis_action">
        <option value="export">Odeslat balíky</option>
        <option value="print">Tisk</option>
        <option value="print_list">Tisk soupisky</option>
        <option value="delete">Vymazat</option>
      </select>
      <input type="submit" class="btn btn-primary btn-sm" name="export_geis" value="GEIS"><br>
      <input type="checkbox" value="1" name="makePickup"> založit nový svoz
      | Pozice štítku:
      <select name="print_position">
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
        <option value="4">4</option>
      </select>
    {/if}
    {if $identity->isAllowed('Admin:DeliveryModeCS', 'coolBalik')}
      <br>
      <strong>Boxxi balíky:</strong><br>
      <select name="coolbalik_action">
        <option value="export">Odeslat balíky</option>
      </select>
      <input type="submit" class="btn btn-primary btn-sm" name="export_coolbalik" value="Boxxi balík"><br>
    {/if}
  {/if}


  <script>

  $("#checkAll").click(function(){
    $('.ordid_chk').not(this).prop('checked', this.checked);
  });

  </script>

  </form>


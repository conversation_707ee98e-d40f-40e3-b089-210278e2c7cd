{var title = '<PERSON><PERSON><PERSON><PERSON> objednávka'}

{block #content}
  {control orderQuickForm}
{/block}

{block #footerBlock}
<script src="{$baseUri|noescape}/js/libs/jquery.autocomplete.js" type="text/javascript"></script>
<script>
  if ($('#ordifirname_ac').length) {
    $('#ordifirname_ac').autocomplete({
      serviceUrl: {$presenter->link('firNameAc')|noescape},
      onSelect: function (suggestion) {
        $('#ordusrid_ac').val(suggestion.usrid);
        $('#ordifirname_ac').val(suggestion.usrifirname);
        $('#ordiname_ac').val(suggestion.usriname);
        $('#ordilname_ac').val(suggestion.usrilname);
        $('#ordtel_ac').val(suggestion.usrtel);
        $('#ordmail_ac').val(suggestion.usrmail);
      }
    })
  }
</script>
{/block}

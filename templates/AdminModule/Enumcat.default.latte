{var title = $enum_enutypid[$presenter->getParam('typid')]}

{block #content}
  <p><a href="{plink Enumcat:edit, 0, $presenter->getParam('typid')}" class="btn btn-primary btn-sm">Přidat novou položku</a></p>
  <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Id</th>
    <th>Název</th>
    <th>Status</th>
    <th></th>
  </tr>
  {foreach $enumcats as $row}
    <tr>
      <td>{$row->enuid}</td>
      <td>{$row->enuname}</td>
      <td>{$enum_enustatus[$row->enustatus]}</td>
      <td><a href="{plink Enumcat:edit, $row->enuid, $row->enutypid}">{('edit'|glyph)|noescape}</a></td>
    </tr>
  {/foreach}
  </table>
{/block}

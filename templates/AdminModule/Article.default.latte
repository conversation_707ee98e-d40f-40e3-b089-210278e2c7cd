{var $title = '<PERSON><PERSON><PERSON><PERSON>'}

{block #content}
 <p><a href="{plink edit, 0}" class="btn btn-primary btn-sm"><PERSON><PERSON></a></p>
 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Název</th>
    <th>URL klíč</th>
    <th>Datum</th>
    <th>Zobrazení</th>
    <th>Typ</th>
    <th>Umístění</th>
    <th></th>
    <th></th>
    <th></th>
  </tr>
  {foreach $dataRows as $row}
    {php
    if (empty($row->arturlkey)) {
      $row->arturlkey = Nette\Utils\Strings::webalize($row->artname);
    }
    }
    <tr>
      <td>{$row->artname}</td>
      <td>{$row->arturlkey}</td>
      <td>{$row->artdate|date:'d.m.Y'}</td>
      <td>{$row->artcnt}</td>
      <td>{$enum_arttypid[$row->arttypid]}</td>
      <td>{$enum_arttop[$row->arttop]}</td>
      <td><a href="{plink Article:edit, $row->artid}">{('edit'|glyph)|noescape}</a></td>
      <td><a href="{plink :Front:Article:detail, $row->artid, $row->arturlkey}">{('front'|glyph)|noescape}</a></td>
      <td><a href="{plink delete, $row->artid}" onclick="return DeleteConfirm('článek {$row->artname|noescape}');">{('delete'|glyph)|noescape}</a></td>
    </tr>
  {/foreach}
  </table>
{/block}

{var title = 'Editace dodávky'}

{block #content}
   {if isset($dataRow["delid"])}
  <script>
  $(function() {
    $( ".autocomplete" ).keydown(function (event) {
      if (event.which == 13) {
        $.ajax({
            url: {plink 'autocompleteProducts'},
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            data: "proid=" + this.value,
            cache: false,
            complete: function (data) {
                if (data.readyState == 4) {
                    id = "#frmstiItemsEditForm-newitem-";
                    $(id + 'stiproid').val(data.responseJSON[0].id);
                    $('newitem_ean').val(data.responseJSON[0].ean);
                    $(id + 'stiname').val(data.responseJSON[0].value);
                } else {
                    alert('Informace se bohužel nepodařilo načíst.');
                }
            }
        });
        event.preventDefault();
        return false;
      }
    });

    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('stiname', 'stiproid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>
  {/if}



  <h3>{block #title} Editace dodávky {if $id >0} {$dataRow->delname} {/if} {/block}</h3>

  {control editForm}

  {if $id > 0 && $dataRow->delstatus !== 1}
    <a href="{plink addItems $id}">Přidat položky čtečka</a>
  {/if}

  {ifset $stoItems}
  <h4 id="edititems">Položky dodávky</h4>
  {form stoItemsEditForm}
    <ul class="error" n:if="$form->hasErrors()">
      <li n:foreach="$form->errors as $error">{($error)}</li>
    </ul>
    <table class="table table-condensed table-hover table-bordered">
      <tr>
        <th>ID zboží</th>
        <th>EAN</th>
        <th>Katalogové č.</th>
        <th>název</th>
        <th>počet</th>
        <th colspan="3"></th>
      </tr>

        {ifset $form['newitem']}
      <tr>
        <th colspan="10">nová položka</th>
      </tr>
      <tr>
        <td>{php echo $form['newitem']['stiproid']->control }</td>
        <td id="new_item_ean"></td>
        <td></td>
        <td>{php echo $form['newitem']['stiname']->control } </td>
        <td>{php echo $form['newitem']['stiqty']->control }</td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td colspan="8">{input saveitems}</td>
      </tr>
      {/ifset}

      {var $sumQty=0}
      {foreach  $form['items']->getComponents() as $cont}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
      {var $stiid=$form['items'][$cont->name]['stiid']->value}
        <td>{$stoItems[$stiid]->stiproid}</td>
        <td>{$stoItems[$stiid]->stiean}</td>
        <td>{$stoItems[$stiid]->procode}</td>
        <td>{$stoItems[$stiid]->proname} </td>
        <td>
          {if $dataRow->delstatus === 1}
          {$stoItems[$stiid]->stiqty}
          {else}
          {php echo $form['items'][$cont->name]['stiqty']->control }
          {/if}
        </td>
        {ifset $form['newitem']}
        <td><a href="{plink deleteItem, $stiid, (int)$presenter->getParameter('id')}" onclick="return DeleteConfirm('položku dodávky');">{('delete'|glyph)|noescape}</a></td>
        {/ifset}
        <td><a href="{plink Product:edit, $stoItems[$stiid]->stiproid}">{('edit'|glyph)|noescape}</a></td>
      </tr>

      {/foreach}
    </table>
  {/form}
  {/ifset}

  {ifset $statusLog}
  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie</h3>
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->deldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_delstatus[$row->delstatus]}</td>
    <td>{$row->deladmname}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}

  <a href="{plink Delivery:delete, $id}"  onclick="return DeleteConfirm('dodávku');">Vymazat dodávku</a>
  {/ifset}

  {if !empty($dataRow->delid) && $dataRow->delstatus !== 1}
      <script type="text/javascript">
            $("#frm-stoItemsEditForm-newitem-stiname").focus();
      </script>
  {/if}
{/block}

{block #footerBlock}

  <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>

{/block}

{var title = '<PERSON><PERSON><PERSON>'}

{block #content}
  <script type="text/javascript">
    $(function() {
      $('input.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat: 'dd.mm.yy'
      });

      $.datepicker.regional['cs'] = {
        closeText: 'Zavřít',
        prevText: '&#x3c;Dříve',
        nextText: 'Později&#x3e;',
        currentText: 'Nyní',
        monthNames: ['leden', 'únor', 'březen', 'duben', 'květen', 'červen', 'červenec', 'srpen',
          'zář<PERSON>', 'říjen', 'listopad', 'prosinec'
        ],
        monthNamesShort: ['led', 'úno', 'bře', 'dub', 'kvě', 'čer', 'čvc', 'srp', 'z<PERSON><PERSON>', 'říj', 'lis', 'pro'],
        dayNames: ['ned<PERSON>', 'ponděl<PERSON>', 'úterý', 'středa', 'čtvrtek', 'pátek', 'sobota'],
        dayNamesShort: ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],
        dayNamesMin: ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],
        weekHeader: 'Týd',
        dateFormat: 'dd/mm/yy',
        firstDay: 1,
        isRTL: false,
        showMonthAfterYear: false,
        yearSuffix: ''
      };

      $.datepicker.setDefaults($.datepicker.regional['cs']);

    });
  </script>
  <style type="text/css">
    <!--
    input.datepicker
    {
      border: 1px solid #C0C0C0;
      padding: 2pt;
      background: transparent url({$baseUri} + '/admin/ico/calendar.png') no-repeat right;
    }
    -->
  </style>

  <script>
  function noteSH(id) {
     if ($('#'+id).is(':visible')) {
      $('#'+id).hide();
     } else {
       $('#'+id).show();
     }
     return false;
  }
  </script>

  <p>[ <a href="{plink products}">Objednané produkty</a> ]</p>

  <div class="panel panel-primary panel-custom">

    <div class="panel-heading"><strong>Filtrace</strong></div>

    <div class="panel-body">

    {form searchForm class=>'form-inline'}

      <div class="input-group">
        <span class="input-group-addon">{label code /}</span>
        {input code class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label name /}</span>
        {input name class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label phone /}</span>
        {input phone class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label status /}</span>
        {input status class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label deldatefrom /}</span>
        {input deldatefrom class=>'form-control datepicker',  autocomplete=>"off"}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label deldateto /}</span>
        {input deldateto class=>'form-control datepicker',  autocomplete=>"off"}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label delmonth /}</span>
        {input delmonth class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label delid /}</span>
        {input delid class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label paycode /}</span>
        {input paycode class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label idoklad /}</span>
        {input idoklad class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">Řadit podle</span>
        {input orderby class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">Řadit</span>
        {input orderbytype class=>'form-control'}
      </div>

      <br>

      <div class="input-group">
        {label onlyegg}{input onlyegg}{/label} | {label notclosed}{input notclosed}{/label} | {label ispayed}{input ispayed}{/label}
      </div>

      {input search class=>"btn btn-success"}
      {input clear class=>"btn btn-default"}

    {/form}

    </div>

  </div>

  {include @ordersList.latte}
  {control paginator}

{/block}

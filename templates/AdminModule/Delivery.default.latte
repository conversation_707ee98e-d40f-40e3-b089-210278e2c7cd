{var title = 'Naskladnění'}

{block #content}
<script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('proname', 'proid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

 <h3>{block #title}Dodávky{/block}</h3>
 <p>[ <a href="{plink edit}">přidat novou dodávku</a> ]</p>

  <div class="panel panel-primary panel-custom">

  <div class="panel-heading"><strong>Filtrace</strong></div>

  <div class="panel-body">

  {form searchForm class=>'form-inline'}

    <div class="input-group">
      <span class="input-group-addon">{label status /}</span>
      {input status class=>'form-control'}
    </div>

    {input search class=>"btn btn-success"}
    {input clear class=>"btn btn-default"}

  {/form}

  </div>

</div>

 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>ID</th>
    <th>Popis</th>
    <th>Datum</th>
    <th>Status</th>
    <th colspan="2"></th>
  </tr>
  {foreach $dataRows as $row}

    {php
    $style = "";
    if ($row->delstatus == 0) {
      $style = "#FFFF80";
    } else if ($row->delstatus == 1) {
      $style = "#80FF80";
    }
    }
    <tr {if !empty($style)} style="background-color: {$style|noescape}"{/if}>
      <td>{$row->delid}</td>
      <td>{$row->delname}</td>
      <td>{if !empty($row->deldate)}{$row->deldate|date:'d.m.Y'}{/if}</td>
      <td>{$enum_delstatus[$row->delstatus]}</td>
      <td><a href="{plink Delivery:edit, $row->delid}">{('edit'|glyph)|noescape}</a></td>
    </tr>
  {/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}

# nastaveni ftp serveru
variables:
  FTP_URL: "ftp.pstrosivejce.cz/public_html/"
  FTP_USERNAME: "deploy.pstrosivejce.cz"
  FTP_PASSWORD: "dC7GNTqxoemohkLo"

## definice akci
stages:
  - deploy
  - clear-cache

# natazeni docker image s git-ftp
image: dotsunited/git-ftp

deploy:
  # nazev akce
  stage: deploy
  # spustime pozadovane skripty
  script:
    - git --version
    - git ftp --version
    # pouze pro vstupni deploy
    #- git ftp init --user $FTP_USERNAME --passwd $FTP_PASSWORD $FTP_URL
    # deploy posledniho commitu
    - git ftp push --user $FTP_USERNAME --passwd $FTP_PASSWORD $FTP_URL
  # nastaveni spusteni - pouze na master vetvi
  only:
    - master
  # manualni spusteni, kdyz je zakomentovano spousti se automaticky
  #when: manual

clear-cache:
  # nazev akce
  stage: clear-cache
  # spustime pozadovane skripty
  script:
    curl -O --insecure https://www.pstrosivejce.cz/cc.php?k=lZwJIL
  only:
    - master
  # manualni spusteni, kdyz je zakomentovano spousti se automaticky
  #when: manual

@ECHO OFF

:begin

echo ===============================================
echo c - console
echo d - deploy
echo g - gulp
echo w - gulp production
echo s - gulp styles
echo j - gulp javascripts
echo e - open explorer
echo q - quit

CHOICE /C cdgwsjeq /N

IF ERRORLEVEL 8 GOTO end
IF ERRORLEVEL 7 GOTO exp
IF ERRORLEVEL 6 GOTO js
IF ERRORLEVEL 5 GOTO css
IF ERRORLEVEL 4 GOTO prod
IF ERRORLEVEL 3 GOTO gulp
IF ERRORLEVEL 2 GOTO deploy
IF ERRORLEVEL 1 GOTO console

:console
cmd
GOTO end

:deploy
call gulp deploy
GOTO begin

:gulp
call gulp
GOTO begin

:prod
call gulp deploy
GOTO begin

:css
call gulp makecss
GOTO begin

:js
call gulp makejs
GOTO begin

:exp
start .
cmd
GOTO end

:end
